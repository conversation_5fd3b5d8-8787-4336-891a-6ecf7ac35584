import api from './api';

export interface Role {
  id: number;
  name: string;
  description: string;
  storeId: number | null;
  storeName: string | null;
  isActive: boolean;
  permissionIds: number[];
}

export interface CreateRoleRequest {
  name: string;
  description: string;
  storeId: number | null;
  isActive: boolean;
  permissionIds: number[];
}

export interface UpdateRoleRequest {
  id: number;
  name: string;
  description: string;
  storeId: number | null;
  isActive: boolean;
  permissionIds: number[];
}

const getRoles = async (): Promise<Role[]> => {
  const response = await api.get('/admin/role');
  return response.data;
};

const getRole = async (id: number): Promise<Role> => {
  const response = await api.get(`/admin/role/${id}`);
  return response.data;
};

const createRole = async (role: CreateRoleRequest): Promise<Role> => {
  const response = await api.post('/admin/role', role);
  return response.data;
};

const updateRole = async (role: UpdateRoleRequest): Promise<void> => {
  await api.put(`/admin/role/${role.id}`, role);
};

const deleteRole = async (id: number): Promise<void> => {
  await api.delete(`/admin/role/${id}`);
};

const getRolesByStore = async (storeId: number): Promise<Role[]> => {
  const response = await api.get(`/admin/role/store/${storeId}`);
  return response.data;
};

export const roleService = {
  getRoles,
  getRole,
  createRole,
  updateRole,
  deleteRole,
  getRolesByStore,
};
