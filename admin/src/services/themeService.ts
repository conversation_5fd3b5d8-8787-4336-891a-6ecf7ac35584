import api from './api';

const THEME_API_URL = '/admin/theme-configuration';

const themeService = {
  // Get all themes (for super admin) or by store
  getThemesByStore: async (storeId?: number) => {
    // If storeId is undefined (super admin), get all themes
    if (storeId === undefined) {
      const response = await api.get(THEME_API_URL);
      return response.data;
    }
    // Otherwise get themes for specific store
    const response = await api.get(`${THEME_API_URL}/store/${storeId}`);
    return response.data;
  },

  // Get all themes (for super admin)
  getAllThemes: async () => {
    const response = await api.get(THEME_API_URL);
    return response.data;
  },

  // Get active theme for a store
  getActiveTheme: async (storeId: number) => {
    if (!storeId) {
      throw new Error('Store ID is required to get active theme');
    }
    const response = await api.get(`${THEME_API_URL}/store/${storeId}/active`);
    return response.data;
  },

  // Get a specific theme by ID
  getThemeById: async (id: number) => {
    const response = await api.get(`${THEME_API_URL}/${id}`);
    return response.data;
  },

  // Create a new theme configuration
  createTheme: async (themeData: any) => {
    const response = await api.post(THEME_API_URL, themeData);
    return response.data;
  },

  // Update a theme configuration
  updateTheme: async (id: number, themeData: any) => {
    const response = await api.put(`${THEME_API_URL}/${id}`, themeData);
    return response.data;
  },

  // Activate a theme configuration
  activateTheme: async (id: number) => {
    const response = await api.post(`${THEME_API_URL}/${id}/activate`);
    return response.data;
  },

  // Delete a theme configuration
  deleteTheme: async (id: number) => {
    const response = await api.delete(`${THEME_API_URL}/${id}`);
    return response.data;
  },

  // Duplicate a theme configuration
  duplicateTheme: async (id: number) => {
    const response = await api.post(`${THEME_API_URL}/${id}/duplicate`);
    return response.data;
  },

  // Assign theme to a store
  assignThemeToStore: async (themeId: number, storeId: number) => {
    const response = await api.post(`${THEME_API_URL}/${themeId}/assign-to-store/${storeId}`);
    return response.data;
  },

  // Get stores assigned to a theme
  getThemeAssignments: async (themeId: number) => {
    const response = await api.get(`${THEME_API_URL}/${themeId}/assignments`);
    return response.data;
  },

  // Get all available themes for a store
  getAvailableThemesForStore: async (storeId: number) => {
    const response = await api.get(`${THEME_API_URL}/available-for-store/${storeId}`);
    return response.data;
  }
};

export default themeService;
