import api from './api';

export interface UserRole {
  userId: string;
  userEmail: string;
  userFullName: string;
  roleIds: number[];
}

const getUserRoles = async (): Promise<UserRole[]> => {
  const response = await api.get('/admin/userrole');
  return response.data;
};

const getUserRole = async (userId: string): Promise<UserRole> => {
  const response = await api.get(`/admin/userrole/${userId}`);
  return response.data;
};

const assignRolesToUser = async (userRole: UserRole): Promise<UserRole> => {
  const response = await api.post('/admin/userrole', userRole);
  return response.data;
};

const getUserRolesByStore = async (storeId: number): Promise<UserRole[]> => {
  const response = await api.get(`/admin/userrole/store/${storeId}`);
  return response.data;
};

export const userRoleService = {
  getUserRoles,
  getUserRole,
  assignRolesToUser,
  getUserRolesByStore,
};
