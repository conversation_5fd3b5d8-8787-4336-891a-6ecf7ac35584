import api from './api';

export interface ThemeConfiguration {
  id: number;
  storeId: number;
  storeName: string;
  name: string;
  description: string;
  isActive: boolean;
  
  // Primary Colors
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  
  // Text Colors
  textPrimaryColor: string;
  textSecondaryColor: string;
  textLightColor: string;
  
  // Background Colors
  backgroundPrimaryColor: string;
  backgroundSecondaryColor: string;
  backgroundAccentColor: string;
  
  // Button Styles
  buttonPrimaryColor: string;
  buttonSecondaryColor: string;
  buttonTextColor: string;
  buttonBorderRadius: string;
  
  // Card Styles
  cardBackgroundColor: string;
  cardBorderColor: string;
  cardBorderRadius: string;
  cardShadow: string;
  
  // Typography
  headingFontFamily: string;
  bodyFontFamily: string;
  fontBaseSize: string;
  
  // Spacing
  spacingUnit: string;
  containerMaxWidth: string;
  containerPadding: string;
  
  // Header Styles
  headerBackgroundColor: string;
  headerTextColor: string;
  headerHeight: string;
  
  // Footer Styles
  footerBackgroundColor: string;
  footerTextColor: string;
  
  // Navigation Styles
  navLinkColor: string;
  navLinkActiveColor: string;
  navLinkHoverColor: string;
  
  // Form Styles
  inputBackgroundColor: string;
  inputBorderColor: string;
  inputBorderRadius: string;
  inputFocusBorderColor: string;
  
  // Custom CSS
  customCSS: string;
  
  // Timestamps
  createdAt: string;
  updatedAt: string;
}

export interface CreateThemeConfigurationRequest {
  storeId: number;
  name: string;
  description: string;
  isActive: boolean;
  
  // Primary Colors
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  
  // Text Colors
  textPrimaryColor: string;
  textSecondaryColor: string;
  textLightColor: string;
  
  // Background Colors
  backgroundPrimaryColor: string;
  backgroundSecondaryColor: string;
  backgroundAccentColor: string;
  
  // Button Styles
  buttonPrimaryColor: string;
  buttonSecondaryColor: string;
  buttonTextColor: string;
  buttonBorderRadius: string;
  
  // Card Styles
  cardBackgroundColor: string;
  cardBorderColor: string;
  cardBorderRadius: string;
  cardShadow: string;
  
  // Typography
  headingFontFamily: string;
  bodyFontFamily: string;
  fontBaseSize: string;
  
  // Spacing
  spacingUnit: string;
  containerMaxWidth: string;
  containerPadding: string;
  
  // Header Styles
  headerBackgroundColor: string;
  headerTextColor: string;
  headerHeight: string;
  
  // Footer Styles
  footerBackgroundColor: string;
  footerTextColor: string;
  
  // Navigation Styles
  navLinkColor: string;
  navLinkActiveColor: string;
  navLinkHoverColor: string;
  
  // Form Styles
  inputBackgroundColor: string;
  inputBorderColor: string;
  inputBorderRadius: string;
  inputFocusBorderColor: string;
  
  // Custom CSS
  customCSS: string;
}

export interface UpdateThemeConfigurationRequest extends Omit<CreateThemeConfigurationRequest, 'storeId'> {}

// Get all theme configurations (for super admin)
const getAllThemeConfigurations = async (): Promise<ThemeConfiguration[]> => {
  const response = await api.get('/admin/theme-configuration');
  return response.data;
};

// Get theme configurations for a specific store
const getThemeConfigurationsByStore = async (storeId: number): Promise<ThemeConfiguration[]> => {
  const response = await api.get(`/admin/theme-configuration/store/${storeId}`);
  return response.data;
};

// Get active theme configuration for a specific store
const getActiveThemeConfigurationByStore = async (storeId: number): Promise<ThemeConfiguration> => {
  const response = await api.get(`/admin/theme-configuration/store/${storeId}/active`);
  return response.data;
};

// Get theme configuration by ID
const getThemeConfigurationById = async (id: number): Promise<ThemeConfiguration> => {
  const response = await api.get(`/admin/theme-configuration/${id}`);
  return response.data;
};

// Create a new theme configuration
const createThemeConfiguration = async (config: CreateThemeConfigurationRequest): Promise<ThemeConfiguration> => {
  const response = await api.post('/admin/theme-configuration', config);
  return response.data;
};

// Update an existing theme configuration
const updateThemeConfiguration = async (id: number, config: UpdateThemeConfigurationRequest): Promise<void> => {
  await api.put(`/admin/theme-configuration/${id}`, config);
};

// Activate a theme configuration
const activateThemeConfiguration = async (id: number): Promise<void> => {
  await api.post(`/admin/theme-configuration/${id}/activate`);
};

// Delete a theme configuration
const deleteThemeConfiguration = async (id: number): Promise<void> => {
  await api.delete(`/admin/theme-configuration/${id}`);
};

// Duplicate a theme configuration
const duplicateThemeConfiguration = async (id: number): Promise<ThemeConfiguration> => {
  const response = await api.post(`/admin/theme-configuration/${id}/duplicate`);
  return response.data;
};

export const themeConfigService = {
  getAllThemeConfigurations,
  getThemeConfigurationsByStore,
  getActiveThemeConfigurationByStore,
  getThemeConfigurationById,
  createThemeConfiguration,
  updateThemeConfiguration,
  activateThemeConfiguration,
  deleteThemeConfiguration,
  duplicateThemeConfiguration
};

export default themeConfigService;
