import api from './api';

export interface WebsiteConfiguration {
  id: number;
  storeId: number;
  storeName: string;
  websiteTitle: string;
  logoUrl: string;
  metaDescription: string;
  metaKeywords: string;
  announcementText: string;
  showAnnouncement: boolean;
  phone: string;
  email: string;
  address: string;
  facebookUrl: string;
  instagramUrl: string;
  twitterUrl: string;
  whatsappNumber: string;
  youtubeUrl: string;
  showBannerSection: boolean;
  showCategorySection: boolean;
  showNewArrivalsSection: boolean;
  showCollectionSection: boolean;
  showBestSellingSection: boolean;
  bannerTitle: string;
  bannerSubtitle: string;
  bannerButtonText: string;
  bannerButtonLink: string;
  categorySectionTitle: string;
  newArrivalsSectionTitle: string;
  collectionSectionTitle: string;
  bestSellingSectionTitle: string;
  createdAt: string;
  updatedAt: string;
}

export interface UpdateWebsiteConfigurationRequest {
  websiteTitle: string;
  logoUrl: string;
  metaDescription: string;
  metaKeywords: string;
  announcementText: string;
  showAnnouncement: boolean;
  phone: string;
  email: string;
  address: string;
  facebookUrl: string;
  instagramUrl: string;
  twitterUrl: string;
  whatsappNumber: string;
  youtubeUrl: string;
  showBannerSection: boolean;
  showCategorySection: boolean;
  showNewArrivalsSection: boolean;
  showCollectionSection: boolean;
  showBestSellingSection: boolean;
  bannerTitle: string;
  bannerSubtitle: string;
  bannerButtonText: string;
  bannerButtonLink: string;
  categorySectionTitle: string;
  newArrivalsSectionTitle: string;
  collectionSectionTitle: string;
  bestSellingSectionTitle: string;
}

export interface SectionVisibilityRequest {
  showBannerSection?: boolean;
  showCategorySection?: boolean;
  showNewArrivalsSection?: boolean;
  showCollectionSection?: boolean;
  showBestSellingSection?: boolean;
}

// Get all website configurations (for super admin)
const getAllWebsiteConfigurations = async (): Promise<WebsiteConfiguration[]> => {
  const response = await api.get('/admin/website-configuration');
  return response.data;
};

// Get website configuration for a specific store
const getWebsiteConfigurationByStore = async (storeId: number): Promise<WebsiteConfiguration> => {
  const response = await api.get(`/admin/website-configuration/${storeId}`);
  return response.data;
};

// Update website configuration for a specific store
const updateWebsiteConfiguration = async (storeId: number, config: UpdateWebsiteConfigurationRequest): Promise<void> => {
  await api.put(`/admin/website-configuration/${storeId}`, config);
};

// Update section visibility for a specific store
const updateSectionVisibility = async (storeId: number, sectionVisibility: SectionVisibilityRequest): Promise<void> => {
  await api.put(`/admin/website-configuration/${storeId}/sections`, sectionVisibility);
};

export const websiteConfigService = {
  getAllWebsiteConfigurations,
  getWebsiteConfigurationByStore,
  updateWebsiteConfiguration,
  updateSectionVisibility
};

export default websiteConfigService;
