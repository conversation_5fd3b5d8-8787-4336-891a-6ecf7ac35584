import api from './api';

export interface UserModulePermission {
  id: number;
  userId: string;
  userFullName?: string;
  userName?: string;
  module: string;
  canView: boolean;
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
  storeId?: number;
  storeName?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateUserModulePermissionRequest {
  userId: string;
  module: string;
  canView: boolean;
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
  storeId?: number;
}

export interface UpdateUserModulePermissionRequest {
  canView: boolean;
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
}

const getUserModulePermissions = async (): Promise<UserModulePermission[]> => {
  const response = await api.get('/admin/user-permissions');
  return response.data;
};

const getUserPermissionsByUser = async (userId: string): Promise<UserModulePermission[]> => {
  const response = await api.get(`/admin/user-permissions/user/${userId}`);
  return response.data;
};

const getUserPermissionsByStore = async (storeId: number): Promise<UserModulePermission[]> => {
  const response = await api.get(`/admin/user-permissions/store/${storeId}`);
  return response.data;
};

const createUserModulePermission = async (permission: CreateUserModulePermissionRequest): Promise<UserModulePermission> => {
  const response = await api.post('/admin/user-permissions', permission);
  return response.data;
};

const updateUserModulePermission = async (id: number, permission: UpdateUserModulePermissionRequest): Promise<void> => {
  await api.put(`/admin/user-permissions/${id}`, permission);
};

const deleteUserModulePermission = async (id: number): Promise<void> => {
  await api.delete(`/admin/user-permissions/${id}`);
};

export const userModulePermissionService = {
  getUserModulePermissions,
  getUserPermissionsByUser,
  getUserPermissionsByStore,
  createUserModulePermission,
  updateUserModulePermission,
  deleteUserModulePermission,
};

export default userModulePermissionService;
