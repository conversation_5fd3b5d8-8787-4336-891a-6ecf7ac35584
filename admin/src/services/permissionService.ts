import api from './api';

export interface Permission {
  id: number;
  name: string;
  description: string;
  module: string;
  action: string;
}

const getPermissions = async (): Promise<Permission[]> => {
  const response = await api.get('/admin/permission');
  return response.data;
};

const getModules = async (): Promise<string[]> => {
  const response = await api.get('/admin/permission/modules');
  return response.data;
};

const getUserPermissions = async (): Promise<Permission[]> => {
  const response = await api.get('/admin/permission/user');
  return response.data;
};

const getRolePermissions = async (roleId: number): Promise<Permission[]> => {
  const response = await api.get(`/admin/permission/role/${roleId}`);
  return response.data;
};

export const permissionService = {
  getPermissions,
  getModules,
  getUserPermissions,
  getRolePermissions,
};
