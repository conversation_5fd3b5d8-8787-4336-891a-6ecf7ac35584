import api from './api';

export interface Store {
  id: number;
  name: string;
  description: string;
  storeKey: string;
  isActive: boolean;
  primaryDomain: string;
  additionalDomains: string[];
  logoUrl: string;
  faviconUrl: string;
  email: string;
  phone: string;
  address: string;
  facebookUrl: string;
  instagramUrl: string;
  twitterUrl: string;
  whatsappNumber: string;
  createdAt: string;
  updatedAt: string;
  themeConfiguration?: any;
  websiteConfiguration?: any;
}

export interface CreateStoreRequest {
  name: string;
  description: string;
  isActive: boolean;
  primaryDomain: string;
  additionalDomains: string[];
  logoUrl: string;
  faviconUrl: string;
  email: string;
  phone: string;
  address: string;
  facebookUrl: string;
  instagramUrl: string;
  twitterUrl: string;
  whatsappNumber: string;
}

export interface UpdateStoreRequest {
  id: number;
  name: string;
  description: string;
  isActive: boolean;
  primaryDomain: string;
  additionalDomains: string[];
  logoUrl: string;
  faviconUrl: string;
  email: string;
  phone: string;
  address: string;
  facebookUrl: string;
  instagramUrl: string;
  twitterUrl: string;
  whatsappNumber: string;
}

export interface StoreAdmin {
  id: number;
  userId: string;
  userEmail: string;
  userName: string;
  storeId: number;
  storeName: string;
  canManageProducts: boolean;
  canManageOrders: boolean;
  canManageCustomers: boolean;
  canManageSettings: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateStoreAdminRequest {
  userEmail: string;
  storeId: number;
  canManageProducts: boolean;
  canManageOrders: boolean;
  canManageCustomers: boolean;
  canManageSettings: boolean;
}

export interface UpdateStoreAdminRequest {
  id: number;
  canManageProducts: boolean;
  canManageOrders: boolean;
  canManageCustomers: boolean;
  canManageSettings: boolean;
}

const getStores = async (): Promise<Store[]> => {
  const response = await api.get('/admin/store');
  return response.data;
};

const getStore = async (id: number): Promise<Store> => {
  const response = await api.get(`/admin/store/${id}`);
  return response.data;
};

const createStore = async (store: CreateStoreRequest): Promise<Store> => {
  const response = await api.post('/admin/store', store);
  return response.data;
};

const updateStore = async (store: UpdateStoreRequest): Promise<void> => {
  await api.put(`/admin/store/${store.id}`, store);
};

const deleteStore = async (id: number): Promise<void> => {
  await api.delete(`/admin/store/${id}`);
};

// Store Admin methods
const getStoreAdmins = async (storeId: number): Promise<StoreAdmin[]> => {
  const response = await api.get(`/admin/storeAdmin/admins/${storeId}`);
  return response.data;
};

const createStoreAdmin = async (admin: CreateStoreAdminRequest): Promise<StoreAdmin> => {
  const response = await api.post('/admin/storeAdmin/admins', admin);
  return response.data;
};

const updateStoreAdmin = async (id: number, admin: UpdateStoreAdminRequest): Promise<void> => {
  await api.put(`/admin/storeAdmin/admins/${id}`, admin);
};

const deleteStoreAdmin = async (id: number): Promise<void> => {
  await api.delete(`/admin/storeAdmin/admins/${id}`);
};

// Theme Configuration methods
const getThemeConfiguration = async (storeId: number): Promise<any> => {
  const response = await api.get(`/admin/themeConfiguration/${storeId}`);
  return response.data;
};

const updateThemeConfiguration = async (storeId: number, config: any): Promise<void> => {
  await api.put(`/admin/themeConfiguration/${storeId}`, config);
};

// Website Configuration methods
const getWebsiteConfiguration = async (storeId: number): Promise<any> => {
  const response = await api.get(`/admin/websiteConfiguration/${storeId}`);
  return response.data;
};

const updateWebsiteConfiguration = async (storeId: number, config: any): Promise<void> => {
  await api.put(`/admin/websiteConfiguration/${storeId}`, config);
};

export const storeService = {
  // Store methods
  getStores,
  getStore,
  createStore,
  updateStore,
  deleteStore,

  // Store Admin methods
  getStoreAdmins,
  createStoreAdmin,
  updateStoreAdmin,
  deleteStoreAdmin,

  // Configuration methods
  getThemeConfiguration,
  updateThemeConfiguration,
  getWebsiteConfiguration,
  updateWebsiteConfiguration,
};
