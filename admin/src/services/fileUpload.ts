import api from './api';

export interface ImageItem {
  id: number;
  name: string;
  url: string;
  size: number;
  contentType: string;
  uploadDate: string;
  isPublic: boolean;
}

/**
 * Upload a single image file to the server
 * @param file The file to upload
 * @returns Promise with the URL of the uploaded image
 */
export const uploadImage = async (file: File): Promise<string> => {
  const formData = new FormData();
  formData.append('file', file);

  try {
    const response = await api.post('/admin/FileUploadAdmin/images', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data.url;
  } catch (error) {
    console.error('Error uploading image:', error);
    throw error;
  }
};

/**
 * Upload multiple image files to the server
 * @param files Array of files to upload
 * @returns Promise with an array of URLs for the uploaded images
 */
export const uploadMultipleImages = async (files: File[]): Promise<string[]> => {
  const formData = new FormData();
  files.forEach(file => {
    formData.append('files', file);
  });

  try {
    const response = await api.post('/admin/FileUploadAdmin/bulk-images', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data.urls;
  } catch (error) {
    console.error('Error uploading multiple images:', error);
    throw error;
  }
};

/**
 * Delete an image from the server
 * @param fileName The filename to delete
 * @returns Promise that resolves when the image is deleted
 */
export const deleteImage = async (fileName: string): Promise<void> => {
  try {
    // Extract filename from URL if a full URL is provided
    const name = fileName.includes('/')
      ? fileName.split('/').pop()
      : fileName;

    await api.delete(`/admin/FileUploadAdmin/images/${name}`);
  } catch (error) {
    console.error('Error deleting image:', error);
    throw error;
  }
};

/**
 * Get a paginated list of images from the server
 * @param page Page number (1-based)
 * @param pageSize Number of items per page
 * @returns Promise with the images and pagination information
 */
export const getImages = async (page: number = 1, pageSize: number = 20): Promise<{ images: ImageItem[], totalCount: number, currentPage: number, pageSize: number, totalPages: number }> => {
  try {
    const response = await api.get('/admin/FileUploadAdmin/images', {
      params: { page, pageSize }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching images:', error);
    throw error;
  }
};
