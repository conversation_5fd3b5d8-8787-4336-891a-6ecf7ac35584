import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { 
  fetchUserModulePermissions, 
  createUserModulePermission, 
  updateUserModulePermission, 
  deleteUserModulePermission 
} from '../../store/slices/userModulePermissionSlice';
import { AppDispatch, RootState } from '../../store';
import { 
  Box, 
  Button, 
  Card, 
  CardContent, 
  Checkbox, 
  Dialog, 
  DialogActions, 
  DialogContent, 
  DialogTitle, 
  FormControl, 
  FormControlLabel, 
  Grid, 
  InputLabel, 
  MenuItem, 
  Paper, 
  Select, 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow, 
  Typography 
} from '@mui/material';
import { Add, Delete, Edit } from '@mui/icons-material';
import { CreateUserModulePermissionRequest, UpdateUserModulePermissionRequest } from '../../services/userModulePermissionService';
import { toast } from 'react-toastify';

const UserModulePermissionPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { permissions, loading, error } = useSelector((state: RootState) => state.userModulePermissions);
  const { users } = useSelector((state: RootState) => state.users);
  const { stores } = useSelector((state: RootState) => state.store);
  const [openDialog, setOpenDialog] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [selectedPermission, setSelectedPermission] = useState<number | null>(null);
  const [formData, setFormData] = useState<CreateUserModulePermissionRequest>({
    userId: '',
    module: '',
    canView: true,
    canCreate: false,
    canEdit: false,
    canDelete: false,
    storeId: undefined
  });

  // Available modules
  const modules = [
    'Users',
    'Roles',
    'Products',
    'Orders',
    'Stores',
    'Customers',
    'Collections',
    'Categories',
    'Coupons',
    'Inventory',
    'Settings',
    'Navigation',
    'WebsiteConfiguration',
    'ThemeConfiguration'
  ];

  useEffect(() => {
    dispatch(fetchUserModulePermissions());
  }, [dispatch]);

  const handleOpenDialog = (edit = false, permissionId?: number) => {
    if (edit && permissionId) {
      const permission = permissions.find(p => p.id === permissionId);
      if (permission) {
        setFormData({
          userId: permission.userId,
          module: permission.module,
          canView: permission.canView,
          canCreate: permission.canCreate,
          canEdit: permission.canEdit,
          canDelete: permission.canDelete,
          storeId: permission.storeId
        });
        setSelectedPermission(permissionId);
        setEditMode(true);
      }
    } else {
      setFormData({
        userId: '',
        module: '',
        canView: true,
        canCreate: false,
        canEdit: false,
        canDelete: false,
        storeId: undefined
      });
      setEditMode(false);
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value } = e.target;
    if (name) {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  const handleSubmit = async () => {
    try {
      if (editMode && selectedPermission) {
        await dispatch(updateUserModulePermission({
          id: selectedPermission,
          permission: {
            canView: formData.canView,
            canCreate: formData.canCreate,
            canEdit: formData.canEdit,
            canDelete: formData.canDelete
          }
        }));
        toast.success('Permission updated successfully');
      } else {
        await dispatch(createUserModulePermission(formData));
        toast.success('Permission created successfully');
      }
      handleCloseDialog();
    } catch (error) {
      toast.error('An error occurred');
      console.error('Error saving permission:', error);
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this permission?')) {
      try {
        await dispatch(deleteUserModulePermission(id));
        toast.success('Permission deleted successfully');
      } catch (error) {
        toast.error('An error occurred');
        console.error('Error deleting permission:', error);
      }
    }
  };

  if (loading && permissions.length === 0) {
    return <Typography>Loading...</Typography>;
  }

  if (error) {
    return <Typography color="error">Error: {error}</Typography>;
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">User Module Permissions</Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<Add />}
          onClick={() => handleOpenDialog()}
        >
          Add Permission
        </Button>
      </Box>

      <Card>
        <CardContent>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>User</TableCell>
                  <TableCell>Module</TableCell>
                  <TableCell>Store</TableCell>
                  <TableCell>View</TableCell>
                  <TableCell>Create</TableCell>
                  <TableCell>Edit</TableCell>
                  <TableCell>Delete</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {permissions.map(permission => (
                  <TableRow key={permission.id}>
                    <TableCell>{permission.userFullName || permission.userName}</TableCell>
                    <TableCell>{permission.module}</TableCell>
                    <TableCell>{permission.storeName || 'Global'}</TableCell>
                    <TableCell>
                      <Checkbox checked={permission.canView} disabled />
                    </TableCell>
                    <TableCell>
                      <Checkbox checked={permission.canCreate} disabled />
                    </TableCell>
                    <TableCell>
                      <Checkbox checked={permission.canEdit} disabled />
                    </TableCell>
                    <TableCell>
                      <Checkbox checked={permission.canDelete} disabled />
                    </TableCell>
                    <TableCell>
                      <Button
                        size="small"
                        startIcon={<Edit />}
                        onClick={() => handleOpenDialog(true, permission.id)}
                      >
                        Edit
                      </Button>
                      <Button
                        size="small"
                        color="error"
                        startIcon={<Delete />}
                        onClick={() => handleDelete(permission.id)}
                      >
                        Delete
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
                {permissions.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={8} align="center">
                      No permissions found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>{editMode ? 'Edit Permission' : 'Add Permission'}</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            {!editMode && (
              <>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>User</InputLabel>
                    <Select
                      name="userId"
                      value={formData.userId}
                      onChange={handleInputChange}
                      label="User"
                    >
                      {users.map(user => (
                        <MenuItem key={user.id} value={user.id}>
                          {user.firstName} {user.lastName} ({user.email})
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>Module</InputLabel>
                    <Select
                      name="module"
                      value={formData.module}
                      onChange={handleInputChange}
                      label="Module"
                    >
                      {modules.map(module => (
                        <MenuItem key={module} value={module}>
                          {module}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>Store (Optional)</InputLabel>
                    <Select
                      name="storeId"
                      value={formData.storeId || ''}
                      onChange={handleInputChange}
                      label="Store (Optional)"
                    >
                      <MenuItem value="">Global (All Stores)</MenuItem>
                      {stores.map(store => (
                        <MenuItem key={store.id} value={store.id}>
                          {store.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
              </>
            )}
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Permissions
              </Typography>
              <FormControlLabel
                control={
                  <Checkbox
                    name="canView"
                    checked={formData.canView}
                    onChange={handleCheckboxChange}
                  />
                }
                label="View"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    name="canCreate"
                    checked={formData.canCreate}
                    onChange={handleCheckboxChange}
                  />
                }
                label="Create"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    name="canEdit"
                    checked={formData.canEdit}
                    onChange={handleCheckboxChange}
                  />
                }
                label="Edit"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    name="canDelete"
                    checked={formData.canDelete}
                    onChange={handleCheckboxChange}
                  />
                }
                label="Delete"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSubmit} variant="contained" color="primary">
            {editMode ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default UserModulePermissionPage;
