import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Box, Typography, Paper, Tabs, Tab } from '@mui/material';
import { RootState } from '../store';
import StoreThemeSelector from '../components/store/StoreThemeSelector';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`store-theme-tabpanel-${index}`}
      aria-labelledby={`store-theme-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `store-theme-tab-${index}`,
    'aria-controls': `store-theme-tabpanel-${index}`,
  };
}

const StoreThemeManagement: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const { currentStore } = useSelector((state: RootState) => state.store);
  
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Typography variant="h4" gutterBottom>
        Store Theme Management
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Customize your store's appearance by selecting and configuring themes.
      </Typography>

      <Paper sx={{ width: '100%', mb: 4 }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="store theme management tabs">
          <Tab label="Theme Selection" {...a11yProps(0)} />
          <Tab label="Theme Customization" {...a11yProps(1)} />
        </Tabs>

        <TabPanel value={tabValue} index={0}>
          {currentStore && <StoreThemeSelector storeId={currentStore.id} />}
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Typography variant="h6">Theme Customization</Typography>
          <Typography variant="body1" color="text.secondary">
            Customize the active theme with your brand colors and styles.
          </Typography>
          {/* Add theme customization component here */}
        </TabPanel>
      </Paper>
    </Box>
  );
};

export default StoreThemeManagement;