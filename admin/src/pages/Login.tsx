import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import {
  Typography,
  Container,
  Box,
  Paper,
  TextField,
  Button,
  Grid,
  Divider,
  Alert,
  CircularProgress
} from '@mui/material';
import { login, clearError } from '../store/slices/authSlice';
import { RootState } from '../store';

const AdminLogin: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated, user, loading, error } = useSelector((state: RootState) => state.auth);

  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // Get redirect path from location state or default to admin dashboard
  const from = location.state?.from?.pathname || '/admin';

  useEffect(() => {
    // Redirect if already authenticated as admin
    if (isAuthenticated && user?.roles.includes('Admin')) {
      navigate(from, { replace: true });
    } else if (isAuthenticated) {
      // If authenticated but not admin, redirect to home
      navigate('/', { replace: true });
    }

    // Clear any previous errors
    // @ts-ignore
    dispatch(clearError());
  }, [isAuthenticated, user, navigate, dispatch, from]);

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.email) errors.email = 'Email is required';
    else if (!/\S+@\S+\.\S+/.test(formData.email)) errors.email = 'Email is invalid';

    if (!formData.password) errors.password = 'Password is required';

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });

    // Clear error when user types
    if (formErrors[name]) {
      setFormErrors({
        ...formErrors,
        [name]: ''
      });
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      // @ts-ignore
      dispatch(login(formData));
    }
  };

  return (
    <Container maxWidth="sm" sx={{ py: 8 }}>
      <Paper
        sx={{
          p: 4,
          borderRadius: 2,
          boxShadow: '0 4px 20px rgba(0,0,0,0.1)'
        }}
      >
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold', color: '#ff3f6c' }}>
            Admin Login
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Login to access the admin dashboard
          </Typography>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <form onSubmit={handleSubmit}>
          <TextField
            fullWidth
            label="Email"
            name="email"
            type="email"
            value={formData.email}
            onChange={handleInputChange}
            error={!!formErrors.email}
            helperText={formErrors.email}
            margin="normal"
            required
            sx={{ mb: 2 }}
          />

          <TextField
            fullWidth
            label="Password"
            name="password"
            type="password"
            value={formData.password}
            onChange={handleInputChange}
            error={!!formErrors.password}
            helperText={formErrors.password}
            margin="normal"
            required
            sx={{ mb: 3 }}
          />

          <Button
            type="submit"
            fullWidth
            variant="contained"
            color="primary"
            size="large"
            disabled={loading}
            sx={{
              py: 1.5,
              backgroundColor: '#ff3f6c',
              '&:hover': { backgroundColor: '#ff527b' },
              mb: 2
            }}
          >
            {loading ? <CircularProgress size={24} color="inherit" /> : 'Login'}
          </Button>

          <Box sx={{ textAlign: 'center', mt: 2 }}>
            <Typography variant="body2" color="text.secondary">
              Not an admin? <Link to="/login" style={{ color: '#ff3f6c', textDecoration: 'none' }}>Customer Login</Link>
            </Typography>
          </Box>
        </form>

        <Divider sx={{ my: 3 }} />

        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Demo Admin Accounts:
          </Typography>
          <Typography variant="body2" color="text.secondary">
            <EMAIL> / Admin123! (Super Admin)
          </Typography>
          <Typography variant="body2" color="text.secondary">
            <EMAIL> / Admin123! (Admin)
          </Typography>
        </Box>
      </Paper>
    </Container>
  );
};

export default AdminLogin;
