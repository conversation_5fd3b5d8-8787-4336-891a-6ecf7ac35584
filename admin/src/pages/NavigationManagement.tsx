import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import {
  Box,
  Typography,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControlLabel,
  Switch,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  IconButton,
  Chip
} from '@mui/material';
import { Add, Edit, Delete, Refresh } from '@mui/icons-material';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../store';
import { fetchNavigationMenus, createNavigationMenu, updateNavigationMenu, deleteNavigationMenu, NavigationMenu } from '../store/slices/navigationSlice';
import StoreSelector from '../components/common/StoreSelector';

// Helper function to check if user is super admin
const isSuperAdmin = (user: any) => {
  return user?.roles?.includes('SuperAdmin');
};

interface NavigationMenuFormData {
  id?: number;
  name: string;
  url: string;
  parentId: number | null;
  displayOrder: number;
  isActive: boolean;
  icon: string | null;
  storeId: number;
}

const NavigationManagement: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { menus, loading, error } = useSelector((state: RootState) => state.navigation);
  const { selectedStore, accessibleStores } = useSelector((state: RootState) => state.store);
  const { user } = useSelector((state: RootState) => state.auth);

  const [openDialog, setOpenDialog] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [formData, setFormData] = useState<NavigationMenuFormData>({
    name: '',
    url: '',
    parentId: null,
    displayOrder: 0,
    isActive: true,
    icon: null,
    storeId: 1
  });

  useEffect(() => {
    loadNavigationMenus();
  }, [dispatch, selectedStore]);

  const loadNavigationMenus = () => {
    dispatch(fetchNavigationMenus(selectedStore?.id));
  };

  const handleOpenDialog = (isEdit: boolean, menu?: NavigationMenu) => {
    setIsEditMode(isEdit);
    if (isEdit && menu) {
      setFormData({
        id: menu.id,
        name: menu.name,
        url: menu.url,
        parentId: menu.parentId || null,
        displayOrder: menu.displayOrder,
        isActive: menu.isActive,
        icon: menu.icon || null,
        storeId: menu.storeId
      });
    } else {
      // Use the selected store ID
      const defaultStoreId = selectedStore?.id || 1;

      setFormData({
        name: '',
        url: '',
        parentId: null,
        displayOrder: menus.length + 1,
        isActive: true,
        icon: null,
        storeId: defaultStoreId
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }> | { target: { name?: string; value: unknown } }) => {
    const { name, value } = e.target;
    if (name) {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  const handleSwitchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData({
      ...formData,
      [name]: checked
    });
  };

  const handleSubmit = () => {
    if (isEditMode && formData.id) {
      dispatch(updateNavigationMenu({ id: formData.id, menu: formData }))
        .unwrap()
        .then(() => {
          toast.success('Navigation menu updated successfully');
          handleCloseDialog();
        })
        .catch((error) => {
          toast.error(`Failed to update navigation menu: ${error}`);
        });
    } else {
      dispatch(createNavigationMenu(formData))
        .unwrap()
        .then(() => {
          toast.success('Navigation menu created successfully');
          handleCloseDialog();
        })
        .catch((error) => {
          toast.error(`Failed to create navigation menu: ${error}`);
        });
    }
  };

  const handleDelete = (id: number) => {
    // Check if the menu has children
    const hasChildren = menus.some(menu => menu.parentId === id);

    if (hasChildren) {
      toast.error('Cannot delete a menu that has child menus. Please delete or reassign child menus first.');
      return;
    }

    if (window.confirm('Are you sure you want to delete this navigation menu?')) {
      dispatch(deleteNavigationMenu(id))
        .unwrap()
        .then(() => {
          toast.success('Navigation menu deleted successfully');
        })
        .catch((error) => {
          toast.error(`Failed to delete navigation menu: ${error}`);
        });
    }
  };

  const handleRefresh = () => {
    loadNavigationMenus();
  };

  // Get all possible parent menus (excluding the current menu being edited)
  const possibleParents = menus.filter(menu => !formData.id || menu.id !== formData.id);

  // Available icons
  const icons = [
    { value: 'home', label: 'Home' },
    { value: 'category', label: 'Category' },
    { value: 'collections', label: 'Collections' },
    { value: 'offer', label: 'Sale' },
    { value: 'new_releases', label: 'New Releases' },
    { value: 'contact_mail', label: 'Contact' },
    { value: 'info', label: 'Info' }
  ];

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4">Navigation Menu Management</Typography>
        <Box display="flex" alignItems="center">
          <Box sx={{ minWidth: 200, mr: 2 }}>
            <StoreSelector
              showLabel={true}
              variant="outlined"
              size="small"
            />
          </Box>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={handleRefresh}
            sx={{ mr: 1 }}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => handleOpenDialog(false)}
          >
            Add Menu
          </Button>
        </Box>
      </Box>

      {loading ? (
        <Typography>Loading...</Typography>
      ) : error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>ID</TableCell>
                <TableCell>Name</TableCell>
                <TableCell>URL</TableCell>
                <TableCell>Parent</TableCell>
                <TableCell>Display Order</TableCell>
                <TableCell>Icon</TableCell>
                <TableCell>Status</TableCell>
                {isSuperAdmin(user) && <TableCell>Store</TableCell>}
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {menus.map((menu) => (
                <TableRow key={menu.id}>
                  <TableCell>{menu.id}</TableCell>
                  <TableCell>{menu.name}</TableCell>
                  <TableCell>{menu.url}</TableCell>
                  <TableCell>
                    {menu.parentId ?
                      menus.find(m => m.id === menu.parentId)?.name || 'Unknown'
                      : 'None'}
                  </TableCell>
                  <TableCell>{menu.displayOrder}</TableCell>
                  <TableCell>{menu.icon}</TableCell>
                  <TableCell>
                    <Chip
                      label={menu.isActive ? 'Active' : 'Inactive'}
                      color={menu.isActive ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  {isSuperAdmin(user) && (
                    <TableCell>
                      {accessibleStores.find(s => s.id === menu.storeId)?.name || `Store ${menu.storeId}`}
                    </TableCell>
                  )}
                  <TableCell>
                    <IconButton
                      color="primary"
                      onClick={() => handleOpenDialog(true, menu)}
                    >
                      <Edit />
                    </IconButton>
                    <IconButton
                      color="error"
                      onClick={() => handleDelete(menu.id)}
                    >
                      <Delete />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>{isEditMode ? 'Edit Navigation Menu' : 'Add Navigation Menu'}</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>
            <TextField
              name="name"
              label="Name"
              value={formData.name}
              onChange={handleInputChange}
              fullWidth
              required
            />
            <TextField
              name="url"
              label="URL"
              value={formData.url}
              onChange={handleInputChange}
              fullWidth
              required
              helperText="e.g., /products, /category/1, etc."
            />
            <FormControl fullWidth>
              <InputLabel id="parent-menu-label">Parent Menu</InputLabel>
              <Select
                labelId="parent-menu-label"
                name="parentId"
                value={formData.parentId || ''}
                onChange={handleInputChange}
                label="Parent Menu"
              >
                <MenuItem value="">
                  <em>None</em>
                </MenuItem>
                {possibleParents.map((menu) => (
                  <MenuItem key={menu.id} value={menu.id}>
                    {menu.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <TextField
              name="displayOrder"
              label="Display Order"
              type="number"
              value={formData.displayOrder}
              onChange={handleInputChange}
              fullWidth
            />
            <FormControl fullWidth>
              <InputLabel id="icon-label">Icon</InputLabel>
              <Select
                labelId="icon-label"
                name="icon"
                value={formData.icon || ''}
                onChange={handleInputChange}
                label="Icon"
              >
                <MenuItem value="">
                  <em>None</em>
                </MenuItem>
                {icons.map((icon) => (
                  <MenuItem key={icon.value} value={icon.value}>
                    {icon.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <FormControlLabel
              control={
                <Switch
                  name="isActive"
                  checked={formData.isActive}
                  onChange={handleSwitchChange}
                />
              }
              label="Active"
            />

            {isSuperAdmin(user) && (
              <FormControl fullWidth sx={{ mt: 2 }}>
                <InputLabel id="store-label">Store</InputLabel>
                <Select
                  labelId="store-label"
                  name="storeId"
                  value={formData.storeId}
                  onChange={(e) => {
                    const value = e.target.value;
                    setFormData({
                      ...formData,
                      storeId: Number(value)
                    });
                  }}
                  label="Store"
                  disabled={isEditMode} // Can't change store when editing
                >
                  {accessibleStores.map((store) => (
                    <MenuItem key={store.id} value={store.id}>
                      {store.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSubmit} variant="contained">
            {isEditMode ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default NavigationManagement;
