import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Divider,
  Tabs,
  Tab,
  <PERSON><PERSON><PERSON>bar,
  Alert,
  Pagination,
  Stack
} from '@mui/material';
import { deleteImage, getImages, ImageItem } from '../services/fileUpload';
import BulkImageUploader from '../components/common/BulkImageUploader';
import ImageGallery from '../components/common/ImageGallery';

interface PaginationState {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  pageSize: number;
}

const BulkImageUploadPage: React.FC = () => {
  const [images, setImages] = useState<ImageItem[]>([]);
  const [tabValue, setTabValue] = useState(0);
  const [alertMessage, setAlertMessage] = useState<{ type: 'success' | 'error'; message: string } | null>(null);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState<PaginationState>({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    pageSize: 36
  });

  // Fetch images from API on component mount
  const fetchImages = async (page: number = 1) => {
    try {
      setLoading(true);
      const response = await getImages(page, pagination.pageSize);
      setImages(response.images || []);
      setPagination({
        currentPage: response.currentPage,
        totalPages: response.totalPages,
        totalCount: response.totalCount,
        pageSize: response.pageSize
      });
    } catch (error) {
      console.error('Error fetching images:', error);
      setAlertMessage({
        type: 'error',
        message: 'Failed to load images. Please try again.'
      });
    } finally {
      setLoading(false);
    }
  };

  // Load images on component mount
  useEffect(() => {
    fetchImages();
  }, []);

  // Handle page change
  const handlePageChange = (page: number) => {
    fetchImages(page);
  };

  const handleImagesUploaded = (urls: string[]) => {
    // Show success message
    setAlertMessage({
      type: 'success',
      message: `Successfully uploaded ${urls.length} image${urls.length !== 1 ? 's' : ''}`
    });

    // Refresh the gallery to show the newly uploaded images
    fetchImages(1);

    // Switch to gallery tab
    setTabValue(1);
  };

  const handleDeleteImage = async (url: string) => {
    try {
      setLoading(true);
      // Extract filename from URL
      const filename = url.split('/').pop();
      if (!filename) {
        throw new Error('Invalid image URL');
      }

      await deleteImage(filename);

      // Refresh the gallery after deletion
      await fetchImages(pagination.currentPage);

      return Promise.resolve();
    } catch (error) {
      console.error('Error deleting image:', error);
      setAlertMessage({
        type: 'error',
        message: 'Failed to delete image. Please try again.'
      });
      return Promise.reject(error);
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleCloseAlert = () => {
    setAlertMessage(null);
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Bulk Image Upload & Gallery
        </Typography>
        <Divider sx={{ mb: 3 }} />

        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label="image management tabs">
            <Tab label="Upload Images" id="tab-0" aria-controls="tabpanel-0" />
            <Tab
              label={`Gallery (${pagination.totalCount})`}
              id="tab-1"
              aria-controls="tabpanel-1"
            />
          </Tabs>
        </Box>

        <Box role="tabpanel" hidden={tabValue !== 0} id="tabpanel-0" aria-labelledby="tab-0">
          {tabValue === 0 && (
            <BulkImageUploader
              onImagesUploaded={handleImagesUploaded}
              maxFiles={20}
              title="Upload Multiple Images"
              showPreview={true}
            />
          )}
        </Box>

        <Box role="tabpanel" hidden={tabValue !== 1} id="tabpanel-1" aria-labelledby="tab-1">
          {tabValue === 1 && (
            <>
              <ImageGallery
                images={images}
                onDelete={handleDeleteImage}
                loading={loading}
                title={`Image Gallery (${pagination.totalCount} images)`}
                showCopyButton={true}
                showDeleteButton={true}
                showDownloadButton={true}
                thumbnailSize="small"
                infiniteScroll={false}
                gridItemProps={{ xs: 6, sm: 4, md: 3, lg: 2 }}
                dialogSize="fullscreen"
              />

              {/* Pagination Controls */}
              {pagination.totalPages > 1 && (
                <Stack spacing={2} sx={{ mt: 3, display: 'flex', alignItems: 'center' }}>
                  <Pagination
                    count={pagination.totalPages}
                    page={pagination.currentPage}
                    onChange={(e, page) => handlePageChange(page)}
                    color="primary"
                    showFirstButton
                    showLastButton
                  />
                  <Typography variant="caption" color="text.secondary">
                    Showing {images.length} of {pagination.totalCount} images
                  </Typography>
                </Stack>
              )}
            </>
          )}
        </Box>
      </Paper>

      {/* Alert Snackbar */}
      <Snackbar
        open={!!alertMessage}
        autoHideDuration={6000}
        onClose={handleCloseAlert}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        {alertMessage && (
          <Alert
            onClose={handleCloseAlert}
            severity={alertMessage.type}
            sx={{ width: '100%' }}
          >
            {alertMessage.message}
          </Alert>
        )}
      </Snackbar>
    </Container>
  );
};

export default BulkImageUploadPage;
