import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  But<PERSON>,
  Card,
  CardContent,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  FormControlLabel,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Switch,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
  useTheme,
} from '@mui/material';
import { Add, Delete, Edit, Refresh } from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../store';
import {
  User,
  UserType,
  fetchUsers,
  createUser,
  updateUser,
  deleteUser,
  CreateUserRequest,
  UpdateUserRequest
} from '../../store/slices/userSlice';
import StoreSelector from '../../components/common/StoreSelector';
import LoadingButton from '@mui/lab/LoadingButton';
import SaveIcon from '@mui/icons-material/Save';
import { format } from 'date-fns';

// Define available roles
const AVAILABLE_ROLES = ['Admin', 'Staff', 'Customer', 'SuperAdmin'];

// Define user types
const USER_TYPES = [
  { value: UserType.SuperAdmin, label: 'Super Admin' },
  { value: UserType.Admin, label: 'Admin' },
  { value: UserType.Staff, label: 'Staff' },
  { value: UserType.Customer, label: 'Customer' }
];

interface UserFormData {
  id?: string;
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  userType: UserType;
  roles: string[];
  password?: string;
  confirmPassword?: string;
  storeIds: number[];
}

const UserManagementPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { users, loading, error } = useSelector((state: RootState) => state.users);
  const { stores } = useSelector((state: RootState) => state.store);
  const { user: currentUser } = useSelector((state: RootState) => state.auth);
  const { enqueueSnackbar } = useSnackbar();
  const theme = useTheme();

  const [openDialog, setOpenDialog] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [confirmDelete, setConfirmDelete] = useState<string | null>(null);

  const [formData, setFormData] = useState<UserFormData>({
    email: '',
    firstName: '',
    lastName: '',
    phoneNumber: '',
    userType: UserType.Staff,
    roles: ['Staff'],
    password: '',
    confirmPassword: '',
    storeIds: []
  });

  // Check if current user is super admin
  const isSuperAdmin = currentUser?.roles?.includes('SuperAdmin') || false;

  useEffect(() => {
    loadUsers();
  }, [dispatch, selectedStore]);

  const loadUsers = () => {
    dispatch(fetchUsers());
  };

  const handleOpenDialog = (isEdit: boolean, user?: User) => {
    setIsEditMode(isEdit);
    if (isEdit && user) {
      setFormData({
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        phoneNumber: user.phoneNumber || '',
        userType: user.userType,
        roles: user.roles,
        password: '',
        confirmPassword: '',
        storeIds: user.storeIds || []
      });
    } else {
      setFormData({
        email: '',
        firstName: '',
        lastName: '',
        phoneNumber: '',
        userType: UserType.Staff,
        roles: ['Staff'],
        password: '',
        confirmPassword: '',
        storeIds: selectedStore ? [selectedStore.id] : []
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value } = e.target;
    if (name) {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  const handleUserTypeChange = (e: React.ChangeEvent<{ name?: string; value: unknown }>) => {
    const userType = e.target.value as UserType;
    let roles: string[] = [];

    // Set default roles based on user type
    switch (userType) {
      case UserType.SuperAdmin:
        roles = ['SuperAdmin'];
        break;
      case UserType.Admin:
        roles = ['Admin'];
        break;
      case UserType.Staff:
        roles = ['Staff'];
        break;
      case UserType.Customer:
        roles = ['Customer'];
        break;
      default:
        roles = [];
    }

    setFormData({
      ...formData,
      userType,
      roles
    });
  };

  const handleRoleChange = (e: React.ChangeEvent<{ name?: string; value: unknown }>) => {
    setFormData({
      ...formData,
      roles: e.target.value as string[]
    });
  };

  const handleStoreChange = (e: React.ChangeEvent<{ name?: string; value: unknown }>) => {
    setFormData({
      ...formData,
      storeIds: e.target.value as number[]
    });
  };

  const validateForm = () => {
    if (!formData.email || !formData.firstName || !formData.lastName) {
      enqueueSnackbar('Please fill in all required fields', { variant: 'error' });
      return false;
    }

    if (!isEditMode && (!formData.password || formData.password.length < 6)) {
      enqueueSnackbar('Password must be at least 6 characters', { variant: 'error' });
      return false;
    }

    if (!isEditMode && formData.password !== formData.confirmPassword) {
      enqueueSnackbar('Passwords do not match', { variant: 'error' });
      return false;
    }

    if (formData.roles.length === 0) {
      enqueueSnackbar('Please select at least one role', { variant: 'error' });
      return false;
    }

    if ((formData.userType === UserType.Admin || formData.userType === UserType.Staff) &&
      formData.storeIds.length === 0) {
      enqueueSnackbar('Please assign the user to at least one store', { variant: 'error' });
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      if (isEditMode && formData.id) {
        // Update user
        const updateData: UpdateUserRequest = {
          firstName: formData.firstName,
          lastName: formData.lastName,
          phoneNumber: formData.phoneNumber,
          userType: formData.userType,
          roles: formData.roles,
          storeIds: formData.storeIds
        };

        if (formData.password) {
          updateData.newPassword = formData.password;
        }

        await dispatch(updateUser({ id: formData.id, user: updateData })).unwrap();
        enqueueSnackbar('User updated successfully', { variant: 'success' });
      } else {
        // Create user
        const createData: CreateUserRequest = {
          email: formData.email,
          password: formData.password!,
          firstName: formData.firstName,
          lastName: formData.lastName,
          phoneNumber: formData.phoneNumber,
          userType: formData.userType,
          roles: formData.roles,
          storeIds: formData.storeIds
        };

        await dispatch(createUser(createData)).unwrap();
        enqueueSnackbar('User created successfully', { variant: 'success' });
      }

      handleCloseDialog();
      dispatch(fetchUsers());
    } catch (error: any) {
      enqueueSnackbar(error.message || 'An error occurred', { variant: 'error' });
    }
  };

  const handleDelete = (id: string) => {
    setConfirmDelete(id);
  };

  const confirmDeleteUser = async () => {
    if (!confirmDelete) return;

    try {
      await dispatch(deleteUser(confirmDelete)).unwrap();
      enqueueSnackbar('User deleted successfully', { variant: 'success' });
      setConfirmDelete(null);
    } catch (error: any) {
      enqueueSnackbar(error.message || 'An error occurred', { variant: 'error' });
    }
  };

  const handleRefresh = () => {
    dispatch(fetchUsers());
  };

  const getUserTypeName = (type: UserType) => {
    const userType = USER_TYPES.find(t => t.value === type);
    return userType ? userType.label : 'Unknown';
  };

  const getStoreName = (storeId: number) => {
    const store = stores.find(s => s.id === storeId);
    return store ? store.name : `Store ${storeId}`;
  };

  // Filter users based on selected store
  const filteredUsers = selectedStore
    ? users.filter(user => user.storeIds?.includes(selectedStore.id))
    : users;

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        User Management
      </Typography>

      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={6}>
              <StoreSelector
                showLabel={true}
                variant="outlined"
                size="small"
                width="100%"
              />
            </Grid>
            <Grid item xs={12} md={isSuperAdmin ? 6 : 12} sx={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                variant="outlined"
                startIcon={<Refresh />}
                onClick={handleRefresh}
                sx={{ mr: 1 }}
              >
                Refresh
              </Button>
              <Button
                variant="contained"
                startIcon={<Add />}
                onClick={() => handleOpenDialog(false)}
              >
                Add User
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Name</TableCell>
              <TableCell>Email</TableCell>
              <TableCell>Type</TableCell>
              <TableCell>Roles</TableCell>
              {isSuperAdmin && <TableCell>Stores</TableCell>}
              <TableCell>Created</TableCell>
              <TableCell>Last Login</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredUsers.map((user) => (
              <TableRow key={user.id}>
                <TableCell>{`${user.firstName} ${user.lastName}`}</TableCell>
                <TableCell>{user.email}</TableCell>
                <TableCell>{getUserTypeName(user.userType)}</TableCell>
                <TableCell>
                  {user.roles.map(role => (
                    <Chip
                      key={role}
                      label={role}
                      size="small"
                      color={role === 'SuperAdmin' ? 'error' : role === 'Admin' ? 'primary' : 'default'}
                      sx={{ mr: 0.5, mb: 0.5 }}
                    />
                  ))}
                </TableCell>
                {isSuperAdmin && (
                  <TableCell>
                    {user.storeIds?.map(storeId => (
                      <Chip
                        key={storeId}
                        label={accessibleStores.find(s => s.id === storeId)?.name || `Store ${storeId}`}
                        size="small"
                        sx={{ mr: 0.5, mb: 0.5 }}
                      />
                    ))}
                  </TableCell>
                )}
                <TableCell>{format(new Date(user.createdAt), 'MMM d, yyyy')}</TableCell>
                <TableCell>
                  {user.lastLoginDate ? format(new Date(user.lastLoginDate), 'MMM d, yyyy') : 'Never'}
                </TableCell>
                <TableCell>
                  <IconButton
                    color="primary"
                    onClick={() => handleOpenDialog(true, user)}
                  >
                    <Edit />
                  </IconButton>
                  <IconButton
                    color="error"
                    onClick={() => handleDelete(user.id)}
                    disabled={user.roles.includes('SuperAdmin')}
                  >
                    <Delete />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
            {filteredUsers.length === 0 && (
              <TableRow>
                <TableCell colSpan={isSuperAdmin ? 8 : 7} align="center">
                  No users found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* User Form Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>{isEditMode ? 'Edit User' : 'Add User'}</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                name="firstName"
                label="First Name"
                value={formData.firstName}
                onChange={handleInputChange}
                fullWidth
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                name="lastName"
                label="Last Name"
                value={formData.lastName}
                onChange={handleInputChange}
                fullWidth
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                name="email"
                label="Email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                fullWidth
                required
                disabled={isEditMode}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                name="phoneNumber"
                label="Phone Number"
                value={formData.phoneNumber}
                onChange={handleInputChange}
                fullWidth
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>User Type</InputLabel>
                <Select
                  name="userType"
                  value={formData.userType}
                  onChange={handleUserTypeChange}
                  label="User Type"
                >
                  {USER_TYPES.map((type) => (
                    <MenuItem
                      key={type.value}
                      value={type.value}
                      disabled={type.value === UserType.SuperAdmin && !isSuperAdmin}
                    >
                      {type.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Roles</InputLabel>
                <Select
                  name="roles"
                  multiple
                  value={formData.roles}
                  onChange={handleRoleChange}
                  label="Roles"
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {(selected as string[]).map((role) => (
                        <Chip key={role} label={role} />
                      ))}
                    </Box>
                  )}
                >
                  {AVAILABLE_ROLES.map((role) => (
                    <MenuItem
                      key={role}
                      value={role}
                      disabled={role === 'SuperAdmin' && !isSuperAdmin}
                    >
                      {role}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            {isSuperAdmin && (
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>Assign to Stores</InputLabel>
                  <Select
                    name="storeIds"
                    multiple
                    value={formData.storeIds}
                    onChange={handleStoreChange}
                    label="Assign to Stores"
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {(selected as number[]).map((storeId) => (
                          <Chip key={storeId} label={getStoreName(storeId)} />
                        ))}
                      </Box>
                    )}
                  >
                    {stores.map((store) => (
                      <MenuItem key={store.id} value={store.id}>
                        {store.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            )}
            <Grid item xs={12} md={6}>
              <TextField
                name="password"
                label={isEditMode ? "New Password (leave blank to keep current)" : "Password"}
                type="password"
                value={formData.password}
                onChange={handleInputChange}
                fullWidth
                required={!isEditMode}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                name="confirmPassword"
                label="Confirm Password"
                type="password"
                value={formData.confirmPassword}
                onChange={handleInputChange}
                fullWidth
                required={!isEditMode || !!formData.password}
                error={!!formData.password && formData.password !== formData.confirmPassword}
                helperText={
                  formData.password && formData.password !== formData.confirmPassword
                    ? "Passwords don't match"
                    : ""
                }
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <LoadingButton
            loading={loading}
            loadingPosition="start"
            startIcon={<SaveIcon />}
            variant="contained"
            onClick={handleSubmit}
          >
            {isEditMode ? 'Update' : 'Create'}
          </LoadingButton>
        </DialogActions>
      </Dialog>

      {/* Confirm Delete Dialog */}
      <Dialog open={!!confirmDelete} onClose={() => setConfirmDelete(null)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this user? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDelete(null)}>Cancel</Button>
          <Button onClick={confirmDeleteUser} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default UserManagementPage;
