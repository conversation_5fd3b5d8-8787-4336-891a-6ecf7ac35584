import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  useTheme,
} from '@mui/material';
import { useSnackbar } from 'notistack';
import { UserRole, userRoleService } from '../../services/userRoleService';
import { Role, roleService } from '../../services/roleService';
import { storeService } from '../../services';
import LoadingButton from '@mui/lab/LoadingButton';
import SaveIcon from '@mui/icons-material/Save';

interface Store {
  id: number;
  name: string;
}

interface User {
  id: string;
  email: string;
  fullName: string;
}

const UserRolesPage: React.FC = () => {
  const [userRoles, setUserRoles] = useState<UserRole[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [stores, setStores] = useState<Store[]>([]);
  const [selectedStore, setSelectedStore] = useState<number | ''>('');
  const [selectedUser, setSelectedUser] = useState<string | null>(null);
  const [selectedRoles, setSelectedRoles] = useState<number[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const { enqueueSnackbar } = useSnackbar();
  const theme = useTheme();

  useEffect(() => {
    fetchInitialData();
  }, []);

  useEffect(() => {
    if (selectedStore !== '') {
      fetchUserRolesByStore(selectedStore as number);
      fetchRolesByStore(selectedStore as number);
    } else {
      fetchAllUserRoles();
      fetchAllRoles();
    }
  }, [selectedStore]);

  const fetchInitialData = async () => {
    try {
      setLoading(true);
      const storesData = await storeService.getStores();
      setStores(storesData);
      await fetchAllUserRoles();
      await fetchAllRoles();
    } catch (error) {
      console.error('Error fetching initial data:', error);
      enqueueSnackbar('Failed to load data', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const fetchAllUserRoles = async () => {
    try {
      const data = await userRoleService.getUserRoles();
      setUserRoles(data);
    } catch (error) {
      console.error('Error fetching user roles:', error);
      enqueueSnackbar('Failed to load user roles', { variant: 'error' });
    }
  };

  const fetchUserRolesByStore = async (storeId: number) => {
    try {
      const data = await userRoleService.getUserRolesByStore(storeId);
      setUserRoles(data);
    } catch (error) {
      console.error('Error fetching user roles by store:', error);
      enqueueSnackbar('Failed to load user roles for this store', { variant: 'error' });
    }
  };

  const fetchAllRoles = async () => {
    try {
      const data = await roleService.getRoles();
      setRoles(data);
    } catch (error) {
      console.error('Error fetching roles:', error);
      enqueueSnackbar('Failed to load roles', { variant: 'error' });
    }
  };

  const fetchRolesByStore = async (storeId: number) => {
    try {
      const data = await roleService.getRolesByStore(storeId);
      setRoles(data);
    } catch (error) {
      console.error('Error fetching roles by store:', error);
      enqueueSnackbar('Failed to load roles for this store', { variant: 'error' });
    }
  };

  const handleUserSelect = (userId: string) => {
    const user = userRoles.find(ur => ur.userId === userId);
    setSelectedUser(userId);
    setSelectedRoles(user ? user.roleIds : []);
  };

  const handleRoleChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setSelectedRoles(event.target.value as number[]);
  };

  const handleSaveUserRoles = async () => {
    if (!selectedUser) return;

    try {
      setSaving(true);
      const user = userRoles.find(ur => ur.userId === selectedUser);
      
      if (!user) {
        enqueueSnackbar('User not found', { variant: 'error' });
        return;
      }

      const updatedUserRole: UserRole = {
        userId: user.userId,
        userEmail: user.userEmail,
        userFullName: user.userFullName,
        roleIds: selectedRoles
      };

      await userRoleService.assignRolesToUser(updatedUserRole);
      
      // Update the local state
      setUserRoles(prev => 
        prev.map(ur => ur.userId === selectedUser 
          ? { ...ur, roleIds: selectedRoles } 
          : ur
        )
      );
      
      enqueueSnackbar('User roles updated successfully', { variant: 'success' });
    } catch (error) {
      console.error('Error saving user roles:', error);
      enqueueSnackbar('Failed to update user roles', { variant: 'error' });
    } finally {
      setSaving(false);
    }
  };

  const getRoleNameById = (roleId: number): string => {
    const role = roles.find(r => r.id === roleId);
    return role ? role.name : 'Unknown Role';
  };

  const getAvailableRoles = (): Role[] => {
    if (selectedStore === '') {
      return roles;
    }
    
    return roles.filter(role => 
      role.storeId === selectedStore || role.storeId === null
    );
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        User Role Management
      </Typography>

      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Filter by Store</InputLabel>
                <Select
                  value={selectedStore}
                  onChange={(e) => setSelectedStore(e.target.value as number | '')}
                  label="Filter by Store"
                  disabled={loading}
                >
                  <MenuItem value="">All Stores</MenuItem>
                  {stores.map(store => (
                    <MenuItem key={store.id} value={store.id}>
                      {store.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      <Grid container spacing={4}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Users
              </Typography>
              
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Name</TableCell>
                      <TableCell>Email</TableCell>
                      <TableCell>Current Roles</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {loading ? (
                      <TableRow>
                        <TableCell colSpan={3} align="center">
                          Loading...
                        </TableCell>
                      </TableRow>
                    ) : userRoles.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={3} align="center">
                          No users found
                        </TableCell>
                      </TableRow>
                    ) : (
                      userRoles.map((userRole) => (
                        <TableRow 
                          key={userRole.userId}
                          onClick={() => handleUserSelect(userRole.userId)}
                          selected={selectedUser === userRole.userId}
                          hover
                          sx={{ cursor: 'pointer' }}
                        >
                          <TableCell>{userRole.userFullName}</TableCell>
                          <TableCell>{userRole.userEmail}</TableCell>
                          <TableCell>
                            <Box display="flex" flexWrap="wrap" gap={0.5}>
                              {userRole.roleIds.map(roleId => (
                                <Chip 
                                  key={roleId} 
                                  label={getRoleNameById(roleId)} 
                                  size="small" 
                                  color="primary"
                                  variant="outlined"
                                />
                              ))}
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Assign Roles
              </Typography>
              
              {selectedUser ? (
                <>
                  <Typography variant="body1" gutterBottom>
                    Selected User: {userRoles.find(ur => ur.userId === selectedUser)?.userFullName}
                  </Typography>
                  
                  <FormControl fullWidth sx={{ mt: 2, mb: 3 }}>
                    <InputLabel>Roles</InputLabel>
                    <Select
                      multiple
                      value={selectedRoles}
                      onChange={handleRoleChange}
                      label="Roles"
                      renderValue={(selected) => (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {(selected as number[]).map((roleId) => (
                            <Chip key={roleId} label={getRoleNameById(roleId)} />
                          ))}
                        </Box>
                      )}
                    >
                      {getAvailableRoles().map((role) => (
                        <MenuItem key={role.id} value={role.id}>
                          {role.name} {role.storeId ? `(${stores.find(s => s.id === role.storeId)?.name})` : '(Global)'}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                  
                  <Box display="flex" justifyContent="flex-end">
                    <LoadingButton
                      variant="contained"
                      color="primary"
                      onClick={handleSaveUserRoles}
                      loading={saving}
                      loadingPosition="start"
                      startIcon={<SaveIcon />}
                    >
                      Save Roles
                    </LoadingButton>
                  </Box>
                </>
              ) : (
                <Typography variant="body1" color="textSecondary">
                  Select a user from the list to assign roles
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default UserRolesPage;
