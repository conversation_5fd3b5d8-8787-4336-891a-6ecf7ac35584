import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  useTheme,
} from '@mui/material';
import { useSnackbar } from 'notistack';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../store';
import {
  UserRole,
  fetchUserRoles,
  fetchUserRolesByStore,
  assignRolesToUser
} from '../../store/slices/userRoleSlice';
import {
  Role,
  fetchRoles,
  fetchRolesByStore
} from '../../store/slices/roleSlice';
import StoreSelector from '../../components/common/StoreSelector';
import LoadingButton from '@mui/lab/LoadingButton';
import SaveIcon from '@mui/icons-material/Save';
import { Refresh } from '@mui/icons-material';

const UserRoleManagementPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { userRoles, loading, error } = useSelector((state: RootState) => state.userRoles);
  const { roles } = useSelector((state: RootState) => state.roles);
  const { stores, selectedStore } = useSelector((state: RootState) => state.store);
  const { user: currentUser } = useSelector((state: RootState) => state.auth);
  const { enqueueSnackbar } = useSnackbar();
  const theme = useTheme();

  const [selectedUser, setSelectedUser] = useState<string | null>(null);
  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);

  const [saving, setSaving] = useState(false);

  // Check if current user is super admin
  const isSuperAdmin = currentUser?.roles?.includes('SuperAdmin') || false;

  useEffect(() => {
    loadData();
  }, [dispatch, selectedStore, isSuperAdmin]);

  const loadData = () => {
    if (selectedStore) {
      dispatch(fetchUserRolesByStore(selectedStore.id));
      dispatch(fetchRolesByStore(selectedStore.id));
    } else if (isSuperAdmin) {
      // Only super admins can see all users across all stores
      dispatch(fetchUserRoles());
      dispatch(fetchRoles());
    }
  };

  useEffect(() => {
    if (selectedUser) {
      const user = userRoles.find(ur => ur.userId === selectedUser);
      if (user) {
        setSelectedRoles(user.roleIds);
      }
    } else {
      setSelectedRoles([]);
    }
  }, [selectedUser, userRoles]);

  const handleUserChange = (userId: string) => {
    setSelectedUser(userId);
  };

  const handleRoleChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setSelectedRoles(event.target.value as string[]);
  };

  const handleStoreChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    const value = event.target.value;
    setSelectedStoreId(value === '' ? null : Number(value));
    setSelectedUser(null);
  };

  const handleRefresh = () => {
    if (selectedStoreId) {
      dispatch(fetchUserRolesByStore(selectedStoreId));
      dispatch(fetchRolesByStore(selectedStoreId));
    } else if (isSuperAdmin) {
      dispatch(fetchUserRoles());
      dispatch(fetchRoles());
    }
  };

  const handleSaveRoles = async () => {
    if (!selectedUser) {
      enqueueSnackbar('Please select a user', { variant: 'error' });
      return;
    }

    try {
      setSaving(true);
      const user = userRoles.find(ur => ur.userId === selectedUser);

      if (!user) {
        enqueueSnackbar('User not found', { variant: 'error' });
        return;
      }

      const updatedUserRole: UserRole = {
        userId: user.userId,
        userEmail: user.userEmail,
        userFullName: user.userFullName,
        roleIds: selectedRoles
      };

      await dispatch(assignRolesToUser(updatedUserRole)).unwrap();
      enqueueSnackbar('User roles updated successfully', { variant: 'success' });
    } catch (error: any) {
      enqueueSnackbar(error.message || 'Failed to update user roles', { variant: 'error' });
    } finally {
      setSaving(false);
    }
  };

  const getRoleNameById = (roleId: string): string => {
    const role = roles.find(r => r.id === roleId);
    return role ? role.name : roleId;
  };

  const getAvailableRoles = () => {
    return roles.filter(role => {
      // If a store is selected, only show roles for that store or global roles
      if (selectedStore) {
        return role.storeId === selectedStore.id || role.storeId === null;
      }
      return true;
    });
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        User Role Management
      </Typography>

      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <StoreSelector
                showLabel={true}
                variant="outlined"
                size="small"
                width="100%"
                disabled={loading}
              />
            </Grid>
            <Grid item xs={12} md={6} sx={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                variant="outlined"
                startIcon={<Refresh />}
                onClick={handleRefresh}
                disabled={loading}
              >
                Refresh
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      <Grid container spacing={4}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Users
              </Typography>
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Name</TableCell>
                      <TableCell>Email</TableCell>
                      <TableCell>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {userRoles.map((userRole) => (
                      <TableRow
                        key={userRole.userId}
                        selected={selectedUser === userRole.userId}
                        hover
                        onClick={() => handleUserChange(userRole.userId)}
                        sx={{ cursor: 'pointer' }}
                      >
                        <TableCell>{userRole.userFullName}</TableCell>
                        <TableCell>{userRole.userEmail}</TableCell>
                        <TableCell>
                          <Button
                            variant="outlined"
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleUserChange(userRole.userId);
                            }}
                          >
                            Select
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                    {userRoles.length === 0 && (
                      <TableRow>
                        <TableCell colSpan={3} align="center">
                          No users found
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Assign Roles
              </Typography>

              {selectedUser ? (
                <>
                  <Typography variant="body1" gutterBottom>
                    Selected User: {userRoles.find(ur => ur.userId === selectedUser)?.userFullName}
                  </Typography>

                  <FormControl fullWidth sx={{ mt: 2, mb: 3 }}>
                    <InputLabel>Roles</InputLabel>
                    <Select
                      multiple
                      value={selectedRoles}
                      onChange={handleRoleChange}
                      label="Roles"
                      renderValue={(selected) => (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {(selected as string[]).map((roleId) => (
                            <Chip key={roleId} label={getRoleNameById(roleId)} />
                          ))}
                        </Box>
                      )}
                    >
                      {getAvailableRoles().map((role) => (
                        <MenuItem key={role.id} value={role.id}>
                          {role.name} {role.storeId ? `(${accessibleStores.find(s => s.id === role.storeId)?.name || `Store ${role.storeId}`})` : '(Global)'}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>

                  <LoadingButton
                    loading={saving}
                    loadingPosition="start"
                    startIcon={<SaveIcon />}
                    variant="contained"
                    onClick={handleSaveRoles}
                    fullWidth
                  >
                    Save Role Assignments
                  </LoadingButton>
                </>
              ) : (
                <Typography variant="body1" color="textSecondary" align="center">
                  Please select a user from the list
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default UserRoleManagementPage;
