import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams, useNavigate, Link } from 'react-router-dom';
import {
  Box,
  Button,
  Container,
  Grid,
  Paper,
  Typography,
  Divider,
  Tabs,
  Tab,
  CircularProgress,
  Alert,
  Breadcrumbs
} from '@mui/material';
import { ArrowBack, Edit } from '@mui/icons-material';
import { AppDispatch, RootState } from '../store';
import { fetchProductById } from '../store/slices/productSlice';
import ProductForm from '../components/admin/ProductForm';
import ProductVariantsManager from '../components/admin/ProductVariantsManager';
import MultiVariantManager from '../components/admin/MultiVariantManager';
import ProductImagesDialog from '../components/admin/ProductImagesDialog';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`product-tabpanel-${index}`}
      aria-labelledby={`product-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const ProductDetail: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { product, loading, error } = useSelector((state: RootState) => state.products);

  const [tabValue, setTabValue] = useState(0);
  const [editMode, setEditMode] = useState(false);
  const [openImagesDialog, setOpenImagesDialog] = useState(false);

  useEffect(() => {
    if (id) {
      console.log('Fetching product with ID:', id);
      dispatch(fetchProductById(parseInt(id)));
    }
  }, [dispatch, id]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleEditClick = () => {
    setEditMode(true);
  };

  const handleEditCancel = () => {
    setEditMode(false);
  };

  const handleEditSuccess = () => {
    setEditMode(false);
    if (id) {
      dispatch(fetchProductById(parseInt(id)));
    }
  };

  const handleManageImages = () => {
    setOpenImagesDialog(true);
  };

  const handleImagesDialogClose = () => {
    setOpenImagesDialog(false);
    if (id) {
      dispatch(fetchProductById(parseInt(id)));
    }
  };

  if (loading) {
    return (
      <Container sx={{ py: 4 }} maxWidth="lg">
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (error || !product) {
    return (
      <Container sx={{ py: 4 }} maxWidth="lg">
        <Alert severity="error">
          {error || 'Product not found'}
        </Alert>
      </Container>
    );
  }

  return (
    <Container sx={{ py: 4 }} maxWidth="lg">
      <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
        <Link to="/admin" style={{ textDecoration: 'none', color: 'inherit' }}>
          Admin
        </Link>
        <Link to="/products" style={{ textDecoration: 'none', color: 'inherit' }}>
          Products
        </Link>
        <Typography color="text.primary">{product.name}</Typography>
      </Breadcrumbs>

      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Button
          startIcon={<ArrowBack />}
          onClick={() => navigate('/products')}
        >
          Back to Products
        </Button>

        {!editMode && (
          <Button
            variant="contained"
            startIcon={<Edit />}
            onClick={handleEditClick}
          >
            Edit Product
          </Button>
        )}
      </Box>

      {editMode ? (
        <Paper sx={{ p: 3 }}>
          <ProductForm
            product={product}
            onSubmitSuccess={handleEditSuccess}
            onCancel={handleEditCancel}
          />
        </Paper>
      ) : (
        <>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <Box sx={{ position: 'relative' }}>
                  <img
                    src={product.images && product.images.length > 0
                      ? product.images.find(img => img.isMain)?.imageUrl || product.images[0].imageUrl
                      : (product.imageUrl || 'https://source.unsplash.com/random?product')}
                    alt={product.name}
                    style={{ width: '100%', height: 'auto', objectFit: 'contain' }}
                  />
                  <Button
                    variant="outlined"
                    size="small"
                    onClick={handleManageImages}
                    sx={{ mt: 1, width: '100%' }}
                  >
                    Manage Images
                  </Button>
                </Box>
              </Grid>

              <Grid item xs={12} md={8}>
                <Typography variant="h4" gutterBottom>
                  {product.name}
                </Typography>

                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h5" color="primary" sx={{ mr: 2 }}>
                    ${product.price.toFixed(2)}
                  </Typography>
                  {product.cost > 0 && product.cost > product.price && (
                    <Typography variant="body1" sx={{ textDecoration: 'line-through', color: 'text.secondary' }}>
                      ${product.cost.toFixed(2)}
                    </Typography>
                  )}
                </Box>

                <Divider sx={{ my: 2 }} />

                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      SKU
                    </Typography>
                    <Typography variant="body1">
                      {product.sku || 'N/A'}
                    </Typography>
                  </Grid>

                  <Grid item xs={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Barcode
                    </Typography>
                    <Typography variant="body1">
                      {product.barcode || 'N/A'}
                    </Typography>
                  </Grid>

                  <Grid item xs={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Category
                    </Typography>
                    <Typography variant="body1">
                      {product.categoryName}
                    </Typography>
                  </Grid>

                  <Grid item xs={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Stock
                    </Typography>
                    <Typography variant="body1">
                      {product.stockQuantity} units
                    </Typography>
                  </Grid>

                  <Grid item xs={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Status
                    </Typography>
                    <Typography variant="body1" color={product.isActive ? 'success.main' : 'error.main'}>
                      {product.isActive ? 'Active' : 'Inactive'}
                    </Typography>
                  </Grid>

                  <Grid item xs={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Featured
                    </Typography>
                    <Typography variant="body1">
                      {product.isFeatured ? 'Yes' : 'No'}
                    </Typography>
                  </Grid>

                  <Grid item xs={12}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Description
                    </Typography>
                    <Typography variant="body1">
                      {product.description || 'No description available.'}
                    </Typography>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Paper>

          <Paper sx={{ p: 3 }}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs value={tabValue} onChange={handleTabChange} aria-label="product tabs">
                <Tab label="Variants" id="product-tab-0" aria-controls="product-tabpanel-0" />
                <Tab label="Custom Attributes" id="product-tab-1" aria-controls="product-tabpanel-1" />
                <Tab label="Collections" id="product-tab-2" aria-controls="product-tabpanel-2" />
              </Tabs>
            </Box>

            <TabPanel value={tabValue} index={0}>
              <MultiVariantManager
                product={product}
                onSave={() => dispatch(fetchProductById(product.id))}
                onCancel={() => {}}
              />
            </TabPanel>

            <TabPanel value={tabValue} index={1}>
              <Typography variant="h6" gutterBottom>
                Custom Attributes
              </Typography>

              {product.customAttributes && Object.keys(product.customAttributes).length > 0 ? (
                <Grid container spacing={2}>
                  {Object.entries(product.customAttributes).map(([key, value]) => (
                    <Grid item xs={6} key={key}>
                      <Typography variant="subtitle2" color="text.secondary">
                        {key}
                      </Typography>
                      <Typography variant="body1">
                        {value || 'N/A'}
                      </Typography>
                    </Grid>
                  ))}
                </Grid>
              ) : (
                <Typography variant="body1" color="text.secondary">
                  No custom attributes defined for this product.
                </Typography>
              )}
            </TabPanel>

            <TabPanel value={tabValue} index={2}>
              <Typography variant="h6" gutterBottom>
                Collections
              </Typography>

              {product.collectionNames && product.collectionNames.length > 0 ? (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {product.collectionNames.map((name, index) => (
                    <Chip key={index} label={name} />
                  ))}
                </Box>
              ) : (
                <Typography variant="body1" color="text.secondary">
                  This product is not part of any collection.
                </Typography>
              )}
            </TabPanel>
          </Paper>
        </>
      )}

      {/* Images Dialog */}
      <ProductImagesDialog
        open={openImagesDialog}
        onClose={handleImagesDialogClose}
        product={product}
      />
    </Container>
  );
};

export default ProductDetail;
