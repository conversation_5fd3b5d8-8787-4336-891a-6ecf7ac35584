import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  CardHeader,
  Divider,
  List,
  ListItem,
  ListItemText,
  CircularProgress,
  Button,
  Alert,
  Chip
} from '@mui/material';
import {
  ShoppingBag,
  LocalShipping,
  People,
  AttachMoney,
  TrendingUp,
  Category,
  Collections,
  Store as StoreIcon
} from '@mui/icons-material';
import { Link } from 'react-router-dom';
import { RootState, AppDispatch } from '../store';
import { fetchAccessibleStores } from '../store/slices/storeSlice';

interface Order {
  id: number;
  customer: string;
  total: number;
  status: string;
  date: string;
}

interface Product {
  id: number;
  name: string;
  sales: number;
  revenue: number;
}

interface DashboardStats {
  totalOrders: number;
  totalProducts: number;
  totalCustomers: number;
  totalRevenue: number;
  recentOrders: Order[];
  topProducts: Product[];
}

const Dashboard: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const { selectedStore, accessibleStores } = useSelector((state: RootState) => state.store);
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<DashboardStats>({
    totalOrders: 0,
    totalProducts: 0,
    totalCustomers: 0,
    totalRevenue: 0,
    recentOrders: [],
    topProducts: []
  });

  useEffect(() => {
    // Load accessible stores if not already loaded
    if (user && accessibleStores.length === 0) {
      dispatch(fetchAccessibleStores());
    }
  }, [dispatch, user, accessibleStores.length]);

  useEffect(() => {
    // In a real application, you would fetch dashboard data here based on the selected store
    setLoading(true);

    // Simulate API call with different data based on selected store
    setTimeout(() => {
      // If a store is selected, show store-specific data
      if (selectedStore) {
        setStats({
          totalOrders: 156,
          totalProducts: 243,
          totalCustomers: 89,
          totalRevenue: 15680,
          recentOrders: [
            { id: 1, customer: 'John Doe', total: 129.99, status: 'Delivered', date: '2023-04-15' },
            { id: 2, customer: 'Jane Smith', total: 89.99, status: 'Processing', date: '2023-04-14' },
            { id: 3, customer: 'Bob Johnson', total: 199.99, status: 'Shipped', date: '2023-04-13' },
            { id: 4, customer: 'Alice Brown', total: 149.99, status: 'Pending', date: '2023-04-12' },
            { id: 5, customer: 'Charlie Wilson', total: 79.99, status: 'Delivered', date: '2023-04-11' }
          ],
          topProducts: [
            { id: 1, name: 'Cotton T-Shirt', sales: 45, revenue: 1349.55 },
            { id: 2, name: 'Denim Jeans', sales: 38, revenue: 2279.62 },
            { id: 3, name: 'Casual Shirt', sales: 32, revenue: 1599.68 },
            { id: 4, name: 'Summer Dress', sales: 28, revenue: 1399.72 },
            { id: 5, name: 'Leather Jacket', sales: 24, revenue: 2399.76 }
          ]
        });
      } else {
        // If no store is selected (should only happen for super admin before selecting a store)
        setStats({
          totalOrders: 0,
          totalProducts: 0,
          totalCustomers: 0,
          totalRevenue: 0,
          recentOrders: [],
          topProducts: []
        });
      }
      setLoading(false);
    }, 1000);
  }, [dispatch, selectedStore]);

  // Show loading indicator
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  // For super admin, show message if no store is selected
  if (user?.roles.includes('SuperAdmin') && !selectedStore) {
    return (
      <Box>
        <Typography variant="h4" component="h1" gutterBottom>
          Dashboard
        </Typography>
        <Alert severity="info" sx={{ mb: 3 }}>
          Please select a store from the dropdown in the top bar to view its dashboard data.
        </Alert>
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Dashboard
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Typography variant="subtitle1" color="text.secondary">
            Welcome back, {user?.firstName || 'Admin'}!
          </Typography>

          {selectedStore && (
            <Chip
              icon={<StoreIcon fontSize="small" />}
              label={`Store: ${selectedStore.name}`}
              color="primary"
              size="small"
              sx={{ ml: 2 }}
            />
          )}
        </Box>

        {selectedStore && (
          <Typography variant="body2" color="text.secondary">
            You are viewing data for store: <strong>{selectedStore.name}</strong>
            {!selectedStore.isActive && (
              <Chip
                label="Inactive"
                size="small"
                color="warning"
                sx={{ ml: 1, height: 20 }}
              />
            )}
          </Typography>
        )}
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Paper
            elevation={2}
            sx={{
              p: 2,
              display: 'flex',
              flexDirection: 'column',
              height: 140,
              borderRadius: 2,
              position: 'relative',
              overflow: 'hidden'
            }}
          >
            <Box
              sx={{
                position: 'absolute',
                top: -15,
                right: -15,
                backgroundColor: 'rgba(76, 175, 80, 0.1)',
                borderRadius: '50%',
                width: 100,
                height: 100,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center'
              }}
            >
              <ShoppingBag sx={{ fontSize: 40, color: 'primary.main', opacity: 0.7 }} />
            </Box>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              Total Products
            </Typography>
            <Typography variant="h3" component="div" sx={{ fontWeight: 'bold', mt: 'auto' }}>
              {stats.totalProducts}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              <TrendingUp sx={{ fontSize: 14, verticalAlign: 'middle', mr: 0.5, color: 'success.main' }} />
              12% increase from last month
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper
            elevation={2}
            sx={{
              p: 2,
              display: 'flex',
              flexDirection: 'column',
              height: 140,
              borderRadius: 2,
              position: 'relative',
              overflow: 'hidden'
            }}
          >
            <Box
              sx={{
                position: 'absolute',
                top: -15,
                right: -15,
                backgroundColor: 'rgba(33, 150, 243, 0.1)',
                borderRadius: '50%',
                width: 100,
                height: 100,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center'
              }}
            >
              <LocalShipping sx={{ fontSize: 40, color: 'info.main', opacity: 0.7 }} />
            </Box>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              Total Orders
            </Typography>
            <Typography variant="h3" component="div" sx={{ fontWeight: 'bold', mt: 'auto' }}>
              {stats.totalOrders}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              <TrendingUp sx={{ fontSize: 14, verticalAlign: 'middle', mr: 0.5, color: 'success.main' }} />
              8% increase from last month
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper
            elevation={2}
            sx={{
              p: 2,
              display: 'flex',
              flexDirection: 'column',
              height: 140,
              borderRadius: 2,
              position: 'relative',
              overflow: 'hidden'
            }}
          >
            <Box
              sx={{
                position: 'absolute',
                top: -15,
                right: -15,
                backgroundColor: 'rgba(156, 39, 176, 0.1)',
                borderRadius: '50%',
                width: 100,
                height: 100,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center'
              }}
            >
              <People sx={{ fontSize: 40, color: 'secondary.main', opacity: 0.7 }} />
            </Box>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              Total Customers
            </Typography>
            <Typography variant="h3" component="div" sx={{ fontWeight: 'bold', mt: 'auto' }}>
              {stats.totalCustomers}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              <TrendingUp sx={{ fontSize: 14, verticalAlign: 'middle', mr: 0.5, color: 'success.main' }} />
              5% increase from last month
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper
            elevation={2}
            sx={{
              p: 2,
              display: 'flex',
              flexDirection: 'column',
              height: 140,
              borderRadius: 2,
              position: 'relative',
              overflow: 'hidden'
            }}
          >
            <Box
              sx={{
                position: 'absolute',
                top: -15,
                right: -15,
                backgroundColor: 'rgba(255, 152, 0, 0.1)',
                borderRadius: '50%',
                width: 100,
                height: 100,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center'
              }}
            >
              <AttachMoney sx={{ fontSize: 40, color: 'warning.main', opacity: 0.7 }} />
            </Box>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              Total Revenue
            </Typography>
            <Typography variant="h3" component="div" sx={{ fontWeight: 'bold', mt: 'auto' }}>
              ${stats.totalRevenue.toLocaleString()}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              <TrendingUp sx={{ fontSize: 14, verticalAlign: 'middle', mr: 0.5, color: 'success.main' }} />
              15% increase from last month
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* Recent Orders & Top Products */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper elevation={2} sx={{ p: 2, borderRadius: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">Recent Orders</Typography>
              <Button component={Link} to="/orders" size="small" color="primary">
                View All
              </Button>
            </Box>
            <Divider sx={{ mb: 2 }} />
            <List disablePadding>
              {stats.recentOrders.map((order: any) => (
                <React.Fragment key={order.id}>
                  <ListItem sx={{ py: 1, px: 0 }}>
                    <ListItemText
                      primary={`Order #${order.id} - ${order.customer}`}
                      secondary={`${order.date} • ${order.status}`}
                    />
                    <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                      ${order.total}
                    </Typography>
                  </ListItem>
                  <Divider component="li" />
                </React.Fragment>
              ))}
            </List>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={2} sx={{ p: 2, borderRadius: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">Top Products</Typography>
              <Button component={Link} to="/products" size="small" color="primary">
                View All
              </Button>
            </Box>
            <Divider sx={{ mb: 2 }} />
            <List disablePadding>
              {stats.topProducts.map((product: any) => (
                <React.Fragment key={product.id}>
                  <ListItem sx={{ py: 1, px: 0 }}>
                    <ListItemText
                      primary={product.name}
                      secondary={`${product.sales} units sold`}
                    />
                    <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                      ${product.revenue.toFixed(2)}
                    </Typography>
                  </ListItem>
                  <Divider component="li" />
                </React.Fragment>
              ))}
            </List>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
