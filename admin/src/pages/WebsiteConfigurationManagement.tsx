import React, { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Box,
  Typography,
  Paper,
  Grid,
  TextField,
  Button,
  Tabs,
  Tab,
  FormControlLabel,
  Switch,
  Divider,
  CircularProgress,
  Alert,
  IconButton,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction
} from '@mui/material';
import {
  Save as SaveIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
  Upload as UploadIcon
} from '@mui/icons-material';
import { uploadImage } from '../services/fileUpload';
import { toast } from 'react-toastify';
import { AppDispatch, RootState } from '../store';
import {
  fetchWebsiteConfigurationByStore,
  updateWebsiteConfiguration,
  updateSectionVisibility,
  fetchWebsiteBanners,
  createWebsiteBanner,
  updateWebsiteBanner,
  deleteWebsiteBanner,
  reorderWebsiteBanners,
} from '../store/slices/websiteConfigSlice';
import { WebsiteConfiguration, UpdateWebsiteConfigurationRequest } from '../services/websiteConfigService';
import { WebsiteBanner } from '../store/slices/websiteConfigSlice';
import StoreSelector from '../components/common/StoreSelector';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`website-config-tabpanel-${index}`}
      aria-labelledby={`website-config-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `website-config-tab-${index}`,
    'aria-controls': `website-config-tabpanel-${index}`,
  };
}

const WebsiteConfigurationManagement: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { currentConfiguration, banners, loading, error } = useSelector((state: RootState) => state.websiteConfig);
  const { selectedStore } = useSelector((state: RootState) => state.store);

  const [tabValue, setTabValue] = useState(0);
  const [formData, setFormData] = useState<Partial<UpdateWebsiteConfigurationRequest>>({});
  const [bannerFormOpen, setBannerFormOpen] = useState(false);
  const [editingBanner, setEditingBanner] = useState<WebsiteBanner | null>(null);
  const [bannerFormData, setBannerFormData] = useState<Partial<WebsiteBanner>>({
    imageUrl: '',
    title: '',
    subtitle: '',
    buttonText: 'Shop Now',
    buttonLink: '/products',
    displayOrder: 0,
    isActive: true
  });

  // Fetch website configuration and banners when selected store changes
  useEffect(() => {
    if (selectedStore) {
      dispatch(fetchWebsiteConfigurationByStore(selectedStore.id));
      dispatch(fetchWebsiteBanners(selectedStore.id));
    }
  }, [dispatch, selectedStore]);

  // Update form data when configuration changes
  useEffect(() => {
    if (currentConfiguration) {
      setFormData({
        websiteTitle: currentConfiguration.websiteTitle,
        logoUrl: currentConfiguration.logoUrl,
        metaDescription: currentConfiguration.metaDescription,
        metaKeywords: currentConfiguration.metaKeywords,
        announcementText: currentConfiguration.announcementText,
        showAnnouncement: currentConfiguration.showAnnouncement,
        phone: currentConfiguration.phone,
        email: currentConfiguration.email,
        address: currentConfiguration.address,
        facebookUrl: currentConfiguration.facebookUrl,
        instagramUrl: currentConfiguration.instagramUrl,
        twitterUrl: currentConfiguration.twitterUrl,
        whatsappNumber: currentConfiguration.whatsappNumber,
        youtubeUrl: currentConfiguration.youtubeUrl,
        showBannerSection: currentConfiguration.showBannerSection,
        showCategorySection: currentConfiguration.showCategorySection,
        showNewArrivalsSection: currentConfiguration.showNewArrivalsSection,
        showCollectionSection: currentConfiguration.showCollectionSection,
        showBestSellingSection: currentConfiguration.showBestSellingSection,
        bannerTitle: currentConfiguration.bannerTitle,
        bannerSubtitle: currentConfiguration.bannerSubtitle,
        bannerButtonText: currentConfiguration.bannerButtonText,
        bannerButtonLink: currentConfiguration.bannerButtonLink,
        categorySectionTitle: currentConfiguration.categorySectionTitle,
        newArrivalsSectionTitle: currentConfiguration.newArrivalsSectionTitle,
        collectionSectionTitle: currentConfiguration.collectionSectionTitle,
        bestSellingSectionTitle: currentConfiguration.bestSellingSectionTitle
      });
    } else {
      // Initialize with default values if no configuration is available
      setFormData({
        websiteTitle: selectedStore?.name || 'MyShop',
        logoUrl: selectedStore?.logoUrl || '',
        metaDescription: '',
        metaKeywords: '',
        announcementText: 'Free shipping on all orders above ₹999',
        showAnnouncement: true,
        phone: '',
        email: '',
        address: '',
        facebookUrl: '',
        instagramUrl: '',
        twitterUrl: '',
        whatsappNumber: '',
        youtubeUrl: '',
        showBannerSection: true,
        showCategorySection: true,
        showNewArrivalsSection: true,
        showCollectionSection: true,
        showBestSellingSection: true,
        bannerTitle: 'Welcome to ' + (selectedStore?.name || 'MyShop'),
        bannerSubtitle: 'Discover our exclusive range of products',
        bannerButtonText: 'Shop Now',
        bannerButtonLink: '/products',
        categorySectionTitle: 'Shop By Category',
        newArrivalsSectionTitle: 'New Arrivals',
        collectionSectionTitle: 'Collections',
        bestSellingSectionTitle: 'Best Selling Products'
      });
    }
  }, [currentConfiguration, selectedStore]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!event.target.files || event.target.files.length === 0) {
      return;
    }

    const file = event.target.files[0];
    try {
      setLoading(true);
      const imageUrl = await uploadImage(file);

      // Extract just the filename from the URL
      const filename = imageUrl.split('/').pop();

      setFormData({
        ...formData,
        logoUrl: filename
      });

      toast.success('Logo uploaded successfully');
    } catch (error) {
      toast.error('Failed to upload logo');
      console.error('Error uploading logo:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSaveConfiguration = () => {
    if (!selectedStore) {
      toast.error('Please select a store first');
      return;
    }

    dispatch(updateWebsiteConfiguration({ storeId: selectedStore.id, config: formData as UpdateWebsiteConfigurationRequest }))
      .unwrap()
      .then(() => {
        toast.success('Website configuration updated successfully');
      })
      .catch((error) => {
        toast.error(`Failed to update configuration: ${error}`);
      });
  };

  const handleSectionVisibilityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    const updatedFormData = {
      ...formData,
      [name]: checked
    };
    setFormData(updatedFormData);

    if (!selectedStore) {
      toast.error('Please select a store first');
      return;
    }

    // Update section visibility in the backend
    const sectionVisibility = {
      [name]: checked
    };

    dispatch(updateSectionVisibility({
      storeId: selectedStore.id,
      sectionVisibility
    }))
      .unwrap()
      .then(() => {
        toast.success('Section visibility updated');
      })
      .catch((error) => {
        toast.error(`Failed to update section visibility: ${error}`);
      });
  };

  const handleBannerInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setBannerFormData({
      ...bannerFormData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const bannerFileInputRef = useRef<HTMLInputElement>(null);

  const handleBannerImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!event.target.files || event.target.files.length === 0) {
      return;
    }

    const file = event.target.files[0];
    try {
      setLoading(true);
      const imageUrl = await uploadImage(file);

      // Extract just the filename from the URL
      const filename = imageUrl.split('/').pop();

      setBannerFormData({
        ...bannerFormData,
        imageUrl: filename
      });

      toast.success('Banner image uploaded successfully');
    } catch (error) {
      toast.error('Failed to upload banner image');
      console.error('Error uploading banner image:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleOpenBannerForm = (banner?: WebsiteBanner) => {
    if (banner) {
      setEditingBanner(banner);
      setBannerFormData(banner);
    } else {
      setEditingBanner(null);
      setBannerFormData({
        imageUrl: '',
        title: '',
        subtitle: '',
        buttonText: 'Shop Now',
        buttonLink: '/products',
        displayOrder: banners.length,
        isActive: true
      });
    }
    setBannerFormOpen(true);
  };

  const handleCloseBannerForm = () => {
    setBannerFormOpen(false);
    setEditingBanner(null);
  };

  const handleSaveBanner = () => {
    if (!selectedStore) {
      toast.error('Please select a store first');
      return;
    }

    // Add storeId to banner data
    const bannerWithStoreId = {
      ...bannerFormData,
      storeId: selectedStore.id
    };

    if (editingBanner) {
      dispatch(updateWebsiteBanner({ id: editingBanner.id, banner: bannerWithStoreId }))
        .unwrap()
        .then(() => {
          toast.success('Banner updated successfully');
          handleCloseBannerForm();
          // Refresh banners after update
          dispatch(fetchWebsiteBanners(selectedStore.id));
        })
        .catch((error) => {
          toast.error(`Failed to update banner: ${error}`);
        });
    } else {
      dispatch(createWebsiteBanner(bannerWithStoreId))
        .unwrap()
        .then(() => {
          toast.success('Banner created successfully');
          handleCloseBannerForm();
          // Refresh banners after creation
          dispatch(fetchWebsiteBanners(selectedStore.id));
        })
        .catch((error) => {
          toast.error(`Failed to create banner: ${error}`);
        });
    }
  };

  const handleDeleteBanner = (id: number) => {
    if (!selectedStore) {
      toast.error('Please select a store first');
      return;
    }

    if (window.confirm('Are you sure you want to delete this banner?')) {
      dispatch(deleteWebsiteBanner({ storeId: selectedStore.id, id }))
        .unwrap()
        .then(() => {
          toast.success('Banner deleted successfully');
          // Refresh banners after deletion
          dispatch(fetchWebsiteBanners(selectedStore.id));
        })
        .catch((error) => {
          toast.error(`Failed to delete banner: ${error}`);
        });
    }
  };

  const handleMoveBanner = (id: number, direction: 'up' | 'down') => {
    if (!selectedStore) {
      toast.error('Please select a store first');
      return;
    }

    const currentIndex = banners.findIndex(banner => banner.id === id);
    if (
      (direction === 'up' && currentIndex > 0) ||
      (direction === 'down' && currentIndex < banners.length - 1)
    ) {
      const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
      const targetBanner = banners[newIndex];

      // Create updated banners array with new order
      const updatedBanners = [...banners];
      const temp = { ...updatedBanners[currentIndex] };
      updatedBanners[currentIndex] = { ...updatedBanners[newIndex] };
      updatedBanners[newIndex] = temp;

      dispatch(reorderWebsiteBanners({
        storeId: selectedStore.id,
        banners: updatedBanners
      }))
        .unwrap()
        .then(() => {
          toast.success('Banner order updated');
          // Refresh banners after reordering
          dispatch(fetchWebsiteBanners(selectedStore.id));
        })
        .catch((error) => {
          toast.error(`Failed to update banner order: ${error}`);
        });
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" component="h1">
            Website Configuration
          </Typography>
          {selectedStore && (
            <Typography variant="subtitle1" color="text.secondary">
              {selectedStore.name}
            </Typography>
          )}
        </Box>
        <Box sx={{ minWidth: 200 }}>
          <StoreSelector
            showLabel={true}
            variant="outlined"
            size="small"
          />
        </Box>
      </Box>

      {!selectedStore && (
        <Alert severity="info" sx={{ mb: 3 }}>
          Please select a store to manage its website configuration
        </Alert>
      )}

      <Paper sx={{ width: '100%', mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab label="General Settings" {...a11yProps(0)} />
          <Tab label="Announcements" {...a11yProps(1)} />
          <Tab label="Home Page Sections" {...a11yProps(2)} />
          <Tab label="Banner Management" {...a11yProps(3)} />
          <Tab label="Social Media" {...a11yProps(4)} />
          <Tab label="SEO Settings" {...a11yProps(5)} />
        </Tabs>

        {/* General Settings Tab */}
        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                General Information
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Website Title"
                name="websiteTitle"
                value={formData.websiteTitle || ''}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Logo URL"
                name="logoUrl"
                value={formData.logoUrl || ''}
                onChange={handleInputChange}
                helperText="Enter the URL of your website logo or upload a new one"
                InputProps={{
                  endAdornment: (
                    <Button
                      variant="contained"
                      component="label"
                      startIcon={<UploadIcon />}
                      size="small"
                      onClick={() => fileInputRef.current?.click()}
                    >
                      Upload
                      <input
                        ref={fileInputRef}
                        type="file"
                        hidden
                        accept="image/*"
                        onChange={handleLogoUpload}
                      />
                    </Button>
                  ),
                }}
              />
              <Box sx={{ mt: 1 }}>
                <Typography variant="caption" color="text.secondary" display="block">
                  Recommended dimensions: 400x100px with transparent background (PNG format)
                </Typography>
              </Box>
            </Grid>
            {formData.logoUrl && (
              <Grid item xs={12} md={6}>
                <Box sx={{ textAlign: 'center', mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>Logo Preview:</Typography>
                  <Box sx={{
                    p: 2,
                    borderRadius: 1,
                    border: '1px solid #eee',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    bgcolor: '#f9f9f9'
                  }}>
                    <Box
                      component="img"
                      src={formData.logoUrl && formData.logoUrl.startsWith('http')
                        ? formData.logoUrl
                        : `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:5295'}/uploads/images/${formData.logoUrl}`}
                      alt="Logo Preview"
                      sx={{
                        maxHeight: '80px',
                        maxWidth: '100%',
                        objectFit: 'contain'
                      }}
                      onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                        // Break the infinite loop by checking if we're already showing a placeholder
                        if (!e.currentTarget.src.includes('placeholder')) {
                          e.currentTarget.src = 'https://via.placeholder.com/200x80?text=No+Image';
                          // Stop the error propagation
                          e.stopPropagation();
                        }
                      }}
                    />
                  </Box>
                </Box>
              </Grid>
            )}
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Email"
                name="email"
                value={formData.email || ''}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Phone"
                name="phone"
                value={formData.phone || ''}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Address"
                name="address"
                value={formData.address || ''}
                onChange={handleInputChange}
                multiline
                rows={3}
              />
            </Grid>
            <Grid item xs={12}>
              <Button
                variant="contained"
                color="primary"
                startIcon={<SaveIcon />}
                onClick={handleSaveConfiguration}
                disabled={loading}
              >
                Save Changes
              </Button>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Announcements Tab */}
        <TabPanel value={tabValue} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Announcement Settings
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Configure the announcement bar that appears at the top of all pages
              </Typography>
            </Grid>
            <Grid item xs={12} md={8}>
              <TextField
                fullWidth
                label="Announcement Text"
                name="announcementText"
                value={formData.announcementText || ''}
                onChange={handleInputChange}
                helperText="This text will scroll in the announcement bar at the top of the page"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.showAnnouncement || false}
                    onChange={handleInputChange}
                    name="showAnnouncement"
                    color="primary"
                  />
                }
                label="Show Announcement"
              />
            </Grid>
            <Grid item xs={12}>
              <Button
                variant="contained"
                color="primary"
                startIcon={<SaveIcon />}
                onClick={handleSaveConfiguration}
                disabled={loading}
              >
                Save Changes
              </Button>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Home Page Sections Tab */}
        <TabPanel value={tabValue} index={2}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Section Visibility
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.showBannerSection || false}
                    onChange={handleSectionVisibilityChange}
                    name="showBannerSection"
                    color="primary"
                  />
                }
                label="Show Banner Section"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.showCategorySection || false}
                    onChange={handleSectionVisibilityChange}
                    name="showCategorySection"
                    color="primary"
                  />
                }
                label="Show Category Section"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.showNewArrivalsSection || false}
                    onChange={handleSectionVisibilityChange}
                    name="showNewArrivalsSection"
                    color="primary"
                  />
                }
                label="Show New Arrivals Section"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.showCollectionSection || false}
                    onChange={handleSectionVisibilityChange}
                    name="showCollectionSection"
                    color="primary"
                  />
                }
                label="Show Collection Section"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.showBestSellingSection || false}
                    onChange={handleSectionVisibilityChange}
                    name="showBestSellingSection"
                    color="primary"
                  />
                }
                label="Show Best Selling Section"
              />
            </Grid>

            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="h6" gutterBottom>
                Section Titles
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Category Section Title"
                name="categorySectionTitle"
                value={formData.categorySectionTitle || ''}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="New Arrivals Section Title"
                name="newArrivalsSectionTitle"
                value={formData.newArrivalsSectionTitle || ''}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Collection Section Title"
                name="collectionSectionTitle"
                value={formData.collectionSectionTitle || ''}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Best Selling Section Title"
                name="bestSellingSectionTitle"
                value={formData.bestSellingSectionTitle || ''}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12}>
              <Button
                variant="contained"
                color="primary"
                startIcon={<SaveIcon />}
                onClick={handleSaveConfiguration}
                disabled={loading}
              >
                Save Changes
              </Button>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Banner Management Tab */}
        <TabPanel value={tabValue} index={3}>
          <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">
              Banner Management
            </Typography>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={() => handleOpenBannerForm()}
            >
              Add Banner
            </Button>
          </Box>

          <Grid container spacing={3}>
            {banners.map((banner) => (
              <Grid item xs={12} md={6} lg={4} key={banner.id}>
                <Card>
                  <CardMedia
                    component="img"
                    height="200"
                    image={banner.imageUrl && banner.imageUrl.startsWith('http')
                      ? banner.imageUrl
                      : banner.imageUrl
                        ? `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:5295'}/uploads/images/${banner.imageUrl}`
                        : 'https://via.placeholder.com/400x200?text=No+Banner+Image'}
                    alt={banner.title}
                    onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                      // Break the infinite loop by checking if we're already showing a placeholder
                      if (!e.currentTarget.src.includes('placeholder')) {
                        e.currentTarget.src = 'https://via.placeholder.com/400x200?text=No+Banner+Image';
                        // Stop the error propagation
                        e.stopPropagation();
                      }
                    }}
                  />
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      {banner.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {banner.subtitle}
                    </Typography>
                    <Box sx={{ mt: 1 }}>
                      <Typography variant="body2">
                        Button: {banner.buttonText} → {banner.buttonLink}
                      </Typography>
                      <Typography variant="body2">
                        Order: {banner.displayOrder} | Status: {banner.isActive ? 'Active' : 'Inactive'}
                      </Typography>
                    </Box>
                  </CardContent>
                  <CardActions>
                    <IconButton
                      size="small"
                      color="primary"
                      onClick={() => handleMoveBanner(banner.id, 'up')}
                      disabled={banners.indexOf(banner) === 0}
                    >
                      <ArrowUpwardIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      color="primary"
                      onClick={() => handleMoveBanner(banner.id, 'down')}
                      disabled={banners.indexOf(banner) === banners.length - 1}
                    >
                      <ArrowDownwardIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      color="primary"
                      onClick={() => handleOpenBannerForm(banner)}
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      color="error"
                      onClick={() => handleDeleteBanner(banner.id)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>

          {/* Banner Form Dialog */}
          <Dialog open={bannerFormOpen} onClose={handleCloseBannerForm} maxWidth="md" fullWidth>
            <DialogTitle>
              {editingBanner ? 'Edit Banner' : 'Add Banner'}
            </DialogTitle>
            <DialogContent>
              <Grid container spacing={3} sx={{ mt: 0 }}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Image URL"
                    name="imageUrl"
                    value={bannerFormData.imageUrl || ''}
                    onChange={handleBannerInputChange}
                    required
                    helperText="Use the filename from uploaded images (e.g., image.jpg) or full URL for external images"
                    InputProps={{
                      endAdornment: (
                        <Button
                          variant="contained"
                          component="label"
                          startIcon={<UploadIcon />}
                          size="small"
                          onClick={() => bannerFileInputRef.current?.click()}
                        >
                          Upload
                          <input
                            ref={bannerFileInputRef}
                            type="file"
                            hidden
                            accept="image/*"
                            onChange={handleBannerImageUpload}
                          />
                        </Button>
                      ),
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Title"
                    name="title"
                    value={bannerFormData.title || ''}
                    onChange={handleBannerInputChange}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Subtitle"
                    name="subtitle"
                    value={bannerFormData.subtitle || ''}
                    onChange={handleBannerInputChange}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Button Text"
                    name="buttonText"
                    value={bannerFormData.buttonText || ''}
                    onChange={handleBannerInputChange}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Button Link"
                    name="buttonLink"
                    value={bannerFormData.buttonLink || ''}
                    onChange={handleBannerInputChange}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Display Order"
                    name="displayOrder"
                    type="number"
                    value={bannerFormData.displayOrder || 0}
                    onChange={handleBannerInputChange}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={bannerFormData.isActive || false}
                        onChange={handleBannerInputChange}
                        name="isActive"
                        color="primary"
                      />
                    }
                    label="Active"
                  />
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseBannerForm}>Cancel</Button>
              <Button
                onClick={handleSaveBanner}
                variant="contained"
                color="primary"
                disabled={!bannerFormData.imageUrl}
              >
                Save
              </Button>
            </DialogActions>
          </Dialog>
        </TabPanel>

        {/* Social Media Tab */}
        <TabPanel value={tabValue} index={4}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Social Media Links
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Instagram URL"
                name="instagramUrl"
                value={formData.instagramUrl || ''}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Facebook URL"
                name="facebookUrl"
                value={formData.facebookUrl || ''}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Twitter URL"
                name="twitterUrl"
                value={formData.twitterUrl || ''}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="YouTube URL"
                name="youtubeUrl"
                value={formData.youtubeUrl || ''}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="WhatsApp Number"
                name="whatsappNumber"
                value={formData.whatsappNumber || ''}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12}>
              <Button
                variant="contained"
                color="primary"
                startIcon={<SaveIcon />}
                onClick={handleSaveConfiguration}
                disabled={loading}
              >
                Save Changes
              </Button>
            </Grid>
          </Grid>
        </TabPanel>

        {/* SEO Settings Tab */}
        <TabPanel value={tabValue} index={5}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                SEO Settings
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Meta Description"
                name="metaDescription"
                value={formData.metaDescription || ''}
                onChange={handleInputChange}
                multiline
                rows={3}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Meta Keywords"
                name="metaKeywords"
                value={formData.metaKeywords || ''}
                onChange={handleInputChange}
                helperText="Separate keywords with commas"
              />
            </Grid>
            <Grid item xs={12}>
              <Button
                variant="contained"
                color="primary"
                startIcon={<SaveIcon />}
                onClick={handleSaveConfiguration}
                disabled={loading}
              >
                Save Changes
              </Button>
            </Grid>
          </Grid>
        </TabPanel>
      </Paper>
    </Box>
  );
};

export default WebsiteConfigurationManagement;
