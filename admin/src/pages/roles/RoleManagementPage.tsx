import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  FormControlLabel,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Switch,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
  useTheme,
} from '@mui/material';
import { Add, Delete, Edit, Refresh } from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../store';
import {
  Role,
  fetchRoles,
  fetchRolesByStore,
  createRole,
  updateRole,
  deleteRole,
  CreateRoleRequest,
  UpdateRoleRequest
} from '../../store/slices/roleSlice';
// No longer using permissions
import { fetchAllStores } from '../../store/slices/storeSlice';
import LoadingButton from '@mui/lab/LoadingButton';
import SaveIcon from '@mui/icons-material/Save';
import { useNavigate } from 'react-router-dom';

interface RoleFormData {
  id?: string;
  name: string;
  description: string;
}

// No longer using permissions

const RoleManagementPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { roles, loading, error } = useSelector((state: RootState) => state.roles);
  const { stores } = useSelector((state: RootState) => state.store);
  const { user: currentUser } = useSelector((state: RootState) => state.auth);
  const { enqueueSnackbar } = useSnackbar();
  const theme = useTheme();

  const [openDialog, setOpenDialog] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [confirmDelete, setConfirmDelete] = useState<string | null>(null);
  const [selectedStoreId, setSelectedStoreId] = useState<number | null>(null);
  const [formData, setFormData] = useState<RoleFormData>({
    name: '',
    description: ''
  });

  // Check if current user is super admin
  const isSuperAdmin = currentUser?.roles?.includes('SuperAdmin') || false;

  useEffect(() => {
    dispatch(fetchAllStores());

    if (selectedStoreId) {
      dispatch(fetchRolesByStore(selectedStoreId));
    } else {
      dispatch(fetchRoles());
    }
  }, [dispatch, selectedStoreId]);

  const handleOpenDialog = (isEdit: boolean, role?: Role) => {
    setIsEditMode(isEdit);
    if (isEdit && role) {
      setFormData({
        id: role.id,
        name: role.name,
        description: role.description || ''
      });
    } else {
      setFormData({
        name: '',
        description: ''
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value } = e.target;
    if (name) {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  // No longer using permission-related functions

  const validateForm = () => {
    if (!formData.name) {
      enqueueSnackbar('Role name is required', { variant: 'error' });
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      if (isEditMode && formData.id) {
        // Update role
        const updateData: UpdateRoleRequest = {
          id: formData.id,
          name: formData.name,
          description: formData.description
        };

        await dispatch(updateRole(updateData)).unwrap();
        enqueueSnackbar('Role updated successfully', { variant: 'success' });
      } else {
        // Create role
        const createData: CreateRoleRequest = {
          name: formData.name,
          description: formData.description
        };

        await dispatch(createRole(createData)).unwrap();
        enqueueSnackbar('Role created successfully', { variant: 'success' });
      }

      handleCloseDialog();
      if (selectedStoreId) {
        dispatch(fetchRolesByStore(selectedStoreId));
      } else {
        dispatch(fetchRoles());
      }
    } catch (error: any) {
      enqueueSnackbar(error.message || 'An error occurred', { variant: 'error' });
    }
  };

  const handleDelete = (id: string) => {
    setConfirmDelete(id);
  };

  const confirmDeleteRole = async () => {
    if (!confirmDelete) return;

    try {
      await dispatch(deleteRole(confirmDelete)).unwrap();
      enqueueSnackbar('Role deleted successfully', { variant: 'success' });
      setConfirmDelete(null);
    } catch (error: any) {
      enqueueSnackbar(error.message || 'An error occurred', { variant: 'error' });
    }
  };

  const handleRefresh = () => {
    if (selectedStoreId) {
      dispatch(fetchRolesByStore(selectedStoreId));
    } else {
      dispatch(fetchRoles());
    }
  };

  // No longer using store and permission related functions

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Role Management
      </Typography>

      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Grid container spacing={3} alignItems="center">
            {isSuperAdmin && (
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Filter by Store</InputLabel>
                  <Select
                    value={selectedStoreId || ''}
                    onChange={(e) => setSelectedStoreId(e.target.value === '' ? null : Number(e.target.value))}
                    label="Filter by Store"
                  >
                    <MenuItem value="">All Stores</MenuItem>
                    {stores.map(store => (
                      <MenuItem key={store.id} value={store.id}>
                        {store.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            )}
            <Grid item xs={12} md={isSuperAdmin ? 6 : 12} sx={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                variant="outlined"
                startIcon={<Refresh />}
                onClick={handleRefresh}
                sx={{ mr: 1 }}
              >
                Refresh
              </Button>
              <Button
                variant="contained"
                startIcon={<Add />}
                onClick={() => handleOpenDialog(false)}
              >
                Add Role
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Name</TableCell>
              <TableCell>Description</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {roles.map((role) => (
              <TableRow key={role.id}>
                <TableCell>{role.name}</TableCell>
                <TableCell>{role.description}</TableCell>
                <TableCell>
                  <IconButton
                    color="primary"
                    onClick={() => handleOpenDialog(true, role)}
                    disabled={role.name === 'SuperAdmin'}
                  >
                    <Edit />
                  </IconButton>
                  <IconButton
                    color="error"
                    onClick={() => handleDelete(role.id)}
                    disabled={role.name === 'SuperAdmin'}
                  >
                    <Delete />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
            {roles.length === 0 && (
              <TableRow>
                <TableCell colSpan={3} align="center">
                  No roles found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Role Form Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>{isEditMode ? 'Edit Role' : 'Add Role'}</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                name="name"
                label="Role Name"
                value={formData.name}
                onChange={handleInputChange}
                fullWidth
                required
                disabled={isEditMode && formData.name === 'SuperAdmin'}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="description"
                label="Description"
                value={formData.description}
                onChange={handleInputChange}
                fullWidth
                multiline
                rows={2}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <LoadingButton
            loading={loading}
            loadingPosition="start"
            startIcon={<SaveIcon />}
            variant="contained"
            onClick={handleSubmit}
            disabled={isEditMode && formData.name === 'SuperAdmin'}
          >
            {isEditMode ? 'Update' : 'Create'}
          </LoadingButton>
        </DialogActions>
      </Dialog>

      {/* Confirm Delete Dialog */}
      <Dialog open={!!confirmDelete} onClose={() => setConfirmDelete(null)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this role? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDelete(null)}>Cancel</Button>
          <Button onClick={confirmDeleteRole} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default RoleManagementPage;
