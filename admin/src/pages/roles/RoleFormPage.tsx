import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  Checkbox,
  Divider,
  FormControl,
  FormControlLabel,
  FormGroup,
  FormHelperText,
  FormLabel,
  Grid,
  MenuItem,
  Select,
  TextField,
  Typography,
  useTheme,
} from '@mui/material';
import { useNavigate, useParams } from 'react-router-dom';
import { useSnackbar } from 'notistack';
import { Role, roleService, CreateRoleRequest, UpdateRoleRequest } from '../../services/roleService';
import { Permission, permissionService } from '../../services/permissionService';
import { storeService } from '../../services';
import LoadingButton from '@mui/lab/LoadingButton';
import SaveIcon from '@mui/icons-material/Save';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';

interface Store {
  id: number;
  name: string;
}

interface PermissionsByModule {
  [module: string]: Permission[];
}

const RoleFormPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const isEditMode = Boolean(id);
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const theme = useTheme();

  const [role, setRole] = useState<CreateRoleRequest | UpdateRoleRequest>({
    id: isEditMode ? parseInt(id as string) : 0,
    name: '',
    description: '',
    storeId: null,
    isActive: true,
    permissionIds: [],
  });

  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [permissionsByModule, setPermissionsByModule] = useState<PermissionsByModule>({});
  const [stores, setStores] = useState<Store[]>([]);
  const [loading, setLoading] = useState(false);
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Fetch permissions
        const permissionsData = await permissionService.getPermissions();
        setPermissions(permissionsData);
        
        // Group permissions by module
        const groupedPermissions: PermissionsByModule = {};
        permissionsData.forEach(permission => {
          if (!groupedPermissions[permission.module]) {
            groupedPermissions[permission.module] = [];
          }
          groupedPermissions[permission.module].push(permission);
        });
        setPermissionsByModule(groupedPermissions);
        
        // Fetch stores
        const storesData = await storeService.getStores();
        setStores(storesData);
        
        // If editing, fetch role details
        if (isEditMode) {
          const roleData = await roleService.getRole(parseInt(id as string));
          setRole({
            id: roleData.id,
            name: roleData.name,
            description: roleData.description,
            storeId: roleData.storeId,
            isActive: roleData.isActive,
            permissionIds: roleData.permissionIds,
          });
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        enqueueSnackbar('Failed to load data', { variant: 'error' });
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [id, isEditMode, enqueueSnackbar]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value } = e.target;
    if (name) {
      setRole(prev => ({ ...prev, [name]: value }));
      
      // Clear error for this field
      if (formErrors[name]) {
        setFormErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[name];
          return newErrors;
        });
      }
    }
  };

  const handleStoreChange = (e: React.ChangeEvent<{ name?: string; value: unknown }>) => {
    const value = e.target.value as number | null;
    setRole(prev => ({ ...prev, storeId: value }));
  };

  const handleCheckboxChange = (permissionId: number) => {
    setRole(prev => {
      const permissionIds = [...prev.permissionIds];
      const index = permissionIds.indexOf(permissionId);
      
      if (index === -1) {
        permissionIds.push(permissionId);
      } else {
        permissionIds.splice(index, 1);
      }
      
      return { ...prev, permissionIds };
    });
  };

  const handleModuleSelectAll = (module: string, isSelected: boolean) => {
    const modulePermissionIds = permissionsByModule[module].map(p => p.id);
    
    setRole(prev => {
      let newPermissionIds = [...prev.permissionIds];
      
      if (isSelected) {
        // Add all module permissions that aren't already selected
        modulePermissionIds.forEach(id => {
          if (!newPermissionIds.includes(id)) {
            newPermissionIds.push(id);
          }
        });
      } else {
        // Remove all module permissions
        newPermissionIds = newPermissionIds.filter(id => !modulePermissionIds.includes(id));
      }
      
      return { ...prev, permissionIds: newPermissionIds };
    });
  };

  const isModuleFullySelected = (module: string): boolean => {
    const modulePermissionIds = permissionsByModule[module].map(p => p.id);
    return modulePermissionIds.every(id => role.permissionIds.includes(id));
  };

  const isModulePartiallySelected = (module: string): boolean => {
    const modulePermissionIds = permissionsByModule[module].map(p => p.id);
    return modulePermissionIds.some(id => role.permissionIds.includes(id)) && 
           !modulePermissionIds.every(id => role.permissionIds.includes(id));
  };

  const validateForm = (): boolean => {
    const errors: { [key: string]: string } = {};
    
    if (!role.name.trim()) {
      errors.name = 'Name is required';
    }
    
    if (!role.description.trim()) {
      errors.description = 'Description is required';
    }
    
    if (role.permissionIds.length === 0) {
      errors.permissions = 'At least one permission must be selected';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      enqueueSnackbar('Please fix the errors in the form', { variant: 'error' });
      return;
    }
    
    try {
      setLoading(true);
      
      if (isEditMode) {
        await roleService.updateRole(role as UpdateRoleRequest);
        enqueueSnackbar('Role updated successfully', { variant: 'success' });
      } else {
        await roleService.createRole(role as CreateRoleRequest);
        enqueueSnackbar('Role created successfully', { variant: 'success' });
      }
      
      navigate('/roles');
    } catch (error) {
      console.error('Error saving role:', error);
      enqueueSnackbar('Failed to save role', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">{isEditMode ? 'Edit Role' : 'Create Role'}</Typography>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/roles')}
        >
          Back to Roles
        </Button>
      </Box>

      <form onSubmit={handleSubmit}>
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>Role Details</Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Role Name"
                  name="name"
                  value={role.name}
                  onChange={handleInputChange}
                  error={!!formErrors.name}
                  helperText={formErrors.name}
                  disabled={loading || role.name === 'SuperAdmin'}
                  required
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <FormLabel>Store</FormLabel>
                  <Select
                    name="storeId"
                    value={role.storeId === null ? '' : role.storeId}
                    onChange={handleStoreChange}
                    displayEmpty
                    disabled={loading || role.name === 'SuperAdmin'}
                  >
                    <MenuItem value="">Global (All Stores)</MenuItem>
                    {stores.map(store => (
                      <MenuItem key={store.id} value={store.id}>
                        {store.name}
                      </MenuItem>
                    ))}
                  </Select>
                  <FormHelperText>
                    Global roles apply to all stores, store-specific roles only apply to the selected store
                  </FormHelperText>
                </FormControl>
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  name="description"
                  value={role.description}
                  onChange={handleInputChange}
                  error={!!formErrors.description}
                  helperText={formErrors.description}
                  disabled={loading}
                  required
                  multiline
                  rows={2}
                />
              </Grid>
              
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={role.isActive}
                      onChange={(e) => setRole(prev => ({ ...prev, isActive: e.target.checked }))}
                      name="isActive"
                      disabled={loading || role.name === 'SuperAdmin'}
                    />
                  }
                  label="Active"
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>Permissions</Typography>
            
            {formErrors.permissions && (
              <FormHelperText error>{formErrors.permissions}</FormHelperText>
            )}
            
            {Object.keys(permissionsByModule).map(module => (
              <Box key={module} mb={3}>
                <FormControl component="fieldset" fullWidth>
                  <FormLabel component="legend" sx={{ display: 'flex', alignItems: 'center' }}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={isModuleFullySelected(module)}
                          indeterminate={isModulePartiallySelected(module)}
                          onChange={(e) => handleModuleSelectAll(module, e.target.checked)}
                          disabled={loading || role.name === 'SuperAdmin'}
                        />
                      }
                      label={<Typography variant="subtitle1">{module}</Typography>}
                    />
                  </FormLabel>
                  
                  <Divider sx={{ my: 1 }} />
                  
                  <FormGroup>
                    <Grid container spacing={2}>
                      {permissionsByModule[module].map(permission => (
                        <Grid item xs={12} sm={6} md={4} key={permission.id}>
                          <FormControlLabel
                            control={
                              <Checkbox
                                checked={role.permissionIds.includes(permission.id)}
                                onChange={() => handleCheckboxChange(permission.id)}
                                disabled={loading || role.name === 'SuperAdmin'}
                              />
                            }
                            label={permission.name}
                          />
                        </Grid>
                      ))}
                    </Grid>
                  </FormGroup>
                </FormControl>
              </Box>
            ))}
          </CardContent>
        </Card>

        <Box mt={3} display="flex" justifyContent="flex-end">
          <Button
            variant="outlined"
            onClick={() => navigate('/roles')}
            sx={{ mr: 2 }}
            disabled={loading}
          >
            Cancel
          </Button>
          
          <LoadingButton
            type="submit"
            variant="contained"
            color="primary"
            loading={loading}
            loadingPosition="start"
            startIcon={<SaveIcon />}
            disabled={role.name === 'SuperAdmin'}
          >
            {isEditMode ? 'Update Role' : 'Create Role'}
          </LoadingButton>
        </Box>
      </form>
    </Box>
  );
};

export default RoleFormPage;
