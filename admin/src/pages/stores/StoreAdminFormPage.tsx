import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Divider,
  FormControlLabel,
  Grid,
  IconButton,
  Paper,
  Switch,
  TextField,
  Typography,
} from '@mui/material';
import { ArrowBack } from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { storeService } from '../../services';

interface StoreAdminFormData {
  userEmail: string;
  canManageProducts: boolean;
  canManageOrders: boolean;
  canManageCustomers: boolean;
  canManageSettings: boolean;
}

const StoreAdminFormPage: React.FC = () => {
  const { storeId, adminId } = useParams<{ storeId: string; adminId: string }>();
  const isEditMode = Boolean(adminId);
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();

  const [loading, setLoading] = useState(isEditMode);
  const [saving, setSaving] = useState(false);
  const [store, setStore] = useState<any>(null);

  const [formData, setFormData] = useState<StoreAdminFormData>({
    userEmail: '',
    canManageProducts: true,
    canManageOrders: true,
    canManageCustomers: true,
    canManageSettings: false,
  });

  useEffect(() => {
    if (storeId) {
      fetchStoreData(parseInt(storeId));
    }

    if (isEditMode && adminId) {
      fetchAdminData(parseInt(adminId));
    }
  }, [storeId, adminId, isEditMode]);

  const fetchStoreData = async (id: number) => {
    try {
      const storeData = await storeService.getStore(id);
      setStore(storeData);
    } catch (error) {
      console.error('Error fetching store:', error);
      enqueueSnackbar('Failed to load store data', { variant: 'error' });
      navigate('/stores');
    }
  };

  const fetchAdminData = async (id: number) => {
    try {
      setLoading(true);
      // This would need to be implemented in the API and service
      const admins = await storeService.getStoreAdmins(parseInt(storeId!));
      const admin = admins.find(a => a.id === id);
      
      if (admin) {
        setFormData({
          userEmail: admin.userEmail,
          canManageProducts: admin.canManageProducts,
          canManageOrders: admin.canManageOrders,
          canManageCustomers: admin.canManageCustomers,
          canManageSettings: admin.canManageSettings,
        });
      } else {
        enqueueSnackbar('Admin not found', { variant: 'error' });
        navigate(`/stores/view/${storeId}`);
      }
    } catch (error) {
      console.error('Error fetching admin:', error);
      enqueueSnackbar('Failed to load admin data', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setSaving(true);
      
      if (isEditMode && adminId) {
        await storeService.updateStoreAdmin(parseInt(adminId), {
          id: parseInt(adminId),
          ...formData,
        });
        enqueueSnackbar('Store admin updated successfully', { variant: 'success' });
      } else {
        await storeService.createStoreAdmin({
          storeId: parseInt(storeId!),
          ...formData,
        });
        enqueueSnackbar('Store admin added successfully', { variant: 'success' });
      }
      
      navigate(`/stores/view/${storeId}`);
    } catch (error) {
      console.error('Error saving store admin:', error);
      enqueueSnackbar('Failed to save store admin', { variant: 'error' });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" alignItems="center" mb={3}>
        <IconButton onClick={() => navigate(`/stores/view/${storeId}`)} sx={{ mr: 2 }}>
          <ArrowBack />
        </IconButton>
        <Typography variant="h4">
          {isEditMode ? 'Edit Store Admin' : 'Add Store Admin'}
        </Typography>
      </Box>

      {store && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Store: {store.name}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {store.description || 'No description provided'}
            </Typography>
          </CardContent>
        </Card>
      )}

      <Paper component="form" onSubmit={handleSubmit} sx={{ p: 3 }}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom>
              Admin Information
            </Typography>
            <Divider sx={{ mb: 2 }} />
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              required
              label="Admin Email"
              name="userEmail"
              type="email"
              value={formData.userEmail}
              onChange={handleChange}
              disabled={saving || isEditMode}
              helperText={isEditMode ? "Email cannot be changed" : "Enter the email of the user to add as admin"}
            />
          </Grid>

          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom>
              Permissions
            </Typography>
            <Divider sx={{ mb: 2 }} />
          </Grid>

          <Grid item xs={12} md={6}>
            <FormControlLabel
              control={
                <Switch
                  checked={formData.canManageProducts}
                  onChange={handleChange}
                  name="canManageProducts"
                  color="primary"
                  disabled={saving}
                />
              }
              label="Can Manage Products"
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <FormControlLabel
              control={
                <Switch
                  checked={formData.canManageOrders}
                  onChange={handleChange}
                  name="canManageOrders"
                  color="primary"
                  disabled={saving}
                />
              }
              label="Can Manage Orders"
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <FormControlLabel
              control={
                <Switch
                  checked={formData.canManageCustomers}
                  onChange={handleChange}
                  name="canManageCustomers"
                  color="primary"
                  disabled={saving}
                />
              }
              label="Can Manage Customers"
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <FormControlLabel
              control={
                <Switch
                  checked={formData.canManageSettings}
                  onChange={handleChange}
                  name="canManageSettings"
                  color="primary"
                  disabled={saving}
                />
              }
              label="Can Manage Settings"
            />
          </Grid>

          <Grid item xs={12}>
            <Box display="flex" justifyContent="flex-end" gap={2} mt={3}>
              <Button
                variant="outlined"
                onClick={() => navigate(`/stores/view/${storeId}`)}
                disabled={saving}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                disabled={saving}
                startIcon={saving && <CircularProgress size={20} />}
              >
                {isEditMode ? 'Update Admin' : 'Add Admin'}
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Paper>
    </Box>
  );
};

export default StoreAdminFormPage;
