import React, { useEffect, useState } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  Box,
  Paper,
  Typography,
  Tabs,
  Tab,
  Grid,
  TextField,
  FormControlLabel,
  Switch,
  Button,
  CircularProgress,
  Divider,
  Alert
} from '@mui/material';
import { toast } from 'react-toastify';
import { AppDispatch, RootState } from '../../store';
import {
  fetchWebsiteConfigurationByStore,
  updateWebsiteConfiguration,
  updateSectionVisibility
} from '../../store/slices/websiteConfigSlice';
import { WebsiteConfiguration, UpdateWebsiteConfigurationRequest } from '../../services/websiteConfigService';
import PageHeader from '../../components/common/PageHeader';
import StoreSelector from '../../components/common/StoreSelector';
import { ArrowBack, Save } from '@mui/icons-material';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`website-config-tabpanel-${index}`}
      aria-labelledby={`website-config-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `website-config-tab-${index}`,
    'aria-controls': `website-config-tabpanel-${index}`,
  };
}

const StoreWebsiteConfigPage: React.FC = () => {
  const { storeId } = useParams<{ storeId: string }>();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { currentConfiguration, loading, error } = useSelector((state: RootState) => state.websiteConfig);
  const { selectedStore } = useSelector((state: RootState) => state.store);

  const [tabValue, setTabValue] = useState(0);
  const [formData, setFormData] = useState<Partial<UpdateWebsiteConfigurationRequest>>({});

  useEffect(() => {
    if (storeId) {
      dispatch(fetchWebsiteConfigurationByStore(parseInt(storeId)));
    }
  }, [dispatch, storeId]);

  // If selectedStore changes and doesn't match the current storeId, navigate to the new store
  useEffect(() => {
    if (selectedStore && storeId && parseInt(storeId) !== selectedStore.id) {
      navigate(`/stores/website-config/${selectedStore.id}`);
    }
  }, [selectedStore, storeId, navigate]);

  useEffect(() => {
    if (currentConfiguration) {
      setFormData({
        websiteTitle: currentConfiguration.websiteTitle,
        logoUrl: currentConfiguration.logoUrl,
        metaDescription: currentConfiguration.metaDescription,
        metaKeywords: currentConfiguration.metaKeywords,
        announcementText: currentConfiguration.announcementText,
        showAnnouncement: currentConfiguration.showAnnouncement,
        phone: currentConfiguration.phone,
        email: currentConfiguration.email,
        address: currentConfiguration.address,
        facebookUrl: currentConfiguration.facebookUrl,
        instagramUrl: currentConfiguration.instagramUrl,
        twitterUrl: currentConfiguration.twitterUrl,
        whatsappNumber: currentConfiguration.whatsappNumber,
        youtubeUrl: currentConfiguration.youtubeUrl,
        showBannerSection: currentConfiguration.showBannerSection,
        showCategorySection: currentConfiguration.showCategorySection,
        showNewArrivalsSection: currentConfiguration.showNewArrivalsSection,
        showCollectionSection: currentConfiguration.showCollectionSection,
        showBestSellingSection: currentConfiguration.showBestSellingSection,
        bannerTitle: currentConfiguration.bannerTitle,
        bannerSubtitle: currentConfiguration.bannerSubtitle,
        bannerButtonText: currentConfiguration.bannerButtonText,
        bannerButtonLink: currentConfiguration.bannerButtonLink,
        categorySectionTitle: currentConfiguration.categorySectionTitle,
        newArrivalsSectionTitle: currentConfiguration.newArrivalsSectionTitle,
        collectionSectionTitle: currentConfiguration.collectionSectionTitle,
        bestSellingSectionTitle: currentConfiguration.bestSellingSectionTitle
      });
    }
  }, [currentConfiguration]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const handleSaveConfiguration = () => {
    if (!storeId) return;

    dispatch(updateWebsiteConfiguration({
      storeId: parseInt(storeId),
      config: formData as UpdateWebsiteConfigurationRequest
    }))
      .unwrap()
      .then(() => {
        toast.success('Website configuration updated successfully');
      })
      .catch((error) => {
        toast.error(`Failed to update configuration: ${error}`);
      });
  };

  const handleSectionVisibilityChange = (section: string, visible: boolean) => {
    if (!storeId) return;

    const sectionVisibility = {
      [section]: visible
    };

    dispatch(updateSectionVisibility({
      storeId: parseInt(storeId),
      sectionVisibility
    }))
      .unwrap()
      .then(() => {
        toast.success(`Section visibility updated successfully`);
      })
      .catch((error) => {
        toast.error(`Failed to update section visibility: ${error}`);
      });
  };

  const handleGoBack = () => {
    navigate(`/stores/view/${storeId}`);
  };

  if (loading && !currentConfiguration) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <PageHeader
          title={`Website Configuration${currentConfiguration ? ` - ${currentConfiguration.storeName}` : ''}`}
          subtitle="Manage website appearance and content"
          icon={<ArrowBack />}
          onIconClick={handleGoBack}
        />
        <Box sx={{ minWidth: 200 }}>
          <StoreSelector
            showLabel={true}
            variant="outlined"
            size="small"
          />
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ width: '100%', mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab label="General Settings" {...a11yProps(0)} />
          <Tab label="Announcements" {...a11yProps(1)} />
          <Tab label="Home Page Sections" {...a11yProps(2)} />
          <Tab label="Social Media" {...a11yProps(3)} />
          <Tab label="SEO Settings" {...a11yProps(4)} />
        </Tabs>

        {/* General Settings Tab */}
        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                General Information
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Website Title"
                name="websiteTitle"
                value={formData.websiteTitle || ''}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Logo URL"
                name="logoUrl"
                value={formData.logoUrl || ''}
                onChange={handleInputChange}
                helperText="Enter the URL of your website logo"
              />
              <Box sx={{ mt: 1 }}>
                <Typography variant="caption" color="text.secondary" display="block">
                  Recommended dimensions: 400x100px with transparent background (PNG format)
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="h6" gutterBottom>
                Contact Information
              </Typography>
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Email"
                name="email"
                value={formData.email || ''}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Phone"
                name="phone"
                value={formData.phone || ''}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="WhatsApp Number"
                name="whatsappNumber"
                value={formData.whatsappNumber || ''}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Address"
                name="address"
                value={formData.address || ''}
                onChange={handleInputChange}
                multiline
                rows={3}
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* Announcements Tab */}
        <TabPanel value={tabValue} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Announcement Settings
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Configure the announcement bar that appears at the top of all pages
              </Typography>
            </Grid>
            <Grid item xs={12} md={8}>
              <TextField
                fullWidth
                label="Announcement Text"
                name="announcementText"
                value={formData.announcementText || ''}
                onChange={handleInputChange}
                helperText="This text will scroll in the announcement bar at the top of the page"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.showAnnouncement || false}
                    onChange={handleInputChange}
                    name="showAnnouncement"
                  />
                }
                label="Show Announcement"
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* Home Page Sections Tab */}
        <TabPanel value={tabValue} index={2}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Home Page Section Visibility
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Control which sections appear on the home page
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.showBannerSection || false}
                    onChange={(e) => {
                      handleInputChange(e);
                      handleSectionVisibilityChange('showBannerSection', e.target.checked);
                    }}
                    name="showBannerSection"
                  />
                }
                label="Show Banner Section"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.showCategorySection || false}
                    onChange={(e) => {
                      handleInputChange(e);
                      handleSectionVisibilityChange('showCategorySection', e.target.checked);
                    }}
                    name="showCategorySection"
                  />
                }
                label="Show Category Section"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.showNewArrivalsSection || false}
                    onChange={(e) => {
                      handleInputChange(e);
                      handleSectionVisibilityChange('showNewArrivalsSection', e.target.checked);
                    }}
                    name="showNewArrivalsSection"
                  />
                }
                label="Show New Arrivals Section"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.showCollectionSection || false}
                    onChange={(e) => {
                      handleInputChange(e);
                      handleSectionVisibilityChange('showCollectionSection', e.target.checked);
                    }}
                    name="showCollectionSection"
                  />
                }
                label="Show Collection Section"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.showBestSellingSection || false}
                    onChange={(e) => {
                      handleInputChange(e);
                      handleSectionVisibilityChange('showBestSellingSection', e.target.checked);
                    }}
                    name="showBestSellingSection"
                  />
                }
                label="Show Best Selling Section"
              />
            </Grid>

            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="h6" gutterBottom>
                Section Titles
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Category Section Title"
                name="categorySectionTitle"
                value={formData.categorySectionTitle || ''}
                onChange={handleInputChange}
                disabled={!formData.showCategorySection}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="New Arrivals Section Title"
                name="newArrivalsSectionTitle"
                value={formData.newArrivalsSectionTitle || ''}
                onChange={handleInputChange}
                disabled={!formData.showNewArrivalsSection}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Collection Section Title"
                name="collectionSectionTitle"
                value={formData.collectionSectionTitle || ''}
                onChange={handleInputChange}
                disabled={!formData.showCollectionSection}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Best Selling Section Title"
                name="bestSellingSectionTitle"
                value={formData.bestSellingSectionTitle || ''}
                onChange={handleInputChange}
                disabled={!formData.showBestSellingSection}
              />
            </Grid>

            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="h6" gutterBottom>
                Banner Configuration
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Banner Title"
                name="bannerTitle"
                value={formData.bannerTitle || ''}
                onChange={handleInputChange}
                disabled={!formData.showBannerSection}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Banner Subtitle"
                name="bannerSubtitle"
                value={formData.bannerSubtitle || ''}
                onChange={handleInputChange}
                disabled={!formData.showBannerSection}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Banner Button Text"
                name="bannerButtonText"
                value={formData.bannerButtonText || ''}
                onChange={handleInputChange}
                disabled={!formData.showBannerSection}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Banner Button Link"
                name="bannerButtonLink"
                value={formData.bannerButtonLink || ''}
                onChange={handleInputChange}
                disabled={!formData.showBannerSection}
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* Social Media Tab */}
        <TabPanel value={tabValue} index={3}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Social Media Links
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Add your social media profiles to display in the website footer
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Facebook URL"
                name="facebookUrl"
                value={formData.facebookUrl || ''}
                onChange={handleInputChange}
                placeholder="https://facebook.com/yourbusiness"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Instagram URL"
                name="instagramUrl"
                value={formData.instagramUrl || ''}
                onChange={handleInputChange}
                placeholder="https://instagram.com/yourbusiness"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Twitter URL"
                name="twitterUrl"
                value={formData.twitterUrl || ''}
                onChange={handleInputChange}
                placeholder="https://twitter.com/yourbusiness"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="YouTube URL"
                name="youtubeUrl"
                value={formData.youtubeUrl || ''}
                onChange={handleInputChange}
                placeholder="https://youtube.com/channel/yourbusiness"
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* SEO Settings Tab */}
        <TabPanel value={tabValue} index={4}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                SEO Settings
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Optimize your website for search engines
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Meta Description"
                name="metaDescription"
                value={formData.metaDescription || ''}
                onChange={handleInputChange}
                multiline
                rows={3}
                helperText="A brief description of your website (150-160 characters recommended)"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Meta Keywords"
                name="metaKeywords"
                value={formData.metaKeywords || ''}
                onChange={handleInputChange}
                helperText="Comma-separated keywords related to your business"
              />
            </Grid>
          </Grid>
        </TabPanel>
      </Paper>

      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 4 }}>
        <Button
          variant="contained"
          color="primary"
          startIcon={<Save />}
          onClick={handleSaveConfiguration}
          disabled={loading}
        >
          {loading ? <CircularProgress size={24} /> : 'Save Configuration'}
        </Button>
      </Box>
    </Box>
  );
};

export default StoreWebsiteConfigPage;
