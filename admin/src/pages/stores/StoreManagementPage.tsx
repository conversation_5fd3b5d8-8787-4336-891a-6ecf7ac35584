import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  useTheme,
} from '@mui/material';
import { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Visibility as ViewIcon } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { storeService } from '../../services';
import { useSnackbar } from 'notistack';
import ConfirmDialog from '../../components/common/ConfirmDialog';
import { PermissionGuard } from '../../components/common';

interface Store {
  id: number;
  name: string;
  storeKey: string;
  primaryDomain: string;
  logoUrl: string;
  isActive: boolean;
}

const StoreManagementPage: React.FC = () => {
  const [stores, setStores] = useState<Store[]>([]);
  const [loading, setLoading] = useState(true);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [storeToDelete, setStoreToDelete] = useState<Store | null>(null);
  const { enqueueSnackbar } = useSnackbar();
  const navigate = useNavigate();
  const theme = useTheme();

  useEffect(() => {
    fetchStores();
  }, []);

  const fetchStores = async () => {
    try {
      setLoading(true);
      const data = await storeService.getStores();
      setStores(data);
    } catch (error) {
      console.error('Error fetching stores:', error);
      enqueueSnackbar('Failed to load stores', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (id: number) => {
    navigate(`/stores/edit/${id}`);
  };

  const handleView = (id: number) => {
    navigate(`/stores/view/${id}`);
  };

  const handleDelete = (store: Store) => {
    setStoreToDelete(store);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!storeToDelete) return;
    
    try {
      await storeService.deleteStore(storeToDelete.id);
      setStores(stores.filter(store => store.id !== storeToDelete.id));
      enqueueSnackbar('Store deleted successfully', { variant: 'success' });
    } catch (error) {
      console.error('Error deleting store:', error);
      enqueueSnackbar('Failed to delete store. It may have associated data.', { variant: 'error' });
    } finally {
      setDeleteDialogOpen(false);
      setStoreToDelete(null);
    }
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Store Management</Typography>
        <PermissionGuard module="Store" action="Create">
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={() => navigate('/stores/create')}
          >
            Create Store
          </Button>
        </PermissionGuard>
      </Box>

      <Card>
        <CardContent>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Logo</TableCell>
                  <TableCell>Name</TableCell>
                  <TableCell>Store Key</TableCell>
                  <TableCell>Domain</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={6} align="center">
                      Loading...
                    </TableCell>
                  </TableRow>
                ) : stores.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} align="center">
                      No stores found
                    </TableCell>
                  </TableRow>
                ) : (
                  stores.map((store) => (
                    <TableRow key={store.id}>
                      <TableCell>
                        {store.logoUrl ? (
                          <Box
                            component="img"
                            src={store.logoUrl}
                            alt={store.name}
                            sx={{
                              height: '40px',
                              maxWidth: '100px',
                              objectFit: 'contain',
                            }}
                            onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                              e.currentTarget.src = 'https://via.placeholder.com/100x40?text=No+Logo';
                            }}
                          />
                        ) : (
                          <Box
                            sx={{
                              height: '40px',
                              width: '100px',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              bgcolor: 'rgba(0,0,0,0.05)',
                              borderRadius: 1,
                            }}
                          >
                            <Typography variant="caption" color="text.secondary">
                              No Logo
                            </Typography>
                          </Box>
                        )}
                      </TableCell>
                      <TableCell>{store.name}</TableCell>
                      <TableCell>{store.storeKey}</TableCell>
                      <TableCell>{store.primaryDomain}</TableCell>
                      <TableCell>
                        <Chip
                          label={store.isActive ? 'Active' : 'Inactive'}
                          color={store.isActive ? 'success' : 'error'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <PermissionGuard module="Store" action="View">
                          <IconButton
                            color="info"
                            onClick={() => handleView(store.id)}
                            size="small"
                          >
                            <ViewIcon />
                          </IconButton>
                        </PermissionGuard>
                        <PermissionGuard module="Store" action="Edit">
                          <IconButton
                            color="primary"
                            onClick={() => handleEdit(store.id)}
                            size="small"
                          >
                            <EditIcon />
                          </IconButton>
                        </PermissionGuard>
                        <PermissionGuard module="Store" action="Delete">
                          <IconButton
                            color="error"
                            onClick={() => handleDelete(store)}
                            size="small"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </PermissionGuard>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      <ConfirmDialog
        open={deleteDialogOpen}
        title="Delete Store"
        content={`Are you sure you want to delete the store "${storeToDelete?.name}"? This action cannot be undone and will remove all associated data.`}
        onConfirm={confirmDelete}
        onCancel={() => {
          setDeleteDialogOpen(false);
          setStoreToDelete(null);
        }}
      />
    </Box>
  );
};

export default StoreManagementPage;
