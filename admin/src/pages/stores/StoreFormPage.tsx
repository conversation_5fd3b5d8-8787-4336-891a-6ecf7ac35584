import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Divider,
  FormControlLabel,
  IconButton,
  Paper,
  Stack,
  Switch,
  TextField,
  Typography,
  List,
  ListItem,
  ListItemText,
} from '@mui/material';
import { Add as AddIcon, Delete as DeleteIcon, ArrowBack } from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { storeService } from '../../services';
import ImageUpload from '../../components/common/ImageUpload';
import { uploadImage } from '../../services/fileUpload';

interface StoreFormData {
  name: string;
  description: string;
  isActive: boolean;
  primaryDomain: string;
  additionalDomains: string[];
  logoUrl: string;
  faviconUrl: string;
  email: string;
  phone: string;
  address: string;
  facebookUrl: string;
  instagramUrl: string;
  twitterUrl: string;
  whatsappNumber: string;
}

const StoreFormPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const isEditMode = Boolean(id);
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();

  const [loading, setLoading] = useState(isEditMode);
  const [saving, setSaving] = useState(false);
  const [newDomain, setNewDomain] = useState('');
  const [storeKey, setStoreKey] = useState('');
  const [uploadingLogo, setUploadingLogo] = useState(false);
  const [uploadingFavicon, setUploadingFavicon] = useState(false);

  const [formData, setFormData] = useState<StoreFormData>({
    name: '',
    description: '',
    isActive: true,
    primaryDomain: '',
    additionalDomains: [],
    logoUrl: '',
    faviconUrl: '',
    email: '',
    phone: '',
    address: '',
    facebookUrl: '',
    instagramUrl: '',
    twitterUrl: '',
    whatsappNumber: '',
  });

  useEffect(() => {
    if (isEditMode && id) {
      fetchStoreData(parseInt(id));
    }
  }, [id, isEditMode]);

  const fetchStoreData = async (storeId: number) => {
    try {
      setLoading(true);
      const store = await storeService.getStore(storeId);
      setFormData({
        name: store.name || '',
        description: store.description || '',
        isActive: store.isActive,
        primaryDomain: store.primaryDomain || '',
        additionalDomains: store.additionalDomains || [],
        logoUrl: store.logoUrl || '',
        faviconUrl: store.faviconUrl || '',
        email: store.email || '',
        phone: store.phone || '',
        address: store.address || '',
        facebookUrl: store.facebookUrl || '',
        instagramUrl: store.instagramUrl || '',
        twitterUrl: store.twitterUrl || '',
        whatsappNumber: store.whatsappNumber || '',
      });
      setStoreKey(store.storeKey);
    } catch (error) {
      console.error('Error fetching store:', error);
      enqueueSnackbar('Failed to load store data', { variant: 'error' });
      navigate('/stores');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value,
    });
  };

  const handleAddDomain = () => {
    if (newDomain && !formData.additionalDomains.includes(newDomain)) {
      setFormData({
        ...formData,
        additionalDomains: [...formData.additionalDomains, newDomain],
      });
      setNewDomain('');
    }
  };

  const handleRemoveDomain = (domain: string) => {
    setFormData({
      ...formData,
      additionalDomains: formData.additionalDomains.filter((d) => d !== domain),
    });
  };

  const handleLogoUpload = async (file: File) => {
    try {
      setUploadingLogo(true);
      const imageUrl = await uploadImage(file);
      setFormData({
        ...formData,
        logoUrl: imageUrl,
      });
    } catch (error) {
      console.error('Error uploading logo:', error);
      enqueueSnackbar('Failed to upload logo', { variant: 'error' });
    } finally {
      setUploadingLogo(false);
    }
  };

  const handleFaviconUpload = async (file: File) => {
    try {
      setUploadingFavicon(true);
      const imageUrl = await uploadImage(file);
      setFormData({
        ...formData,
        faviconUrl: imageUrl,
      });
    } catch (error) {
      console.error('Error uploading favicon:', error);
      enqueueSnackbar('Failed to upload favicon', { variant: 'error' });
    } finally {
      setUploadingFavicon(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setSaving(true);

      if (isEditMode && id) {
        await storeService.updateStore({
          id: parseInt(id),
          ...formData,
        });
        enqueueSnackbar('Store updated successfully', { variant: 'success' });
      } else {
        await storeService.createStore(formData);
        enqueueSnackbar('Store created successfully', { variant: 'success' });
      }

      navigate('/stores');
    } catch (error) {
      console.error('Error saving store:', error);
      enqueueSnackbar('Failed to save store', { variant: 'error' });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" alignItems="center" mb={3}>
        <IconButton onClick={() => navigate('/stores')} sx={{ mr: 2 }}>
          <ArrowBack />
        </IconButton>
        <Typography variant="h4">
          {isEditMode ? 'Edit Store' : 'Create New Store'}
        </Typography>
      </Box>

      <Paper component="form" onSubmit={handleSubmit} sx={{ p: 3 }}>
        <Stack spacing={3}>
          {/* Basic Information */}
          <Box>
            <Typography variant="h6" gutterBottom>
              Basic Information
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Stack spacing={2}>
              <TextField
                fullWidth
                required
                label="Store Name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                disabled={saving}
              />

              <TextField
                fullWidth
                required
                label="Primary Domain"
                name="primaryDomain"
                value={formData.primaryDomain}
                onChange={handleChange}
                disabled={saving}
                helperText="e.g., mystore.com"
              />

              <TextField
                fullWidth
                multiline
                rows={3}
                label="Description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                disabled={saving}
              />

              {isEditMode && (
                <TextField
                  fullWidth
                  label="Store Key"
                  value={storeKey}
                  disabled
                  helperText="This is a unique identifier for the store and cannot be changed"
                />
              )}

              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isActive}
                    onChange={handleChange}
                    name="isActive"
                    color="primary"
                    disabled={saving}
                  />
                }
                label="Active"
              />
            </Stack>
          </Box>

          {/* Additional Domains */}
          <Box>
            <Typography variant="h6" gutterBottom>
              Additional Domains
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Box display="flex" alignItems="center" mb={2}>
              <TextField
                fullWidth
                label="Add Domain"
                value={newDomain}
                onChange={(e) => setNewDomain(e.target.value)}
                disabled={saving}
                helperText="e.g., store-alternate.com"
                sx={{ mr: 2 }}
              />
              <Button
                variant="contained"
                color="primary"
                onClick={handleAddDomain}
                disabled={!newDomain || saving}
                startIcon={<AddIcon />}
              >
                Add
              </Button>
            </Box>

            <Paper variant="outlined" sx={{ p: 2, minHeight: '100px' }}>
              {formData.additionalDomains.length === 0 ? (
                <Typography color="text.secondary" align="center">
                  No additional domains added
                </Typography>
              ) : (
                <List>
                  {formData.additionalDomains.map((domain, index) => (
                    <ListItem
                      key={index}
                      secondaryAction={
                        <IconButton
                          edge="end"
                          aria-label="delete"
                          onClick={() => handleRemoveDomain(domain)}
                          disabled={saving}
                        >
                          <DeleteIcon />
                        </IconButton>
                      }
                    >
                      <ListItemText primary={domain} />
                    </ListItem>
                  ))}
                </List>
              )}
            </Paper>
          </Box>

          {/* Store Branding */}
          <Box>
            <Typography variant="h6" gutterBottom>
              Store Branding
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Stack direction={{ xs: 'column', md: 'row' }} spacing={2}>
              <Box flex={1}>
                <Typography variant="subtitle1" gutterBottom>
                  Logo
                </Typography>
                <ImageUpload
                  currentImage={formData.logoUrl}
                  onImageUpload={handleLogoUpload}
                  loading={uploadingLogo}
                  label="Upload Store Logo"
                />
              </Box>

              <Box flex={1}>
                <Typography variant="subtitle1" gutterBottom>
                  Favicon
                </Typography>
                <ImageUpload
                  currentImage={formData.faviconUrl}
                  onImageUpload={handleFaviconUpload}
                  loading={uploadingFavicon}
                  label="Upload Store Favicon"
                />
              </Box>
            </Stack>
          </Box>

          {/* Contact Information */}
          <Box>
            <Typography variant="h6" gutterBottom>
              Contact Information
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Stack spacing={2}>
              <Stack direction={{ xs: 'column', md: 'row' }} spacing={2}>
                <TextField
                  fullWidth
                  label="Email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  disabled={saving}
                />

                <TextField
                  fullWidth
                  label="Phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  disabled={saving}
                />
              </Stack>

              <TextField
                fullWidth
                label="Address"
                name="address"
                multiline
                rows={3}
                value={formData.address}
                onChange={handleChange}
                disabled={saving}
              />
            </Stack>
          </Box>

          {/* Social Media */}
          <Box>
            <Typography variant="h6" gutterBottom>
              Social Media
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Stack spacing={2}>
              <Stack direction={{ xs: 'column', md: 'row' }} spacing={2}>
                <TextField
                  fullWidth
                  label="Facebook URL"
                  name="facebookUrl"
                  value={formData.facebookUrl}
                  onChange={handleChange}
                  disabled={saving}
                />

                <TextField
                  fullWidth
                  label="Instagram URL"
                  name="instagramUrl"
                  value={formData.instagramUrl}
                  onChange={handleChange}
                  disabled={saving}
                />
              </Stack>

              <Stack direction={{ xs: 'column', md: 'row' }} spacing={2}>
                <TextField
                  fullWidth
                  label="Twitter URL"
                  name="twitterUrl"
                  value={formData.twitterUrl}
                  onChange={handleChange}
                  disabled={saving}
                />

                <TextField
                  fullWidth
                  label="WhatsApp Number"
                  name="whatsappNumber"
                  value={formData.whatsappNumber}
                  onChange={handleChange}
                  disabled={saving}
                  helperText="Include country code, e.g., +1234567890"
                />
              </Stack>
            </Stack>
          </Box>

          {/* Form Actions */}
          <Box display="flex" justifyContent="flex-end" gap={2} mt={3}>
            <Button
              variant="outlined"
              onClick={() => navigate('/stores')}
              disabled={saving}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="contained"
              color="primary"
              disabled={saving}
              startIcon={saving && <CircularProgress size={20} />}
            >
              {isEditMode ? 'Update Store' : 'Create Store'}
            </Button>
          </Box>
        </Stack>
      </Paper>
    </Box>
  );
};

export default StoreFormPage;
