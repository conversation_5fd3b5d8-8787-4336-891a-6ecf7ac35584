import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Divider,
  Grid,
  IconButton,
  Paper,
  Tab,
  Tabs,
  Typography,
  Chip,
  List,
  ListItem,
  ListItemText,
  Avatar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  ArrowBack,
  Edit as EditIcon,
  Delete as DeleteIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Person as PersonIcon,
  Domain as DomainIcon,
  Settings as SettingsIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
  Facebook as FacebookIcon,
  Instagram as InstagramIcon,
  Twitter as TwitterIcon,
  WhatsApp as WhatsAppIcon,
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { storeService } from '../../services';
import ConfirmDialog from '../../components/common/ConfirmDialog';
import { PermissionGuard } from '../../components/common';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`store-tabpanel-${index}`}
      aria-labelledby={`store-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
}

const StoreDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();

  const [store, setStore] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [tabValue, setTabValue] = useState(0);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [storeAdmins, setStoreAdmins] = useState<any[]>([]);
  const [loadingAdmins, setLoadingAdmins] = useState(false);

  useEffect(() => {
    if (id) {
      fetchStoreData(parseInt(id));
    }
  }, [id]);

  const fetchStoreData = async (storeId: number) => {
    try {
      setLoading(true);
      const storeData = await storeService.getStore(storeId);
      setStore(storeData);

      // Fetch store admins
      fetchStoreAdmins(storeId);
    } catch (error) {
      console.error('Error fetching store:', error);
      enqueueSnackbar('Failed to load store data', { variant: 'error' });
      navigate('/stores');
    } finally {
      setLoading(false);
    }
  };

  const fetchStoreAdmins = async (storeId: number) => {
    try {
      setLoadingAdmins(true);
      const admins = await storeService.getStoreAdmins(storeId);
      setStoreAdmins(admins);
    } catch (error) {
      console.error('Error fetching store admins:', error);
      enqueueSnackbar('Failed to load store admins', { variant: 'error' });
    } finally {
      setLoadingAdmins(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleEdit = () => {
    navigate(`/stores/edit/${id}`);
  };

  const handleDelete = () => {
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!store) return;

    try {
      await storeService.deleteStore(store.id);
      enqueueSnackbar('Store deleted successfully', { variant: 'success' });
      navigate('/stores');
    } catch (error) {
      console.error('Error deleting store:', error);
      enqueueSnackbar('Failed to delete store. It may have associated data.', { variant: 'error' });
    } finally {
      setDeleteDialogOpen(false);
    }
  };

  const handleAddAdmin = () => {
    navigate(`/stores/${id}/admins/add`);
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (!store) {
    return (
      <Box>
        <Typography variant="h5" color="error">
          Store not found
        </Typography>
        <Button
          variant="contained"
          color="primary"
          onClick={() => navigate('/stores')}
          sx={{ mt: 2 }}
        >
          Back to Stores
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" alignItems="center" mb={3}>
        <IconButton onClick={() => navigate('/stores')} sx={{ mr: 2 }}>
          <ArrowBack />
        </IconButton>
        <Typography variant="h4" sx={{ flexGrow: 1 }}>
          Store Details
        </Typography>
        <Box>
          <PermissionGuard module="Store" action="Edit">
            <Button
              variant="outlined"
              color="primary"
              startIcon={<EditIcon />}
              onClick={handleEdit}
              sx={{ mr: 1 }}
            >
              Edit
            </Button>
          </PermissionGuard>
          <PermissionGuard module="Store" action="Delete">
            <Button
              variant="outlined"
              color="error"
              startIcon={<DeleteIcon />}
              onClick={handleDelete}
            >
              Delete
            </Button>
          </PermissionGuard>
        </Box>
      </Box>

      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={2}>
              {store.logoUrl ? (
                <Box
                  component="img"
                  src={store.logoUrl}
                  alt={store.name}
                  sx={{
                    width: '100%',
                    maxHeight: '100px',
                    objectFit: 'contain',
                  }}
                  onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                    e.currentTarget.src = 'https://via.placeholder.com/200x100?text=No+Logo';
                  }}
                />
              ) : (
                <Box
                  sx={{
                    height: '100px',
                    width: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    bgcolor: 'rgba(0,0,0,0.05)',
                    borderRadius: 1,
                  }}
                >
                  <Typography variant="caption" color="text.secondary">
                    No Logo
                  </Typography>
                </Box>
              )}
            </Grid>
            <Grid item xs={12} sm={10}>
              <Box display="flex" alignItems="center" mb={1}>
                <Typography variant="h5" component="h1" sx={{ mr: 2 }}>
                  {store.name}
                </Typography>
                <Chip
                  label={store.isActive ? 'Active' : 'Inactive'}
                  color={store.isActive ? 'success' : 'error'}
                  size="small"
                  icon={store.isActive ? <CheckCircleIcon /> : <CancelIcon />}
                />
              </Box>
              <Typography variant="body1" color="text.secondary" gutterBottom>
                {store.description || 'No description provided'}
              </Typography>
              <Box display="flex" alignItems="center" mt={1}>
                <DomainIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2">
                  {store.primaryDomain || 'No primary domain set'}
                </Typography>
              </Box>
              <Box display="flex" alignItems="center" mt={1}>
                <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                  Store Key:
                </Typography>
                <Chip label={store.storeKey} size="small" />
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab label="General Information" icon={<SettingsIcon />} iconPosition="start" />
          <Tab label="Domains" icon={<DomainIcon />} iconPosition="start" />
          <Tab label="Store Admins" icon={<PersonIcon />} iconPosition="start" />
        </Tabs>

        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Contact Information
                  </Typography>
                  <Divider sx={{ mb: 2 }} />

                  <List>
                    <ListItem>
                      <EmailIcon sx={{ mr: 2, color: 'text.secondary' }} />
                      <ListItemText
                        primary="Email"
                        secondary={store.email || 'Not provided'}
                      />
                    </ListItem>
                    <ListItem>
                      <PhoneIcon sx={{ mr: 2, color: 'text.secondary' }} />
                      <ListItemText
                        primary="Phone"
                        secondary={store.phone || 'Not provided'}
                      />
                    </ListItem>
                    <ListItem>
                      <LocationIcon sx={{ mr: 2, color: 'text.secondary' }} />
                      <ListItemText
                        primary="Address"
                        secondary={store.address || 'Not provided'}
                      />
                    </ListItem>
                  </List>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Social Media
                  </Typography>
                  <Divider sx={{ mb: 2 }} />

                  <List>
                    <ListItem>
                      <FacebookIcon sx={{ mr: 2, color: 'text.secondary' }} />
                      <ListItemText
                        primary="Facebook"
                        secondary={store.facebookUrl || 'Not provided'}
                      />
                    </ListItem>
                    <ListItem>
                      <InstagramIcon sx={{ mr: 2, color: 'text.secondary' }} />
                      <ListItemText
                        primary="Instagram"
                        secondary={store.instagramUrl || 'Not provided'}
                      />
                    </ListItem>
                    <ListItem>
                      <TwitterIcon sx={{ mr: 2, color: 'text.secondary' }} />
                      <ListItemText
                        primary="Twitter"
                        secondary={store.twitterUrl || 'Not provided'}
                      />
                    </ListItem>
                    <ListItem>
                      <WhatsAppIcon sx={{ mr: 2, color: 'text.secondary' }} />
                      <ListItemText
                        primary="WhatsApp"
                        secondary={store.whatsappNumber || 'Not provided'}
                      />
                    </ListItem>
                  </List>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="h6">
                      Store Configuration
                    </Typography>
                    <PermissionGuard module="WebsiteConfiguration" action="Edit">
                      <Button
                        variant="outlined"
                        color="primary"
                        startIcon={<SettingsIcon />}
                        onClick={() => navigate(`/stores/${id}/website-config`)}
                        size="small"
                      >
                        Website Configuration
                      </Button>
                    </PermissionGuard>
                  </Box>
                  <Divider sx={{ mb: 2 }} />

                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6} md={4}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Created At
                      </Typography>
                      <Typography variant="body1">
                        {new Date(store.createdAt).toLocaleString()}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Last Updated
                      </Typography>
                      <Typography variant="body1">
                        {new Date(store.updatedAt).toLocaleString()}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Status
                      </Typography>
                      <Chip
                        label={store.isActive ? 'Active' : 'Inactive'}
                        color={store.isActive ? 'success' : 'error'}
                        size="small"
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Primary Domain
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <Box p={2} bgcolor="rgba(0,0,0,0.03)" borderRadius={1} mb={3}>
                <Typography variant="body1">
                  {store.primaryDomain || 'No primary domain set'}
                </Typography>
              </Box>

              <Typography variant="h6" gutterBottom>
                Additional Domains
              </Typography>
              <Divider sx={{ mb: 2 }} />

              {store.additionalDomains && store.additionalDomains.length > 0 ? (
                <List>
                  {store.additionalDomains.map((domain: string, index: number) => (
                    <ListItem key={index}>
                      <DomainIcon sx={{ mr: 2, color: 'text.secondary' }} />
                      <ListItemText primary={domain} />
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Typography color="text.secondary" align="center" py={3}>
                  No additional domains configured
                </Typography>
              )}
            </CardContent>
          </Card>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Typography variant="h6">
              Store Administrators
            </Typography>
            <PermissionGuard module="StoreAdmin" action="Create">
              <Button
                variant="contained"
                color="primary"
                startIcon={<PersonIcon />}
                onClick={handleAddAdmin}
              >
                Add Admin
              </Button>
            </PermissionGuard>
          </Box>

          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>User</TableCell>
                  <TableCell>Email</TableCell>
                  <TableCell>Permissions</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loadingAdmins ? (
                  <TableRow>
                    <TableCell colSpan={4} align="center">
                      <CircularProgress size={24} />
                    </TableCell>
                  </TableRow>
                ) : storeAdmins.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={4} align="center">
                      No administrators found for this store
                    </TableCell>
                  </TableRow>
                ) : (
                  storeAdmins.map((admin) => (
                    <TableRow key={admin.id}>
                      <TableCell>
                        <Box display="flex" alignItems="center">
                          <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
                            {admin.userName?.charAt(0) || 'U'}
                          </Avatar>
                          <Typography>{admin.userName}</Typography>
                        </Box>
                      </TableCell>
                      <TableCell>{admin.userEmail}</TableCell>
                      <TableCell>
                        <Box display="flex" flexWrap="wrap" gap={0.5}>
                          {admin.canManageProducts && (
                            <Chip label="Products" size="small" color="primary" />
                          )}
                          {admin.canManageOrders && (
                            <Chip label="Orders" size="small" color="primary" />
                          )}
                          {admin.canManageCustomers && (
                            <Chip label="Customers" size="small" color="primary" />
                          )}
                          {admin.canManageSettings && (
                            <Chip label="Settings" size="small" color="primary" />
                          )}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <PermissionGuard module="StoreAdmin" action="Edit">
                          <IconButton
                            color="primary"
                            onClick={() => navigate(`/stores/${id}/admins/edit/${admin.id}`)}
                            size="small"
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </PermissionGuard>
                        <PermissionGuard module="StoreAdmin" action="Delete">
                          <IconButton
                            color="error"
                            // onClick={() => handleDeleteAdmin(admin)}
                            size="small"
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </PermissionGuard>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>
      </Paper>

      <ConfirmDialog
        open={deleteDialogOpen}
        title="Delete Store"
        content={`Are you sure you want to delete the store "${store?.name}"? This action cannot be undone and will remove all associated data.`}
        onConfirm={confirmDelete}
        onCancel={() => setDeleteDialogOpen(false)}
      />
    </Box>
  );
};

export default StoreDetailPage;
