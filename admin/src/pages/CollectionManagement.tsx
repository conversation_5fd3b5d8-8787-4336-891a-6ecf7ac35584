import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import {
  Box,
  Typography,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControlLabel,
  Switch,
  IconButton,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  Menu
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Refresh,
  CheckCircle,
  Cancel,
  ArrowDropDown
} from '@mui/icons-material';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../store';
import {
  fetchCollections,
  createCollection,
  updateCollection,
  deleteCollection,
  toggleCollectionSelection,
  selectAllCollections,
  clearCollectionSelection,
  bulkUpdateCollectionStatus,
  bulkDeleteCollections
} from '../store/slices/collectionSlice';
import ImageUpload from '../components/common/ImageUpload';
import { uploadImage } from '../services/fileUpload';
import StoreSelector from '../components/common/StoreSelector';

// Helper function to check if user is super admin
const isSuperAdmin = (user: any) => {
  return user?.roles?.includes('SuperAdmin');
};

interface CollectionFormData {
  id?: number;
  name: string;
  description: string;
  imageUrl: string;
  isActive: boolean;
  displayOrder: number;
  storeId: number;
}

const CollectionManagement: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { collections, loading, error, selectedCollections } = useSelector((state: RootState) => state.collections);
  const { selectedStore, accessibleStores } = useSelector((state: RootState) => state.store);
  const { user } = useSelector((state: RootState) => state.auth);

  const [openDialog, setOpenDialog] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [formData, setFormData] = useState<CollectionFormData>({
    name: '',
    description: '',
    imageUrl: '',
    isActive: true,
    displayOrder: 0,
    storeId: 1
  });
  const [uploadingImage, setUploadingImage] = useState(false);
  const [bulkActionAnchorEl, setBulkActionAnchorEl] = useState<null | HTMLElement>(null);
  const bulkActionMenuOpen = Boolean(bulkActionAnchorEl);

  useEffect(() => {
    loadCollections();
  }, [dispatch, selectedStore]);

  const loadCollections = () => {
    dispatch(fetchCollections(selectedStore?.id));
  };

  const handleOpenDialog = (isEdit: boolean, collection?: any) => {
    setIsEditMode(isEdit);
    if (isEdit && collection) {
      setFormData({
        id: collection.id,
        name: collection.name,
        description: collection.description,
        imageUrl: collection.imageUrl,
        isActive: collection.isActive,
        displayOrder: collection.displayOrder,
        storeId: collection.storeId
      });
    } else {
      // Use the selected store ID
      const defaultStoreId = selectedStore?.id || 1;

      setFormData({
        name: '',
        description: '',
        imageUrl: '',
        isActive: true,
        displayOrder: collections.length + 1,
        storeId: defaultStoreId
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleSwitchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData({
      ...formData,
      [name]: checked
    });
  };

  const handleSubmit = () => {
    if (isEditMode && formData.id) {
      dispatch(updateCollection({ id: formData.id, collection: formData }))
        .unwrap()
        .then(() => {
          toast.success('Collection updated successfully');
          handleCloseDialog();
        })
        .catch((error) => {
          toast.error(`Failed to update collection: ${error}`);
        });
    } else {
      dispatch(createCollection(formData))
        .unwrap()
        .then(() => {
          toast.success('Collection created successfully');
          handleCloseDialog();
        })
        .catch((error) => {
          toast.error(`Failed to create collection: ${error}`);
        });
    }
  };

  const handleDelete = (id: number) => {
    if (window.confirm('Are you sure you want to delete this collection?')) {
      dispatch(deleteCollection(id))
        .unwrap()
        .then(() => {
          // Image deletion is handled on the server side
          toast.success('Collection deleted successfully');
        })
        .catch((error) => {
          toast.error(`Failed to delete collection: ${error}`);
        });
    }
  };

  const handleImageUpload = async (file: File) => {
    setUploadingImage(true);

    try {
      const imageUrl = await uploadImage(file);
      setFormData(prev => ({
        ...prev,
        imageUrl
      }));
      toast.success('Image uploaded successfully');
    } catch (error) {
      toast.error('Failed to upload image');
      console.error('Error uploading image:', error);
    } finally {
      setUploadingImage(false);
    }
  };

  const handleRefresh = () => {
    loadCollections();
  };

  const isSaleCollection = (name: string) => {
    return name.toLowerCase().includes('sale');
  };

  // Bulk actions
  const handleBulkActionClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setBulkActionAnchorEl(event.currentTarget);
  };

  const handleBulkActionClose = () => {
    setBulkActionAnchorEl(null);
  };

  const handleBulkActivate = () => {
    if (selectedCollections.length === 0) {
      toast.warning('No collections selected');
      return;
    }

    dispatch(bulkUpdateCollectionStatus({ ids: selectedCollections, isActive: true }))
      .unwrap()
      .then(() => {
        toast.success(`${selectedCollections.length} collections activated successfully`);
        handleBulkActionClose();
      })
      .catch((error) => {
        toast.error(`Failed to activate collections: ${error}`);
      });
  };

  const handleBulkDeactivate = () => {
    if (selectedCollections.length === 0) {
      toast.warning('No collections selected');
      return;
    }

    dispatch(bulkUpdateCollectionStatus({ ids: selectedCollections, isActive: false }))
      .unwrap()
      .then(() => {
        toast.success(`${selectedCollections.length} collections deactivated successfully`);
        handleBulkActionClose();
      })
      .catch((error) => {
        toast.error(`Failed to deactivate collections: ${error}`);
      });
  };

  const handleBulkDelete = () => {
    if (selectedCollections.length === 0) {
      toast.warning('No collections selected');
      return;
    }

    if (window.confirm(`Are you sure you want to delete ${selectedCollections.length} collections?`)) {
      dispatch(bulkDeleteCollections(selectedCollections))
        .unwrap()
        .then(() => {
          toast.success(`${selectedCollections.length} collections deleted successfully`);
          handleBulkActionClose();
        })
        .catch((error) => {
          toast.error(`Failed to delete collections: ${error}`);
        });
    }
  };

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      dispatch(selectAllCollections());
    } else {
      dispatch(clearCollectionSelection());
    }
  };

  const handleToggleSelect = (collectionId: number) => {
    dispatch(toggleCollectionSelection(collectionId));
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4">Collection Management</Typography>
        <Box display="flex" alignItems="center">
          <Box sx={{ minWidth: 200, mr: 2 }}>
            <StoreSelector
              showLabel={true}
              variant="outlined"
              size="small"
            />
          </Box>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={handleRefresh}
            sx={{ mr: 1 }}
          >
            Refresh
          </Button>
          {selectedCollections.length > 0 && (
            <Box sx={{ mr: 1 }}>
              <Button
                variant="outlined"
                color="primary"
                onClick={handleBulkActionClick}
                endIcon={<ArrowDropDown />}
              >
                Bulk Actions ({selectedCollections.length})
              </Button>
              <Menu
                anchorEl={bulkActionAnchorEl}
                open={bulkActionMenuOpen}
                onClose={handleBulkActionClose}
              >
                <MenuItem onClick={handleBulkActivate}>
                  <CheckCircle fontSize="small" sx={{ mr: 1 }} />
                  Activate Selected
                </MenuItem>
                <MenuItem onClick={handleBulkDeactivate}>
                  <Cancel fontSize="small" sx={{ mr: 1 }} />
                  Deactivate Selected
                </MenuItem>
                <MenuItem onClick={handleBulkDelete}>
                  <Delete fontSize="small" sx={{ mr: 1 }} />
                  Delete Selected
                </MenuItem>
              </Menu>
            </Box>
          )}
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => handleOpenDialog(false)}
          >
            Add Collection
          </Button>
        </Box>
      </Box>

      {loading ? (
        <Typography>Loading...</Typography>
      ) : error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell padding="checkbox">
                  <Checkbox
                    indeterminate={selectedCollections.length > 0 && selectedCollections.length < collections.length}
                    checked={collections.length > 0 && selectedCollections.length === collections.length}
                    onChange={handleSelectAll}
                  />
                </TableCell>
                <TableCell>ID</TableCell>
                <TableCell>Name</TableCell>
                <TableCell>Description</TableCell>
                <TableCell>Image</TableCell>
                <TableCell>Display Order</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Sale</TableCell>
                {isSuperAdmin(user) && <TableCell>Store</TableCell>}
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {collections.map((collection) => (
                <TableRow
                  key={collection.id}
                  selected={selectedCollections.includes(collection.id)}
                  hover
                >
                  <TableCell padding="checkbox">
                    <Checkbox
                      checked={selectedCollections.includes(collection.id)}
                      onChange={() => handleToggleSelect(collection.id)}
                    />
                  </TableCell>
                  <TableCell>{collection.id}</TableCell>
                  <TableCell>{collection.name}</TableCell>
                  <TableCell>{collection.description.substring(0, 50)}...</TableCell>
                  <TableCell>
                    {collection.imageUrl && (
                      <Box
                        component="img"
                        src={collection.imageUrl}
                        alt={collection.name}
                        sx={{ width: 50, height: 50, objectFit: 'cover' }}
                      />
                    )}
                  </TableCell>
                  <TableCell>{collection.displayOrder}</TableCell>
                  <TableCell>
                    <Chip
                      label={collection.isActive ? 'Active' : 'Inactive'}
                      color={collection.isActive ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    {isSaleCollection(collection.name) && (
                      <Chip
                        label="SALE"
                        color="error"
                        size="small"
                      />
                    )}
                  </TableCell>
                  {isSuperAdmin(user) && (
                    <TableCell>
                      {accessibleStores.find(s => s.id === collection.storeId)?.name || `Store ${collection.storeId}`}
                    </TableCell>
                  )}
                  <TableCell>
                    <IconButton
                      color="primary"
                      onClick={() => handleOpenDialog(true, collection)}
                    >
                      <Edit />
                    </IconButton>
                    <IconButton
                      color="error"
                      onClick={() => handleDelete(collection.id)}
                    >
                      <Delete />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>{isEditMode ? 'Edit Collection' : 'Add Collection'}</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>
            <TextField
              name="name"
              label="Name"
              value={formData.name}
              onChange={handleInputChange}
              fullWidth
              required
              helperText="Add 'Sale' to the name to mark it as a sale collection"
            />
            <TextField
              name="description"
              label="Description"
              value={formData.description}
              onChange={handleInputChange}
              fullWidth
              multiline
              rows={4}
            />
            <Box sx={{ my: 2 }}>
              <Typography variant="subtitle1" gutterBottom>
                Collection Image
              </Typography>
              <ImageUpload
                currentImage={formData.imageUrl}
                onImageUpload={handleImageUpload}
                loading={uploadingImage}
                label="Upload Collection Image"
              />
            </Box>
            <TextField
              name="displayOrder"
              label="Display Order"
              type="number"
              value={formData.displayOrder}
              onChange={handleInputChange}
              fullWidth
            />
            <FormControlLabel
              control={
                <Switch
                  name="isActive"
                  checked={formData.isActive}
                  onChange={handleSwitchChange}
                />
              }
              label="Active"
            />

            {isSuperAdmin(user) && (
              <FormControl fullWidth sx={{ mt: 2 }}>
                <InputLabel id="store-label">Store</InputLabel>
                <Select
                  labelId="store-label"
                  name="storeId"
                  value={formData.storeId}
                  onChange={(e) => {
                    const value = e.target.value;
                    setFormData({
                      ...formData,
                      storeId: Number(value)
                    });
                  }}
                  label="Store"
                  disabled={isEditMode} // Can't change store when editing
                >
                  {accessibleStores.map((store) => (
                    <MenuItem key={store.id} value={store.id}>
                      {store.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSubmit} variant="contained">
            {isEditMode ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CollectionManagement;
