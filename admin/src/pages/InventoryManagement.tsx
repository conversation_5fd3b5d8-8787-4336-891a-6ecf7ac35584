import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Box,
  Typography,
  Button,
  Grid,
  TextField,
  InputAdornment,
  FormControlLabel,
  Checkbox,
  IconButton,
  Tooltip,
  Paper,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent
} from '@mui/material';
import {
  Search as SearchIcon,
  Refresh as RefreshIcon,
  CloudUpload as CloudUploadIcon,
  CloudDownload as CloudDownloadIcon,
  SwapHoriz as SwapHorizIcon
} from '@mui/icons-material';
import { toast } from 'react-toastify';
import { AppDispatch, RootState } from '../store';
import {
  fetchInventory,
  fetchLocations,
  exportInventory,
  InventoryItem
} from '../store/slices/inventorySlice';
import InventoryTable from '../components/admin/InventoryTable';
import InventoryEditForm from '../components/admin/InventoryEditForm';
import InventoryTransferForm from '../components/admin/InventoryTransferForm';
import InventoryImportDialog from '../components/admin/InventoryImportDialog';
import StoreSelector from '../components/common/StoreSelector';

const InventoryManagement: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { loading, error } = useSelector((state: RootState) => state.inventory);
  const { selectedStore, accessibleStores } = useSelector((state: RootState) => state.store);
  const { user } = useSelector((state: RootState) => state.auth);
  const isSuperAdmin = user?.roles.includes('SuperAdmin');

  const [searchTerm, setSearchTerm] = useState('');
  const [lowStockOnly, setLowStockOnly] = useState(false);
  const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [transferDialogOpen, setTransferDialogOpen] = useState(false);
  const [importDialogOpen, setImportDialogOpen] = useState(false);
  useEffect(() => {
    loadInventory();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedStore, dispatch]);

  const loadInventory = () => {
    dispatch(fetchInventory({
      storeId: selectedStore?.id,
      search: searchTerm || undefined,
      lowStock: lowStockOnly || undefined
    }));
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    loadInventory();
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleLowStockChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLowStockOnly(e.target.checked);
    dispatch(fetchInventory({
      storeId: selectedStore?.id,
      search: searchTerm || undefined,
      lowStock: e.target.checked
    }));
  };

  const handleEditItem = (item: InventoryItem) => {
    setSelectedItem(item);
    setEditDialogOpen(true);
  };

  const handleTransferItem = (item: InventoryItem) => {
    setSelectedItem(item);
    setTransferDialogOpen(true);
  };

  const handleExport = () => {
    dispatch(exportInventory(selectedStore?.id))
      .unwrap()
      .then(() => {
        toast.success('Inventory exported successfully');
      })
      .catch((error) => {
        toast.error(`Export failed: ${error}`);
      });
  };

  const handleEditSuccess = () => {
    toast.success('Inventory updated successfully');
    loadInventory();
  };

  const handleTransferSuccess = () => {
    toast.success('Inventory transferred successfully');
    loadInventory();
  };

  const handleImportSuccess = () => {
    loadInventory();
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Inventory Management
        </Typography>
        <Box>
          <Button
            variant="outlined"
            startIcon={<CloudUploadIcon />}
            onClick={() => setImportDialogOpen(true)}
            sx={{ mr: 1 }}
          >
            Import
          </Button>
          <Button
            variant="outlined"
            startIcon={<CloudDownloadIcon />}
            onClick={handleExport}
          >
            Export
          </Button>
        </Box>
      </Box>

      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={3}>
            <StoreSelector
              showLabel={true}
              variant="outlined"
              size="small"
              width="100%"
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <form onSubmit={handleSearch}>
              <TextField
                fullWidth
                size="small"
                placeholder="Search by product name, SKU, or location"
                value={searchTerm}
                onChange={handleSearchChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end">
                      <Tooltip title="Refresh">
                        <IconButton onClick={loadInventory} edge="end">
                          <RefreshIcon />
                        </IconButton>
                      </Tooltip>
                    </InputAdornment>
                  )
                }}
              />
            </form>
          </Grid>

          <Grid item xs={12} md={3}>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={lowStockOnly}
                    onChange={handleLowStockChange}
                  />
                }
                label="Low Stock Only"
              />
              <Tooltip title="Transfer Inventory">
                <IconButton
                  color="primary"
                  onClick={() => setTransferDialogOpen(true)}
                  disabled={!selectedItem}
                >
                  <SwapHorizIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      <InventoryTable
        onEdit={handleEditItem}
        storeId={selectedStore?.id}
        search={searchTerm || undefined}
        lowStock={lowStockOnly || undefined}
      />

      <InventoryEditForm
        open={editDialogOpen}
        onClose={() => setEditDialogOpen(false)}
        item={selectedItem}
        onSuccess={handleEditSuccess}
      />

      <InventoryTransferForm
        open={transferDialogOpen}
        onClose={() => setTransferDialogOpen(false)}
        item={selectedItem}
        onSuccess={handleTransferSuccess}
        storeId={selectedStore?.id}
      />

      <InventoryImportDialog
        open={importDialogOpen}
        onClose={(success) => {
          setImportDialogOpen(false);
          if (success) handleImportSuccess();
        }}
        storeId={selectedStore?.id}
      />
    </Box>
  );
};

export default InventoryManagement;
