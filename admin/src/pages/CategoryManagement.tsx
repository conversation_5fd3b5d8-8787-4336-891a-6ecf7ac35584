import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import {
  Box,
  Typography,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControlLabel,
  Switch,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  IconButton,
  Chip,
  Checkbox,
  Menu
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Refresh,
  CheckCircle,
  Cancel,
  ArrowDropDown
} from '@mui/icons-material';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../store';
import {
  fetchCategories,
  createCategory,
  updateCategory,
  deleteCategory,
  toggleCategorySelection,
  selectAllCategories,
  clearCategorySelection,
  bulkUpdateCategoryStatus,
  bulkDeleteCategories
} from '../store/slices/categorySlice';
import ImageUpload from '../components/common/ImageUpload';
import { uploadImage } from '../services/fileUpload';
import StoreSelector from '../components/common/StoreSelector';

// Helper function to check if user is super admin
const isSuperAdmin = (user: any) => {
  return user?.roles?.includes('SuperAdmin');
};

interface CategoryFormData {
  id?: number;
  name: string;
  description: string;
  imageUrl: string;
  isActive: boolean;
  displayOrder: number;
  parentId: number | null;
  isSale: boolean;
  storeId: number;
}

const CategoryManagement: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { categories, loading, error, selectedCategories } = useSelector((state: RootState) => state.categories);
  const { selectedStore, accessibleStores } = useSelector((state: RootState) => state.store);
  const { user } = useSelector((state: RootState) => state.auth);

  const [openDialog, setOpenDialog] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [formData, setFormData] = useState<CategoryFormData>({
    name: '',
    description: '',
    imageUrl: '',
    isActive: true,
    displayOrder: 0,
    parentId: null,
    isSale: false,
    storeId: 1
  });
  const [uploadingImage, setUploadingImage] = useState(false);
  const [bulkActionAnchorEl, setBulkActionAnchorEl] = useState<null | HTMLElement>(null);
  const bulkActionMenuOpen = Boolean(bulkActionAnchorEl);

  useEffect(() => {
    loadCategories();
  }, [dispatch, selectedStore]);

  const loadCategories = () => {
    dispatch(fetchCategories({ storeId: selectedStore?.id }));
  };

  const handleOpenDialog = (isEdit: boolean, category?: any) => {
    setIsEditMode(isEdit);
    if (isEdit && category) {
      setFormData({
        id: category.id,
        name: category.name,
        description: category.description,
        imageUrl: category.imageUrl,
        isActive: category.isActive,
        displayOrder: category.displayOrder,
        parentId: category.parentId,
        isSale: category.isSale,
        storeId: category.storeId
      });
    } else {
      // Use the selected store ID
      const defaultStoreId = selectedStore?.id || 1;

      setFormData({
        name: '',
        description: '',
        imageUrl: '',
        isActive: true,
        displayOrder: categories.length + 1,
        parentId: null,
        isSale: false,
        storeId: defaultStoreId
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value } = e.target;
    if (name) {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  const handleSwitchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData({
      ...formData,
      [name]: checked
    });
  };

  const handleSubmit = () => {
    if (isEditMode && formData.id) {
      dispatch(updateCategory({ id: formData.id, category: formData }))
        .unwrap()
        .then(() => {
          toast.success('Category updated successfully');
          handleCloseDialog();
        })
        .catch((error) => {
          toast.error(`Failed to update category: ${error}`);
        });
    } else {
      dispatch(createCategory(formData))
        .unwrap()
        .then(() => {
          toast.success('Category created successfully');
          handleCloseDialog();
        })
        .catch((error) => {
          toast.error(`Failed to create category: ${error}`);
        });
    }
  };

  const handleDelete = (id: number) => {
    // Check if the category has children
    const hasChildren = categories.some(cat => cat.parentId === id);

    if (hasChildren) {
      toast.error('Cannot delete a category that has child categories. Please delete or reassign child categories first.');
      return;
    }

    if (window.confirm('Are you sure you want to delete this category?')) {
      dispatch(deleteCategory(id))
        .unwrap()
        .then(() => {
          // Note: Image deletion is handled on the server side
          toast.success('Category deleted successfully');
        })
        .catch((error) => {
          toast.error(`Failed to delete category: ${error}`);
        });
    }
  };

  const handleImageUpload = async (file: File) => {
    setUploadingImage(true);

    try {
      const imageUrl = await uploadImage(file);
      setFormData(prev => ({
        ...prev,
        imageUrl
      }));
      toast.success('Image uploaded successfully');
    } catch (error) {
      toast.error('Failed to upload image');
      console.error('Error uploading image:', error);
    } finally {
      setUploadingImage(false);
    }
  };

  const handleRefresh = () => {
    loadCategories();
  };

  // Bulk actions
  const handleBulkActionClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setBulkActionAnchorEl(event.currentTarget);
  };

  const handleBulkActionClose = () => {
    setBulkActionAnchorEl(null);
  };

  const handleBulkActivate = () => {
    if (selectedCategories.length === 0) {
      toast.warning('No categories selected');
      return;
    }

    dispatch(bulkUpdateCategoryStatus({ ids: selectedCategories, isActive: true }))
      .unwrap()
      .then(() => {
        toast.success(`${selectedCategories.length} categories activated successfully`);
        handleBulkActionClose();
      })
      .catch((error) => {
        toast.error(`Failed to activate categories: ${error}`);
      });
  };

  const handleBulkDeactivate = () => {
    if (selectedCategories.length === 0) {
      toast.warning('No categories selected');
      return;
    }

    dispatch(bulkUpdateCategoryStatus({ ids: selectedCategories, isActive: false }))
      .unwrap()
      .then(() => {
        toast.success(`${selectedCategories.length} categories deactivated successfully`);
        handleBulkActionClose();
      })
      .catch((error) => {
        toast.error(`Failed to deactivate categories: ${error}`);
      });
  };

  const handleBulkDelete = () => {
    if (selectedCategories.length === 0) {
      toast.warning('No categories selected');
      return;
    }

    // Check if any selected category has children
    const hasChildren = categories.some(cat =>
      selectedCategories.includes(cat.id) &&
      categories.some(c => c.parentId === cat.id)
    );

    if (hasChildren) {
      toast.error('Cannot delete categories that have child categories. Please delete or reassign child categories first.');
      return;
    }

    if (window.confirm(`Are you sure you want to delete ${selectedCategories.length} categories?`)) {
      dispatch(bulkDeleteCategories(selectedCategories))
        .unwrap()
        .then(() => {
          toast.success(`${selectedCategories.length} categories deleted successfully`);
          handleBulkActionClose();
        })
        .catch((error) => {
          toast.error(`Failed to delete categories: ${error}`);
        });
    }
  };

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      dispatch(selectAllCategories());
    } else {
      dispatch(clearCategorySelection());
    }
  };

  const handleToggleSelect = (categoryId: number) => {
    dispatch(toggleCategorySelection(categoryId));
  };

  // Get all possible parent categories (excluding the current category being edited)
  const possibleParents = categories.filter(cat => !formData.id || cat.id !== formData.id);

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4">Category Management</Typography>
        <Box display="flex" alignItems="center">
          <Box sx={{ minWidth: 200, mr: 2 }}>
            <StoreSelector
              showLabel={true}
              variant="outlined"
              size="small"
            />
          </Box>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={handleRefresh}
            sx={{ mr: 1 }}
          >
            Refresh
          </Button>
          {selectedCategories.length > 0 && (
            <Box sx={{ mr: 1 }}>
              <Button
                variant="outlined"
                color="primary"
                onClick={handleBulkActionClick}
                endIcon={<ArrowDropDown />}
              >
                Bulk Actions ({selectedCategories.length})
              </Button>
              <Menu
                anchorEl={bulkActionAnchorEl}
                open={bulkActionMenuOpen}
                onClose={handleBulkActionClose}
              >
                <MenuItem onClick={handleBulkActivate}>
                  <CheckCircle fontSize="small" sx={{ mr: 1 }} />
                  Activate Selected
                </MenuItem>
                <MenuItem onClick={handleBulkDeactivate}>
                  <Cancel fontSize="small" sx={{ mr: 1 }} />
                  Deactivate Selected
                </MenuItem>
                <MenuItem onClick={handleBulkDelete}>
                  <Delete fontSize="small" sx={{ mr: 1 }} />
                  Delete Selected
                </MenuItem>
              </Menu>
            </Box>
          )}
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => handleOpenDialog(false)}
          >
            Add Category
          </Button>
        </Box>
      </Box>

      {loading ? (
        <Typography>Loading...</Typography>
      ) : error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell padding="checkbox">
                  <Checkbox
                    indeterminate={selectedCategories.length > 0 && selectedCategories.length < categories.length}
                    checked={categories.length > 0 && selectedCategories.length === categories.length}
                    onChange={handleSelectAll}
                  />
                </TableCell>
                <TableCell>ID</TableCell>
                <TableCell>Name</TableCell>
                <TableCell>Parent</TableCell>
                <TableCell>Description</TableCell>
                <TableCell>Image</TableCell>
                <TableCell>Display Order</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Sale</TableCell>
                {isSuperAdmin(user) && <TableCell>Store</TableCell>}
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {categories.map((category) => (
                <TableRow
                  key={category.id}
                  selected={selectedCategories.includes(category.id)}
                  hover
                >
                  <TableCell padding="checkbox">
                    <Checkbox
                      checked={selectedCategories.includes(category.id)}
                      onChange={() => handleToggleSelect(category.id)}
                    />
                  </TableCell>
                  <TableCell>{category.id}</TableCell>
                  <TableCell>{category.name}</TableCell>
                  <TableCell>
                    {category.parentId ?
                      categories.find(c => c.id === category.parentId)?.name || 'Unknown'
                      : 'None'}
                  </TableCell>
                  <TableCell>{category.description.substring(0, 50)}...</TableCell>
                  <TableCell>
                    {category.imageUrl && (
                      <Box
                        component="img"
                        src={category.imageUrl}
                        alt={category.name}
                        sx={{ width: 50, height: 50, objectFit: 'cover' }}
                      />
                    )}
                  </TableCell>
                  <TableCell>{category.displayOrder}</TableCell>
                  <TableCell>
                    <Chip
                      label={category.isActive ? 'Active' : 'Inactive'}
                      color={category.isActive ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    {category.isSale && (
                      <Chip
                        label="SALE"
                        color="error"
                        size="small"
                      />
                    )}
                  </TableCell>
                  {isSuperAdmin(user) && (
                    <TableCell>
                      {accessibleStores.find(s => s.id === category.storeId)?.name || `Store ${category.storeId}`}
                    </TableCell>
                  )}
                  <TableCell>
                    <IconButton
                      color="primary"
                      onClick={() => handleOpenDialog(true, category)}
                    >
                      <Edit />
                    </IconButton>
                    <IconButton
                      color="error"
                      onClick={() => handleDelete(category.id)}
                    >
                      <Delete />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>{isEditMode ? 'Edit Category' : 'Add Category'}</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>
            <TextField
              name="name"
              label="Name"
              value={formData.name}
              onChange={handleInputChange}
              fullWidth
              required
            />
            <TextField
              name="description"
              label="Description"
              value={formData.description}
              onChange={handleInputChange}
              fullWidth
              multiline
              rows={4}
            />
            <Box sx={{ my: 2 }}>
              <Typography variant="subtitle1" gutterBottom>
                Category Image
              </Typography>
              <ImageUpload
                currentImage={formData.imageUrl}
                onImageUpload={handleImageUpload}
                loading={uploadingImage}
                label="Upload Category Image"
              />
            </Box>
            <TextField
              name="displayOrder"
              label="Display Order"
              type="number"
              value={formData.displayOrder}
              onChange={handleInputChange}
              fullWidth
            />
            <FormControl fullWidth>
              <InputLabel id="parent-category-label">Parent Category</InputLabel>
              <Select
                labelId="parent-category-label"
                name="parentId"
                value={formData.parentId || ''}
                onChange={(e) => {
                  const value = e.target.value;
                  setFormData({
                    ...formData,
                    parentId: value === '' ? null : Number(value)
                  });
                }}
                label="Parent Category"
              >
                <MenuItem value="">
                  <em>None</em>
                </MenuItem>
                {possibleParents
                  .filter(cat => cat.storeId === formData.storeId) // Only show parents from the same store
                  .map((category) => (
                    <MenuItem key={category.id} value={category.id}>
                      {category.name}
                    </MenuItem>
                  ))}
              </Select>
            </FormControl>

            {isSuperAdmin(user) && (
              <FormControl fullWidth sx={{ mt: 2 }}>
                <InputLabel id="store-label">Store</InputLabel>
                <Select
                  labelId="store-label"
                  name="storeId"
                  value={formData.storeId}
                  onChange={(e) => {
                    const value = e.target.value;
                    setFormData({
                      ...formData,
                      storeId: Number(value),
                      // Reset parent if changing store
                      parentId: null
                    });
                  }}
                  label="Store"
                  disabled={isEditMode} // Can't change store when editing
                >
                  {accessibleStores.map((store) => (
                    <MenuItem key={store.id} value={store.id}>
                      {store.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}
            <Box sx={{ display: 'flex', gap: 2 }}>
              <FormControlLabel
                control={
                  <Switch
                    name="isActive"
                    checked={formData.isActive}
                    onChange={handleSwitchChange}
                  />
                }
                label="Active"
              />
              <FormControlLabel
                control={
                  <Switch
                    name="isSale"
                    checked={formData.isSale}
                    onChange={handleSwitchChange}
                  />
                }
                label="Sale Item"
              />
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSubmit} variant="contained">
            {isEditMode ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CategoryManagement;
