import React, { useState, useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import { getValidImageUrl, createImageErrorHandler } from '../utils/imageUtils';
import {
  Box,
  Button,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Chip,
  TextField,
  InputAdornment,
  Grid,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Checkbox,
  FormControlLabel
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Image as ImageIcon,
  CloudUpload as CloudUploadIcon,
  CloudDownload as CloudDownloadIcon,
  Refresh as RefreshIcon,
  Visibility as VisibilityIcon,
  DeleteSweep as DeleteSweepIcon
} from '@mui/icons-material';
import { toast } from 'react-toastify';
import { AppDispatch, RootState } from '../store';
import {
  fetchProducts,
  deleteProduct,
  exportProducts,
  exportProductsTemplate,
  setCurrentPage
} from '../store/slices/productSlice';
import ProductForm from '../components/admin/ProductForm';
import ProductImagesDialog from '../components/admin/ProductImagesDialog';
import ImportProductsDialog from '../components/admin/ImportProductsDialog';
import ConfirmDialog from '../components/common/ConfirmDialog';
import StoreSelector from '../components/common/StoreSelector';

// Use the imported getValidImageUrl function from imageUtils.ts

const ProductManagement: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { products, loading, error, totalCount, totalPages, currentPage } = useSelector((state: RootState) => state.products);
  const { selectedStore, accessibleStores } = useSelector((state: RootState) => state.store);
  const { user } = useSelector((state: RootState) => state.auth);
  const isSuperAdmin = user?.roles.includes('SuperAdmin');

  const [search, setSearch] = useState('');
  const [pageSize, setPageSize] = useState(10);
  const [openForm, setOpenForm] = useState(false);
  const [editingProduct, setEditingProduct] = useState<any>(null);
  const [openImagesDialog, setOpenImagesDialog] = useState(false);
  const [selectedProductForImages, setSelectedProductForImages] = useState<any>(null);
  const [openImportDialog, setOpenImportDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [productToDelete, setProductToDelete] = useState<number | null>(null);
  const [selectedProducts, setSelectedProducts] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [openBulkDeleteDialog, setOpenBulkDeleteDialog] = useState(false);

  const loadProducts = useCallback(() => {
    dispatch(fetchProducts({
      page: currentPage,
      pageSize,
      search: search || undefined,
      isActive: undefined, // Show all products, including inactive ones
      storeId: selectedStore?.id
    }));
  }, [dispatch, currentPage, pageSize, search, selectedStore?.id]);

  useEffect(() => {
    loadProducts();
  }, [loadProducts]);

  useEffect(() => {
    if (error) {
      console.error('Product error in component:', error);
      toast.error(`Error: ${error}`);
    }
  }, [error]);

  const handleChangePage = (_: unknown, newPage: number) => {
    dispatch(setCurrentPage(newPage + 1));
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setPageSize(parseInt(event.target.value, 10));
    dispatch(setCurrentPage(1));
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(event.target.value);
  };

  const handleSearchSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    dispatch(setCurrentPage(1));
    loadProducts();
  };

  const handleAddProduct = () => {
    setEditingProduct(null);
    setOpenForm(true);
  };

  const handleEditProduct = (product: any) => {
    setEditingProduct(product);
    setOpenForm(true);
  };

  const handleCloseForm = () => {
    setOpenForm(false);
    setEditingProduct(null);
  };

  const handleFormSubmitSuccess = () => {
    setOpenForm(false);
    setEditingProduct(null);
    loadProducts();
    toast.success(editingProduct ? 'Product updated successfully' : 'Product created successfully');
  };

  const handleOpenImagesDialog = (product: any) => {
    setSelectedProductForImages(product);
    setOpenImagesDialog(true);
  };

  const handleCloseImagesDialog = () => {
    setOpenImagesDialog(false);
    setSelectedProductForImages(null);
    loadProducts(); // Reload to get updated images
  };

  const handleOpenImportDialog = () => {
    setOpenImportDialog(true);
  };

  const handleCloseImportDialog = (success?: boolean) => {
    setOpenImportDialog(false);
    if (success) {
      loadProducts();
    }
  };

  const handleExportTemplate = () => {
    dispatch(exportProductsTemplate());
  };

  const handleExportProducts = () => {
    // Pass the selected store ID to the export function
    dispatch(exportProducts(selectedStore?.id));
  };

  const handleDeleteClick = (productId: number) => {
    setProductToDelete(productId);
    setOpenDeleteDialog(true);
  };

  const handleDeleteConfirm = async () => {
    if (productToDelete) {
      try {
        await dispatch(deleteProduct(productToDelete)).unwrap();
        toast.success('Product deleted successfully');
        loadProducts();
      } catch (error) {
        toast.error('Failed to delete product');
      }
      setOpenDeleteDialog(false);
      setProductToDelete(null);
    }
  };

  const handleDeleteCancel = () => {
    setOpenDeleteDialog(false);
    setProductToDelete(null);
  };

  // Handle checkbox selection for a single product
  const handleSelectProduct = (productId: number) => {
    setSelectedProducts(prev => {
      if (prev.includes(productId)) {
        return prev.filter(id => id !== productId);
      } else {
        return [...prev, productId];
      }
    });
  };

  // Handle select all checkbox
  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSelectAll(event.target.checked);
    if (event.target.checked) {
      setSelectedProducts(products.map(product => product.id));
    } else {
      setSelectedProducts([]);
    }
  };

  // Handle bulk delete
  const handleBulkDeleteClick = () => {
    if (selectedProducts.length === 0) {
      toast.warning('No products selected');
      return;
    }
    setOpenBulkDeleteDialog(true);
  };

  const handleBulkDeleteConfirm = async () => {
    if (selectedProducts.length === 0) return;

    try {
      // Delete each selected product one by one
      for (const productId of selectedProducts) {
        await dispatch(deleteProduct(productId)).unwrap();
      }

      toast.success(`${selectedProducts.length} products deleted successfully`);
      setSelectedProducts([]);
      setSelectAll(false);
      loadProducts();
    } catch (error) {
      toast.error('Failed to delete some products');
    }

    setOpenBulkDeleteDialog(false);
  };

  const handleBulkDeleteCancel = () => {
    setOpenBulkDeleteDialog(false);
  };

  // Handle refresh grid
  const handleRefreshGrid = () => {
    loadProducts();
    toast.info('Product list refreshed');
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Product Management
        </Typography>
        <Box>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleAddProduct}
            sx={{ mr: 1 }}
          >
            Add Product
          </Button>
          <Button
            variant="outlined"
            startIcon={<CloudUploadIcon />}
            onClick={handleOpenImportDialog}
            sx={{ mr: 1 }}
          >
            Import
          </Button>
          <Button
            variant="outlined"
            startIcon={<CloudDownloadIcon />}
            onClick={handleExportProducts}
            sx={{ mr: 1 }}
          >
            Export
          </Button>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={handleRefreshGrid}
          >
            Refresh
          </Button>
        </Box>
      </Box>

      {/* Store selector and search row */}
      <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Box sx={{ minWidth: 200 }}>
            <StoreSelector
              showLabel={true}
              variant="outlined"
              size="small"
            />
          </Box>
          <form onSubmit={handleSearchSubmit} style={{ flexGrow: 1 }}>
            <TextField
              fullWidth
              variant="outlined"
              placeholder="Search products..."
              value={search}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton type="submit">
                      <SearchIcon />
                    </IconButton>
                    {search && (
                      <IconButton onClick={() => { setSearch(''); dispatch(setCurrentPage(1)); }}>
                        <RefreshIcon />
                      </IconButton>
                    )}
                  </InputAdornment>
                )
              }}
            />
          </form>
        </Box>
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          {selectedProducts.length > 0 && (
            <Button
              variant="outlined"
              color="error"
              startIcon={<DeleteSweepIcon />}
              onClick={handleBulkDeleteClick}
            >
              Delete Selected ({selectedProducts.length})
            </Button>
          )}
          <Button
            variant="outlined"
            startIcon={<CloudDownloadIcon />}
            onClick={handleExportTemplate}
          >
            Download Import Template
          </Button>
        </Box>
      </Box>

      <Paper sx={{ width: '100%', overflow: 'hidden' }}>
        <TableContainer sx={{ maxHeight: 'calc(100vh - 300px)' }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell padding="checkbox">
                  <Checkbox
                    checked={selectAll}
                    onChange={handleSelectAll}
                    indeterminate={selectedProducts.length > 0 && selectedProducts.length < products.length}
                  />
                </TableCell>
                <TableCell>ID</TableCell>
                <TableCell>Image</TableCell>
                <TableCell>Name</TableCell>
                <TableCell>SKU</TableCell>
                <TableCell>Category</TableCell>
                <TableCell>Price</TableCell>
                <TableCell>Stock</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading && products.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={10} align="center">
                    <CircularProgress />
                  </TableCell>
                </TableRow>
              ) : products.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={10} align="center">
                    No products found
                  </TableCell>
                </TableRow>
              ) : (
                products.map((product) => (
                  <TableRow key={product.id}>
                    <TableCell padding="checkbox">
                      <Checkbox
                        checked={selectedProducts.includes(product.id)}
                        onChange={() => handleSelectProduct(product.id)}
                      />
                    </TableCell>
                    <TableCell>{product.id}</TableCell>
                    <TableCell>
                      {product.mainImage || product.imageUrl ? (
                        <Box
                          component="img"
                          src={getValidImageUrl(product.mainImage || product.imageUrl)}
                          alt={product.name}
                          sx={{ width: 50, height: 50, objectFit: 'cover', borderRadius: 1 }}
                          onError={(e) => createImageErrorHandler()(e, product)}
                        />
                      ) : (
                        <Box sx={{ width: 50, height: 50, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: 'grey.200', borderRadius: 1 }}>
                          <ImageIcon color="disabled" />
                        </Box>
                      )}
                    </TableCell>
                    <TableCell>{product.name}</TableCell>
                    <TableCell>{product.variants && product.variants.length > 0 ? product.variants[0].sku : 'N/A'}</TableCell>
                    <TableCell>{product.categoryName}</TableCell>
                    <TableCell>${product.variants && product.variants.length > 0 ? product.variants[0].price.toFixed(2) : '0.00'}</TableCell>
                    <TableCell>{product.variants && product.variants.length > 0 ? product.variants[0].stockQuantity : 0}</TableCell>
                    <TableCell>
                      <Chip
                        label={product.isActive ? 'Active' : 'Inactive'}
                        color={product.isActive ? 'success' : 'default'}
                        size="small"
                      />
                      {product.isFeatured && (
                        <Chip
                          label="Featured"
                          color="primary"
                          size="small"
                          sx={{ ml: 1 }}
                        />
                      )}
                    </TableCell>
                    <TableCell>
                      <Tooltip title="View Details">
                        <IconButton component={Link} to={`/products/${product.id}`} size="small">
                          <VisibilityIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Edit Product">
                        <IconButton onClick={() => handleEditProduct(product)} size="small">
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Manage Images">
                        <IconButton onClick={() => handleOpenImagesDialog(product)} size="small">
                          <ImageIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete Product">
                        <IconButton onClick={() => handleDeleteClick(product.id)} size="small" color="error">
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50]}
          component="div"
          count={totalCount}
          rowsPerPage={pageSize}
          page={currentPage - 1}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Paper>

      {/* Product Form Dialog */}
      <Dialog
        open={openForm}
        onClose={handleCloseForm}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>{editingProduct ? 'Edit Product' : 'Add New Product'}</DialogTitle>
        <DialogContent>
          <ProductForm
            product={editingProduct}
            onSubmitSuccess={handleFormSubmitSuccess}
            onCancel={handleCloseForm}
          />
        </DialogContent>
      </Dialog>

      {/* Product Images Dialog */}
      {selectedProductForImages && (
        <ProductImagesDialog
          open={openImagesDialog}
          onClose={handleCloseImagesDialog}
          product={selectedProductForImages}
        />
      )}

      {/* Import Products Dialog */}
      <ImportProductsDialog
        open={openImportDialog}
        onClose={handleCloseImportDialog}
      />

      {/* Confirm Delete Dialog */}
      <ConfirmDialog
        open={openDeleteDialog}
        title="Delete Product"
        content="Are you sure you want to delete this product? This action cannot be undone."
        onConfirm={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
      />

      {/* Confirm Bulk Delete Dialog */}
      <ConfirmDialog
        open={openBulkDeleteDialog}
        title="Delete Selected Products"
        content={`Are you sure you want to delete ${selectedProducts.length} selected products? This action cannot be undone.`}
        onConfirm={handleBulkDeleteConfirm}
        onCancel={handleBulkDeleteCancel}
      />
    </Box>
  );
};

export default ProductManagement;
