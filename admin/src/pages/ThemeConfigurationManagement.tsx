import React, { useState, useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Box,
  Typography,
  Paper,
  Grid,
  TextField,
  Button,
  Tabs,
  Tab,
  FormControlLabel,
  Switch,
  Divider,
  Card,
  CardContent,
  CardActions,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert,
  Snackbar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  InputAdornment,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Badge,
  Avatar,
  SelectChangeEvent
} from '@mui/material';
import {
  Save as SaveIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Check as CheckIcon,
  Refresh as RefreshIcon,
  ContentCopy as CopyIcon,
  Visibility as VisibilityIcon,
  ColorLens as ColorLensIcon,
  Store as StoreIcon,
  Link as LinkIcon
} from '@mui/icons-material';
import { ChromePicker } from 'react-color';
import { RootState, AppDispatch } from '../store';
import {
  fetchAllThemes,
  createTheme,
  updateTheme,
  activateTheme,
  deleteTheme,
  ThemeConfiguration,
  fetchThemeAssignments,
  assignThemeToStore,
  fetchAvailableStores,
  StoreBasicInfo
} from '../store/slices/themeSlice';
import ThemePreview from '../components/theme/ThemePreview';
import ThemeLivePreview from '../components/theme/ThemeLivePreview';
import { Store } from '../store/slices/storeSlice';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`theme-tabpanel-${index}`}
      aria-labelledby={`theme-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `theme-tab-${index}`,
    'aria-controls': `theme-tabpanel-${index}`,
  };
}

const ThemeConfigurationManagement: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { themes, activeTheme, loading, error } = useSelector((state: RootState) => state.theme);
  const { themeAssignments, availableStores } = useSelector((state: RootState) => state.theme);
  const { stores } = useSelector((state: RootState) => state.store);
  const { user } = useSelector((state: RootState) => state.auth);
  const isSuperAdmin = user?.role === 'SuperAdmin';
  const [isAdmin, setIsAdmin] = useState(true); // Set based on user role

  const [tabValue, setTabValue] = useState(0);
  const [selectedTheme, setSelectedTheme] = useState<ThemeConfiguration | null>(null);
  const [formData, setFormData] = useState<Partial<ThemeConfiguration>>({});
  const [showColorPicker, setShowColorPicker] = useState<string | null>(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error'>('success');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [themeToDelete, setThemeToDelete] = useState<number | null>(null);
  const [createMode, setCreateMode] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [showLivePreview, setShowLivePreview] = useState(false);
  const [assignDialogOpen, setAssignDialogOpen] = useState(false);
  const [selectedStoreId, setSelectedStoreId] = useState<number>(0);
  const [selectedStoreFilter, setSelectedStoreFilter] = useState<number | null>(null);
  const [storeFilter, setStoreFilter] = useState<number | string>('');

  useEffect(() => {
    dispatch(fetchAllThemes());
  }, [dispatch]);

  useEffect(() => {
    if (selectedTheme) {
      setFormData({ ...selectedTheme });
    } else if (themes.length > 0) {
      setSelectedTheme(themes[0]);
      setFormData({ ...themes[0] });
    }
  }, [selectedTheme, themes]);

  useEffect(() => {
    if (isAdmin) {
      dispatch(fetchAvailableStores());
    }
  }, [dispatch, isAdmin]);

  useEffect(() => {
    if (selectedTheme) {
      dispatch(fetchThemeAssignments(selectedTheme.id));
    }
  }, [dispatch, selectedTheme]);

  useEffect(() => {
    if (isSuperAdmin) {
      dispatch(fetchAllStores());
    }
  }, [dispatch, isSuperAdmin]);

  const filteredThemes = useMemo(() => {
    if (!storeFilter) return themes;
    return themes.filter(theme => theme.storeId === storeFilter);
  }, [themes, storeFilter]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value,
    });
  };

  const handleColorChange = (color: any, field: string) => {
    setFormData({
      ...formData,
      [field]: color.hex,
    });
  };

  const handleColorPickerClick = (field: string) => {
    setShowColorPicker(field);
  };

  const handleColorPickerClose = () => {
    setShowColorPicker(null);
  };

  const handleSaveTheme = async () => {
    try {
      if (createMode) {
        await dispatch(createTheme(formData)).unwrap();
        setSnackbarMessage('Theme created successfully');
        setCreateMode(false);
      } else if (selectedTheme) {
        await dispatch(updateTheme({ id: selectedTheme.id, themeData: formData })).unwrap();
        setSnackbarMessage('Theme updated successfully');
      }
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      dispatch(fetchAllThemes());
    } catch (error) {
      setSnackbarMessage('Error saving theme');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    }
  };

  const handleActivateTheme = async (id: number) => {
    try {
      await dispatch(activateTheme(id)).unwrap();
      setSnackbarMessage('Theme activated successfully');
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      dispatch(fetchAllThemes());
    } catch (error) {
      setSnackbarMessage('Error activating theme');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    }
  };

  const handleDeleteClick = (id: number) => {
    setThemeToDelete(id);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (themeToDelete) {
      try {
        await dispatch(deleteTheme(themeToDelete)).unwrap();
        setSnackbarMessage('Theme deleted successfully');
        setSnackbarSeverity('success');
        setSnackbarOpen(true);
        dispatch(fetchAllThemes());
        if (selectedTheme && selectedTheme.id === themeToDelete) {
          setSelectedTheme(null);
          setFormData({});
        }
      } catch (error) {
        setSnackbarMessage('Error deleting theme');
        setSnackbarSeverity('error');
        setSnackbarOpen(true);
      }
    }
    setDeleteDialogOpen(false);
    setThemeToDelete(null);
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setThemeToDelete(null);
  };

  const handleCreateNewTheme = () => {
    setCreateMode(true);
    setSelectedTheme(null);
    setFormData({
      name: 'New Theme',
      description: 'New theme configuration',
      // Set store ID based on user role
      storeId: isSuperAdmin ? (storeFilter || (availableStores[0]?.id || 0)) : user?.storeId,
      isActive: false,
      primaryColor: '#ff3f6c',
      secondaryColor: '#282c3f',
      accentColor: '#03a685',
      // Rest of the properties remain the same
      textPrimaryColor: '#282c3f',
      textSecondaryColor: '#94969f',
      textLightColor: '#ffffff',
      backgroundPrimaryColor: '#ffffff',
      backgroundSecondaryColor: '#f5f5f6',
      backgroundAccentColor: '#fff5f5',
      buttonPrimaryColor: '#ff3f6c',
      buttonSecondaryColor: '#ffffff',
      buttonTextColor: '#ffffff',
      buttonBorderRadius: '4px',
      cardBackgroundColor: '#ffffff',
      cardBorderColor: '#e0e0e0',
      cardBorderRadius: '8px',
      cardShadow: '0 2px 8px rgba(0,0,0,0.1)',
      headingFontFamily: "'Poppins', sans-serif",
      bodyFontFamily: "'Roboto', sans-serif",
      fontBaseSize: '16px',
      spacingUnit: '8px',
      containerMaxWidth: '1200px',
      containerPadding: '16px',
      headerBackgroundColor: '#ffffff',
      headerTextColor: '#282c3f',
      headerHeight: '64px',
      footerBackgroundColor: '#f5f5f6',
      footerTextColor: '#282c3f',
      navLinkColor: '#282c3f',
      navLinkActiveColor: '#ff3f6c',
      navLinkHoverColor: '#ff3f6c',
      linkColor: '#03a685',
      inputBackgroundColor: '#ffffff',
      inputBorderColor: '#d4d5d9',
      inputBorderRadius: '4px',
      inputFocusBorderColor: '#ff3f6c',
      customCSS: '',
    });
  };

  const handleDuplicateTheme = (theme: ThemeConfiguration) => {
    setCreateMode(true);
    setSelectedTheme(null);
    const newTheme = { ...theme };
    delete (newTheme as any).id;
    newTheme.name = `${theme.name} (Copy)`;
    newTheme.isActive = false;
    setFormData(newTheme);
  };

  const handleSelectTheme = (theme: ThemeConfiguration) => {
    setCreateMode(false);
    setSelectedTheme(theme);
    setFormData({ ...theme });
  };

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  const renderColorPicker = (field: string, label: string) => {
    return (
      <Grid item xs={12} sm={6} md={4} lg={3}>
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            {label}
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <TextField
              fullWidth
              name={field}
              value={formData[field as keyof typeof formData] || ''}
              onChange={handleInputChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Box
                      sx={{
                        width: 24,
                        height: 24,
                        borderRadius: '4px',
                        backgroundColor: formData[field as keyof typeof formData] as string || '#fff',
                        border: '1px solid #ddd',
                        cursor: 'pointer',
                      }}
                      onClick={() => handleColorPickerClick(field)}
                    />
                  </InputAdornment>
                ),
              }}
            />
          </Box>
          {showColorPicker === field && (
            <Box sx={{ position: 'absolute', zIndex: 2 }}>
              <Box
                sx={{ position: 'fixed', top: 0, right: 0, bottom: 0, left: 0 }}
                onClick={handleColorPickerClose}
              />
              <ChromePicker
                color={formData[field as keyof typeof formData] as string || '#fff'}
                onChange={(color) => handleColorChange(color, field)}
              />
            </Box>
          )}
        </Box>
      </Grid>
    );
  };

  const handleTogglePreview = () => {
    setShowPreview(!showPreview);
  };

  const handleToggleLivePreview = () => {
    setShowLivePreview(!showLivePreview);
  };

  const handleOpenAssignDialog = () => {
    setAssignDialogOpen(true);
  };

  const handleCloseAssignDialog = () => {
    setAssignDialogOpen(false);
    setSelectedStoreId(0);
  };

  const handleStoreChange = (event: SelectChangeEvent<number>) => {
    setSelectedStoreId(event.target.value as number);
  };

  const handleAssignTheme = async () => {
    if (selectedTheme && selectedStoreId) {
      try {
        await dispatch(assignThemeToStore({ 
          themeId: selectedTheme.id, 
          storeId: selectedStoreId 
        })).unwrap();
        
        setSnackbarMessage('Theme assigned to store successfully');
        setSnackbarSeverity('success');
        setSnackbarOpen(true);
        
        // Refresh assignments
        dispatch(fetchThemeAssignments(selectedTheme.id));
      } catch (error) {
        setSnackbarMessage('Error assigning theme to store');
        setSnackbarSeverity('error');
        setSnackbarOpen(true);
      }
      handleCloseAssignDialog();
    }
  };

  const handleStoreFilterChange = (event: SelectChangeEvent<number | string>) => {
    setStoreFilter(event.target.value);
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Typography variant="h4" gutterBottom>
        Theme Configuration
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Manage your website's theme and styling. Create multiple themes and activate the one you want to use.
      </Typography>

      {/* Add store filter for super admin */}
      {isSuperAdmin && (
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <FormControl fullWidth size="small">
                <InputLabel id="store-filter-label">Filter by Store</InputLabel>
                <Select
                  labelId="store-filter-label"
                  value={storeFilter}
                  onChange={handleStoreFilterChange}
                  label="Filter by Store"
                >
                  <MenuItem value="">
                    <em>All Stores</em>
                  </MenuItem>
                  {availableStores.map((store) => (
                    <MenuItem key={store.id} value={store.id}>
                      {store.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Paper>
      )}

      <Grid container spacing={3}>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Available Themes
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleCreateNewTheme}
              fullWidth
              sx={{ mb: 2 }}
            >
              Create New Theme
            </Button>
            <Divider sx={{ my: 2 }} />
            {loading && <CircularProgress size={24} sx={{ display: 'block', mx: 'auto', my: 2 }} />}
            {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
            {filteredThemes.map((theme) => (
              <Card
                key={theme.id}
                sx={{
                  mb: 2,
                  border: selectedTheme?.id === theme.id ? `2px solid ${theme.primaryColor}` : 'none',
                  cursor: 'pointer',
                }}
                onClick={() => handleSelectTheme(theme)}
              >
                <CardContent sx={{ pb: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                      {theme.name}
                    </Typography>
                    {theme.isActive && (
                      <Chip
                        label="Active"
                        size="small"
                        color="primary"
                        sx={{ backgroundColor: theme.primaryColor }}
                      />
                    )}
                  </Box>
                  <Typography variant="body2" color="text.secondary" noWrap>
                    {theme.description}
                  </Typography>
                  {/* Add store name for super admin */}
                  {isSuperAdmin && (
                    <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                      Store: {availableStores.find(s => s.id === theme.storeId)?.name || 'Unknown'}
                    </Typography>
                  )}
                </CardContent>
                <CardActions>
                  <Tooltip title="Edit">
                    <IconButton size="small" onClick={(e) => { e.stopPropagation(); handleSelectTheme(theme); }}>
                      <EditIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Duplicate">
                    <IconButton size="small" onClick={(e) => { e.stopPropagation(); handleDuplicateTheme(theme); }}>
                      <CopyIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                  {!theme.isActive && (
                    <Tooltip title="Delete">
                      <IconButton
                        size="small"
                        onClick={(e) => { e.stopPropagation(); handleDeleteClick(theme.id); }}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  )}
                  {!theme.isActive && (
                    <Tooltip title="Activate">
                      <IconButton
                        size="small"
                        onClick={(e) => { e.stopPropagation(); handleActivateTheme(theme.id); }}
                        sx={{ ml: 'auto' }}
                      >
                        <CheckIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  )}
                  <Tooltip title="Preview">
                    <IconButton
                      size="small"
                      color="primary"
                      onClick={(e) => { 
                        e.stopPropagation(); 
                        handleSelectTheme(theme);
                        setShowPreview(true);
                      }}
                    >
                      <VisibilityIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </CardActions>
              </Card>
            ))}
          </Paper>
          
          {/* Add Theme Preview Panel */}
          {selectedTheme && showPreview && (
            <Paper sx={{ p: 2, mb: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Theme Preview</Typography>
                <IconButton size="small" onClick={handleTogglePreview}>
                  <VisibilityIcon fontSize="small" />
                </IconButton>
              </Box>
              <ThemePreview 
                theme={selectedTheme} 
                onDelete={handleDeleteClick} 
                showDeleteButton={!selectedTheme.isActive} 
              />
            </Paper>
          )}
        </Grid>

        <Grid item xs={12} md={showLivePreview ? 6 : 9}>
          <Paper sx={{ width: '100%' }}>
            {(selectedTheme || createMode) ? (
              <>
                <Box sx={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  alignItems: 'center', 
                  p: 2, 
                  borderBottom: 1, 
                  borderColor: 'divider' 
                }}>
                  <Tabs value={tabValue} onChange={handleTabChange} aria-label="theme configuration tabs">
                    <Tab label="General" {...a11yProps(0)} />
                    <Tab label="Colors" {...a11yProps(1)} />
                    <Tab label="Typography" {...a11yProps(2)} />
                    <Tab label="Components" {...a11yProps(3)} />
                    <Tab label="Layout" {...a11yProps(4)} />
                    <Tab label="Custom CSS" {...a11yProps(5)} />
                    {isAdmin && <Tab label="Store Assignments" {...a11yProps(6)} />}
                  </Tabs>
                  <Tooltip title="Live Preview">
                    <IconButton onClick={handleToggleLivePreview}>
                      <ColorLensIcon />
                    </IconButton>
                  </Tooltip>
                </Box>

                <TabPanel value={tabValue} index={0}>
                  <Grid container spacing={3}>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="Theme Name"
                        name="name"
                        value={formData.name || ''}
                        onChange={handleInputChange}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.isActive || false}
                            onChange={handleInputChange}
                            name="isActive"
                            disabled={formData.isActive}
                          />
                        }
                        label="Active Theme"
                      />
                    </Grid>
                    
                    {/* Add store selection dropdown for super admins */}
                    {isSuperAdmin && (
                      <Grid item xs={12} sm={6}>
                        <FormControl fullWidth>
                          <InputLabel id="store-select-label">Store</InputLabel>
                          <Select
                            labelId="store-select-label"
                            name="storeId"
                            value={formData.storeId || ''}
                            onChange={(e) => handleInputChange(e as React.ChangeEvent<HTMLInputElement>)}
                            required
                          >
                            {availableStores.map((store) => (
                              <MenuItem key={store.id} value={store.id}>
                                {store.name}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </Grid>
                    )}
                    
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Description"
                        name="description"
                        value={formData.description || ''}
                        onChange={handleInputChange}
                        multiline
                        rows={2}
                      />
                    </Grid>
                  </Grid>
                </TabPanel>

                <TabPanel value={tabValue} index={1}>
                  <Typography variant="h6" gutterBottom>
                    Primary Colors
                  </Typography>
                  <Grid container spacing={3}>
                    {renderColorPicker('primaryColor', 'Primary Color')}
                    {renderColorPicker('secondaryColor', 'Secondary Color')}
                    {renderColorPicker('accentColor', 'Accent Color')}
                  </Grid>

                  <Divider sx={{ my: 3 }} />

                  <Typography variant="h6" gutterBottom>
                    Text Colors
                  </Typography>
                  <Grid container spacing={3}>
                    {renderColorPicker('textPrimaryColor', 'Primary Text')}
                    {renderColorPicker('textSecondaryColor', 'Secondary Text')}
                    {renderColorPicker('textLightColor', 'Light Text')}
                  </Grid>

                  <Divider sx={{ my: 3 }} />

                  <Typography variant="h6" gutterBottom>
                    Background Colors
                  </Typography>
                  <Grid container spacing={3}>
                    {renderColorPicker('backgroundPrimaryColor', 'Primary Background')}
                    {renderColorPicker('backgroundSecondaryColor', 'Secondary Background')}
                    {renderColorPicker('backgroundAccentColor', 'Accent Background')}
                  </Grid>
                </TabPanel>

                <TabPanel value={tabValue} index={2}>
                  <Typography variant="h6" gutterBottom>
                    Typography Settings
                  </Typography>
                  <Grid container spacing={3}>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="Heading Font Family"
                        name="headingFontFamily"
                        value={formData.headingFontFamily || ''}
                        onChange={handleInputChange}
                        helperText="Example: 'Poppins', sans-serif"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="Body Font Family"
                        name="bodyFontFamily"
                        value={formData.bodyFontFamily || ''}
                        onChange={handleInputChange}
                        helperText="Example: 'Roboto', sans-serif"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="Base Font Size"
                        name="fontBaseSize"
                        value={formData.fontBaseSize || ''}
                        onChange={handleInputChange}
                        helperText="Example: 16px"
                      />
                    </Grid>
                  </Grid>
                </TabPanel>

                <TabPanel value={tabValue} index={3}>
                  <Typography variant="h6" gutterBottom>
                    Button Styles
                  </Typography>
                  <Grid container spacing={3}>
                    {renderColorPicker('buttonPrimaryColor', 'Button Primary Color')}
                    {renderColorPicker('buttonSecondaryColor', 'Button Secondary Color')}
                    {renderColorPicker('buttonTextColor', 'Button Text Color')}
                    <Grid item xs={12} sm={6} md={4}>
                      <TextField
                        fullWidth
                        label="Button Border Radius"
                        name="buttonBorderRadius"
                        value={formData.buttonBorderRadius || ''}
                        onChange={handleInputChange}
                        helperText="Example: 4px"
                      />
                    </Grid>
                  </Grid>

                  <Divider sx={{ my: 3 }} />

                  <Typography variant="h6" gutterBottom>
                    Card Styles
                  </Typography>
                  <Grid container spacing={3}>
                    {renderColorPicker('cardBackgroundColor', 'Card Background')}
                    {renderColorPicker('cardBorderColor', 'Card Border')}
                    <Grid item xs={12} sm={6} md={4}>
                      <TextField
                        fullWidth
                        label="Card Border Radius"
                        name="cardBorderRadius"
                        value={formData.cardBorderRadius || ''}
                        onChange={handleInputChange}
                        helperText="Example: 8px"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                      <TextField
                        fullWidth
                        label="Card Shadow"
                        name="cardShadow"
                        value={formData.cardShadow || ''}
                        onChange={handleInputChange}
                        helperText="Example: 0 2px 8px rgba(0,0,0,0.1)"
                      />
                    </Grid>
                  </Grid>

                  <Divider sx={{ my: 3 }} />

                  <Typography variant="h6" gutterBottom>
                    Form Styles
                  </Typography>
                  <Grid container spacing={3}>
                    {renderColorPicker('inputBackgroundColor', 'Input Background')}
                    {renderColorPicker('inputBorderColor', 'Input Border')}
                    {renderColorPicker('inputFocusBorderColor', 'Input Focus Border')}
                    <Grid item xs={12} sm={6} md={4}>
                      <TextField
                        fullWidth
                        label="Input Border Radius"
                        name="inputBorderRadius"
                        value={formData.inputBorderRadius || ''}
                        onChange={handleInputChange}
                        helperText="Example: 4px"
                      />
                    </Grid>
                  </Grid>
                </TabPanel>

                <TabPanel value={tabValue} index={4}>
                  <Typography variant="h6" gutterBottom>
                    Spacing & Layout
                  </Typography>
                  <Grid container spacing={3}>
                    <Grid item xs={12} sm={6} md={4}>
                      <TextField
                        fullWidth
                        label="Spacing Unit"
                        name="spacingUnit"
                        value={formData.spacingUnit || ''}
                        onChange={handleInputChange}
                        helperText="Example: 8px"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                      <TextField
                        fullWidth
                        label="Container Max Width"
                        name="containerMaxWidth"
                        value={formData.containerMaxWidth || ''}
                        onChange={handleInputChange}
                        helperText="Example: 1200px"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                      <TextField
                        fullWidth
                        label="Container Padding"
                        name="containerPadding"
                        value={formData.containerPadding || ''}
                        onChange={handleInputChange}
                        helperText="Example: 16px"
                      />
                    </Grid>
                  </Grid>

                  <Divider sx={{ my: 3 }} />

                  <Typography variant="h6" gutterBottom>
                    Header Styles
                  </Typography>
                  <Grid container spacing={3}>
                    {renderColorPicker('headerBackgroundColor', 'Header Background')}
                    {renderColorPicker('headerTextColor', 'Header Text')}
                    <Grid item xs={12} sm={6} md={4}>
                      <TextField
                        fullWidth
                        label="Header Height"
                        name="headerHeight"
                        value={formData.headerHeight || ''}
                        onChange={handleInputChange}
                        helperText="Example: 64px"
                      />
                    </Grid>
                  </Grid>

                  <Divider sx={{ my: 3 }} />

                  <Typography variant="h6" gutterBottom>
                    Footer Styles
                  </Typography>
                  <Grid container spacing={3}>
                    {renderColorPicker('footerBackgroundColor', 'Footer Background')}
                    {renderColorPicker('footerTextColor', 'Footer Text')}
                  </Grid>

                  <Divider sx={{ my: 3 }} />

                  <Typography variant="h6" gutterBottom>
                    Navigation Styles
                  </Typography>
                  <Grid container spacing={3}>
                    {renderColorPicker('navLinkColor', 'Nav Link Color')}
                    {renderColorPicker('navLinkActiveColor', 'Nav Link Active')}
                    {renderColorPicker('navLinkHoverColor', 'Nav Link Hover')}
                    {renderColorPicker('linkColor', 'Link Color')}
                  </Grid>
                </TabPanel>

                <TabPanel value={tabValue} index={5}>
                  <Typography variant="h6" gutterBottom>
                    Custom CSS
                  </Typography>
                  <Typography variant="body2" color="text.secondary" paragraph>
                    Add custom CSS to override or extend the theme styles. This CSS will be applied globally.
                  </Typography>
                  <TextField
                    fullWidth
                    name="customCSS"
                    value={formData.customCSS || ''}
                    onChange={handleInputChange}
                    multiline
                    rows={10}
                    placeholder="/* Add your custom CSS here */\n\n.my-custom-class {\n  color: red;\n}"
                  />
                </TabPanel>

                <TabPanel value={tabValue} index={6}>
                  <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="h6">
                      Store Assignments
                      <Typography variant="body2" color="text.secondary">
                        Assign this theme to stores to make it available for them to use
                      </Typography>
                    </Typography>
                    <Button
                      variant="contained"
                      startIcon={<StoreIcon />}
                      onClick={handleOpenAssignDialog}
                      disabled={!selectedTheme}
                    >
                      Assign to Store
                    </Button>
                  </Box>
                  
                  {themeAssignments.length > 0 ? (
                    <List>
                      {themeAssignments.map((assignment) => (
                        <ListItem
                          key={assignment.storeId}
                          divider
                          secondaryAction={
                            assignment.isActive && (
                              <Chip 
                                label="Active" 
                                color="primary" 
                                size="small"
                                sx={{ bgcolor: selectedTheme?.primaryColor }}
                              />
                            )
                          }
                        >
                          <ListItemText
                            primary={assignment.storeName}
                            secondary={`Assigned: ${new Date(assignment.assignedAt).toLocaleDateString()}`}
                          />
                        </ListItem>
                      ))}
                    </List>
                  ) : (
                    <Alert severity="info">
                      This theme is not assigned to any stores yet.
                    </Alert>
                  )}
                </TabPanel>

                <Box sx={{ p: 3, display: 'flex', justifyContent: 'flex-end' }}>
                  <Button
                    variant="contained"
                    startIcon={<SaveIcon />}
                    onClick={handleSaveTheme}
                    disabled={loading}
                  >
                    {createMode ? 'Create Theme' : 'Save Changes'}
                  </Button>
                </Box>
              </>
            ) : (
              <Box sx={{ p: 4, textAlign: 'center' }}>
                <Typography variant="h6" gutterBottom>
                  No theme selected
                </Typography>
                <Typography variant="body1" color="text.secondary" paragraph>
                  Select a theme from the list or create a new one to start editing.
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleCreateNewTheme}
                >
                  Create New Theme
                </Button>
              </Box>
            )}
          </Paper>
        </Grid>

        {/* Add Live Preview Panel */}
        {selectedTheme && showLivePreview && (
          <Grid item xs={12} md={3}>
            <Paper sx={{ height: '100%' }}>
              <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center', borderBottom: 1, borderColor: 'divider' }}>
                <Typography variant="h6">Live Preview</Typography>
                <IconButton size="small" onClick={handleToggleLivePreview}>
                  <VisibilityIcon fontSize="small" />
                </IconButton>
              </Box>
              <Box sx={{ height: 'calc(100% - 56px)' }}>
                <ThemeLivePreview 
                  theme={formData} 
                  onDelete={handleDeleteClick} 
                  showDeleteButton={selectedTheme && !selectedTheme.isActive} 
                />
              </Box>
            </Paper>
          </Grid>
        )}
      </Grid>

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbarSeverity} sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>

      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
      >
        <DialogTitle>Delete Theme</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this theme? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel}>Cancel</Button>
          <Button onClick={handleDeleteConfirm} color="error">Delete</Button>
        </DialogActions>
      </Dialog>

      <Dialog open={assignDialogOpen} onClose={handleCloseAssignDialog}>
        <DialogTitle>Assign Theme to Store</DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ mb: 2 }}>
            Select a store to assign this theme to. The store will be able to activate and use this theme.
          </DialogContentText>
          <FormControl fullWidth>
            <InputLabel id="store-select-label">Store</InputLabel>
            <Select
              labelId="store-select-label"
              value={selectedStoreId}
              label="Store"
              onChange={handleStoreChange}
            >
              <MenuItem value={0} disabled>Select a store</MenuItem>
              {availableStores.map((store) => (
                <MenuItem key={store.id} value={store.id}>
                  {store.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseAssignDialog}>Cancel</Button>
          <Button 
            onClick={handleAssignTheme} 
            variant="contained" 
            disabled={!selectedStoreId}
          >
            Assign
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ThemeConfigurationManagement;
