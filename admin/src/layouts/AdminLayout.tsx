import React, { useState, useEffect } from 'react';
import { Outlet, Link, useNavigate, useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  AppBar,
  Toolbar,
  Typography,
  Box,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  IconButton,
  Container,
  CssBaseline,
  Menu,
  MenuItem,
  Chip
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard,
  Category,
  Collections,
  ShoppingBag,
  LocalShipping,
  People,
  Settings,
  ExitToApp,
  Person,
  Inventory,
  Menu as MenuList,
  LocalOffer,
  Web,
  Image as ImageIcon,
  Palette,
  Security,
  AdminPanelSettings,
  VpnKey
} from '@mui/icons-material';
import { RootState } from '../store';
import { logout } from '../store/slices/authSlice';
import StoreSelector from '../components/common/StoreSelector';

const drawerWidth = 240;

const AdminLayout: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useSelector((state: RootState) => state.auth);

  const [mobileOpen, setMobileOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [currentMenuName, setCurrentMenuName] = useState<string>('Dashboard');

  // Update current menu name based on URL path
  useEffect(() => {
    const path = location.pathname;

    if (path === '/') {
      setCurrentMenuName('Dashboard');
    } else if (path.includes('/products')) {
      setCurrentMenuName('Products');
    } else if (path.includes('/categories')) {
      setCurrentMenuName('Categories');
    } else if (path.includes('/collections')) {
      setCurrentMenuName('Collections');
    } else if (path.includes('/orders')) {
      setCurrentMenuName('Orders');
    } else if (path.includes('/inventory')) {
      setCurrentMenuName('Inventory');
    } else if (path.includes('/navigation')) {
      setCurrentMenuName('Navigation Menus');
    } else if (path.includes('/users/roles')) {
      setCurrentMenuName('User Roles');
    } else if (path.includes('/users/permissions')) {
      setCurrentMenuName('User Module Permissions');
    } else if (path.includes('/users')) {
      setCurrentMenuName('Users');
    } else if (path.includes('/roles')) {
      setCurrentMenuName('Roles & Permissions');
    } else if (path.includes('/stores')) {
      setCurrentMenuName('Store Management');
    } else if (path.includes('/coupons')) {
      setCurrentMenuName('Coupons');
    } else if (path.includes('/website-configuration')) {
      setCurrentMenuName('Website Configuration');
    } else if (path.includes('/theme-configuration')) {
      setCurrentMenuName('Theme Configuration');
    } else if (path.includes('/images')) {
      setCurrentMenuName('Image Gallery');
    } else if (path.includes('/settings')) {
      setCurrentMenuName('Settings');
    }
  }, [location.pathname]);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    // @ts-ignore
    dispatch(logout());
    navigate('/login');
    handleMenuClose();
  };

  // Check if user is admin
  if (!user || !user.roles.includes('Admin')) {
    navigate('/login');
    return null;
  }

  const drawer = (
    <div>
      <Toolbar sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 2 }}>
        <Typography variant="h6" noWrap component="div" sx={{ mb: 1 }}>
          MyShop
        </Typography>
        <Typography variant="subtitle2" color="text.secondary">
          Admin Panel
        </Typography>
      </Toolbar>
      <Divider />
      <List>
        <ListItem
          component={Link}
          to="/"
          onClick={() => setCurrentMenuName('Dashboard')}
        >
          <ListItemIcon>
            <Dashboard />
          </ListItemIcon>
          <ListItemText primary="Dashboard" />
        </ListItem>
        <ListItem
          component={Link}
          to="/products"
          onClick={() => setCurrentMenuName('Products')}
        >
          <ListItemIcon>
            <ShoppingBag />
          </ListItemIcon>
          <ListItemText primary="Products" />
        </ListItem>
        <ListItem
          component={Link}
          to="/categories"
          onClick={() => setCurrentMenuName('Categories')}
        >
          <ListItemIcon>
            <Category />
          </ListItemIcon>
          <ListItemText primary="Categories" />
        </ListItem>
        <ListItem
          component={Link}
          to="/collections"
          onClick={() => setCurrentMenuName('Collections')}
        >
          <ListItemIcon>
            <Collections />
          </ListItemIcon>
          <ListItemText primary="Collections" />
        </ListItem>
        <ListItem
          component={Link}
          to="/orders"
          onClick={() => setCurrentMenuName('Orders')}
        >
          <ListItemIcon>
            <LocalShipping />
          </ListItemIcon>
          <ListItemText primary="Orders" />
        </ListItem>
        <ListItem
          component={Link}
          to="/inventory"
          onClick={() => setCurrentMenuName('Inventory')}
        >
          <ListItemIcon>
            <Inventory />
          </ListItemIcon>
          <ListItemText primary="Inventory" />
        </ListItem>
        <ListItem
          component={Link}
          to="/navigation"
          onClick={() => setCurrentMenuName('Navigation Menus')}
        >
          <ListItemIcon>
            <MenuList />
          </ListItemIcon>
          <ListItemText primary="Navigation Menus" />
        </ListItem>
        <ListItem
          component={Link}
          to="/users"
          onClick={() => setCurrentMenuName('Users')}
        >
          <ListItemIcon>
            <People />
          </ListItemIcon>
          <ListItemText primary="Users" />
        </ListItem>
        <ListItem
          component={Link}
          to="/users/roles"
          onClick={() => setCurrentMenuName('User Roles')}
        >
          <ListItemIcon>
            <AdminPanelSettings />
          </ListItemIcon>
          <ListItemText primary="User Roles" />
        </ListItem>
        <ListItem
          component={Link}
          to="/users/permissions"
          onClick={() => setCurrentMenuName('User Module Permissions')}
        >
          <ListItemIcon>
            <Security />
          </ListItemIcon>
          <ListItemText primary="User Permissions" />
        </ListItem>
        <ListItem
          component={Link}
          to="/roles"
          onClick={() => setCurrentMenuName('Roles & Permissions')}
        >
          <ListItemIcon>
            <VpnKey />
          </ListItemIcon>
          <ListItemText primary="Roles & Permissions" />
        </ListItem>
        {user && user.roles.includes('SuperAdmin') && (
          <ListItem
            component={Link}
            to="/stores"
            onClick={() => setCurrentMenuName('Store Management')}
          >
            <ListItemIcon>
              <Security />
            </ListItemIcon>
            <ListItemText primary="Store Management" />
          </ListItem>
        )}
        <ListItem
          component={Link}
          to="/coupons"
          onClick={() => setCurrentMenuName('Coupons')}
        >
          <ListItemIcon>
            <LocalOffer />
          </ListItemIcon>
          <ListItemText primary="Coupons" />
        </ListItem>
        <ListItem
          component={Link}
          to="/website-configuration"
          onClick={() => setCurrentMenuName('Website Configuration')}
        >
          <ListItemIcon>
            <Web />
          </ListItemIcon>
          <ListItemText primary="Website Configuration" />
        </ListItem>
        <ListItem
          component={Link}
          to="/theme-configuration"
          onClick={() => setCurrentMenuName('Theme Configuration')}
        >
          <ListItemIcon>
            <Palette />
          </ListItemIcon>
          <ListItemText primary="Theme Configuration" />
        </ListItem>
        <ListItem
          component={Link}
          to="/images"
          onClick={() => setCurrentMenuName('Image Gallery')}
        >
          <ListItemIcon>
            <ImageIcon />
          </ListItemIcon>
          <ListItemText primary="Image Gallery" />
        </ListItem>
        <ListItem
          component={Link}
          to="/settings"
          onClick={() => setCurrentMenuName('Settings')}
        >
          <ListItemIcon>
            <Settings />
          </ListItemIcon>
          <ListItemText primary="Settings" />
        </ListItem>
      </List>
      <Divider />
      <List>
        <ListItem
          component="a"
          href="/"
          onClick={() => window.location.href = '/'}
        >
          <ListItemIcon>
            <ExitToApp />
          </ListItemIcon>
          <ListItemText primary="Back to Shop" />
        </ListItem>
      </List>
    </div>
  );

  return (
    <Box sx={{ display: 'flex', height: '100vh', width: '100%', overflow: 'hidden' }}>
      <CssBaseline />
      <AppBar
        position="fixed"
        sx={{
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          ml: { sm: `${drawerWidth}px` },
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { sm: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
            <Box sx={{
              ml: 2,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'flex-start'
            }}>
              <Typography variant="caption" color="rgba(255,255,255,0.7)">
                Admin Panel
              </Typography>
              <Typography variant="subtitle1" sx={{ fontWeight: 'bold', lineHeight: 1.2 }}>
                {currentMenuName}
              </Typography>
            </Box>

            {/* Store selector is now hidden in the header for all users */}
          </Box>
          <IconButton
            edge="end"
            aria-label="account of current user"
            aria-controls="menu-appbar"
            aria-haspopup="true"
            onClick={handleProfileMenuOpen}
            color="inherit"
          >
            <Person />
          </IconButton>
          <Menu
            id="menu-appbar"
            anchorEl={anchorEl}
            anchorOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            keepMounted
            transformOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
          >
            <MenuItem onClick={handleMenuClose}>Profile</MenuItem>
            <MenuItem onClick={handleLogout}>Logout</MenuItem>
          </Menu>
        </Toolbar>
      </AppBar>
      <Box
        component="nav"
        sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
        aria-label="mailbox folders"
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true, // Better open performance on mobile.
          }}
          sx={{
            display: { xs: 'block', sm: 'none' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', sm: 'block' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          height: '100vh',
          overflow: 'auto',
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        <Toolbar />
        <Container maxWidth="lg" sx={{ flexGrow: 1 }}>
          <Outlet />
        </Container>
      </Box>
    </Box>
  );
};

export default AdminLayout;
