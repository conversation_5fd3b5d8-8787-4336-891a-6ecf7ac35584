import React from 'react';
import { Box, Typography, Paper, Grid, Card, CardContent, Button, TextField, AppBar, Toolbar, IconButton, Tooltip } from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import { ThemeConfiguration } from '../../store/slices/themeSlice';

interface ThemeLivePreviewProps {
  theme: Partial<ThemeConfiguration>;
  onDelete?: (id: number) => void;
  showDeleteButton?: boolean;
}

const ThemeLivePreview: React.FC<ThemeLivePreviewProps> = ({ 
  theme, 
  onDelete, 
  showDeleteButton = false 
}) => {
  return (
    <Paper sx={{ overflow: 'hidden', height: '100%', display: 'flex', flexDirection: 'column', position: 'relative' }}>
      {showDeleteButton && onDelete && theme.id && (
        <Tooltip title="Delete Theme">
          <IconButton 
            size="small" 
            sx={{ position: 'absolute', top: 8, right: 8, zIndex: 10 }}
            onClick={() => onDelete(theme.id!)}
          >
            <DeleteIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      )}
      
      {/* Header */}
      <AppBar position="static" sx={{ 
        bgcolor: theme.headerBackgroundColor || '#ffffff',
        color: theme.headerTextColor || '#282c3f',
        boxShadow: 'none',
        borderBottom: `1px solid ${theme.cardBorderColor || '#e0e0e0'}`,
        height: theme.headerHeight || '64px',
      }}>
        <Toolbar>
          <Typography variant="h6" sx={{ 
            fontFamily: theme.headingFontFamily || "'Poppins', sans-serif",
            fontWeight: 600,
          }}>
            Store Name
          </Typography>
          <Box sx={{ flexGrow: 1 }} />
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Typography sx={{ 
              color: theme.navLinkColor || '#282c3f',
              '&:hover': { color: theme.navLinkHoverColor || '#ff3f6c' },
              cursor: 'pointer',
              fontFamily: theme.bodyFontFamily || "'Roboto', sans-serif",
            }}>
              Home
            </Typography>
            <Typography sx={{ 
              color: theme.navLinkActiveColor || '#ff3f6c',
              cursor: 'pointer',
              fontFamily: theme.bodyFontFamily || "'Roboto', sans-serif",
            }}>
              Products
            </Typography>
            <Typography sx={{ 
              color: theme.navLinkColor || '#282c3f',
              '&:hover': { color: theme.navLinkHoverColor || '#ff3f6c' },
              cursor: 'pointer',
              fontFamily: theme.bodyFontFamily || "'Roboto', sans-serif",
            }}>
              About
            </Typography>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Main Content */}
      <Box sx={{ 
        flex: 1, 
        overflow: 'auto', 
        p: 3, 
        bgcolor: theme.backgroundPrimaryColor || '#f5f5f5' 
      }}>
        {/* Hero Section */}
        <Box sx={{ 
          p: 4, 
          mb: 4, 
          borderRadius: theme.cardBorderRadius || '8px',
          bgcolor: theme.backgroundSecondaryColor || '#ffffff',
          boxShadow: theme.cardShadow || '0 2px 8px rgba(0,0,0,0.1)',
        }}>
          <Typography variant="h4" gutterBottom sx={{ 
            color: theme.textPrimaryColor || '#282c3f',
            fontFamily: theme.headingFontFamily || "'Poppins', sans-serif",
            fontWeight: 600,
          }}>
            Welcome to our store
          </Typography>
          <Typography variant="body1" sx={{ 
            color: theme.textSecondaryColor || '#666666',
            fontFamily: theme.bodyFontFamily || "'Roboto', sans-serif",
            mb: 2,
          }}>
            Discover our amazing products with the best quality and prices.
          </Typography>
          <Button sx={{ 
            bgcolor: theme.buttonPrimaryColor || '#ff3f6c',
            color: theme.buttonTextColor || '#ffffff',
            borderRadius: theme.buttonBorderRadius || '4px',
            '&:hover': { bgcolor: theme.buttonPrimaryColor || '#ff3f6c', opacity: 0.9 },
            px: 3,
          }}>
            Shop Now
          </Button>
        </Box>

        {/* Featured Products */}
        <Typography variant="h5" gutterBottom sx={{ 
          color: theme.textPrimaryColor || '#282c3f',
          fontFamily: theme.headingFontFamily || "'Poppins', sans-serif",
          fontWeight: 600,
          mb: 2,
        }}>
          Featured Products
        </Typography>
        
        <Grid container spacing={2}>
          {[1, 2, 3].map((item) => (
            <Grid item xs={12} sm={6} md={4} key={item}>
              <Card sx={{ 
                bgcolor: theme.cardBackgroundColor || '#ffffff',
                borderRadius: theme.cardBorderRadius || '8px',
                boxShadow: theme.cardShadow || '0 2px 8px rgba(0,0,0,0.1)',
                border: `1px solid ${theme.cardBorderColor || 'transparent'}`,
                height: '100%',
              }}>
                <Box sx={{ 
                  height: 140, 
                  bgcolor: theme.backgroundAccentColor || '#f0f0f0',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                  <Typography variant="body2" sx={{ color: theme.textLightColor || '#999' }}>
                    Product Image
                  </Typography>
                </Box>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ 
                    color: theme.textPrimaryColor || '#282c3f',
                    fontFamily: theme.headingFontFamily || "'Poppins', sans-serif",
                  }}>
                    Product {item}
                  </Typography>
                  <Typography variant="body2" sx={{ 
                    color: theme.textSecondaryColor || '#666666',
                    fontFamily: theme.bodyFontFamily || "'Roboto', sans-serif",
                    mb: 2,
                  }}>
                    This is a sample product description.
                  </Typography>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="h6" sx={{ 
                      color: theme.accentColor || '#ff3f6c',
                      fontFamily: theme.headingFontFamily || "'Poppins', sans-serif",
                    }}>
                      $99.99
                    </Typography>
                    <Button size="small" sx={{ 
                      bgcolor: theme.buttonSecondaryColor || '#f5f5f6',
                      color: theme.textPrimaryColor || '#282c3f',
                      borderRadius: theme.buttonBorderRadius || '4px',
                      '&:hover': { bgcolor: theme.buttonSecondaryColor || '#f5f5f6', opacity: 0.9 },
                    }}>
                      Add to Cart
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        {/* Newsletter */}
        <Box sx={{ 
          mt: 4, 
          p: 3, 
          borderRadius: theme.cardBorderRadius || '8px',
          bgcolor: theme.backgroundSecondaryColor || '#ffffff',
          boxShadow: theme.cardShadow || '0 2px 8px rgba(0,0,0,0.1)',
        }}>
          <Typography variant="h5" gutterBottom sx={{ 
            color: theme.textPrimaryColor || '#282c3f',
            fontFamily: theme.headingFontFamily || "'Poppins', sans-serif",
          }}>
            Subscribe to our newsletter
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
            <TextField 
              placeholder="Your email address" 
              fullWidth
              sx={{
                '& .MuiOutlinedInput-root': {
                  bgcolor: theme.inputBackgroundColor || '#ffffff',
                  borderRadius: theme.inputBorderRadius || '4px',
                  '& fieldset': {
                    borderColor: theme.inputBorderColor || '#d4d5d9',
                  },
                  '&:hover fieldset': {
                    borderColor: theme.inputBorderColor || '#d4d5d9',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: theme.inputFocusBorderColor || '#ff3f6c',
                  },
                },
              }}
            />
            <Button sx={{ 
              bgcolor: theme.buttonPrimaryColor || '#ff3f6c',
              color: theme.buttonTextColor || '#ffffff',
              borderRadius: theme.buttonBorderRadius || '4px',
              '&:hover': { bgcolor: theme.buttonPrimaryColor || '#ff3f6c', opacity: 0.9 },
              px: 3,
              whiteSpace: 'nowrap',
            }}>
              Subscribe
            </Button>
          </Box>
        </Box>
      </Box>

      {/* Footer */}
      <Box sx={{ 
        p: 3, 
        bgcolor: theme.footerBackgroundColor || '#1e2830',
        color: theme.footerTextColor || '#ffffff',
      }}>
        <Typography variant="body2" sx={{ 
          textAlign: 'center',
          fontFamily: theme.bodyFontFamily || "'Roboto', sans-serif",
        }}>
          © 2023 Store Name. All rights reserved.
        </Typography>
      </Box>
    </Paper>
  );
};

export default ThemeLivePreview;
