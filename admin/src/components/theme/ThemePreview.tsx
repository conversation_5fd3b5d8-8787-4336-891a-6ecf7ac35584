import React from 'react';
import { Box, Typography, Paper, Button, Card, CardContent, TextField, Divider, IconButton, Tooltip } from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import { ThemeConfiguration } from '../../store/slices/themeSlice';

interface ThemePreviewProps {
  theme: ThemeConfiguration;
  onDelete?: (id: number) => void;
  showDeleteButton?: boolean;
}

const ThemePreview: React.FC<ThemePreviewProps> = ({ 
  theme, 
  onDelete, 
  showDeleteButton = false 
}) => {
  return (
    <Box sx={{ p: 2, bgcolor: theme.backgroundPrimaryColor, borderRadius: 1, mb: 2, position: 'relative' }}>
      {showDeleteButton && onDelete && (
        <Tooltip title="Delete Theme">
          <IconButton 
            size="small" 
            sx={{ position: 'absolute', top: 8, right: 8, zIndex: 1 }}
            onClick={() => onDelete(theme.id!)}
          >
            <DeleteIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      )}
      
      <Typography variant="h6" gutterBottom sx={{ 
        color: theme.textPrimaryColor,
        fontFamily: theme.headingFontFamily
      }}>
        Theme Preview
      </Typography>
      
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 2 }}>
        <Box sx={{ 
          width: 40, 
          height: 40, 
          bgcolor: theme.primaryColor,
          borderRadius: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <Typography variant="caption" sx={{ color: '#fff' }}>P</Typography>
        </Box>
        <Box sx={{ 
          width: 40, 
          height: 40, 
          bgcolor: theme.secondaryColor,
          borderRadius: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <Typography variant="caption" sx={{ color: '#fff' }}>S</Typography>
        </Box>
        <Box sx={{ 
          width: 40, 
          height: 40, 
          bgcolor: theme.accentColor,
          borderRadius: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <Typography variant="caption" sx={{ color: '#fff' }}>A</Typography>
        </Box>
      </Box>
      
      <Divider sx={{ my: 2 }} />
      
      <Box sx={{ mb: 2 }}>
        <Typography variant="subtitle1" sx={{ 
          color: theme.textPrimaryColor,
          fontFamily: theme.headingFontFamily
        }}>
          Typography
        </Typography>
        <Typography variant="h5" sx={{ 
          color: theme.textPrimaryColor,
          fontFamily: theme.headingFontFamily
        }}>
          Heading Text
        </Typography>
        <Typography variant="body1" sx={{ 
          color: theme.textPrimaryColor,
          fontFamily: theme.bodyFontFamily
        }}>
          Body text in primary color
        </Typography>
        <Typography variant="body2" sx={{ 
          color: theme.textSecondaryColor,
          fontFamily: theme.bodyFontFamily
        }}>
          Secondary text example
        </Typography>
      </Box>
      
      <Divider sx={{ my: 2 }} />
      
      <Box sx={{ mb: 2 }}>
        <Typography variant="subtitle1" sx={{ 
          color: theme.textPrimaryColor,
          fontFamily: theme.headingFontFamily
        }}>
          Components
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
          <Button 
            variant="contained"
            sx={{ 
              bgcolor: theme.buttonPrimaryColor,
              color: theme.buttonTextColor,
              borderRadius: theme.buttonBorderRadius,
              '&:hover': {
                bgcolor: theme.buttonPrimaryColor,
                opacity: 0.9
              }
            }}
          >
            Primary Button
          </Button>
          
          <Button 
            variant="outlined"
            sx={{ 
              color: theme.buttonPrimaryColor,
              borderColor: theme.buttonPrimaryColor,
              borderRadius: theme.buttonBorderRadius,
            }}
          >
            Outlined Button
          </Button>
        </Box>
        
        <Card sx={{ 
          mb: 2,
          bgcolor: theme.cardBackgroundColor,
          borderRadius: theme.cardBorderRadius,
          boxShadow: theme.cardShadow,
          borderColor: theme.cardBorderColor,
        }}>
          <CardContent>
            <Typography variant="h6" sx={{ 
              color: theme.textPrimaryColor,
              fontFamily: theme.headingFontFamily
            }}>
              Card Title
            </Typography>
            <Typography variant="body2" sx={{ 
              color: theme.textSecondaryColor,
              fontFamily: theme.bodyFontFamily
            }}>
              This is a sample card with the theme's styling applied.
            </Typography>
          </CardContent>
        </Card>
        
        <TextField 
          label="Input Field"
          variant="outlined"
          fullWidth
          sx={{
            mb: 2,
            '& .MuiOutlinedInput-root': {
              bgcolor: theme.inputBackgroundColor,
              borderRadius: theme.inputBorderRadius,
              '& fieldset': {
                borderColor: theme.inputBorderColor,
              },
              '&:hover fieldset': {
                borderColor: theme.inputBorderColor,
              },
              '&.Mui-focused fieldset': {
                borderColor: theme.inputFocusBorderColor,
              },
            },
          }}
        />
      </Box>
    </Box>
  );
};

export default ThemePreview;
