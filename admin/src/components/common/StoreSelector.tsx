import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Chip,
  SelectChangeEvent,
  CircularProgress
} from '@mui/material';
import { Store, setSelectedStore, fetchAccessibleStores } from '../../store/slices/storeSlice';
import { RootState, AppDispatch } from '../../store';

interface StoreSelectorProps {
  showLabel?: boolean;
  variant?: 'standard' | 'outlined' | 'filled';
  size?: 'small' | 'medium';
  width?: string | number;
}

const StoreSelector: React.FC<StoreSelectorProps> = ({
  showLabel = true,
  variant = 'outlined',
  size = 'small',
  width = 200
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const { accessibleStores, selectedStore, loading } = useSelector((state: RootState) => state.store);
  const isSuperAdmin = user?.roles.includes('SuperAdmin');

  useEffect(() => {
    // Always fetch accessible stores when component mounts
    dispatch(fetchAccessibleStores());
  }, [dispatch]);

  useEffect(() => {
    console.log('Accessible stores:', accessibleStores);
    console.log('Selected store:', selectedStore);
  }, [accessibleStores, selectedStore]);

  const handleStoreChange = (event: SelectChangeEvent<number>) => {
    const storeId = event.target.value as number;
    const store = accessibleStores.find(s => s.id === storeId);
    if (store) {
      dispatch(setSelectedStore(store));
    }
  };

  // If user is not a super admin and has only one store, don't show the selector
  if (!isSuperAdmin && accessibleStores.length === 1) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        {showLabel && (
          <Typography variant="body2" color="text.secondary">
            Store:
          </Typography>
        )}
        <Chip
          label={accessibleStores[0]?.name || 'Loading...'}
          color="primary"
          size="small"
        />
      </Box>
    );
  }

  return (
    <FormControl variant={variant} size={size} sx={{ minWidth: width }}>
      {showLabel && <InputLabel>Select Store</InputLabel>}
      <Select
        value={selectedStore?.id || ''}
        onChange={handleStoreChange}
        label={showLabel ? "Select Store" : undefined}
        displayEmpty={!showLabel}
        disabled={loading || accessibleStores.length === 0}
      >
        {loading ? (
          <MenuItem value="">
            <CircularProgress size={20} />
          </MenuItem>
        ) : (
          accessibleStores.map((store) => (
            <MenuItem key={store.id} value={store.id}>
              {store.name}
              {!store.isActive && (
                <Chip
                  label="Inactive"
                  size="small"
                  color="warning"
                  sx={{ ml: 1, height: 20 }}
                />
              )}
            </MenuItem>
          ))
        )}
      </Select>
    </FormControl>
  );
};

export default StoreSelector;
