import React, { useState, useRef } from 'react';
import {
  Box,
  Button,
  Typography,
  CircularProgress,
  Paper,
  Grid,
  IconButton,
  LinearProgress,
  Chip,
  Tooltip
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  Delete as DeleteIcon,
  Clear as ClearIcon,
  Image as ImageIcon
} from '@mui/icons-material';
import { toast } from 'react-toastify';
import { uploadMultipleImages } from '../../services/fileUpload';

interface BulkImageUploaderProps {
  onImagesUploaded: (urls: string[]) => void;
  maxFiles?: number;
  acceptedFileTypes?: string;
  showPreview?: boolean;
  title?: string;
}

interface PreviewFile extends File {
  preview: string;
  uploading?: boolean;
  error?: boolean;
}

const BulkImageUploader: React.FC<BulkImageUploaderProps> = ({
  onImagesUploaded,
  maxFiles = 20,
  acceptedFileTypes = 'image/*',
  showPreview = true,
  title = 'Upload Images'
}) => {
  const [selectedFiles, setSelectedFiles] = useState<PreviewFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!event.target.files || event.target.files.length === 0) return;

    const filesArray = Array.from(event.target.files);

    // Check if adding these files would exceed the maximum
    if (selectedFiles.length + filesArray.length > maxFiles) {
      toast.error(`You can only upload a maximum of ${maxFiles} files at once`);
      return;
    }

    // Create preview URLs for the files
    const newFiles: PreviewFile[] = filesArray.map(file => {
      return Object.assign(file, {
        preview: URL.createObjectURL(file)
      });
    });

    setSelectedFiles(prev => [...prev, ...newFiles]);

    // Reset the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleRemoveFile = (index: number) => {
    const newFiles = [...selectedFiles];

    // Revoke the object URL to avoid memory leaks
    URL.revokeObjectURL(newFiles[index].preview);

    newFiles.splice(index, 1);
    setSelectedFiles(newFiles);
  };

  const handleClearAll = () => {
    // Revoke all object URLs
    selectedFiles.forEach(file => URL.revokeObjectURL(file.preview));
    setSelectedFiles([]);
  };

  const handleUpload = async () => {
    if (selectedFiles.length === 0) {
      toast.error('Please select files to upload');
      return;
    }

    setUploading(true);
    setUploadProgress(0);

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + Math.random() * 10;
          return newProgress >= 95 ? 95 : newProgress;
        });
      }, 300);

      const urls = await uploadMultipleImages(selectedFiles);

      clearInterval(progressInterval);
      setUploadProgress(100);

      // Call the callback with the uploaded URLs
      onImagesUploaded(urls);

      // Clear the selected files
      handleClearAll();

      toast.success(`Successfully uploaded ${urls.length} images`);
    } catch (error) {
      console.error('Error uploading images:', error);
      toast.error('Failed to upload images. Please try again.');
    } finally {
      setUploading(false);
      // Reset progress after a short delay
      setTimeout(() => setUploadProgress(0), 1000);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const filesArray = Array.from(e.dataTransfer.files);

      // Filter for only image files if acceptedFileTypes is set to image/*
      const filteredFiles = acceptedFileTypes === 'image/*'
        ? filesArray.filter(file => file.type.startsWith('image/'))
        : filesArray;

      // Check if adding these files would exceed the maximum
      if (selectedFiles.length + filteredFiles.length > maxFiles) {
        toast.error(`You can only upload a maximum of ${maxFiles} files at once`);
        return;
      }

      // Create preview URLs for the files
      const newFiles: PreviewFile[] = filteredFiles.map(file => {
        return Object.assign(file, {
          preview: URL.createObjectURL(file)
        });
      });

      setSelectedFiles(prev => [...prev, ...newFiles]);
    }
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        {title}
      </Typography>

      {/* Drag and drop area */}
      <Paper
        elevation={3}
        sx={{
          p: 5,
          mb: 3,
          border: '2px dashed #ccc',
          borderRadius: 2,
          backgroundColor: 'background.paper',
          textAlign: 'center',
          cursor: 'pointer',
          transition: 'border-color 0.3s',
          minHeight: '300px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          '&:hover': {
            borderColor: 'primary.main',
          }
        }}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onClick={() => fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          accept={acceptedFileTypes}
          style={{ display: 'none' }}
          id="bulk-image-upload"
          multiple
          type="file"
          onChange={handleFileChange}
          disabled={uploading}
        />

        <CloudUploadIcon sx={{ fontSize: 72, color: 'text.secondary', mb: 3 }} />
        <Typography variant="h6" gutterBottom>
          Drag & Drop Files Here
        </Typography>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          or
        </Typography>
        <Button
          variant="contained"
          component="span"
          disabled={uploading}
          sx={{ mt: 1 }}
        >
          Browse Files
        </Button>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
          Maximum {maxFiles} files. Accepted formats: {acceptedFileTypes === 'image/*' ? 'JPG, PNG, GIF, etc.' : acceptedFileTypes}
        </Typography>
      </Paper>

      {/* Selected files section */}
      {selectedFiles.length > 0 && (
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="subtitle1">
              Selected Files ({selectedFiles.length}/{maxFiles})
            </Typography>
            <Button
              variant="outlined"
              color="error"
              size="small"
              startIcon={<ClearIcon />}
              onClick={handleClearAll}
              disabled={uploading}
            >
              Clear All
            </Button>
          </Box>

          {showPreview && (
            <Grid container spacing={2}>
              {selectedFiles.map((file, index) => (
                <Grid item xs={4} sm={3} md={2} lg={1.5} key={index}>
                  <Paper
                    elevation={2}
                    sx={{
                      position: 'relative',
                      height: '100%',
                      overflow: 'hidden',
                      borderRadius: 1
                    }}
                  >
                    <Box
                      sx={{
                        position: 'relative',
                        paddingTop: '100%', // 1:1 Aspect Ratio
                        width: '100%',
                        overflow: 'hidden'
                      }}
                    >
                      <img
                        src={file.preview}
                        alt={file.name}
                        style={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          width: '100%',
                          height: '100%',
                          objectFit: 'cover'
                        }}
                      />
                      <Tooltip title="Remove">
                        <IconButton
                          size="small"
                          sx={{
                            position: 'absolute',
                            top: 4,
                            right: 4,
                            backgroundColor: 'rgba(255, 255, 255, 0.7)',
                            '&:hover': {
                              backgroundColor: 'rgba(255, 255, 255, 0.9)',
                            }
                          }}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRemoveFile(index);
                          }}
                          disabled={uploading}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                    <Box sx={{ p: 1 }}>
                      <Typography variant="caption" noWrap title={file.name}>
                        {file.name}
                      </Typography>
                      <Typography variant="caption" display="block" color="text.secondary">
                        {(file.size / 1024).toFixed(1)} KB
                      </Typography>
                    </Box>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          )}

          {!showPreview && (
            <Box sx={{ mb: 2 }}>
              {selectedFiles.map((file, index) => (
                <Box
                  key={index}
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    p: 1,
                    mb: 1,
                    borderRadius: 1,
                    bgcolor: 'background.paper'
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <ImageIcon sx={{ mr: 1, color: 'text.secondary' }} />
                    <Typography variant="body2" noWrap sx={{ maxWidth: 200 }} title={file.name}>
                      {file.name}
                    </Typography>
                    <Chip
                      label={`${(file.size / 1024).toFixed(1)} KB`}
                      size="small"
                      sx={{ ml: 1 }}
                    />
                  </Box>
                  <IconButton
                    size="small"
                    onClick={() => handleRemoveFile(index)}
                    disabled={uploading}
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </Box>
              ))}
            </Box>
          )}

          <Button
            variant="contained"
            color="primary"
            fullWidth
            onClick={handleUpload}
            disabled={uploading || selectedFiles.length === 0}
            startIcon={uploading ? <CircularProgress size={24} color="inherit" /> : null}
            sx={{ mt: 2 }}
          >
            {uploading ? `Uploading... ${Math.round(uploadProgress)}%` : `Upload ${selectedFiles.length} Files`}
          </Button>

          {uploading && (
            <LinearProgress
              variant="determinate"
              value={uploadProgress}
              sx={{ mt: 1, height: 8, borderRadius: 4 }}
            />
          )}
        </Box>
      )}
    </Box>
  );
};

export default BulkImageUploader;
