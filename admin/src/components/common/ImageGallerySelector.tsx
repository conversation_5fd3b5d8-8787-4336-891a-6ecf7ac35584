import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Typography,
  CircularProgress,
  Tabs,
  Tab,
  Divider,
  TextField,
  InputAdornment,
  IconButton,
  Pagination,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Tooltip
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  Search as SearchIcon,
  Clear as ClearIcon,
  Check as CheckIcon,
  Image as ImageIcon
} from '@mui/icons-material';
import { toast } from 'react-toastify';
import { getImages, uploadImage, uploadMultipleImages, deleteImage, ImageItem } from '../../services/fileUpload';
import BulkImageUploader from './BulkImageUploader';
import { getValidImageUrl, createImageErrorHandler } from '../../utils/imageUtils';

interface ImageGallerySelectorProps {
  open: boolean;
  onClose: () => void;
  onSelect: (imageUrl: string) => void;
  title?: string;
  multiSelect?: boolean;
  onMultiSelect?: (imageUrls: string[]) => void;
  initialSelectedUrls?: string[];
}

interface PaginationState {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  pageSize: number;
}

const ImageGallerySelector: React.FC<ImageGallerySelectorProps> = ({
  open,
  onClose,
  onSelect,
  title = 'Select Image',
  multiSelect = false,
  onMultiSelect,
  initialSelectedUrls = []
}) => {
  const [tabValue, setTabValue] = useState(0);
  const [images, setImages] = useState<ImageItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedUrls, setSelectedUrls] = useState<string[]>(initialSelectedUrls);
  const [pagination, setPagination] = useState<PaginationState>({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    pageSize: 20
  });

  const fetchImages = useCallback(async (page: number = 1) => {
    setLoading(true);
    try {
      const response = await getImages(page, pagination.pageSize);
      setImages(response.items || []);
      setPagination({
        currentPage: page,
        totalPages: Math.ceil(response.totalCount / pagination.pageSize),
        totalCount: response.totalCount,
        pageSize: pagination.pageSize
      });
    } catch (error) {
      console.error('Error fetching images:', error);
      toast.error('Failed to load images');
    } finally {
      setLoading(false);
    }
  }, [pagination.pageSize]);

  useEffect(() => {
    if (open) {
      fetchImages(1);
    }
  }, [open, fetchImages]);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
    fetchImages(page);
  };

  const handleImagesUploaded = (urls: string[]) => {
    // Refresh the gallery after upload
    fetchImages(1);
    toast.success(`${urls.length} images uploaded successfully`);
  };

  const handleImageSelect = (url: string) => {
    if (multiSelect) {
      setSelectedUrls(prev => {
        if (prev.includes(url)) {
          return prev.filter(u => u !== url);
        } else {
          return [...prev, url];
        }
      });
    } else {
      onSelect(url);
      onClose();
    }
  };

  const handleConfirmSelection = () => {
    if (multiSelect && onMultiSelect) {
      onMultiSelect(selectedUrls);
    }
    onClose();
  };

  const handleDeleteImage = async (imageId: number) => {
    try {
      await deleteImage(imageId);
      // Refresh the gallery
      fetchImages(pagination.currentPage);
      toast.success('Image deleted successfully');
    } catch (error) {
      console.error('Error deleting image:', error);
      toast.error('Failed to delete image');
    }
  };

  const filteredImages = searchQuery
    ? images.filter(img => 
        img.name.toLowerCase().includes(searchQuery.toLowerCase()))
    : images;

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="lg"
      fullWidth
    >
      <DialogTitle>
        {title}
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <ClearIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent dividers>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="image gallery tabs">
          <Tab label="Gallery" id="tab-0" aria-controls="tabpanel-0" />
          <Tab label="Upload" id="tab-1" aria-controls="tabpanel-1" />
        </Tabs>

        <Box sx={{ mt: 2 }}>
          {tabValue === 0 && (
            <>
              <Box sx={{ mb: 2 }}>
                <TextField
                  fullWidth
                  placeholder="Search images..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                    endAdornment: searchQuery ? (
                      <InputAdornment position="end">
                        <IconButton onClick={() => setSearchQuery('')} edge="end">
                          <ClearIcon />
                        </IconButton>
                      </InputAdornment>
                    ) : null
                  }}
                />
              </Box>

              {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                  <CircularProgress />
                </Box>
              ) : filteredImages.length === 0 ? (
                <Box sx={{ p: 4, textAlign: 'center' }}>
                  <ImageIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="body1" color="text.secondary">
                    No images found
                  </Typography>
                </Box>
              ) : (
                <Grid container spacing={2}>
                  {filteredImages.map((image) => (
                    <Grid item xs={6} sm={4} md={3} lg={2} key={image.id}>
                      <Card 
                        sx={{ 
                          cursor: 'pointer',
                          border: selectedUrls.includes(image.url) ? '2px solid primary.main' : 'none',
                          transition: 'transform 0.2s',
                          '&:hover': {
                            transform: 'scale(1.05)'
                          }
                        }}
                        onClick={() => handleImageSelect(image.url)}
                      >
                        <CardMedia
                          component="img"
                          height="120"
                          image={getValidImageUrl(image.url)}
                          alt={image.name}
                          onError={createImageErrorHandler()}
                        />
                        <CardContent sx={{ p: 1 }}>
                          <Typography variant="caption" noWrap>
                            {image.name}
                          </Typography>
                        </CardContent>
                        {selectedUrls.includes(image.url) && (
                          <Box 
                            sx={{ 
                              position: 'absolute', 
                              top: 5, 
                              right: 5, 
                              bgcolor: 'primary.main',
                              borderRadius: '50%',
                              width: 24,
                              height: 24,
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center'
                            }}
                          >
                            <CheckIcon sx={{ color: 'white', fontSize: 16 }} />
                          </Box>
                        )}
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              )}

              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                <Pagination 
                  count={pagination.totalPages} 
                  page={pagination.currentPage}
                  onChange={handlePageChange}
                  color="primary" 
                />
              </Box>
            </>
          )}

          {tabValue === 1 && (
            <BulkImageUploader
              onImagesUploaded={handleImagesUploaded}
              maxFiles={20}
              title="Upload Images to Gallery"
              showPreview={true}
            />
          )}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        {multiSelect && (
          <Button 
            onClick={handleConfirmSelection} 
            variant="contained" 
            color="primary"
            disabled={selectedUrls.length === 0}
          >
            Select {selectedUrls.length > 0 ? `(${selectedUrls.length})` : ''}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default ImageGallerySelector;
