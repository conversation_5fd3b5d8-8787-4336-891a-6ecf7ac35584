import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Box,
  Grid,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Typography,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Tooltip,
  CircularProgress
} from '@mui/material';
import {
  ContentCopy as ContentCopyIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  ArrowBack as ArrowBackIcon,
  ArrowForward as ArrowForwardIcon,
  Download as DownloadIcon
} from '@mui/icons-material';
import { toast } from 'react-toastify';

export interface ImageItem {
  url: string;
  name?: string;
  uploadedAt?: Date;
  description?: string;
}

interface ImageGalleryProps {
  images: ImageItem[];
  onDelete?: (url: string) => Promise<void>;
  loading?: boolean;
  title?: string;
  showCopyButton?: boolean;
  showDeleteButton?: boolean;
  showDownloadButton?: boolean;
  thumbnailSize?: 'small' | 'medium' | 'large';
  infiniteScroll?: boolean;
  itemsPerPage?: number;
  gridItemProps?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
  };
  dialogSize?: 'small' | 'medium' | 'large' | 'fullscreen';
}

const ImageGallery: React.FC<ImageGalleryProps> = ({
  images,
  onDelete,
  loading = false,
  title = 'Image Gallery',
  showCopyButton = true,
  showDeleteButton = true,
  showDownloadButton = true,
  thumbnailSize = 'medium',
  infiniteScroll = true,
  itemsPerPage = 20,
  gridItemProps = { xs: 12, sm: 6, md: 4, lg: 3 },
  dialogSize = 'large'
}) => {
  const [viewImageIndex, setViewImageIndex] = useState<number | null>(null);
  const [deleteConfirmIndex, setDeleteConfirmIndex] = useState<number | null>(null);
  const [deletingIndex, setDeletingIndex] = useState<number | null>(null);
  const [visibleImages, setVisibleImages] = useState<ImageItem[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [dialogFullScreen, setDialogFullScreen] = useState(dialogSize === 'fullscreen');

  const observer = useRef<IntersectionObserver | null>(null);
  const lastImageElementRef = useCallback((node: HTMLElement | null) => {
    if (loading) return;
    if (observer.current) observer.current.disconnect();
    observer.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting && hasMore) {
        setPage(prevPage => prevPage + 1);
      }
    });
    if (node) observer.current.observe(node);
  }, [loading, hasMore]);

  // Initialize visible images
  useEffect(() => {
    if (!infiniteScroll) {
      setVisibleImages(images);
      return;
    }

    const initialItems = images.slice(0, itemsPerPage);
    setVisibleImages(initialItems);
    setHasMore(images.length > itemsPerPage);
  }, [images, infiniteScroll, itemsPerPage]);

  // Load more images when page changes
  useEffect(() => {
    if (!infiniteScroll || page === 1) return;

    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const newItems = images.slice(startIndex, endIndex);

    if (newItems.length > 0) {
      setVisibleImages(prev => [...prev, ...newItems]);
    }

    setHasMore(endIndex < images.length);
  }, [page, images, infiniteScroll, itemsPerPage]);

  // Get thumbnail dimensions based on size
  const getThumbnailDimensions = () => {
    switch (thumbnailSize) {
      case 'small': return { height: 100, width: 100 };
      case 'large': return { height: 180, width: 180 };
      case 'medium':
      default: return { height: 140, width: 140 };
    }
  };

  // Get dialog max width based on size
  const getDialogMaxWidth = () => {
    switch (dialogSize) {
      case 'small': return 'md';
      case 'medium': return 'lg';
      case 'fullscreen': return false;
      case 'large':
      default: return 'xl';
    }
  };

  const handleCopyUrl = (url: string) => {
    navigator.clipboard.writeText(url)
      .then(() => {
        toast.success('URL copied to clipboard');
      })
      .catch(err => {
        console.error('Failed to copy URL:', err);
        toast.error('Failed to copy URL');
      });
  };

  const handleDeleteImage = async (index: number) => {
    if (!onDelete) return;

    setDeletingIndex(index);
    try {
      await onDelete(images[index].url);
      setDeleteConfirmIndex(null);
      toast.success('Image deleted successfully');
    } catch (error) {
      console.error('Error deleting image:', error);
      toast.error('Failed to delete image');
    } finally {
      setDeletingIndex(null);
    }
  };

  const handleDownload = (url: string, filename?: string) => {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename || url.split('/').pop() || 'image';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const formatDate = (date?: Date) => {
    if (!date) return '';
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const handlePrevImage = () => {
    if (viewImageIndex === null) return;
    setViewImageIndex((viewImageIndex - 1 + images.length) % images.length);
  };

  const handleNextImage = () => {
    if (viewImageIndex === null) return;
    setViewImageIndex((viewImageIndex + 1) % images.length);
  };

  return (
    <Box>
      {title && (
        <Typography variant="h6" gutterBottom>
          {title}
        </Typography>
      )}

      <Grid container spacing={thumbnailSize === 'small' ? 1 : 2}>
        {loading && visibleImages.length === 0 ? (
          <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Grid>
        ) : images.length === 0 ? (
          <Grid item xs={12}>
            <Box sx={{ p: 3, textAlign: 'center', bgcolor: 'background.paper', borderRadius: 1 }}>
              <Typography variant="body1" color="text.secondary">
                No images available
              </Typography>
            </Box>
          </Grid>
        ) : (
          // Use visibleImages for infinite scrolling or images if not using infinite scroll
          (infiniteScroll ? visibleImages : images).map((image, index) => {
            const isLastElement = infiniteScroll && index === visibleImages.length - 1;
            const dimensions = getThumbnailDimensions();

            return (
              <Grid
                item
                {...gridItemProps}
                key={index}
                ref={isLastElement ? lastImageElementRef : undefined}
              >
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    transition: 'transform 0.2s, box-shadow 0.2s',
                    width: 'auto',
                    maxWidth: dimensions.width + 20, // Add some padding
                    margin: '0 auto',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: 4
                    }
                  }}
                >
                  <Box sx={{ position: 'relative', display: 'flex', justifyContent: 'center', overflow: 'hidden', width: '100%' }}>
                    <CardMedia
                      component="img"
                      height={dimensions.height}
                      image={image.url}
                      alt={image.name || `Image ${index + 1}`}
                      sx={{
                        objectFit: 'cover',
                        cursor: 'pointer',
                        transition: 'transform 0.3s',
                        width: dimensions.width,
                        height: dimensions.height,
                        margin: '0 auto', // Center the image
                        '&:hover': {
                          transform: 'scale(1.05)'
                        }
                      }}
                      onClick={() => setViewImageIndex(infiniteScroll ?
                        visibleImages.findIndex(img => img.url === image.url) :
                        index
                      )}
                    />
                    <Box
                      sx={{
                        position: 'absolute',
                        top: 0,
                        right: 0,
                        display: 'flex',
                        p: 0.5,
                        backgroundColor: 'rgba(0, 0, 0, 0.4)',
                        borderBottomLeftRadius: 8
                      }}
                    >
                      <Tooltip title="View Image">
                        <IconButton
                          size="small"
                          onClick={() => setViewImageIndex(infiniteScroll ?
                            visibleImages.findIndex(img => img.url === image.url) :
                            index
                          )}
                          sx={{ color: 'white', '&:hover': { color: 'primary.main' } }}
                        >
                          <VisibilityIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </Box>
                  <CardContent sx={{ flexGrow: 1, p: thumbnailSize === 'small' ? 1 : 1.5, pb: 0 }}>
                    {image.name && (
                      <Typography
                        variant={thumbnailSize === 'small' ? 'caption' : 'subtitle2'}
                        noWrap
                        title={image.name}
                        sx={{ fontWeight: 500 }}
                      >
                        {image.name}
                      </Typography>
                    )}
                    {image.uploadedAt && thumbnailSize !== 'small' && (
                      <Typography variant="caption" color="text.secondary" display="block">
                        {formatDate(image.uploadedAt)}
                      </Typography>
                    )}
                  </CardContent>
                  <CardActions sx={{ p: thumbnailSize === 'small' ? 0.5 : 1, pt: 0, justifyContent: 'space-between' }}>
                    <Box>
                      {showCopyButton && (
                        <Tooltip title="Copy URL">
                          <IconButton
                            size="small"
                            onClick={() => handleCopyUrl(image.url)}
                            sx={{ p: thumbnailSize === 'small' ? 0.5 : 1 }}
                          >
                            <ContentCopyIcon fontSize={thumbnailSize === 'small' ? 'inherit' : 'small'} />
                          </IconButton>
                        </Tooltip>
                      )}

                      {showDownloadButton && (
                        <Tooltip title="Download Image">
                          <IconButton
                            size="small"
                            onClick={() => handleDownload(image.url, image.name)}
                            sx={{ p: thumbnailSize === 'small' ? 0.5 : 1 }}
                          >
                            <DownloadIcon fontSize={thumbnailSize === 'small' ? 'inherit' : 'small'} />
                          </IconButton>
                        </Tooltip>
                      )}
                    </Box>

                    {showDeleteButton && onDelete && (
                      <Tooltip title="Delete Image">
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => setDeleteConfirmIndex(infiniteScroll ?
                            visibleImages.findIndex(img => img.url === image.url) :
                            index
                          )}
                          disabled={deletingIndex === index}
                          sx={{ p: thumbnailSize === 'small' ? 0.5 : 1 }}
                        >
                          {deletingIndex === index ?
                            <CircularProgress size={thumbnailSize === 'small' ? 12 : 16} /> :
                            <DeleteIcon fontSize={thumbnailSize === 'small' ? 'inherit' : 'small'} />
                          }
                        </IconButton>
                      </Tooltip>
                    )}
                  </CardActions>
                </Card>
              </Grid>
            );
          })
        )}

        {/* Loading indicator at the bottom for infinite scroll */}
        {infiniteScroll && loading && visibleImages.length > 0 && (
          <Box sx={{ width: '100%', display: 'flex', justifyContent: 'center', py: 2 }}>
            <CircularProgress size={30} />
          </Box>
        )}
      </Grid>

      {/* Image Viewer Dialog */}
      <Dialog
        open={viewImageIndex !== null}
        onClose={() => setViewImageIndex(null)}
        maxWidth={getDialogMaxWidth() as any}
        fullWidth
        fullScreen={dialogFullScreen}
        slotProps={{
          paper: {
            sx: {
              height: dialogFullScreen ? '100%' : '90vh',
              maxHeight: dialogFullScreen ? '100%' : '90vh',
            }
          }
        }}
      >
        <DialogTitle sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          p: 2,
          borderBottom: '1px solid rgba(0, 0, 0, 0.12)'
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', overflow: 'hidden' }}>
            {viewImageIndex !== null && (
              <Typography variant="h6" noWrap sx={{ maxWidth: { xs: '180px', sm: '300px', md: '500px' } }}>
                {(infiniteScroll ? visibleImages : images)[viewImageIndex]?.name || 'Image Preview'}
              </Typography>
            )}
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Tooltip title="Previous Image">
              <IconButton onClick={handlePrevImage} disabled={(infiniteScroll ? visibleImages : images).length <= 1}>
                <ArrowBackIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Next Image">
              <IconButton onClick={handleNextImage} disabled={(infiniteScroll ? visibleImages : images).length <= 1}>
                <ArrowForwardIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title={dialogFullScreen ? "Exit Fullscreen" : "Fullscreen"}>
              <IconButton onClick={() => setDialogFullScreen(!dialogFullScreen)}>
                {dialogFullScreen ?
                  <Box component="span" sx={{ fontSize: '24px' }}>⤓</Box> :
                  <Box component="span" sx={{ fontSize: '24px' }}>⤢</Box>
                }
              </IconButton>
            </Tooltip>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ p: 0, display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
          {viewImageIndex !== null && (
            <Box sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              flexGrow: 1,
              overflow: 'auto',
              position: 'relative',
              backgroundColor: '#f5f5f5'
            }}>
              <img
                src={(infiniteScroll ? visibleImages : images)[viewImageIndex].url}
                alt="Preview"
                style={{
                  maxWidth: '100%',
                  maxHeight: '100%',
                  objectFit: 'contain'
                }}
              />

              {/* Navigation overlay buttons for larger screens */}
              <Box sx={{
                position: 'absolute',
                top: 0,
                bottom: 0,
                left: 0,
                width: '15%',
                display: { xs: 'none', md: 'flex' },
                alignItems: 'center',
                justifyContent: 'flex-start',
                pl: 2,
                opacity: 0,
                transition: 'opacity 0.2s',
                '&:hover': { opacity: 1 },
                cursor: 'pointer'
              }} onClick={handlePrevImage}>
                <IconButton
                  sx={{
                    backgroundColor: 'rgba(0,0,0,0.5)',
                    color: 'white',
                    '&:hover': { backgroundColor: 'rgba(0,0,0,0.7)' }
                  }}
                  disabled={(infiniteScroll ? visibleImages : images).length <= 1}
                >
                  <ArrowBackIcon fontSize="large" />
                </IconButton>
              </Box>

              <Box sx={{
                position: 'absolute',
                top: 0,
                bottom: 0,
                right: 0,
                width: '15%',
                display: { xs: 'none', md: 'flex' },
                alignItems: 'center',
                justifyContent: 'flex-end',
                pr: 2,
                opacity: 0,
                transition: 'opacity 0.2s',
                '&:hover': { opacity: 1 },
                cursor: 'pointer'
              }} onClick={handleNextImage}>
                <IconButton
                  sx={{
                    backgroundColor: 'rgba(0,0,0,0.5)',
                    color: 'white',
                    '&:hover': { backgroundColor: 'rgba(0,0,0,0.7)' }
                  }}
                  disabled={(infiniteScroll ? visibleImages : images).length <= 1}
                >
                  <ArrowForwardIcon fontSize="large" />
                </IconButton>
              </Box>
            </Box>
          )}

          {viewImageIndex !== null && (infiniteScroll ? visibleImages : images)[viewImageIndex]?.description && (
            <Box sx={{ p: 2, borderTop: '1px solid rgba(0, 0, 0, 0.12)' }}>
              <Typography variant="body2" color="text.secondary">
                {(infiniteScroll ? visibleImages : images)[viewImageIndex].description}
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 2, borderTop: '1px solid rgba(0, 0, 0, 0.12)' }}>
          {showCopyButton && viewImageIndex !== null && (
            <Button
              startIcon={<ContentCopyIcon />}
              onClick={() => handleCopyUrl((infiniteScroll ? visibleImages : images)[viewImageIndex].url)}
              variant="outlined"
              size="small"
            >
              Copy URL
            </Button>
          )}
          {showDownloadButton && viewImageIndex !== null && (
            <Button
              startIcon={<DownloadIcon />}
              onClick={() => handleDownload(
                (infiniteScroll ? visibleImages : images)[viewImageIndex].url,
                (infiniteScroll ? visibleImages : images)[viewImageIndex].name
              )}
              variant="outlined"
              size="small"
            >
              Download
            </Button>
          )}
          {showDeleteButton && onDelete && viewImageIndex !== null && (
            <Button
              startIcon={<DeleteIcon />}
              onClick={() => {
                setDeleteConfirmIndex(viewImageIndex);
                setViewImageIndex(null);
              }}
              variant="outlined"
              color="error"
              size="small"
            >
              Delete
            </Button>
          )}
          <Button
            onClick={() => setViewImageIndex(null)}
            variant="contained"
            size="small"
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteConfirmIndex !== null}
        onClose={() => setDeleteConfirmIndex(null)}
      >
        <DialogTitle>Confirm Deletion</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this image? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteConfirmIndex(null)}>
            Cancel
          </Button>
          <Button
            color="error"
            onClick={() => deleteConfirmIndex !== null && handleDeleteImage(deleteConfirmIndex)}
            disabled={deletingIndex !== null}
          >
            {deletingIndex !== null ? 'Deleting...' : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ImageGallery;
