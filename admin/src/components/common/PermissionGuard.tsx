import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import { fetchUserModulePermissions } from '../../store/slices/userModulePermissionSlice';
import { AppDispatch } from '../../store';

interface PermissionGuardProps {
  module: string;
  action: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  storeId?: number;
}

/**
 * A component that conditionally renders its children based on user permissions
 *
 * @param module - The module name (e.g., "Products", "Orders")
 * @param action - The action name (e.g., "View", "Create", "Edit", "Delete")
 * @param children - The content to render if the user has permission
 * @param fallback - Optional content to render if the user doesn't have permission
 * @param storeId - Optional store ID to check store-specific permissions
 */
const PermissionGuard: React.FC<PermissionGuardProps> = ({
  module,
  action,
  children,
  fallback = null,
  storeId
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const { permissions } = useSelector((state: RootState) => state.userModulePermissions);
  const [loading, setLoading] = useState(true);
  const [hasPermission, setHasPermission] = useState(false);

  useEffect(() => {
    const checkPermissions = async () => {
      try {
        // Super admins have all permissions
        if (user?.roles.includes('SuperAdmin')) {
          setHasPermission(true);
          setLoading(false);
          return;
        }

        // If we don't have permissions in the store yet, fetch them
        if (permissions.length === 0) {
          await dispatch(fetchUserModulePermissions());
        }

        // Check if user has the required permission
        let userHasPermission = false;

        // First check user-specific module permissions
        const userModulePermissions = permissions.filter(p => p.userId === user?.id);

        // If storeId is provided, check store-specific permissions first
        if (storeId) {
          const storePermissions = userModulePermissions.filter(p => p.storeId === storeId);

          // Check if any store-specific permission matches the module and action
          for (const permission of storePermissions) {
            if (permission.module.toLowerCase() === module.toLowerCase()) {
              switch (action.toLowerCase()) {
                case 'view':
                  if (permission.canView) userHasPermission = true;
                  break;
                case 'create':
                  if (permission.canCreate) userHasPermission = true;
                  break;
                case 'edit':
                  if (permission.canEdit) userHasPermission = true;
                  break;
                case 'delete':
                  if (permission.canDelete) userHasPermission = true;
                  break;
              }
            }
          }
        }

        // If no store-specific permission found, check global permissions
        if (!userHasPermission) {
          const globalPermissions = userModulePermissions.filter(p => !p.storeId);

          // Check if any global permission matches the module and action
          for (const permission of globalPermissions) {
            if (permission.module.toLowerCase() === module.toLowerCase()) {
              switch (action.toLowerCase()) {
                case 'view':
                  if (permission.canView) userHasPermission = true;
                  break;
                case 'create':
                  if (permission.canCreate) userHasPermission = true;
                  break;
                case 'edit':
                  if (permission.canEdit) userHasPermission = true;
                  break;
                case 'delete':
                  if (permission.canDelete) userHasPermission = true;
                  break;
              }
            }
          }
        }

        // If still no permission found, check if user is an admin
        if (!userHasPermission && user?.roles.includes('Admin')) {
          userHasPermission = true;
        }

        setHasPermission(userHasPermission);
      } catch (error) {
        console.error('Error checking permissions:', error);
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      checkPermissions();
    } else {
      setLoading(false);
    }
  }, [user, module, action, storeId, permissions, dispatch]);

  if (loading) {
    return null;
  }

  return hasPermission ? <>{children}</> : <>{fallback}</>;
};

export default PermissionGuard;
