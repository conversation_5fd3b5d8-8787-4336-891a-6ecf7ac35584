# Common Components

This directory contains reusable components that can be used across the admin and client projects.

## Image-related Components

### ImageGallery

A component for displaying a grid of images with various features:
- Image preview in a modal
- Copy image URL
- Download image
- Delete image (if `onDelete` prop is provided)
- Infinite scrolling or pagination
- Customizable grid layout

```tsx
import { ImageGallery } from '../components/common';

// Example usage
<ImageGallery
  images={images}
  onDelete={handleDeleteImage}
  loading={loading}
  title="Image Gallery"
  showCopyButton={true}
  showDeleteButton={true}
  showDownloadButton={true}
  thumbnailSize="small"
  infiniteScroll={false}
  gridItemProps={{ xs: 6, sm: 4, md: 3, lg: 2 }}
  dialogSize="fullscreen"
/>
```

### ImageGallerySelector

A dialog component for selecting images from a gallery:
- Browse existing images
- Upload new images
- Search images
- Select single or multiple images

```tsx
import { ImageGallerySelector } from '../components/common';

// Example usage
<Button onClick={() => setGallerySelectorOpen(true)}>
  Select Image
</Button>

<ImageGallerySelector
  open={gallerySelectorOpen}
  onClose={() => setGallerySelectorOpen(false)}
  onSelect={handleImageSelect}
  title="Select Product Image"
  multiSelect={false}
/>
```

### BulkImageUploader

A component for uploading multiple images at once:
- Drag and drop support
- File preview
- Progress indicator
- File validation

```tsx
import { BulkImageUploader } from '../components/common';

// Example usage
<BulkImageUploader
  onImagesUploaded={handleImagesUploaded}
  maxFiles={20}
  title="Upload Multiple Images"
  showPreview={true}
/>
```

### ImageUpload

A simple component for uploading a single image:
- Image preview
- File selection
- Progress indicator

```tsx
import { ImageUpload } from '../components/common';

// Example usage
<ImageUpload
  currentImage={product.imageUrl}
  onImageUpload={handleImageUpload}
  loading={uploading}
  label="Upload Product Image"
/>
```

## Using Image Components in the Client Project

To use these components in the client project, import them from the `ImageComponents` barrel file:

```tsx
import { ImageGallery, ImageGallerySelector } from '../components/common/ImageComponents';
```

## Image Utilities

The `imageUtils.ts` file in both projects contains utilities for working with images:

- `getValidImageUrl`: Ensures image URLs are properly formatted
- `createImageErrorHandler`: Creates a fallback handler for image loading errors

```tsx
import { getValidImageUrl, createImageErrorHandler } from '../utils/imageUtils';

// Example usage
<img
  src={getValidImageUrl(product.imageUrl)}
  alt={product.name}
  onError={createImageErrorHandler()}
/>
```
