import React, { useState } from 'react';
import {
  Box,
  Button,
  CircularProgress,
  Typography,
  Paper
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  Image as ImageIcon
} from '@mui/icons-material';

interface ImageUploadProps {
  currentImage?: string;
  onImageUpload: (file: File) => void;
  loading?: boolean;
  label?: string;
  multiple?: boolean;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  currentImage,
  onImageUpload,
  loading = false,
  label = 'Upload Image',
  multiple = false
}) => {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];

      // Create preview URL
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);

      // Call the upload handler
      onImageUpload(file);
    }
  };

  return (
    <Box>
      {(currentImage || previewUrl) ? (
        <Paper
          elevation={2}
          sx={{
            p: 1,
            mb: 2,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center'
          }}
        >
          <Box
            component="img"
            src={previewUrl || currentImage}
            alt="Image preview"
            sx={{
              width: '100%',
              maxHeight: 200,
              objectFit: 'contain',
              borderRadius: 1,
              mb: 1
            }}
          />
          <input
            accept="image/*"
            style={{ display: 'none' }}
            id="image-upload-button"
            type="file"
            onChange={handleFileChange}
            disabled={loading}
            multiple={multiple}
          />
          <label htmlFor="image-upload-button">
            <Button
              variant="outlined"
              component="span"
              size="small"
              startIcon={loading ? <CircularProgress size={20} /> : <CloudUploadIcon />}
              disabled={loading}
            >
              Change Image
            </Button>
          </label>
        </Paper>
      ) : (
        <Paper
          elevation={2}
          sx={{
            p: 3,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: 200,
            border: '2px dashed #ccc',
            borderRadius: 2
          }}
        >
          <ImageIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
          <Typography variant="body1" gutterBottom>
            {label}
          </Typography>
          <input
            accept="image/*"
            style={{ display: 'none' }}
            id="image-upload-button"
            type="file"
            onChange={handleFileChange}
            disabled={loading}
            multiple={multiple}
          />
          <label htmlFor="image-upload-button">
            <Button
              variant="contained"
              component="span"
              startIcon={loading ? <CircularProgress size={20} /> : <CloudUploadIcon />}
              disabled={loading}
            >
              {loading ? 'Uploading...' : 'Select Image'}
            </Button>
          </label>
        </Paper>
      )}
    </Box>
  );
};

export default ImageUpload;
