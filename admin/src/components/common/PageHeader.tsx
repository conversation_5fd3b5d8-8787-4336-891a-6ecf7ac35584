// Save this as /Users/<USER>/Desktop/JaipurCottonHouseApp/JaipurCottonHouse/admin/src/components/common/PageHeader.tsx

import React from 'react';
import { Box, Typography, IconButton, Paper } from '@mui/material';

interface PageHeaderProps {
    title: string;
    subtitle?: string;
    icon?: React.ReactNode;
    onIconClick?: () => void;
    action?: React.ReactNode;
}

const PageHeader: React.FC<PageHeaderProps> = ({
    title,
    subtitle,
    icon,
    onIconClick,
    action
}) => {
    return (
        <Paper
            elevation={0}
            sx={{
                p: 2,
                mb: 3,
                display: 'flex',
                alignItems: 'center',
                backgroundColor: 'transparent',
                borderBottom: '1px solid #e0e0e0'
            }}
        >
            {icon && (
                <IconButton
                    onClick={onIconClick}
                    sx={{ mr: 2 }}
                    size="small"
                >
                    {icon}
                </IconButton>
            )}
            <Box sx={{ flexGrow: 1 }}>
                <Typography variant="h5" component="h1">
                    {title}
                </Typography>
                {subtitle && (
                    <Typography variant="body2" color="text.secondary">
                        {subtitle}
                    </Typography>
                )}
            </Box>
            {action && (
                <Box>
                    {action}
                </Box>
            )}
        </Paper>
    );
};

export default PageHeader;