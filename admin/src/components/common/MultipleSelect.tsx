import React from 'react';
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  ListItemText,
  OutlinedInput,
  SelectChangeEvent,
  Chip,
  Box
} from '@mui/material';

interface Option {
  id: number;
  name: string;
}

interface MultipleSelectProps {
  label: string;
  options: Option[];
  selectedIds: number[];
  onChange: (selectedIds: number[]) => void;
}

const MultipleSelect: React.FC<MultipleSelectProps> = ({
  label,
  options,
  selectedIds,
  onChange
}) => {
  const handleChange = (event: SelectChangeEvent<number[]>) => {
    const value = event.target.value as number[];
    onChange(value);
  };

  return (
    <FormControl fullWidth>
      <InputLabel id={`multiple-select-${label}-label`}>{label}</InputLabel>
      <Select
        labelId={`multiple-select-${label}-label`}
        id={`multiple-select-${label}`}
        multiple
        value={selectedIds}
        onChange={handleChange}
        input={<OutlinedInput label={label} />}
        renderValue={(selected) => (
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
            {selected.map((id) => {
              const option = options.find(opt => opt.id === id);
              return option ? (
                <Chip key={id} label={option.name} size="small" />
              ) : null;
            })}
          </Box>
        )}
      >
        {options.map((option) => (
          <MenuItem key={option.id} value={option.id}>
            <Checkbox checked={selectedIds.indexOf(option.id) > -1} />
            <ListItemText primary={option.name} />
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

export default MultipleSelect;
