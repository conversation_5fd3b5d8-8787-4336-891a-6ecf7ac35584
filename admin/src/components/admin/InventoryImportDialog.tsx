import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  CircularProgress,
  Alert,
  AlertTitle,
  List,
  ListItem,
  ListItemText,
  Divider,
  Paper
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  Check as CheckIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import { AppDispatch, RootState } from '../../store';
import { importInventory, clearImportResult } from '../../store/slices/inventorySlice';
import { toast } from 'react-toastify';

interface InventoryImportDialogProps {
  open: boolean;
  onClose: (success?: boolean) => void;
  storeId?: number;
}

const InventoryImportDialog: React.FC<InventoryImportDialogProps> = ({ open, onClose, storeId }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { loading, error, importResult } = useSelector((state: RootState) => state.inventory);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      setSelectedFile(event.target.files[0]);
    }
  };

  const handleImport = async () => {
    if (!selectedFile) {
      toast.error('Please select a file to import');
      return;
    }

    try {
      await dispatch(importInventory({ file: selectedFile, storeId })).unwrap();
      toast.success('Inventory import started');
    } catch (error) {
      // Error is handled by the reducer and displayed in the UI
    }
  };

  const handleClose = () => {
    setSelectedFile(null);
    dispatch(clearImportResult());
    onClose(!!importResult);
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>Import Inventory</DialogTitle>
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            <AlertTitle>Error</AlertTitle>
            {error}
          </Alert>
        )}

        {!importResult ? (
          <Box sx={{ p: 2 }}>
            <Typography variant="body1" gutterBottom>
              Upload an Excel file (.xlsx) with inventory data. The file should have the following columns:
            </Typography>
            <Paper variant="outlined" sx={{ p: 2, mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom>Required columns:</Typography>
              <List dense>
                <ListItem>
                  <ListItemText primary="Product Name" secondary="Name of the product" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="SKU" secondary="Product SKU (must match existing SKUs)" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Location" secondary="Location name (must match existing locations)" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Stock Quantity" secondary="Current stock quantity" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Reorder Level" secondary="Minimum stock level before reordering" />
                </ListItem>
              </List>
            </Paper>

            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mt: 2 }}>
              <input
                accept=".xlsx"
                style={{ display: 'none' }}
                id="import-file"
                type="file"
                onChange={handleFileChange}
              />
              <label htmlFor="import-file">
                <Button
                  variant="outlined"
                  component="span"
                  startIcon={<CloudUploadIcon />}
                  sx={{ mb: 2 }}
                >
                  Select File
                </Button>
              </label>
              {selectedFile && (
                <Typography variant="body2" color="textSecondary">
                  Selected file: {selectedFile.name}
                </Typography>
              )}
            </Box>
          </Box>
        ) : (
          <Box sx={{ p: 2 }}>
            <Alert 
              severity={importResult.errors && importResult.errors.length > 0 ? "warning" : "success"}
              icon={importResult.errors && importResult.errors.length > 0 ? <ErrorIcon /> : <CheckIcon />}
              sx={{ mb: 2 }}
            >
              <AlertTitle>
                {importResult.errors && importResult.errors.length > 0 
                  ? "Import Completed with Warnings" 
                  : "Import Successful"}
              </AlertTitle>
              {importResult.updatedCount} items were updated successfully.
            </Alert>

            {importResult.errors && importResult.errors.length > 0 && (
              <Box sx={{ mt: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  The following errors occurred:
                </Typography>
                <Paper variant="outlined" sx={{ maxHeight: 200, overflow: 'auto' }}>
                  <List dense>
                    {importResult.errors.map((error: string, index: number) => (
                      <React.Fragment key={index}>
                        <ListItem>
                          <ListItemText primary={error} />
                        </ListItem>
                        {index < importResult.errors.length - 1 && <Divider />}
                      </React.Fragment>
                    ))}
                  </List>
                </Paper>
              </Box>
            )}
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>
          {importResult ? 'Close' : 'Cancel'}
        </Button>
        {!importResult && (
          <Button
            onClick={handleImport}
            variant="contained"
            color="primary"
            disabled={!selectedFile || loading}
            startIcon={loading ? <CircularProgress size={20} /> : <CloudUploadIcon />}
          >
            {loading ? 'Importing...' : 'Import'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default InventoryImportDialog;
