import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  Typography,
  CircularProgress,
  Box,
  Alert
} from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../store';
import { InventoryItem, updateInventoryItem, clearInventoryError } from '../../store/slices/inventorySlice';

interface InventoryEditFormProps {
  open: boolean;
  onClose: () => void;
  item: InventoryItem | null;
  onSuccess: () => void;
}

const InventoryEditForm: React.FC<InventoryEditFormProps> = ({ open, onClose, item, onSuccess }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { loading, error } = useSelector((state: RootState) => state.inventory);
  
  const [formData, setFormData] = useState({
    stockQuantity: 0,
    reorderLevel: 0
  });

  useEffect(() => {
    if (item) {
      setFormData({
        stockQuantity: item.stockQuantity,
        reorderLevel: item.reorderLevel
      });
    }
  }, [item]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: parseInt(value, 10) || 0
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!item) return;

    try {
      await dispatch(updateInventoryItem({
        id: item.id,
        stockQuantity: formData.stockQuantity,
        reorderLevel: formData.reorderLevel
      })).unwrap();
      
      onSuccess();
      onClose();
    } catch (err) {
      // Error is handled by the reducer
    }
  };

  const handleClose = () => {
    dispatch(clearInventoryError());
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        Update Inventory
      </DialogTitle>
      <form onSubmit={handleSubmit}>
        <DialogContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          
          {item && (
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Typography variant="subtitle1">
                  Location: {item.locationName} ({item.locationCode})
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Stock Quantity"
                  name="stockQuantity"
                  type="number"
                  value={formData.stockQuantity}
                  onChange={handleChange}
                  required
                  InputProps={{ inputProps: { min: 0 } }}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Reorder Level"
                  name="reorderLevel"
                  type="number"
                  value={formData.reorderLevel}
                  onChange={handleChange}
                  required
                  InputProps={{ inputProps: { min: 0 } }}
                  helperText="Minimum stock level before reordering"
                />
              </Grid>
            </Grid>
          )}
          
          {!item && (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
              <CircularProgress />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose} disabled={loading}>
            Cancel
          </Button>
          <Button 
            type="submit" 
            variant="contained" 
            color="primary" 
            disabled={loading || !item}
          >
            {loading ? <CircularProgress size={24} /> : 'Save'}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default InventoryEditForm;
