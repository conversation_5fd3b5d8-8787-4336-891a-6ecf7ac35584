import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Box,
  Button,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Tooltip,
  CircularProgress,
  Alert
} from '@mui/material';
import { Add, Edit, Delete, Visibility } from '@mui/icons-material';
import { AppDispatch, RootState } from '../../../store';
import { ProductVariant } from '../../../store/slices/productSlice';
import {
  fetchProductVariants,
  deleteProductVariant
} from '../../../store/slices/productVariantSlice';
import ProductVariantForm from './ProductVariantForm';

interface ProductVariantsManagerProps {
  productId: number;
  productName: string;
}

const ProductVariantsManager: React.FC<ProductVariantsManagerProps> = ({
  productId,
  productName
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const { variants, loading, error } = useSelector((state: RootState) => state.productVariants);

  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [variantToDelete, setVariantToDelete] = useState<ProductVariant | null>(null);

  useEffect(() => {
    loadVariants();
  }, [productId]);

  const loadVariants = () => {
    dispatch(fetchProductVariants(productId));
  };

  const handleAddVariant = () => {
    setSelectedVariant(null);
    setIsFormOpen(true);
  };

  const handleEditVariant = (variant: ProductVariant) => {
    setSelectedVariant(variant);
    setIsFormOpen(true);
  };

  const handleDeleteClick = (variant: ProductVariant) => {
    setVariantToDelete(variant);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (variantToDelete) {
      try {
        await dispatch(deleteProductVariant({
          productId,
          variantId: variantToDelete.id
        })).unwrap();
        setDeleteDialogOpen(false);
        setVariantToDelete(null);
      } catch (error) {
        console.error('Error deleting variant:', error);
      }
    }
  };

  const handleFormClose = () => {
    setIsFormOpen(false);
    setSelectedVariant(null);
  };

  const handleFormSuccess = () => {
    setIsFormOpen(false);
    setSelectedVariant(null);
    loadVariants();
  };

  const renderAttributeChips = (attributes: Record<string, string>) => {
    return Object.entries(attributes).map(([key, value]) => (
      <Chip
        key={key}
        label={`${key}: ${value}`}
        size="small"
        sx={{ mr: 0.5, mb: 0.5 }}
      />
    ));
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          Product Variants for {productName}
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<Add />}
          onClick={handleAddVariant}
        >
          Add Variant
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {loading && variants.length === 0 ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : variants.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body1" color="text.secondary">
            No variants found for this product. Click "Add Variant" to create one.
          </Typography>
        </Paper>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>SKU</TableCell>
                <TableCell>Attributes</TableCell>
                <TableCell align="right">Price</TableCell>
                <TableCell align="right">Stock</TableCell>
                <TableCell align="center">Status</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {variants.map((variant) => (
                <TableRow key={variant.id}>
                  <TableCell>{variant.sku}</TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap' }}>
                      {renderAttributeChips(variant.variantAttributes)}
                    </Box>
                  </TableCell>
                  <TableCell align="right">${variant.price.toFixed(2)}</TableCell>
                  <TableCell align="right">{variant.stockQuantity}</TableCell>
                  <TableCell align="center">
                    <Chip
                      label={variant.isActive ? 'Active' : 'Inactive'}
                      color={variant.isActive ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell align="right">
                    <Tooltip title="Edit">
                      <IconButton onClick={() => handleEditVariant(variant)} size="small">
                        <Edit fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Delete">
                      <IconButton onClick={() => handleDeleteClick(variant)} size="small" color="error">
                        <Delete fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Variant Form Dialog */}
      <Dialog
        open={isFormOpen}
        onClose={handleFormClose}
        maxWidth="md"
        fullWidth
      >
        <DialogContent>
          <ProductVariantForm
            productId={productId}
            variant={selectedVariant || undefined}
            onSubmitSuccess={handleFormSuccess}
            onCancel={handleFormClose}
          />
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Delete Variant</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the variant with SKU: {variantToDelete?.sku}?
            This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleDeleteConfirm} color="error">Delete</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ProductVariantsManager;
