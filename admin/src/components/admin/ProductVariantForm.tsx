import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { v4 as uuidv4 } from 'uuid';
import {
  Box,
  Button,
  TextField,
  Grid,
  Typography,
  Paper,
  Switch,
  FormControlLabel,
  Divider,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import { Add, Delete, Image } from '@mui/icons-material';
import { toast } from 'react-toastify';
import { AppDispatch, RootState } from '../../../store';
import { ProductVariant } from '../../../store/slices/productSlice';
import {
  createProductVariant,
  updateProductVariant,
  CreateProductVariantDto,
  UpdateProductVariantDto
} from '../../../store/slices/productVariantSlice';
import ImageUploader from './ImageUploader';

interface ProductVariantFormProps {
  productId: number;
  variant?: ProductVariant;
  onSubmitSuccess: () => void;
  onCancel: () => void;
}

const ProductVariantForm: React.FC<ProductVariantFormProps> = ({
  productId,
  variant,
  onSubmitSuccess,
  onCancel
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const { loading } = useSelector((state: RootState) => state.productVariants);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [formData, setFormData] = useState<CreateProductVariantDto | UpdateProductVariantDto>({
    sku: '',
    barcode: '',
    price: 0,
    cost: 0,
    mrp: 0,
    stockQuantity: 0,
    reorderLevel: 10,
    isActive: true,
    productId: productId,
    // Physical attributes
    size: '',
    weight: 0,
    weightUnit: 'kg',
    length: 0,
    breadth: 0,
    height: 0,
    dimensionUnit: 'cm',
    volume: 0,
    volumeUnit: 'cm3',
    variantAttributes: {},
    images: [],
    locationId: 1 // Default location ID is always 1
  });

  const [attributeKey, setAttributeKey] = useState('');
  const [attributeValue, setAttributeValue] = useState('');
  const [attributeDialogOpen, setAttributeDialogOpen] = useState(false);
  const [uploadedImages, setUploadedImages] = useState<{ url: string; isMain: boolean; displayOrder?: number }[]>([]);

  // Location fetching removed

  useEffect(() => {
    if (variant) {
      // Always use default location ID 1
      const locationId = 1;

      setFormData({
        sku: variant.sku,
        barcode: variant.barcode || '',
        price: variant.price,
        cost: variant.cost || 0,
        mrp: variant.mrp || variant.price * 1.2,
        stockQuantity: variant.stockQuantity,
        reorderLevel: variant.reorderLevel || 10,
        isActive: variant.isActive,
        // Physical attributes
        size: variant.size || '',
        weight: variant.weight || 0,
        weightUnit: variant.weightUnit || 'kg',
        length: variant.length || 0,
        breadth: variant.breadth || 0,
        height: variant.height || 0,
        dimensionUnit: variant.dimensionUnit || 'cm',
        volume: variant.volume || 0,
        volumeUnit: variant.volumeUnit || 'cm3',
        variantAttributes: variant.variantAttributes || {},
        images: variant.images?.map(img => ({
          url: img.imageUrl || img.url,
          isMain: img.isMain,
          displayOrder: img.displayOrder
        })) || [],
        locationId: locationId
      });

      if (variant.images) {
        setUploadedImages(variant.images.map(img => ({
          url: img.imageUrl || img.url,
          isMain: img.isMain,
          displayOrder: img.displayOrder
        })));
      }
    } else {
      // Set default values for new variant
      setFormData({
        sku: '',
        barcode: '',
        price: 0,
        cost: 0,
        mrp: 0,
        stockQuantity: 0,
        reorderLevel: 10,
        isActive: true,
        productId: productId,
        // Physical attributes
        size: '',
        weight: 0,
        weightUnit: 'kg',
        length: 0,
        breadth: 0,
        height: 0,
        dimensionUnit: 'cm',
        volume: 0,
        volumeUnit: 'cm3',
        variantAttributes: {},
        images: [],
        locationId: 1 // Default location ID
      });
    }
  }, [variant, productId]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : type === 'number' ? parseFloat(value) : value
    });
  };

  const handleAddAttribute = () => {
    if (attributeKey && attributeValue) {
      setFormData({
        ...formData,
        variantAttributes: {
          ...formData.variantAttributes,
          [attributeKey]: attributeValue
        }
      });
      setAttributeKey('');
      setAttributeValue('');
      setAttributeDialogOpen(false);
    }
  };

  const handleRemoveAttribute = (key: string) => {
    const newAttributes = { ...formData.variantAttributes };
    delete newAttributes[key];
    setFormData({
      ...formData,
      variantAttributes: newAttributes
    });
  };

  const handleImagesChange = (images: { url: string; isMain: boolean; displayOrder?: number }[]) => {
    setUploadedImages(images);
    setFormData({
      ...formData,
      images
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Ensure all required fields are present
      const payload = {
        ...formData,
        // Convert images to the format expected by the API
        images: formData.images?.map(img => ({
          imageUrl: img.url,
          isMain: img.isMain,
          displayOrder: img.displayOrder || 0
        })),
        // Ensure variant attributes are properly formatted
        variantAttributes: formData.variantAttributes || {}
      };

      // Log the payload for debugging
      console.log('Saving variant with payload:', payload);

      if (variant) {
        // Update existing variant
        await dispatch(updateProductVariant({
          productId: productId,
          variantId: variant.id,
          variant: payload as UpdateProductVariantDto
        })).unwrap();
        toast.success('Variant updated successfully');
      } else {
        // Create new variant
        await dispatch(createProductVariant({
          productId: productId,
          variant: payload as CreateProductVariantDto
        })).unwrap();
        toast.success('Variant created successfully');
      }

      onSubmitSuccess();
    } catch (error) {
      console.error('Error saving product variant:', error);
      toast.error('Failed to save variant');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Paper sx={{ p: 3 }}>
      <Typography variant="h6" gutterBottom>
        {variant ? 'Edit Product Variant' : 'Add Product Variant'}
      </Typography>

      <form onSubmit={handleSubmit}>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="SKU"
              name="sku"
              value={formData.sku}
              onChange={handleChange}
              required
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Barcode"
              name="barcode"
              value={formData.barcode}
              onChange={handleChange}
            />
          </Grid>

          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="MRP (Maximum Retail Price)"
              name="mrp"
              type="number"
              value={formData.mrp}
              onChange={handleChange}
              required
              inputProps={{ min: 0, step: 0.01 }}
              helperText="Original price before discount"
            />
          </Grid>

          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="Selling Price"
              name="price"
              type="number"
              value={formData.price}
              onChange={handleChange}
              required
              inputProps={{ min: 0, step: 0.01 }}
              helperText="Actual selling price"
            />
          </Grid>

          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="Cost Price"
              name="cost"
              type="number"
              value={formData.cost}
              onChange={handleChange}
              inputProps={{ min: 0, step: 0.01 }}
              helperText="Your cost (not shown to customers)"
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Size"
              name="size"
              value={formData.size}
              onChange={handleChange}
              helperText="Size of the variant (e.g., S, M, L, XL)"
            />
          </Grid>

          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="Stock Quantity"
              name="stockQuantity"
              type="number"
              value={formData.stockQuantity}
              onChange={handleChange}
              required
              inputProps={{ min: 0, step: 1 }}
            />
          </Grid>

          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="Reorder Level"
              name="reorderLevel"
              type="number"
              value={formData.reorderLevel}
              onChange={handleChange}
              inputProps={{ min: 0, step: 1 }}
            />
          </Grid>

          {/* Location dropdown removed - always using default location ID 1 */}

          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom>Physical Attributes</Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="Weight"
                  name="weight"
                  type="number"
                  value={formData.weight}
                  onChange={handleChange}
                  inputProps={{ min: 0, step: 0.01 }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  select
                  fullWidth
                  label="Weight Unit"
                  name="weightUnit"
                  value={formData.weightUnit}
                  onChange={handleChange}
                >
                  <MenuItem value="kg">kg</MenuItem>
                  <MenuItem value="g">g</MenuItem>
                  <MenuItem value="lb">lb</MenuItem>
                  <MenuItem value="oz">oz</MenuItem>
                </TextField>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="Length"
                  name="length"
                  type="number"
                  value={formData.length}
                  onChange={handleChange}
                  inputProps={{ min: 0, step: 0.01 }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="Breadth"
                  name="breadth"
                  type="number"
                  value={formData.breadth}
                  onChange={handleChange}
                  inputProps={{ min: 0, step: 0.01 }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="Height"
                  name="height"
                  type="number"
                  value={formData.height}
                  onChange={handleChange}
                  inputProps={{ min: 0, step: 0.01 }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  select
                  fullWidth
                  label="Dimension Unit"
                  name="dimensionUnit"
                  value={formData.dimensionUnit}
                  onChange={handleChange}
                >
                  <MenuItem value="cm">cm</MenuItem>
                  <MenuItem value="mm">mm</MenuItem>
                  <MenuItem value="in">in</MenuItem>
                </TextField>
              </Grid>
            </Grid>
          </Grid>

          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Switch
                  checked={formData.isActive as boolean}
                  onChange={handleChange}
                  name="isActive"
                  color="primary"
                />
              }
              label="Active"
            />
          </Grid>

          <Grid item xs={12}>
            <Divider sx={{ my: 2 }} />
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="subtitle1">Variant Attributes</Typography>
              <Button
                startIcon={<Add />}
                variant="outlined"
                size="small"
                onClick={() => setAttributeDialogOpen(true)}
              >
                Add Attribute
              </Button>
            </Box>

            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {Object.entries(formData.variantAttributes || {}).map(([key, value]) => (
                <Chip
                  key={key}
                  label={`${key}: ${value}`}
                  onDelete={() => handleRemoveAttribute(key)}
                  color="primary"
                  variant="outlined"
                />
              ))}
              {Object.keys(formData.variantAttributes || {}).length === 0 && (
                <Typography variant="body2" color="text.secondary">
                  No attributes added yet. Click "Add Attribute" to add variant attributes like Size, Color, etc.
                </Typography>
              )}
            </Box>
          </Grid>

          <Grid item xs={12}>
            <Divider sx={{ my: 2 }} />
            <Typography variant="subtitle1" gutterBottom>Product Images</Typography>
            <ImageUploader
              images={uploadedImages}
              onChange={handleImagesChange}
            />
          </Grid>

          <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 3 }}>
            <Button variant="outlined" onClick={onCancel}>
              Cancel
            </Button>
            <Button
              type="submit"
              variant="contained"
              color="primary"
              disabled={loading}
            >
              {variant ? 'Update Variant' : 'Add Variant'}
            </Button>
          </Grid>
        </Grid>
      </form>

      {/* Attribute Dialog */}
      <Dialog open={attributeDialogOpen} onClose={() => setAttributeDialogOpen(false)}>
        <DialogTitle>Add Variant Attribute</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Attribute Name (e.g., Size, Color)"
            fullWidth
            value={attributeKey}
            onChange={(e) => setAttributeKey(e.target.value)}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="Attribute Value (e.g., XL, Red)"
            fullWidth
            value={attributeValue}
            onChange={(e) => setAttributeValue(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAttributeDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleAddAttribute} color="primary">Add</Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default ProductVariantForm;
