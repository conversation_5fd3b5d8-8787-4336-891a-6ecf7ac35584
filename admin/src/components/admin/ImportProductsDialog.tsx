import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  CircularProgress,
  Alert,
  AlertTitle,
  List,
  ListItem,
  ListItemText,
  Divider,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  Check as CheckIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import { AppDispatch, RootState } from '../../store';
import { importProducts } from '../../store/slices/productSlice';
import { toast } from 'react-toastify';

interface ImportProductsDialogProps {
  open: boolean;
  onClose: (success?: boolean) => void;
}

const ImportProductsDialog: React.FC<ImportProductsDialogProps> = ({ open, onClose }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { loading, importResult } = useSelector((state: RootState) => state.products);
  const { selectedStore, accessibleStores } = useSelector((state: RootState) => state.store);
  const { user } = useSelector((state: RootState) => state.auth);
  const isSuperAdmin = user?.roles.includes('SuperAdmin');

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [storeId, setStoreId] = useState<number | undefined>(selectedStore?.id);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      setSelectedFile(event.target.files[0]);
    }
  };

  const handleStoreChange = (event: SelectChangeEvent<number>) => {
    setStoreId(event.target.value as number);
  };

  const handleImport = async () => {
    if (!selectedFile) return;

    try {
      const formData = new FormData();
      formData.append('file', selectedFile);

      await dispatch(importProducts({ formData, storeId })).unwrap();
      toast.success('Products imported successfully');
    } catch (error) {
      toast.error('Failed to import products');
      console.error('Error importing products:', error);
    }
  };

  const handleClose = () => {
    setSelectedFile(null);
    onClose(importResult?.success);
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>Import Products</DialogTitle>
      <DialogContent>
        {!importResult ? (
          <Box sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Upload Product Data
            </Typography>
            <Typography variant="body1" paragraph>
              Upload an Excel file (.xlsx) containing product data. Make sure to use the correct format.
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              You can download a template from the product management page.
            </Typography>

            {/* Store selector for super admins */}
            {isSuperAdmin && accessibleStores.length > 1 && (
              <Box sx={{ mb: 3 }}>
                <FormControl fullWidth variant="outlined" size="small">
                  <InputLabel id="store-select-label">Select Store</InputLabel>
                  <Select
                    labelId="store-select-label"
                    id="store-select"
                    value={storeId || ''}
                    onChange={handleStoreChange}
                    label="Select Store"
                    disabled={loading}
                  >
                    {accessibleStores.map((store) => (
                      <MenuItem key={store.id} value={store.id}>
                        {store.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
                <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                  Products will be imported to the selected store
                </Typography>
              </Box>
            )}

            <Box sx={{ my: 3 }}>
              <input
                accept=".xlsx, .xls"
                style={{ display: 'none' }}
                id="import-products-file"
                type="file"
                onChange={handleFileChange}
                disabled={loading}
              />
              <label htmlFor="import-products-file">
                <Button
                  variant="outlined"
                  component="span"
                  startIcon={<CloudUploadIcon />}
                  disabled={loading}
                >
                  Select File
                </Button>
              </label>
              {selectedFile && (
                <Typography variant="body2" sx={{ mt: 1 }}>
                  Selected file: {selectedFile.name}
                </Typography>
              )}
            </Box>

            <Alert severity="info" sx={{ mt: 2 }}>
              <AlertTitle>Important</AlertTitle>
              <Typography variant="body2">
                - The first row should contain column headers<br />
                - Required columns: Name, Description, Price, CategoryId<br />
                - SKU must be unique for each product<br />
                - Make sure category IDs exist in the system
              </Typography>
            </Alert>
          </Box>
        ) : (
          <Box sx={{ p: 2 }}>
            <Alert severity={importResult.success ? "success" : "error"} sx={{ mb: 3 }}>
              <AlertTitle>{importResult.success ? "Import Successful" : "Import Failed"}</AlertTitle>
              {importResult.message}
            </Alert>

            {importResult.importedProducts && importResult.importedProducts.length > 0 && (
              <Paper sx={{ mb: 3, p: 2 }}>
                <Typography variant="h6" gutterBottom>
                  Successfully Imported Products ({importResult.importedProducts.length})
                </Typography>
                <Divider sx={{ mb: 2 }} />
                <List dense>
                  {importResult.importedProducts.slice(0, 5).map((product: any, index: number) => (
                    <ListItem key={index}>
                      <ListItemText
                        primary={product.name}
                        secondary={`SKU: ${product.sku || 'N/A'} | Price: $${product.price}`}
                      />
                      <CheckIcon color="success" />
                    </ListItem>
                  ))}
                  {importResult.importedProducts.length > 5 && (
                    <ListItem>
                      <ListItemText
                        primary={`And ${importResult.importedProducts.length - 5} more products...`}
                      />
                    </ListItem>
                  )}
                </List>
              </Paper>
            )}

            {importResult.errors && importResult.errors.length > 0 && (
              <Paper sx={{ p: 2 }}>
                <Typography variant="h6" gutterBottom color="error">
                  Errors ({importResult.errors.length})
                </Typography>
                <Divider sx={{ mb: 2 }} />
                <List dense>
                  {importResult.errors.map((error: string, index: number) => (
                    <ListItem key={index}>
                      <ErrorIcon color="error" sx={{ mr: 1 }} />
                      <ListItemText primary={error} />
                    </ListItem>
                  ))}
                </List>
              </Paper>
            )}
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        {!importResult ? (
          <>
            <Button onClick={handleClose}>Cancel</Button>
            <Button
              variant="contained"
              color="primary"
              startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <CloudUploadIcon />}
              onClick={handleImport}
              disabled={loading || !selectedFile}
            >
              {loading ? 'Importing...' : 'Import Products'}
            </Button>
          </>
        ) : (
          <Button onClick={handleClose}>Close</Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default ImportProductsDialog;
