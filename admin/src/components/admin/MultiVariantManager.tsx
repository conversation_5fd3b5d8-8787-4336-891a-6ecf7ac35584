import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Box,
  Button,
  Typography,
  Paper,
  Tabs,
  Tab,
  IconButton,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Grid,
  FormControlLabel,
  Switch,
  Chip,
  CircularProgress,
  Alert,
  InputAdornment
} from '@mui/material';
import { Add, Delete, Close, Save } from '@mui/icons-material';
import { AppDispatch, RootState } from '../../store';
import { Product, ProductVariant } from '../../store/slices/productSlice';
import {
  fetchProductVariants,
  createProductVariant,
  updateProductVariant,
  deleteProductVariant,
  CreateProductVariantDto,
  UpdateProductVariantDto
} from '../../store/slices/productVariantSlice';
import ImageUploader from './ImageUploader';

// Helper function to generate a UUID
const uuidv4 = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0, v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`variant-tabpanel-${index}`}
      aria-labelledby={`variant-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

interface VariantData {
  id?: number;
  tempId: string;
  sku: string;
  barcode: string;
  price: number;
  cost: number;
  mrp: number;
  stockQuantity: number;
  reorderLevel: number;
  isActive: boolean;
  variantAttributes: Record<string, string>;
  weight: number;
  weightUnit: string;
  length: number;
  breadth: number;
  height: number;
  dimensionUnit: string;
  volume: number;
  volumeUnit: string;
  images: { url: string; isMain: boolean; displayOrder?: number }[];
}

interface MultiVariantManagerProps {
  product: Product;
  onSave: () => void;
  onCancel: () => void;
}

const MultiVariantManager: React.FC<MultiVariantManagerProps> = ({
  product,
  onSave,
  onCancel
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const { variants, loading, error } = useSelector((state: RootState) => state.productVariants);

  const [tabValue, setTabValue] = useState(0);
  const [variantsList, setVariantsList] = useState<VariantData[]>([]);
  const [attributeKey, setAttributeKey] = useState('');
  const [attributeValue, setAttributeValue] = useState('');
  const [attributeDialogOpen, setAttributeDialogOpen] = useState(false);
  const [confirmDeleteOpen, setConfirmDeleteOpen] = useState(false);
  const [variantToDelete, setVariantToDelete] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);

  // Common size options
  const sizeOptions = ['XS', 'S', 'M', 'L', 'XL', '2XL', '3XL', '4XL', '5XL'];
  const colorOptions = ['Red', 'Blue', 'Green', 'Black', 'White', 'Yellow', 'Purple', 'Orange', 'Pink', 'Grey'];

  useEffect(() => {
    dispatch(fetchProductVariants(product.id));
  }, [dispatch, product.id]);

  useEffect(() => {
    if (variants.length > 0) {
      const mappedVariants = variants.map(variant => ({
        id: variant.id,
        tempId: variant.id.toString(),
        sku: variant.sku,
        barcode: variant.barcode || '',
        price: variant.price,
        cost: variant.cost || 0,
        mrp: variant.price * 1.2, // Default MRP if not available
        stockQuantity: variant.stockQuantity,
        reorderLevel: variant.reorderLevel || 10,
        isActive: variant.isActive,
        variantAttributes: variant.variantAttributes || {},
        weight: 0.5, // Default values
        weightUnit: 'kg',
        length: 30,
        breadth: 20,
        height: 5,
        dimensionUnit: 'cm',
        volume: 3000,
        volumeUnit: 'cm3',
        images: variant.images?.map(img => ({
          url: img.imageUrl,
          isMain: img.isMain,
          displayOrder: img.displayOrder
        })) || []
      }));
      setVariantsList(mappedVariants);
    } else {
      // Add a default empty variant if none exist
      addNewVariant();
    }
  }, [variants]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const addNewVariant = () => {
    const newVariant: VariantData = {
      tempId: uuidv4(),
      sku: `${product.sku || 'SKU'}-${variantsList.length + 1}`,
      barcode: '',
      price: product.price,
      cost: product.cost || 0,
      mrp: product.price * 1.2,
      stockQuantity: 100,
      reorderLevel: 10,
      isActive: true,
      variantAttributes: { 'Size': sizeOptions[variantsList.length % sizeOptions.length] },
      weight: 0.5,
      weightUnit: 'kg',
      length: 30,
      breadth: 20,
      height: 5,
      dimensionUnit: 'cm',
      volume: 3000,
      volumeUnit: 'cm3',
      images: []
    };

    setVariantsList([...variantsList, newVariant]);
    setTabValue(variantsList.length);
  };

  const handleDeleteVariant = (tempId: string) => {
    setVariantToDelete(tempId);
    setConfirmDeleteOpen(true);
  };

  const confirmDeleteVariant = async () => {
    if (!variantToDelete) return;

    const variantIndex = variantsList.findIndex(v => v.tempId === variantToDelete);
    if (variantIndex === -1) return;

    const variant = variantsList[variantIndex];

    // If it's an existing variant (has an ID), delete it from the server
    if (variant.id) {
      try {
        await dispatch(deleteProductVariant(variant.id)).unwrap();
      } catch (error) {
        console.error('Error deleting variant:', error);
      }
    }

    // Remove from local state
    const newList = variantsList.filter(v => v.tempId !== variantToDelete);
    setVariantsList(newList);

    // Adjust tab value if needed
    if (tabValue >= newList.length) {
      setTabValue(Math.max(0, newList.length - 1));
    }

    setConfirmDeleteOpen(false);
    setVariantToDelete(null);
  };

  const handleVariantChange = (tempId: string, field: keyof VariantData, value: any) => {
    setVariantsList(prevList =>
      prevList.map(variant =>
        variant.tempId === tempId
          ? { ...variant, [field]: value }
          : variant
      )
    );
  };

  const handleAttributeChange = (tempId: string, key: string, value: string) => {
    setVariantsList(prevList =>
      prevList.map(variant =>
        variant.tempId === tempId
          ? {
              ...variant,
              variantAttributes: {
                ...variant.variantAttributes,
                [key]: value
              }
            }
          : variant
      )
    );
  };

  const handleVariantAttributeChange = (tempId: string, key: string, value: string) => {
    handleAttributeChange(tempId, key, value);
  };

  const handleAddAttribute = (tempId: string) => {
    if (attributeKey && attributeValue) {
      handleAttributeChange(tempId, attributeKey, attributeValue);
      setAttributeKey('');
      setAttributeValue('');
      setAttributeDialogOpen(false);
    }
  };

  const handleRemoveAttribute = (tempId: string, key: string) => {
    setVariantsList(prevList =>
      prevList.map(variant => {
        if (variant.tempId === tempId) {
          const newAttributes = { ...variant.variantAttributes };
          delete newAttributes[key];
          return { ...variant, variantAttributes: newAttributes };
        }
        return variant;
      })
    );
  };

  const handleImagesChange = (tempId: string, images: { url: string; isMain: boolean; displayOrder?: number }[]) => {
    handleVariantChange(tempId, 'images', images);
  };

  const handleSaveAll = async () => {
    setSaving(true);

    try {
      // Process each variant
      for (const variant of variantsList) {
        const variantData = {
          sku: variant.sku,
          barcode: variant.barcode,
          price: variant.price,
          cost: variant.cost,
          mrp: variant.mrp,
          stockQuantity: variant.stockQuantity,
          reorderLevel: variant.reorderLevel,
          isActive: variant.isActive,
          variantAttributes: variant.variantAttributes,
          weight: variant.weight,
          weightUnit: variant.weightUnit,
          length: variant.length,
          breadth: variant.breadth,
          height: variant.height,
          dimensionUnit: variant.dimensionUnit,
          volume: variant.volume,
          volumeUnit: variant.volumeUnit,
          productId: product.id,
          images: variant.images
        };

        if (variant.id) {
          // Update existing variant
          await dispatch(updateProductVariant({
            id: variant.id,
            variantData: variantData as UpdateProductVariantDto
          })).unwrap();
        } else {
          // Create new variant
          await dispatch(createProductVariant(
            variantData as CreateProductVariantDto
          )).unwrap();
        }
      }

      setSaving(false);
      onSave();
    } catch (error) {
      console.error('Error saving variants:', error);
      setSaving(false);
    }
  };

  const calculateVolume = (tempId: string) => {
    const variant = variantsList.find(v => v.tempId === tempId);
    if (!variant) return;

    const volume = variant.length * variant.breadth * variant.height;
    handleVariantChange(tempId, 'volume', volume);
  };

  const renderVariantForm = (variant: VariantData) => {
    return (
      <Box>
        {/* Basic Information */}
        <Paper sx={{ p: 2, mb: 3, bgcolor: 'background.default' }}>
          <Typography variant="subtitle1" gutterBottom>Basic Information</Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="SKU Code"
                value={variant.sku}
                onChange={(e) => handleVariantChange(variant.tempId, 'sku', e.target.value)}
                required
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Barcode"
                value={variant.barcode}
                onChange={(e) => handleVariantChange(variant.tempId, 'barcode', e.target.value)}
              />
            </Grid>
          </Grid>
        </Paper>

        {/* Pricing Information */}
        <Paper sx={{ p: 2, mb: 3, bgcolor: 'background.default' }}>
          <Typography variant="subtitle1" gutterBottom>Pricing Information</Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="MRP"
                type="number"
                value={variant.mrp}
                onChange={(e) => handleVariantChange(variant.tempId, 'mrp', parseFloat(e.target.value))}
                required
                InputProps={{
                  inputProps: { min: 0, step: 0.01 },
                  startAdornment: <InputAdornment position="start">₹</InputAdornment>
                }}
              />
            </Grid>

            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Selling Price"
                type="number"
                value={variant.price}
                onChange={(e) => handleVariantChange(variant.tempId, 'price', parseFloat(e.target.value))}
                required
                InputProps={{
                  inputProps: { min: 0, step: 0.01 },
                  startAdornment: <InputAdornment position="start">₹</InputAdornment>
                }}
              />
            </Grid>

            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Cost Price"
                type="number"
                value={variant.cost}
                onChange={(e) => handleVariantChange(variant.tempId, 'cost', parseFloat(e.target.value))}
                InputProps={{
                  inputProps: { min: 0, step: 0.01 },
                  startAdornment: <InputAdornment position="start">₹</InputAdornment>
                }}
              />
            </Grid>
          </Grid>
        </Paper>

        {/* Inventory Information */}
        <Paper sx={{ p: 2, mb: 3, bgcolor: 'background.default' }}>
          <Typography variant="subtitle1" gutterBottom>Inventory Information</Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Stock Quantity"
                type="number"
                value={variant.stockQuantity}
                onChange={(e) => handleVariantChange(variant.tempId, 'stockQuantity', parseInt(e.target.value))}
                required
                InputProps={{ inputProps: { min: 0, step: 1 } }}
                helperText="Current available stock"
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Reorder Level"
                type="number"
                value={variant.reorderLevel}
                onChange={(e) => handleVariantChange(variant.tempId, 'reorderLevel', parseInt(e.target.value))}
                InputProps={{ inputProps: { min: 0, step: 1 } }}
                helperText="Minimum stock level before reordering"
              />
            </Grid>

            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={variant.isActive}
                    onChange={(e) => handleVariantChange(variant.tempId, 'isActive', e.target.checked)}
                    color="primary"
                  />
                }
                label="Active"
              />
            </Grid>
          </Grid>
        </Paper>

        {/* Variant Attributes */}
        <Paper sx={{ p: 2, mb: 3, bgcolor: 'background.default' }}>
          <Typography variant="subtitle1" gutterBottom>Variant Attributes</Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                select
                label="Size"
                value={variant.variantAttributes?.Size || ''}
                onChange={(e) => handleVariantAttributeChange(variant.tempId, 'Size', e.target.value)}
              >
                {sizeOptions.map(size => (
                  <MenuItem key={size} value={size}>{size}</MenuItem>
                ))}
              </TextField>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                select
                label="Color"
                value={variant.variantAttributes?.Color || ''}
                onChange={(e) => handleVariantAttributeChange(variant.tempId, 'Color', e.target.value)}
              >
                {colorOptions.map(color => (
                  <MenuItem key={color} value={color}>{color}</MenuItem>
                ))}
              </TextField>
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="subtitle2">Additional Attributes</Typography>
                <Button
                  startIcon={<Add />}
                  variant="outlined"
                  size="small"
                  onClick={() => setAttributeDialogOpen(true)}
                >
                  Add Attribute
                </Button>
              </Box>

              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {Object.entries(variant.variantAttributes || {}).filter(([key]) => key !== 'Size' && key !== 'Color').map(([key, value]) => (
                  <Chip
                    key={key}
                    label={`${key}: ${value}`}
                    onDelete={() => handleRemoveAttribute(variant.tempId, key)}
                    color="primary"
                    variant="outlined"
                  />
                ))}
                {Object.entries(variant.variantAttributes || {}).filter(([key]) => key !== 'Size' && key !== 'Color').length === 0 && (
                  <Typography variant="body2" color="text.secondary">
                    No additional attributes added yet.
                  </Typography>
                )}
              </Box>
            </Grid>
          </Grid>
        </Paper>

        {/* Physical Attributes */}
        <Paper sx={{ p: 2, mb: 3, bgcolor: 'background.default' }}>
          <Typography variant="subtitle1" gutterBottom>Physical Attributes</Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                label="Weight"
                type="number"
                value={variant.weight}
                onChange={(e) => handleVariantChange(variant.tempId, 'weight', parseFloat(e.target.value))}
                InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                helperText="Product weight"
              />
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                select
                label="Weight Unit"
                value={variant.weightUnit}
                onChange={(e) => handleVariantChange(variant.tempId, 'weightUnit', e.target.value)}
              >
                <MenuItem value="kg">kg</MenuItem>
                <MenuItem value="g">g</MenuItem>
                <MenuItem value="lb">lb</MenuItem>
              </TextField>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                label="Length"
                type="number"
                value={variant.length}
                onChange={(e) => {
                  handleVariantChange(variant.tempId, 'length', parseFloat(e.target.value));
                  setTimeout(() => calculateVolume(variant.tempId), 100);
                }}
                InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                helperText="Product length"
              />
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                label="Breadth"
                type="number"
                value={variant.breadth}
                onChange={(e) => {
                  handleVariantChange(variant.tempId, 'breadth', parseFloat(e.target.value));
                  setTimeout(() => calculateVolume(variant.tempId), 100);
                }}
                InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                helperText="Product width"
              />
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                label="Height"
                type="number"
                value={variant.height}
                onChange={(e) => {
                  handleVariantChange(variant.tempId, 'height', parseFloat(e.target.value));
                  setTimeout(() => calculateVolume(variant.tempId), 100);
                }}
                InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                helperText="Product height"
              />
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                select
                label="Dimension Unit"
                value={variant.dimensionUnit}
                onChange={(e) => handleVariantChange(variant.tempId, 'dimensionUnit', e.target.value)}
              >
                <MenuItem value="cm">cm</MenuItem>
                <MenuItem value="mm">mm</MenuItem>
                <MenuItem value="in">in</MenuItem>
              </TextField>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                label="Volume"
                type="number"
                value={variant.volume}
                disabled
                InputProps={{ inputProps: { min: 0 } }}
                helperText="Calculated automatically"
              />
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                select
                label="Volume Unit"
                value={variant.volumeUnit}
                disabled
              >
                <MenuItem value="cm3">cm³</MenuItem>
                <MenuItem value="mm3">mm³</MenuItem>
                <MenuItem value="in3">in³</MenuItem>
              </TextField>
            </Grid>
          </Grid>
        </Paper>

        {/* Product Images */}
        <Paper sx={{ p: 2, mb: 3, bgcolor: 'background.default' }}>
          <Typography variant="subtitle1" gutterBottom>Product Images</Typography>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <ImageUploader
                images={variant.images}
                onChange={(images) => handleImagesChange(variant.tempId, images)}
              />
            </Grid>
          </Grid>
        </Paper>
      </Box>
    );
  };

  return (
    <Paper sx={{ p: 3, width: '80%', margin: '0 auto' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">
          Manage Product Variants for {product.name}
        </Typography>
        <Box>
          <Button
            variant="outlined"
            color="primary"
            startIcon={<Add />}
            onClick={addNewVariant}
            sx={{ mr: 1 }}
          >
            Add Variant
          </Button>
          <Button
            variant="contained"
            color="primary"
            startIcon={<Save />}
            onClick={handleSaveAll}
            disabled={saving || variantsList.length === 0}
          >
            Save All Variants
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {loading && variantsList.length === 0 ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : variantsList.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body1" color="text.secondary">
            No variants found for this product. Click "Add Variant" to create one.
          </Typography>
        </Paper>
      ) : (
        <Box sx={{ width: '100%' }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons="auto"
            >
              {variantsList.map((variant, index) => (
                <Tab
                  key={variant.tempId}
                  label={
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <span>Variant {index + 1}</span>
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteVariant(variant.tempId);
                        }}
                        sx={{ ml: 1 }}
                      >
                        <Close fontSize="small" />
                      </IconButton>
                    </Box>
                  }
                  id={`variant-tab-${index}`}
                  aria-controls={`variant-tabpanel-${index}`}
                />
              ))}
            </Tabs>
          </Box>

          {variantsList.map((variant, index) => (
            <TabPanel key={variant.tempId} value={tabValue} index={index}>
              {renderVariantForm(variant)}
            </TabPanel>
          ))}
        </Box>
      )}

      {/* Attribute Dialog */}
      <Dialog open={attributeDialogOpen} onClose={() => setAttributeDialogOpen(false)}>
        <DialogTitle>Add Variant Attribute</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Attribute Name (e.g., Size, Color)"
            fullWidth
            value={attributeKey}
            onChange={(e) => setAttributeKey(e.target.value)}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="Attribute Value (e.g., XL, Red)"
            fullWidth
            value={attributeValue}
            onChange={(e) => setAttributeValue(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAttributeDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={() => handleAddAttribute(variantsList[tabValue]?.tempId)}
            color="primary"
            disabled={!attributeKey || !attributeValue}
          >
            Add
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={confirmDeleteOpen} onClose={() => setConfirmDeleteOpen(false)}>
        <DialogTitle>Delete Variant</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this variant? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDeleteOpen(false)}>Cancel</Button>
          <Button onClick={confirmDeleteVariant} color="error">Delete</Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default MultiVariantManager;
