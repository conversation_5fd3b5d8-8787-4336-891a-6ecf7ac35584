import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Box,
  Button,
  TextField,
  Grid,
  FormControlLabel,
  Switch,
  MenuItem,
  CircularProgress,
  Typography,
  Divider,
  Paper,
  Collapse,
  Tabs,
  Tab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip
} from '@mui/material';
import { Add as AddIcon, ExpandLess, ExpandMore } from '@mui/icons-material';
import { AppDispatch, RootState } from '../../store';
import { createProduct, updateProduct, Product, CreateProductDto } from '../../store/slices/productSlice';
import { fetchCategories } from '../../store/slices/categorySlice';
import { fetchCollections } from '../../store/slices/collectionSlice';
import MultipleSelect from '../common/MultipleSelect';
import ImageUpload from '../common/ImageUpload';
import { uploadImage } from '../../services/fileUpload';
import { getProductTypes, ProductType } from '../../services/productTypeService';
import Autocomplete from '@mui/material/Autocomplete';

interface ProductFormProps {
  product?: Product | null;
  onSubmitSuccess: () => void;
  onCancel: () => void;
}

const ProductForm: React.FC<ProductFormProps> = ({ product, onSubmitSuccess, onCancel }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { loading } = useSelector((state: RootState) => state.products);
  const { categories } = useSelector((state: RootState) => state.categories);
  const { collections } = useSelector((state: RootState) => state.collections);
  const { selectedStore } = useSelector((state: RootState) => state.store);
  // Location state removed
  const [uploadingImage, setUploadingImage] = useState(false);
  const [productTypes, setProductTypes] = useState<ProductType[]>([]);

  const [formData, setFormData] = useState<CreateProductDto>({
    name: '',
    description: '',
    barcode: '',
    isActive: true,
    isFeatured: false,
    hasVariants: false,
    categoryId: 0,
    collectionIds: [],
    imageUrl: '',
    // Product specific fields
    styleCode: '',
    returnExchangeCondition: 0,
    color: '',
    HSNCode: '',  // Note the uppercase HSNCode for API
    GSTType: '',  // Note the uppercase GSTType for API
    productGroupId: undefined,
    productTypeId: undefined,
    productTypeName: '',
    storeId: selectedStore?.id || 1
  });

  // Custom attribute management
  const [customAttributeKey, setCustomAttributeKey] = useState('');
  const [customAttributeValue, setCustomAttributeValue] = useState('');
  const [showCustomAttributeDialog, setShowCustomAttributeDialog] = useState(false);

  const [showVariants, setShowVariants] = useState(false);
  const [variantTabValue, setVariantTabValue] = useState(0);
  const [variants, setVariants] = useState<any[]>([]);

  useEffect(() => {
    dispatch(fetchCategories(true));
    dispatch(fetchCollections(true));
    // Fetch product types
    const fetchProductTypes = async () => {
      const types = await getProductTypes();
      setProductTypes(types);
    };
    fetchProductTypes();
  }, [dispatch]);

  // Update storeId when selected store changes
  useEffect(() => {
    if (selectedStore) {
      setFormData(prev => ({
        ...prev,
        storeId: selectedStore.id
      }));
    }
  }, [selectedStore]);

  useEffect(() => {
    if (product) {
      console.log('Product data:', product);
      // Set hasVariants first based on product data
      const hasVariants = product.hasVariants || (product.variants && product.variants.length > 0);

      setFormData({
        name: product.name,
        description: product.description,
        barcode: product.barcode || '',
        isActive: product.isActive,
        isFeatured: product.isFeatured,
        hasVariants: hasVariants, // Use the calculated value
        categoryId: product.categoryId,
        collectionIds: product.collectionIds,
        imageUrl: product.imageUrl,
        // Product specific fields
        styleCode: product.styleCode || '',
        returnExchangeCondition: product.returnExchangeCondition || 0,
        color: product.color || '',
        HSNCode: product.HSNCode || '',  // Note the uppercase HSNCode from API
        GSTType: product.GSTType || '',  // Note the uppercase GSTType from API
        productGroupId: product.productGroupId,
        productTypeId: product.productTypeId,
        productTypeName: product.productTypeName || '',
        customAttributes: product.customAttributes ?
          // Convert from JSON string if needed
          (typeof product.customAttributes === 'string' ?
            JSON.parse(product.customAttributes) :
            product.customAttributes) :
          { fabric: '', type: '' }
      });

      // If there are custom attributes, populate them
      if (product.customAttributes && typeof product.customAttributes === 'object') {
        // Already handled above
      } else if (product.customAttributes && typeof product.customAttributes === 'string') {
        try {
          const parsedAttributes = JSON.parse(product.customAttributes);
          setFormData(prev => ({
            ...prev,
            customAttributes: parsedAttributes
          }));
        } catch (error) {
          console.error('Error parsing custom attributes:', error);
        }
      }

      // Set variants if they exist
      if (product.variants && product.variants.length > 0) {
        console.log('Variant data:', product.variants);
        setVariants(product.variants.map(variant => ({
          id: variant.id,
          tempId: variant.id.toString(),
          sku: variant.sku,
          barcode: variant.barcode || '',
          price: variant.price,
          cost: variant.cost || 0,
          mrp: variant.mrp || variant.price * 1.2,
          stockQuantity: variant.stockQuantity,
          reorderLevel: variant.reorderLevel || 10,
          isActive: variant.isActive,
          size: variant.size || '',
          weight: variant.weight || 0,
          weightUnit: variant.weightUnit || 'kg',
          length: variant.length || 0,
          breadth: variant.breadth || 0,
          height: variant.height || 0,
          dimensionUnit: variant.dimensionUnit || 'cm',
          variantAttributes: variant.variantAttributes || {}
        })));
        // Always show variants panel if product has variants
        setShowVariants(true);
      }

      // If hasVariants is true but no variants exist yet, still show the variants panel
      else if (hasVariants) {
        setShowVariants(true);
      }
    }
  }, [product]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSwitchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({ ...prev, [name]: checked }));

    // Toggle variants section when hasVariants is changed
    if (name === 'hasVariants') {
      setShowVariants(checked);
      if (checked && variants.length === 0) {
        // Add a default variant
        addVariant();
      }
    }
  };

  // Variant management functions
  const addVariant = () => {
    const newVariant = {
      tempId: Date.now().toString(),
      sku: `SKU-${Date.now().toString().substring(8)}-${variants.length + 1}`,
      barcode: '',
      price: 0,
      cost: 0,
      mrp: 0,
      stockQuantity: 100,
      reorderLevel: 10,
      isActive: true,
      // Physical attributes
      size: 'M',
      weight: 0.5,
      weightUnit: 'kg',
      length: 30,
      breadth: 20,
      height: 5,
      dimensionUnit: 'cm',
      volume: 0,
      volumeUnit: 'cm3',
      variantAttributes: {},
      locationId: 1 // Default location ID is always 1
    };

    setVariants([...variants, newVariant]);
    setVariantTabValue(variants.length);
  };

  const handleVariantChange = (tempId: string, field: string, value: any) => {
    setVariants(prevVariants =>
      prevVariants.map(variant =>
        variant.tempId === tempId
          ? { ...variant, [field]: value }
          : variant
      )
    );
  };

  // Variant attributes are now managed at the product level

  const removeVariant = (tempId: string) => {
    setVariants(prevVariants => prevVariants.filter(v => v.tempId !== tempId));
    if (variants.length <= 1) {
      setVariantTabValue(0);
    } else if (variantTabValue >= variants.length - 1) {
      setVariantTabValue(variants.length - 2);
    }
  };

  const handleCollectionsChange = (selectedIds: number[]) => {
    setFormData(prev => ({ ...prev, collectionIds: selectedIds }));
  };

  const handleImageUpload = async (file: File) => {
    try {
      setUploadingImage(true);
      const imageUrl = await uploadImage(file);
      setFormData(prev => ({ ...prev, imageUrl }));
      setUploadingImage(false);
    } catch (error) {
      console.error('Error uploading image:', error);
      setUploadingImage(false);
    }
  };

  // Custom attribute changes are now handled directly in the dialog

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      console.log('Form data before submission:', formData);
      console.log('Product Type ID:', formData.productTypeId);
      console.log('Product Type Name:', formData.productTypeName);

      // Use the customAttributes directly from the form
      const apiFormData = {
        ...formData,
        customAttributes: formData.customAttributes || {}
      };

      console.log('API form data:', apiFormData);

      // If product has variants, include them in the request
      if (formData.hasVariants && variants.length > 0) {
        // For existing variants, we need to update them individually
        const existingVariants = variants.filter(v => v.id);
        const newVariants = variants.filter(v => !v.id);

        // Map new variants to the format expected by the API
        const newVariantsFormatted = newVariants.map(variant => ({
          sku: variant.sku,
          barcode: variant.barcode,
          price: parseFloat(variant.price.toString()),
          cost: parseFloat(variant.cost.toString()),
          mrp: parseFloat(variant.mrp.toString()),
          stockQuantity: parseInt(variant.stockQuantity.toString()),
          reorderLevel: parseInt(variant.reorderLevel.toString()),
          isActive: variant.isActive,
          // Physical attributes
          size: variant.size || '',
          weight: parseFloat(variant.weight.toString()),
          weightUnit: variant.weightUnit,
          length: parseFloat(variant.length.toString()),
          breadth: parseFloat(variant.breadth.toString()),
          height: parseFloat(variant.height.toString()),
          dimensionUnit: variant.dimensionUnit,
          volume: parseFloat((variant.volume || 0).toString()),
          volumeUnit: variant.volumeUnit || 'cm3',
          // Variant attributes
          variantAttributes: variant.variantAttributes || {}
        }));

        // Map existing variants to the format expected by the API
        const existingVariantsFormatted = existingVariants.map(variant => ({
          id: variant.id,
          sku: variant.sku,
          barcode: variant.barcode,
          price: parseFloat(variant.price.toString()),
          cost: parseFloat(variant.cost.toString()),
          mrp: parseFloat(variant.mrp.toString()),
          stockQuantity: parseInt(variant.stockQuantity.toString()),
          reorderLevel: parseInt(variant.reorderLevel.toString()),
          isActive: variant.isActive,
          // Physical attributes
          size: variant.size || '',
          weight: parseFloat(variant.weight.toString()),
          weightUnit: variant.weightUnit,
          length: parseFloat(variant.length.toString()),
          breadth: parseFloat(variant.breadth.toString()),
          height: parseFloat(variant.height.toString()),
          dimensionUnit: variant.dimensionUnit,
          volume: parseFloat((variant.volume || 0).toString()),
          volumeUnit: variant.volumeUnit || 'cm3',
          // Variant attributes
          variantAttributes: variant.variantAttributes || {}
        }));

        // Combine both new and existing variants
        apiFormData.variants = [...newVariantsFormatted, ...existingVariantsFormatted];
      }

      // Ensure productTypeName is included in the request
      const finalFormData = {
        ...apiFormData,
        productTypeName: formData.productTypeName || undefined,
        productTypeId: formData.productTypeId || undefined
      };

      console.log('Final form data being sent to API:', finalFormData);

      if (product) {
        await dispatch(updateProduct({ id: product.id, ...finalFormData })).unwrap();
      } else {
        await dispatch(createProduct(finalFormData)).unwrap();
      }

      //alert(`Product ${product ? 'updated' : 'created'} successfully`);
      onSubmitSuccess();
    } catch (error) {
      console.error('Error saving product:', error);
      alert('Failed to save product');
    }
  };

  return (
    <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2, width: '80%', margin: '0 auto' }}>
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>Basic Information</Typography>
            <Divider sx={{ mb: 3 }} />

            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  required
                  fullWidth
                  label="Product Name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  required
                  fullWidth
                  multiline
                  rows={4}
                  label="Description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                />
              </Grid>

              {/* SKU field removed - SKU is a variant property */}

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Barcode"
                  name="barcode"
                  value={formData.barcode}
                  onChange={handleChange}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Style Code"
                  name="styleCode"
                  value={formData.styleCode}
                  onChange={handleChange}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Color"
                  name="color"
                  value={formData.color}
                  onChange={handleChange}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="HSN Code"
                  name="HSNCode"
                  value={formData.HSNCode}
                  onChange={handleChange}
                  helperText="Harmonized System Nomenclature Code"
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="GST Type"
                  name="GSTType"
                  value={formData.GSTType}
                  onChange={handleChange}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  select
                  label="Return/Exchange Condition"
                  name="returnExchangeCondition"
                  value={formData.returnExchangeCondition}
                  onChange={handleChange}
                >
                  <MenuItem value={0}>No Returns/Exchange</MenuItem>
                  <MenuItem value={1}>7 Days Return/Exchange</MenuItem>
                  <MenuItem value={2}>15 Days Return/Exchange</MenuItem>
                  <MenuItem value={3}>30 Days Return/Exchange</MenuItem>
                </TextField>
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  required
                  fullWidth
                  select
                  label="Category"
                  name="categoryId"
                  value={formData.categoryId}
                  onChange={handleChange}
                >
                  <MenuItem value={0} disabled>Select a category</MenuItem>
                  {categories.map(category => (
                    <MenuItem key={category.id} value={category.id}>{category.name}</MenuItem>
                  ))}
                </TextField>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Autocomplete
                  id="product-type"
                  freeSolo
                  options={productTypes}
                  getOptionLabel={(option) => {
                    // Handle both string and ProductType object
                    if (typeof option === 'string') {
                      return option;
                    }
                    // Make sure we handle the API response format correctly
                    if (option && typeof option === 'object' && 'name' in option) {
                      return option.name;
                    }
                    return '';
                  }}
                  value={formData.productTypeId ?
                    productTypes.find(pt => pt.id === formData.productTypeId) || null :
                    formData.productTypeName ?
                      formData.productTypeName :
                      null
                  }
                  onChange={(_, newValue) => {
                    console.log('Product type selection changed:', newValue);
                    if (typeof newValue === 'string') {
                      // User entered a new product type name
                      console.log('User entered a new product type name:', newValue);
                      setFormData(prev => ({
                        ...prev,
                        productTypeId: undefined,
                        productTypeName: newValue
                      }));
                    } else if (newValue && typeof newValue === 'object' && 'id' in newValue && newValue.id) {
                      // User selected an existing product type from the dropdown
                      console.log('User selected existing product type:', newValue);
                      setFormData(prev => ({
                        ...prev,
                        productTypeId: newValue.id,
                        productTypeName: newValue.name
                      }));
                    } else if (newValue && typeof newValue === 'object' && 'name' in newValue) {
                      // User selected a custom product type object
                      console.log('User selected custom product type object:', newValue);
                      const customValue = newValue as { name: string };
                      setFormData(prev => ({
                        ...prev,
                        productTypeId: undefined,
                        productTypeName: customValue.name
                      }));
                    } else {
                      // User cleared the selection
                      console.log('User cleared product type selection');
                      setFormData(prev => ({
                        ...prev,
                        productTypeId: undefined,
                        productTypeName: ''
                      }));
                    }
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Product Type"
                      helperText="Select existing or enter new product type"
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <MultipleSelect
                  label="Collections"
                  options={collections.map(c => ({ id: c.id, name: c.name }))}
                  selectedIds={formData.collectionIds || []}
                  onChange={handleCollectionsChange}
                />
              </Grid>
            </Grid>
          </Paper>

          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>Custom Attributes</Typography>
            <Divider sx={{ mb: 3 }} />

            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="subtitle1">Product Attributes</Typography>
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<AddIcon />}
                    onClick={() => setShowCustomAttributeDialog(true)}
                  >
                    Add Attribute
                  </Button>
                </Box>

                {Object.entries(formData.customAttributes || {}).length > 0 ? (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {Object.entries(formData.customAttributes || {}).map(([key, value]) => (
                      <Chip
                        key={key}
                        label={`${key}: ${value}`}
                        onDelete={() => {
                          const newAttributes = { ...formData.customAttributes };
                          delete newAttributes[key];
                          setFormData(prev => ({
                            ...prev,
                            customAttributes: newAttributes
                          }));
                        }}
                        color="primary"
                        variant="outlined"
                      />
                    ))}
                  </Box>
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    No attributes added yet. Click "Add Attribute" to add product-specific attributes.
                  </Typography>
                )}
              </Grid>
            </Grid>
          </Paper>

          {/* Pricing & Inventory section removed */}
        </Grid>

        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>Product Image</Typography>
            <Divider sx={{ mb: 3 }} />

            <Box sx={{ mb: 2 }}>
              <ImageUpload
                currentImage={formData.imageUrl}
                onImageUpload={handleImageUpload}
                loading={uploadingImage}
                label="Main Product Image"
                multiple={false}
              />
            </Box>

            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              This is the main product image. You can add more images after creating the product.
            </Typography>
          </Paper>

          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>Status</Typography>
            <Divider sx={{ mb: 3 }} />

            <FormControlLabel
              control={
                <Switch
                  checked={formData.isActive}
                  onChange={handleSwitchChange}
                  name="isActive"
                  color="primary"
                />
              }
              label="Active (visible to customers)"
            />

            <FormControlLabel
              control={
                <Switch
                  checked={formData.isFeatured}
                  onChange={handleSwitchChange}
                  name="isFeatured"
                  color="primary"
                />
              }
              label="Featured (shown on homepage)"
            />

            <FormControlLabel
              control={
                <Switch
                  checked={formData.hasVariants}
                  onChange={handleSwitchChange}
                  name="hasVariants"
                  color="primary"
                />
              }
              label="Has Variants (size, color, etc.)"
            />

            {formData.hasVariants && (
              <Box sx={{ mt: 2 }}>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={showVariants ? <ExpandLess /> : <ExpandMore />}
                  onClick={() => setShowVariants(!showVariants)}
                >
                  {showVariants ? 'Hide Variants' : 'Show Variants'}
                </Button>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>

      {/* Variants Section */}
      {formData.hasVariants && showVariants && (
        <Collapse in={showVariants}>
          <Paper sx={{ p: 3, mt: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">Product Variants</Typography>
              <Button
                variant="outlined"
                startIcon={<AddIcon />}
                onClick={addVariant}
              >
                Add Variant
              </Button>
            </Box>

            {variants.length === 0 ? (
              <Typography color="text.secondary">No variants added yet. Click "Add Variant" to create one.</Typography>
            ) : (
              <Box sx={{ width: '100%' }}>
                <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                  <Tabs
                    value={variantTabValue}
                    onChange={(_, newValue) => setVariantTabValue(newValue)}
                    variant="scrollable"
                    scrollButtons="auto"
                  >
                    {variants.map((variant, index) => (
                      <Tab
                        key={variant.tempId}
                        label={`Variant ${index + 1}`}
                        id={`variant-tab-${index}`}
                        aria-controls={`variant-tabpanel-${index}`}
                      />
                    ))}
                  </Tabs>
                </Box>

                {variants.map((variant, index) => (
                  <div
                    key={variant.tempId}
                    role="tabpanel"
                    hidden={variantTabValue !== index}
                    id={`variant-tabpanel-${index}`}
                    aria-labelledby={`variant-tab-${index}`}
                  >
                    {variantTabValue === index && (
                      <Box sx={{ p: 3 }}>
                        <Grid container spacing={2}>
                          <Grid item xs={12} sm={6}>
                            <TextField
                              fullWidth
                              label="SKU"
                              value={variant.sku}
                              onChange={(e) => handleVariantChange(variant.tempId, 'sku', e.target.value)}
                              required
                            />
                          </Grid>

                          <Grid item xs={12} sm={6}>
                            <TextField
                              fullWidth
                              label="Barcode"
                              value={variant.barcode}
                              onChange={(e) => handleVariantChange(variant.tempId, 'barcode', e.target.value)}
                            />
                          </Grid>

                          <Grid item xs={12} sm={6}>
                            <TextField
                              fullWidth
                              label="Size"
                              value={variant.size || ''}
                              onChange={(e) => handleVariantChange(variant.tempId, 'size', e.target.value)}
                              helperText="Size of the variant (e.g., S, M, L, XL)"
                            />
                          </Grid>

                          <Grid item xs={12} sm={6}>
                            <TextField
                              fullWidth
                              label="MRP (Maximum Retail Price)"
                              type="number"
                              value={variant.mrp || 0}
                              onChange={(e) => handleVariantChange(variant.tempId, 'mrp', parseFloat(e.target.value))}
                              required
                              inputProps={{ min: 0, step: 0.01 }}
                              helperText="Original price before discount"
                            />
                          </Grid>

                          <Grid item xs={12} sm={4}>
                            <TextField
                              fullWidth
                              label="Selling Price"
                              type="number"
                              value={variant.price}
                              onChange={(e) => handleVariantChange(variant.tempId, 'price', parseFloat(e.target.value))}
                              required
                              inputProps={{ min: 0, step: 0.01 }}
                              helperText="Actual selling price"
                            />
                          </Grid>

                          <Grid item xs={12} sm={4}>
                            <TextField
                              fullWidth
                              label="Cost Price"
                              type="number"
                              value={variant.cost}
                              onChange={(e) => handleVariantChange(variant.tempId, 'cost', parseFloat(e.target.value))}
                              inputProps={{ min: 0, step: 0.01 }}
                              helperText="Your cost (not shown to customers)"
                            />
                          </Grid>

                          <Grid item xs={12} sm={4}>
                            <TextField
                              fullWidth
                              label="Stock Quantity"
                              type="number"
                              value={variant.stockQuantity}
                              onChange={(e) => handleVariantChange(variant.tempId, 'stockQuantity', parseInt(e.target.value))}
                              required
                              inputProps={{ min: 0, step: 1 }}
                            />
                          </Grid>

                          {/* Location dropdown removed - always using default location ID 1 */}

                          <Grid item xs={12}>
                            <Typography variant="subtitle2" gutterBottom>Physical Attributes</Typography>
                            <Grid container spacing={2}>
                              <Grid item xs={12} sm={6} md={3}>
                                <TextField
                                  fullWidth
                                  label="Weight"
                                  type="number"
                                  value={variant.weight || 0}
                                  onChange={(e) => handleVariantChange(variant.tempId, 'weight', parseFloat(e.target.value))}
                                  inputProps={{ min: 0, step: 0.01 }}
                                />
                              </Grid>
                              <Grid item xs={12} sm={6} md={3}>
                                <TextField
                                  select
                                  fullWidth
                                  label="Weight Unit"
                                  value={variant.weightUnit || 'kg'}
                                  onChange={(e) => handleVariantChange(variant.tempId, 'weightUnit', e.target.value)}
                                >
                                  <MenuItem value="kg">kg</MenuItem>
                                  <MenuItem value="g">g</MenuItem>
                                  <MenuItem value="lb">lb</MenuItem>
                                  <MenuItem value="oz">oz</MenuItem>
                                </TextField>
                              </Grid>
                              <Grid item xs={12} sm={6} md={3}>
                                <TextField
                                  fullWidth
                                  label="Length"
                                  type="number"
                                  value={variant.length || 0}
                                  onChange={(e) => handleVariantChange(variant.tempId, 'length', parseFloat(e.target.value))}
                                  inputProps={{ min: 0, step: 0.01 }}
                                />
                              </Grid>
                              <Grid item xs={12} sm={6} md={3}>
                                <TextField
                                  fullWidth
                                  label="Breadth"
                                  type="number"
                                  value={variant.breadth || 0}
                                  onChange={(e) => handleVariantChange(variant.tempId, 'breadth', parseFloat(e.target.value))}
                                  inputProps={{ min: 0, step: 0.01 }}
                                />
                              </Grid>
                              <Grid item xs={12} sm={6} md={3}>
                                <TextField
                                  fullWidth
                                  label="Height"
                                  type="number"
                                  value={variant.height || 0}
                                  onChange={(e) => handleVariantChange(variant.tempId, 'height', parseFloat(e.target.value))}
                                  inputProps={{ min: 0, step: 0.01 }}
                                />
                              </Grid>
                              <Grid item xs={12} sm={6} md={3}>
                                <TextField
                                  select
                                  fullWidth
                                  label="Dimension Unit"
                                  value={variant.dimensionUnit || 'cm'}
                                  onChange={(e) => handleVariantChange(variant.tempId, 'dimensionUnit', e.target.value)}
                                >
                                  <MenuItem value="cm">cm</MenuItem>
                                  <MenuItem value="mm">mm</MenuItem>
                                  <MenuItem value="in">in</MenuItem>
                                </TextField>
                              </Grid>
                            </Grid>
                          </Grid>

                          <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                            <Button
                              variant="outlined"
                              color="error"
                              onClick={() => removeVariant(variant.tempId)}
                            >
                              Remove Variant
                            </Button>
                          </Grid>
                        </Grid>
                      </Box>
                    )}
                  </div>
                ))}
              </Box>
            )}
          </Paper>
        </Collapse>
      )}

      <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
        <Button onClick={onCancel} sx={{ mr: 1 }}>
          Cancel
        </Button>
        <Button
          type="submit"
          variant="contained"
          color="primary"
          disabled={loading || uploadingImage}
        >
          {loading || uploadingImage ? (
            <CircularProgress size={24} />
          ) : product ? (
            'Update Product'
          ) : (
            'Create Product'
          )}
        </Button>
      </Box>

      {/* Custom Attribute Dialog */}
      <Dialog open={showCustomAttributeDialog} onClose={() => setShowCustomAttributeDialog(false)}>
        <DialogTitle>Add Custom Attribute</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Attribute Name"
            fullWidth
            value={customAttributeKey}
            onChange={(e) => setCustomAttributeKey(e.target.value)}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="Attribute Value"
            fullWidth
            value={customAttributeValue}
            onChange={(e) => setCustomAttributeValue(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowCustomAttributeDialog(false)}>Cancel</Button>
          <Button
            onClick={() => {
              if (customAttributeKey && customAttributeValue) {
                setFormData(prev => ({
                  ...prev,
                  customAttributes: {
                    ...prev.customAttributes,
                    [customAttributeKey]: customAttributeValue
                  }
                }));
                setCustomAttributeKey('');
                setCustomAttributeValue('');
                setShowCustomAttributeDialog(false);
              }
            }}
            color="primary"
            disabled={!customAttributeKey || !customAttributeValue}
          >
            Add
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ProductForm;
