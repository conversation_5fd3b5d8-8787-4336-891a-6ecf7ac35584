import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  TablePagination,
  CircularProgress,
  Box,
  Typography,
  Tooltip
} from '@mui/material';
import {
  Edit as EditIcon,
  Refresh as RefreshIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import { format } from 'date-fns';
import { AppDispatch, RootState } from '../../store';
import { InventoryItem, fetchInventory, setCurrentPage } from '../../store/slices/inventorySlice';

interface InventoryTableProps {
  onEdit: (item: InventoryItem) => void;
  storeId?: number;
  search?: string;
  lowStock?: boolean;
}

const InventoryTable: React.FC<InventoryTableProps> = ({ onEdit, storeId, search, lowStock }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { inventoryItems, loading, error, totalCount, totalPages, currentPage } = useSelector((state: RootState) => state.inventory);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const handleChangePage = (event: unknown, newPage: number) => {
    dispatch(setCurrentPage(newPage + 1));
    dispatch(fetchInventory({ storeId, page: newPage + 1, pageSize: rowsPerPage, search, lowStock }));
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newRowsPerPage = parseInt(event.target.value, 10);
    setRowsPerPage(newRowsPerPage);
    dispatch(setCurrentPage(1));
    dispatch(fetchInventory({ storeId, page: 1, pageSize: newRowsPerPage, search, lowStock }));
  };

  const handleRefresh = () => {
    dispatch(fetchInventory({ storeId, page: currentPage, pageSize: rowsPerPage, search, lowStock }));
  };

  if (error) {
    return (
      <Box sx={{ p: 2, textAlign: 'center' }}>
        <Typography color="error">{error}</Typography>
        <IconButton onClick={handleRefresh} color="primary" sx={{ mt: 1 }}>
          <RefreshIcon />
        </IconButton>
      </Box>
    );
  }

  return (
    <Paper sx={{ width: '100%', overflow: 'hidden' }}>
      <TableContainer sx={{ maxHeight: 'calc(100vh - 300px)' }}>
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              <TableCell>ID</TableCell>
              <TableCell>Location</TableCell>
              <TableCell>Stock Quantity</TableCell>
              <TableCell>Reorder Level</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Last Restocked</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading && inventoryItems.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} align="center">
                  <CircularProgress />
                </TableCell>
              </TableRow>
            ) : inventoryItems.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} align="center">
                  No inventory items found
                </TableCell>
              </TableRow>
            ) : (
              inventoryItems.map((item) => (
                <TableRow key={item.id}>
                  <TableCell>{item.id}</TableCell>
                  <TableCell>
                    {item.locationName} ({item.locationCode})
                  </TableCell>
                  <TableCell>{item.stockQuantity}</TableCell>
                  <TableCell>{item.reorderLevel}</TableCell>
                  <TableCell>
                    {item.stockQuantity <= item.reorderLevel ? (
                      <Chip
                        icon={<WarningIcon />}
                        label="Low Stock"
                        color="warning"
                        size="small"
                      />
                    ) : (
                      <Chip
                        label="In Stock"
                        color="success"
                        size="small"
                      />
                    )}
                  </TableCell>
                  <TableCell>
                    {format(new Date(item.lastRestockedAt), 'yyyy-MM-dd HH:mm')}
                  </TableCell>
                  <TableCell>
                    <Tooltip title="Edit">
                      <IconButton
                        color="primary"
                        onClick={() => onEdit(item)}
                      >
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>
      <TablePagination
        rowsPerPageOptions={[5, 10, 25, 50]}
        component="div"
        count={totalCount}
        rowsPerPage={rowsPerPage}
        page={currentPage - 1}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />
    </Paper>
  );
};

export default InventoryTable;
