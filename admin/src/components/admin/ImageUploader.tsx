import React, { useState } from 'react';
import {
  Box,
  Button,
  Grid,
  Typography,
  IconButton,
  CircularProgress,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  Delete as DeleteIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  Edit as EditIcon
} from '@mui/icons-material';
import { toast } from 'react-toastify';

interface ImageUploaderProps {
  images: { url: string; isMain: boolean; displayOrder?: number }[];
  onChange: (images: { url: string; isMain: boolean; displayOrder?: number }[]) => void;
  maxImages?: number;
}

const ImageUploader: React.FC<ImageUploaderProps> = ({
  images,
  onChange,
  maxImages = 10
}) => {
  const [uploading, setUploading] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState<number | null>(null);
  const [displayOrder, setDisplayOrder] = useState<number>(0);

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    if (images.length + files.length > maxImages) {
      toast.error(`You can only upload a maximum of ${maxImages} images`);
      return;
    }

    setUploading(true);

    try {
      const newImages: { url: string; isMain: boolean; displayOrder?: number }[] = [];

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        // In a real app, you would upload to a server here
        // For now, we'll just create a local URL
        const imageUrl = URL.createObjectURL(file);
        newImages.push({
          url: imageUrl,
          isMain: images.length === 0 && i === 0, // First image is main if no images exist
          displayOrder: images.length + i + 1
        });
      }

      onChange([...images, ...newImages]);
    } catch (error) {
      console.error('Error uploading images:', error);
      toast.error('Failed to upload images');
    } finally {
      setUploading(false);
    }
  };

  const handleDeleteImage = (index: number) => {
    const newImages = [...images];
    newImages.splice(index, 1);

    // If we deleted the main image, set the first image as main (if any exist)
    if (images[index].isMain && newImages.length > 0) {
      newImages[0].isMain = true;
    }

    onChange(newImages);
  };

  const handleSetMainImage = (index: number) => {
    const newImages = images.map((image, i) => ({
      ...image,
      isMain: i === index
    }));

    onChange(newImages);
  };

  const handleEditImage = (index: number) => {
    setCurrentImageIndex(index);
    setDisplayOrder(images[index].displayOrder || index + 1);
    setEditDialogOpen(true);
  };

  const handleSaveImageEdit = () => {
    if (currentImageIndex === null) return;

    const newImages = [...images];
    newImages[currentImageIndex] = {
      ...newImages[currentImageIndex],
      displayOrder
    };

    onChange(newImages);
    setEditDialogOpen(false);
    setCurrentImageIndex(null);
  };

  return (
    <Box>
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="subtitle1">
          Product Images ({images.length}/{maxImages})
        </Typography>
        <Button
          component="label"
          variant="contained"
          startIcon={<CloudUploadIcon />}
          disabled={uploading || images.length >= maxImages}
        >
          Upload Images
          <input
            type="file"
            hidden
            accept="image/*"
            multiple
            onChange={handleImageUpload}
            disabled={uploading || images.length >= maxImages}
          />
        </Button>
      </Box>

      {uploading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>
          <CircularProgress />
        </Box>
      )}

      <Grid container spacing={2}>
        {images.map((image, index) => (
          <Grid item xs={6} sm={4} md={3} key={index}>
            <Paper
              elevation={3}
              sx={{
                position: 'relative',
                p: 1,
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                border: image.isMain ? '2px solid #ff3f6c' : 'none'
              }}
            >
              <Box
                sx={{
                  position: 'relative',
                  paddingTop: '100%', // 1:1 Aspect Ratio
                  width: '100%',
                  overflow: 'hidden'
                }}
              >
                <img
                  src={image.url}
                  alt={`Product ${index + 1}`}
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover'
                  }}
                />
              </Box>

              <Box sx={{ mt: 1, display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="caption" color="text.secondary">
                  Order: {image.displayOrder || index + 1}
                </Typography>
                <Box>
                  <IconButton
                    size="small"
                    onClick={() => handleSetMainImage(index)}
                    color={image.isMain ? 'primary' : 'default'}
                    title={image.isMain ? 'Main Image' : 'Set as Main Image'}
                  >
                    {image.isMain ? <StarIcon fontSize="small" /> : <StarBorderIcon fontSize="small" />}
                  </IconButton>
                  <IconButton
                    size="small"
                    onClick={() => handleEditImage(index)}
                    title="Edit Image Order"
                  >
                    <EditIcon fontSize="small" />
                  </IconButton>
                  <IconButton
                    size="small"
                    onClick={() => handleDeleteImage(index)}
                    color="error"
                    title="Delete Image"
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </Box>
              </Box>
            </Paper>
          </Grid>
        ))}
      </Grid>

      {images.length === 0 && !uploading && (
        <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'background.default' }}>
          <Typography variant="body2" color="text.secondary">
            No images uploaded yet. Click "Upload Images" to add product images.
          </Typography>
        </Paper>
      )}

      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)}>
        <DialogTitle>Edit Image Order</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Display Order"
            type="number"
            fullWidth
            value={displayOrder}
            onChange={(e) => setDisplayOrder(parseInt(e.target.value) || 0)}
            InputProps={{ inputProps: { min: 1 } }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleSaveImageEdit} color="primary">Save</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ImageUploader;
