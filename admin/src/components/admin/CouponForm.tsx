import React, { useState, useEffect } from 'react';
import {
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  FormControlLabel,
  Checkbox,
  Button,
  Box,
  CircularProgress,
  Typography,
  Divider
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import api from '../../../services/api';

interface Category {
  id: number;
  name: string;
}

interface Collection {
  id: number;
  name: string;
}

interface Product {
  id: number;
  name: string;
}

interface CouponFormProps {
  initialData?: any;
  onSave: (data: any) => void;
  isEdit?: boolean;
}

const CouponForm: React.FC<CouponFormProps> = ({ initialData, onSave, isEdit = false }) => {
  const [formData, setFormData] = useState({
    code: '',
    description: '',
    type: 'Percentage',
    value: 0,
    applicability: 'All',
    categoryId: '',
    collectionId: '',
    productId: '',
    minimumPurchaseAmount: '',
    minimumQuantity: '',
    maximumDiscountAmount: '',
    isActive: true,
    isOneTimeUse: false,
    displayOnCartPage: false,
    usageLimit: 0,
    startDate: new Date(),
    endDate: null as Date | null
  });

  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [collections, setCollections] = useState<Collection[]>([]);
  const [products, setProducts] = useState<Product[]>([]);

  // Initialize form with initial data if provided
  useEffect(() => {
    if (initialData) {
      setFormData({
        code: initialData.code || '',
        description: initialData.description || '',
        type: initialData.type || 'Percentage',
        value: initialData.value || 0,
        applicability: initialData.applicability || 'All',
        categoryId: initialData.categoryId?.toString() || '',
        collectionId: initialData.collectionId?.toString() || '',
        productId: initialData.productId?.toString() || '',
        minimumPurchaseAmount: initialData.minimumPurchaseAmount?.toString() || '',
        minimumQuantity: initialData.minimumQuantity?.toString() || '',
        maximumDiscountAmount: initialData.maximumDiscountAmount?.toString() || '',
        isActive: initialData.isActive ?? true,
        isOneTimeUse: initialData.isOneTimeUse ?? false,
        displayOnCartPage: initialData.displayOnCartPage ?? false,
        usageLimit: initialData.usageLimit || 0,
        startDate: initialData.startDate ? new Date(initialData.startDate) : new Date(),
        endDate: initialData.endDate ? new Date(initialData.endDate) : null
      });
    }
  }, [initialData]);

  // Fetch reference data
  useEffect(() => {
    const fetchData = async () => {
      try {
        await fetchCategories();
        await fetchCollections();
        await fetchProducts();
      } catch (err) {
        console.error('Error fetching reference data:', err);
      }
    };

    fetchData();
  }, []);

  const fetchCategories = async () => {
    try {
      const response = await api.get('/categories');
      setCategories(response.data);
    } catch (err) {
      console.warn('Using mock category data due to API error');
      setCategories([
        { id: 1, name: 'Men' },
        { id: 2, name: 'Women' },
        { id: 3, name: 'Kids' }
      ]);
    }
  };

  const fetchCollections = async () => {
    try {
      const response = await api.get('/collections');
      setCollections(response.data);
    } catch (err) {
      console.warn('Using mock collection data due to API error');
      setCollections([
        { id: 1, name: 'Summer Collection' },
        { id: 2, name: 'Winter Collection' },
        { id: 3, name: 'Festive Collection' }
      ]);
    }
  };

  const fetchProducts = async () => {
    try {
      const response = await api.get('/product');
      setProducts(response.data);
    } catch (err) {
      console.warn('Using mock product data due to API error');
      setProducts([
        { id: 1, name: 'Cotton T-Shirt' },
        { id: 2, name: 'Denim Jeans' },
        { id: 3, name: 'Summer Dress' }
      ]);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value } = e.target as { name: string; value: unknown };
    setFormData({
      ...formData,
      [name]: value
    });

    // Clear error when user types
    if (formErrors[name]) {
      setFormErrors({
        ...formErrors,
        [name]: ''
      });
    }
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData({
      ...formData,
      [name]: checked
    });
  };

  const handleDateChange = (name: string, date: Date | null) => {
    setFormData({
      ...formData,
      [name]: date
    });
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.code) errors.code = 'Coupon code is required';
    if (!formData.description) errors.description = 'Description is required';
    if (formData.value <= 0) errors.value = 'Value must be greater than 0';
    if (formData.type === 'Percentage' && formData.value > 100) errors.value = 'Percentage cannot exceed 100%';

    // Validate based on applicability
    if (formData.applicability === 'Category' && !formData.categoryId) {
      errors.categoryId = 'Category is required for category-specific coupons';
    }
    if (formData.applicability === 'Collection' && !formData.collectionId) {
      errors.collectionId = 'Collection is required for collection-specific coupons';
    }
    if (formData.applicability === 'Product' && !formData.productId) {
      errors.productId = 'Product is required for product-specific coupons';
    }
    if (formData.applicability === 'MinimumPurchase' && !formData.minimumPurchaseAmount) {
      errors.minimumPurchaseAmount = 'Minimum purchase amount is required';
    }

    if (!formData.startDate) errors.startDate = 'Start date is required';

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      const couponData = {
        code: formData.code.trim().toUpperCase(),
        description: formData.description,
        type: formData.type,
        value: Number(formData.value),
        applicability: formData.applicability,
        categoryId: formData.categoryId ? Number(formData.categoryId) : null,
        collectionId: formData.collectionId ? Number(formData.collectionId) : null,
        productId: formData.productId ? Number(formData.productId) : null,
        minimumPurchaseAmount: formData.minimumPurchaseAmount ? Number(formData.minimumPurchaseAmount) : null,
        minimumQuantity: formData.minimumQuantity ? Number(formData.minimumQuantity) : null,
        maximumDiscountAmount: formData.maximumDiscountAmount ? Number(formData.maximumDiscountAmount) : null,
        isActive: Boolean(formData.isActive),
        isOneTimeUse: Boolean(formData.isOneTimeUse),
        displayOnCartPage: Boolean(formData.displayOnCartPage),
        usageLimit: Number(formData.usageLimit),
        startDate: formData.startDate.toISOString(),
        endDate: formData.endDate ? formData.endDate.toISOString() : null
      };

      onSave(couponData);
    } catch (err) {
      console.error('Error preparing coupon data:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box component="form" noValidate sx={{ mt: 2 }}>
      <Grid container spacing={3}>
        {/* Basic Information */}
        <Grid item xs={12}>
          <Typography variant="subtitle1" fontWeight="bold">Basic Information</Typography>
          <Divider sx={{ mb: 2 }} />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            label="Coupon Code"
            name="code"
            value={formData.code}
            onChange={handleInputChange}
            fullWidth
            required
            disabled={isEdit}
            error={!!formErrors.code}
            helperText={formErrors.code}
            inputProps={{ style: { textTransform: 'uppercase' } }}
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth required error={!!formErrors.type}>
            <InputLabel>Coupon Type</InputLabel>
            <Select
              name="type"
              value={formData.type}
              onChange={handleInputChange}
              label="Coupon Type"
            >
              <MenuItem value="Percentage">Percentage Discount</MenuItem>
              <MenuItem value="FixedAmount">Fixed Amount Discount</MenuItem>
              <MenuItem value="FreeShipping">Free Shipping</MenuItem>
            </Select>
            {formErrors.type && <FormHelperText>{formErrors.type}</FormHelperText>}
          </FormControl>
        </Grid>

        <Grid item xs={12}>
          <TextField
            label="Description"
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            fullWidth
            required
            multiline
            rows={2}
            error={!!formErrors.description}
            helperText={formErrors.description}
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            label={formData.type === 'Percentage' ? 'Discount Percentage (%)' : 'Discount Amount (₹)'}
            name="value"
            type="number"
            value={formData.value}
            onChange={handleInputChange}
            fullWidth
            required
            error={!!formErrors.value}
            helperText={formErrors.value}
            InputProps={{
              inputProps: {
                min: 0,
                max: formData.type === 'Percentage' ? 100 : undefined
              }
            }}
          />
        </Grid>

        {/* Applicability */}
        <Grid item xs={12}>
          <Typography variant="subtitle1" fontWeight="bold" sx={{ mt: 2 }}>Applicability</Typography>
          <Divider sx={{ mb: 2 }} />
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth required error={!!formErrors.applicability}>
            <InputLabel>Applicability</InputLabel>
            <Select
              name="applicability"
              value={formData.applicability}
              onChange={handleInputChange}
              label="Applicability"
            >
              <MenuItem value="All">All Products</MenuItem>
              <MenuItem value="Category">Specific Category</MenuItem>
              <MenuItem value="Collection">Specific Collection</MenuItem>
              <MenuItem value="Product">Specific Product</MenuItem>
              <MenuItem value="MinimumPurchase">Minimum Purchase Amount</MenuItem>
            </Select>
            {formErrors.applicability && <FormHelperText>{formErrors.applicability}</FormHelperText>}
          </FormControl>
        </Grid>

        {formData.applicability === 'Category' && (
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth required error={!!formErrors.categoryId}>
              <InputLabel>Category</InputLabel>
              <Select
                name="categoryId"
                value={formData.categoryId}
                onChange={handleInputChange}
                label="Category"
              >
                {categories.map((category) => (
                  <MenuItem key={category.id} value={category.id}>
                    {category.name}
                  </MenuItem>
                ))}
              </Select>
              {formErrors.categoryId && <FormHelperText>{formErrors.categoryId}</FormHelperText>}
            </FormControl>
          </Grid>
        )}

        {formData.applicability === 'Collection' && (
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth required error={!!formErrors.collectionId}>
              <InputLabel>Collection</InputLabel>
              <Select
                name="collectionId"
                value={formData.collectionId}
                onChange={handleInputChange}
                label="Collection"
              >
                {collections.map((collection) => (
                  <MenuItem key={collection.id} value={collection.id}>
                    {collection.name}
                  </MenuItem>
                ))}
              </Select>
              {formErrors.collectionId && <FormHelperText>{formErrors.collectionId}</FormHelperText>}
            </FormControl>
          </Grid>
        )}

        {formData.applicability === 'Product' && (
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth required error={!!formErrors.productId}>
              <InputLabel>Product</InputLabel>
              <Select
                name="productId"
                value={formData.productId}
                onChange={handleInputChange}
                label="Product"
              >
                {products.map((product) => (
                  <MenuItem key={product.id} value={product.id}>
                    {product.name}
                  </MenuItem>
                ))}
              </Select>
              {formErrors.productId && <FormHelperText>{formErrors.productId}</FormHelperText>}
            </FormControl>
          </Grid>
        )}

        {formData.applicability === 'MinimumPurchase' && (
          <Grid item xs={12} sm={6}>
            <TextField
              label="Minimum Purchase Amount (₹)"
              name="minimumPurchaseAmount"
              type="number"
              value={formData.minimumPurchaseAmount}
              onChange={handleInputChange}
              fullWidth
              required
              error={!!formErrors.minimumPurchaseAmount}
              helperText={formErrors.minimumPurchaseAmount}
              InputProps={{
                inputProps: { min: 0 }
              }}
            />
          </Grid>
        )}

        {/* Limits and Conditions */}
        <Grid item xs={12}>
          <Typography variant="subtitle1" fontWeight="bold" sx={{ mt: 2 }}>Limits and Conditions</Typography>
          <Divider sx={{ mb: 2 }} />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            label="Minimum Quantity"
            name="minimumQuantity"
            type="number"
            value={formData.minimumQuantity}
            onChange={handleInputChange}
            fullWidth
            InputProps={{
              inputProps: { min: 0 }
            }}
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            label="Maximum Discount Amount (₹)"
            name="maximumDiscountAmount"
            type="number"
            value={formData.maximumDiscountAmount}
            onChange={handleInputChange}
            fullWidth
            InputProps={{
              inputProps: { min: 0 }
            }}
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            label="Usage Limit (0 for unlimited)"
            name="usageLimit"
            type="number"
            value={formData.usageLimit}
            onChange={handleInputChange}
            fullWidth
            InputProps={{
              inputProps: { min: 0 }
            }}
          />
        </Grid>

        {/* Validity Period */}
        <Grid item xs={12}>
          <Typography variant="subtitle1" fontWeight="bold" sx={{ mt: 2 }}>Validity Period</Typography>
          <Divider sx={{ mb: 2 }} />
        </Grid>

        <Grid item xs={12} sm={6}>
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <DatePicker
              label="Start Date"
              value={formData.startDate}
              onChange={(date) => handleDateChange('startDate', date)}
              slotProps={{
                textField: {
                  fullWidth: true,
                  required: true,
                  error: !!formErrors.startDate,
                  helperText: formErrors.startDate
                }
              }}
            />
          </LocalizationProvider>
        </Grid>

        <Grid item xs={12} sm={6}>
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <DatePicker
              label="End Date (Optional)"
              value={formData.endDate}
              onChange={(date) => handleDateChange('endDate', date)}
              slotProps={{
                textField: {
                  fullWidth: true
                }
              }}
            />
          </LocalizationProvider>
        </Grid>

        {/* Settings */}
        <Grid item xs={12}>
          <Typography variant="subtitle1" fontWeight="bold" sx={{ mt: 2 }}>Settings</Typography>
          <Divider sx={{ mb: 2 }} />
        </Grid>

        <Grid item xs={12}>
          <FormControlLabel
            control={
              <Checkbox
                checked={formData.isActive}
                onChange={handleCheckboxChange}
                name="isActive"
              />
            }
            label="Active"
          />
          <FormControlLabel
            control={
              <Checkbox
                checked={formData.isOneTimeUse}
                onChange={handleCheckboxChange}
                name="isOneTimeUse"
              />
            }
            label="One-time Use"
          />
          <FormControlLabel
            control={
              <Checkbox
                checked={formData.displayOnCartPage}
                onChange={handleCheckboxChange}
                name="displayOnCartPage"
              />
            }
            label="Display on Cart Page"
          />
        </Grid>
      </Grid>

      <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          variant="contained"
          color="primary"
          onClick={handleSubmit}
          disabled={loading}
        >
          {loading ? <CircularProgress size={24} /> : isEdit ? 'Update Coupon' : 'Create Coupon'}
        </Button>
      </Box>
    </Box>
  );
};

export default CouponForm;
