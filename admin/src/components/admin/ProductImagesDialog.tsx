import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  IconButton,
  CircularProgress,
  Paper,
  Tooltip,
  Divider
} from '@mui/material';
import {
  Delete as DeleteIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  CloudUpload as CloudUploadIcon
} from '@mui/icons-material';
import { AppDispatch, RootState } from '../../store';
import {
  Product,
  ProductImage,
  uploadProductImages,
  deleteProductImage,
  setMainProductImage
} from '../../store/slices/productSlice';
import { uploadMultipleImages } from '../../services/fileUpload';
import { toast } from 'react-toastify';
import { getValidImageUrl, createImageErrorHandler } from '../../utils/imageUtils';

// Use the imported getValidImageUrl function from imageUtils.ts

interface ProductImagesDialogProps {
  open: boolean;
  onClose: () => void;
  product: Product;
}

const ProductImagesDialog: React.FC<ProductImagesDialogProps> = ({ open, onClose, product }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { loading } = useSelector((state: RootState) => state.products);
  const [uploadingImages, setUploadingImages] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [previewUrls, setPreviewUrls] = useState<string[]>([]);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      const files = Array.from(event.target.files);
      setSelectedFiles(prevFiles => [...prevFiles, ...files]);

      // Create preview URLs
      const urls = files.map(file => URL.createObjectURL(file));
      setPreviewUrls(prevUrls => [...prevUrls, ...urls]);

      // Reset the file input so the same files can be selected again if needed
      event.target.value = '';
    }
  };

  const handleUploadImages = async () => {
    if (selectedFiles.length === 0) return;

    try {
      setUploadingImages(true);
      console.log('Uploading files:', selectedFiles.map(f => f.name));

      // Upload images to server
      const imageUrls = await uploadMultipleImages(selectedFiles);
      console.log('Received image URLs:', imageUrls);

      if (!imageUrls || imageUrls.length === 0) {
        throw new Error('No image URLs returned from server');
      }

      // Create product images
      const productImages = imageUrls.map((url, index) => ({
        imageUrl: url,
        isMain: false,
        displayOrder: index
      }));

      // Save product images
      await dispatch(uploadProductImages({
        productId: product.id,
        images: productImages
      })).unwrap();

      // Clear selected files and previews
      setSelectedFiles([]);
      setPreviewUrls([]);

      toast.success('Images uploaded successfully');
    } catch (error: any) {
      const errorMessage = error?.response?.data || 'Failed to upload images';
      toast.error(errorMessage);
      console.error('Error uploading images:', error);
    } finally {
      setUploadingImages(false);
    }
  };

  const handleDeleteImage = async (imageId: number) => {
    try {
      await dispatch(deleteProductImage({
        productId: product.id,
        imageId
      })).unwrap();

      toast.success('Image deleted successfully');
    } catch (error) {
      toast.error('Failed to delete image');
      console.error('Error deleting image:', error);
    }
  };

  const handleSetMainImage = async (imageId: number) => {
    try {
      await dispatch(setMainProductImage({
        productId: product.id,
        imageId
      })).unwrap();

      toast.success('Main image updated successfully');
    } catch (error) {
      toast.error('Failed to update main image');
      console.error('Error updating main image:', error);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        Product Images - {product.name}
      </DialogTitle>
      <DialogContent>
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Current Images
          </Typography>
          <Divider sx={{ mb: 2 }} />

          {product.images && product.images.length > 0 ? (
            <Box sx={{ display: 'grid', gridTemplateColumns: { xs: 'repeat(2, 1fr)', sm: 'repeat(3, 1fr)', md: 'repeat(4, 1fr)' }, gap: 2 }}>
              {product.images.map((image: ProductImage) => (
                <Paper
                  key={image.id}
                  elevation={3}
                  sx={{
                    position: 'relative',
                    p: 1,
                    height: '100%',
                    border: image.isMain ? '2px solid #1976d2' : 'none'
                  }}
                >
                  <Box
                    component="img"
                    src={getValidImageUrl(image.url || image.imageUrl)}
                    alt={`Product image ${image.id}`}
                    sx={{
                      width: '100%',
                      height: 150,
                      objectFit: 'cover',
                      borderRadius: 1,
                      mb: 1
                    }}
                    onError={(e) => createImageErrorHandler()(e, image)}
                  />
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Tooltip title={image.isMain ? "Main Image" : "Set as Main Image"}>
                      <IconButton
                        size="small"
                        color={image.isMain ? "primary" : "default"}
                        onClick={() => !image.isMain && handleSetMainImage(image.id!)}
                        disabled={image.isMain || loading}
                      >
                        {image.isMain ? <StarIcon /> : <StarBorderIcon />}
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Delete Image">
                      <IconButton
                        size="small"
                        color="error"
                        onClick={() => handleDeleteImage(image.id!)}
                        disabled={loading}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </Paper>
              ))}
            </Box>
          ) : (
            <Typography variant="body1" color="text.secondary" align="center" sx={{ py: 3 }}>
              No images available for this product
            </Typography>
          )}
        </Box>

        <Box sx={{ mt: 4 }}>
          <Typography variant="h6" gutterBottom>
            Upload New Images
          </Typography>
          <Divider sx={{ mb: 2 }} />

          <Box sx={{ mb: 2 }}>
            <input
              accept="image/*"
              style={{ display: 'none' }}
              id="upload-product-images"
              multiple
              type="file"
              onChange={handleFileChange}
              disabled={uploadingImages}
            />
            <label htmlFor="upload-product-images">
              <Button
                variant="outlined"
                component="span"
                startIcon={<CloudUploadIcon />}
                disabled={uploadingImages}
              >
                Select Images
              </Button>
            </label>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              You can select multiple images at once
            </Typography>
          </Box>

          {previewUrls.length > 0 && (
            <>
              <Typography variant="subtitle1" gutterBottom>
                Selected Images ({previewUrls.length})
              </Typography>
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: 'repeat(2, 1fr)', sm: 'repeat(3, 1fr)', md: 'repeat(4, 1fr)' }, gap: 2, mb: 2 }}>
                {previewUrls.map((url, index) => (
                  <Paper key={index} elevation={2} sx={{ p: 1 }}>
                    <Box
                      component="img"
                      src={url}
                      alt={`Preview ${index}`}
                      sx={{
                        width: '100%',
                        height: 150,
                        objectFit: 'cover',
                        borderRadius: 1
                      }}
                    />
                  </Paper>
                ))}
              </Box>
              <Button
                variant="contained"
                color="primary"
                startIcon={uploadingImages ? <CircularProgress size={20} color="inherit" /> : <CloudUploadIcon />}
                onClick={handleUploadImages}
                disabled={uploadingImages || selectedFiles.length === 0}
              >
                {uploadingImages ? 'Uploading...' : 'Upload Selected Images'}
              </Button>
            </>
          )}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Close</Button>
      </DialogActions>
    </Dialog>
  );
};

export default ProductImagesDialog;
