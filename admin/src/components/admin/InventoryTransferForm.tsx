import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  Typography,
  CircularProgress,
  Box,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent
} from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../store';
import { 
  InventoryItem, 
  Location, 
  transferInventory, 
  clearInventoryError, 
  fetchLocations 
} from '../../store/slices/inventorySlice';

interface InventoryTransferFormProps {
  open: boolean;
  onClose: () => void;
  item: InventoryItem | null;
  onSuccess: () => void;
  storeId?: number;
}

const InventoryTransferForm: React.FC<InventoryTransferFormProps> = ({ 
  open, 
  onClose, 
  item, 
  onSuccess,
  storeId 
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const { loading, error, locations } = useSelector((state: RootState) => state.inventory);
  
  const [formData, setFormData] = useState({
    destinationLocationId: 0,
    quantity: 0
  });

  useEffect(() => {
    if (open && storeId) {
      dispatch(fetchLocations(storeId));
    }
  }, [open, storeId, dispatch]);

  useEffect(() => {
    if (item) {
      setFormData({
        destinationLocationId: 0,
        quantity: 0
      });
    }
  }, [item]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: parseInt(value, 10) || 0
    });
  };

  const handleSelectChange = (e: SelectChangeEvent<number>) => {
    setFormData({
      ...formData,
      destinationLocationId: e.target.value as number
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!item) return;

    try {
      await dispatch(transferInventory({
        sourceLocationId: item.locationId,
        destinationLocationId: formData.destinationLocationId,
        productVariantId: item.id, // This should be the product variant ID, not the inventory item ID
        quantity: formData.quantity
      })).unwrap();
      
      onSuccess();
      onClose();
    } catch (err) {
      // Error is handled by the reducer
    }
  };

  const handleClose = () => {
    dispatch(clearInventoryError());
    onClose();
  };

  // Filter out the current location from the destination options
  const availableDestinations = locations.filter(loc => 
    item && loc.id !== item.locationId && loc.isActive
  );

  const maxQuantity = item ? item.stockQuantity : 0;

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        Transfer Inventory
      </DialogTitle>
      <form onSubmit={handleSubmit}>
        <DialogContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          
          {item && (
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Typography variant="subtitle1">
                  Source Location: {item.locationName} ({item.locationCode})
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Current Stock: {item.stockQuantity}
                </Typography>
              </Grid>
              
              <Grid item xs={12}>
                <FormControl fullWidth required>
                  <InputLabel id="destination-location-label">Destination Location</InputLabel>
                  <Select
                    labelId="destination-location-label"
                    id="destinationLocationId"
                    name="destinationLocationId"
                    value={formData.destinationLocationId}
                    onChange={handleSelectChange}
                    label="Destination Location"
                  >
                    <MenuItem value={0} disabled>Select a location</MenuItem>
                    {availableDestinations.map((location) => (
                      <MenuItem key={location.id} value={location.id}>
                        {location.name} ({location.code})
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Quantity to Transfer"
                  name="quantity"
                  type="number"
                  value={formData.quantity}
                  onChange={handleChange}
                  required
                  InputProps={{ 
                    inputProps: { 
                      min: 1, 
                      max: maxQuantity 
                    } 
                  }}
                  helperText={`Maximum: ${maxQuantity}`}
                />
              </Grid>
            </Grid>
          )}
          
          {!item && (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
              <CircularProgress />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose} disabled={loading}>
            Cancel
          </Button>
          <Button 
            type="submit" 
            variant="contained" 
            color="primary" 
            disabled={
              loading || 
              !item || 
              formData.destinationLocationId === 0 || 
              formData.quantity <= 0 || 
              formData.quantity > maxQuantity
            }
          >
            {loading ? <CircularProgress size={24} /> : 'Transfer'}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default InventoryTransferForm;
