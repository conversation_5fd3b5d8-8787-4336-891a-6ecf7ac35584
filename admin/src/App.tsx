import React, { useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';

// Layouts
import AdminLayout from './layouts/AdminLayout';

// Pages
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import ProductManagement from './pages/ProductManagement';
import ProductDetail from './pages/ProductDetail';
import CategoryManagement from './pages/CategoryManagement';
import CollectionManagement from './pages/CollectionManagement';
import NavigationManagement from './pages/NavigationManagement';
import CouponManagement from './pages/CouponManagement';
import WebsiteConfigurationManagement from './pages/WebsiteConfigurationManagement';
import ThemeConfigurationManagement from './pages/ThemeConfigurationManagement';
import BulkImageUploadPage from './pages/BulkImageUploadPage';
import OrderManagement from './pages/OrderManagement';
import InventoryManagement from './pages/InventoryManagement';
import Settings from './pages/Settings';

// Role-Based Access Control Pages
import { RolesPage, RoleFormPage, RoleManagementPage } from './pages/roles';
import { UserRolesPage, UserRoleManagementPage, UserManagementPage } from './pages/users';
import {
  StoreManagementPage,
  StoreFormPage,
  StoreDetailPage,
  StoreAdminFormPage,
  StoreWebsiteConfigPage
} from './pages/stores';
import UserModulePermissionPage from './pages/UserModulePermissions/UserModulePermissionPage';

// Types
import { RootState, AppDispatch } from './store';
import { getCurrentUser } from './store/slices/authSlice';

// Protected route component
const AdminRoute = ({ children }: { children: React.ReactNode }) => {
  const { isAuthenticated, user, loading } = useSelector((state: RootState) => state.auth);

  // If still loading, don't redirect yet
  if (loading) {
    return <div>Loading...</div>;
  }

  // Only redirect if we're sure the user is not authenticated or doesn't have admin role
  if (!isAuthenticated || (user && !user.roles.includes('Admin'))) {
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
};

function App() {
  const dispatch = useDispatch<AppDispatch>();

  // Check for token and load user on app initialization
  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      dispatch(getCurrentUser());
    }
  }, [dispatch]);

  return (
    <Routes>
      {/* Public Routes */}
      <Route path="/login" element={<Login />} />

      {/* Protected Admin Routes */}
      <Route path="/" element={
        <AdminRoute>
          <AdminLayout />
        </AdminRoute>
      }>
        <Route index element={<Dashboard />} />
        <Route path="products" element={<ProductManagement />} />
        <Route path="products/:id" element={<ProductDetail />} />
        <Route path="categories" element={<CategoryManagement />} />
        <Route path="collections" element={<CollectionManagement />} />
        <Route path="orders" element={<OrderManagement />} />
        <Route path="inventory" element={<InventoryManagement />} />
        <Route path="navigation" element={<NavigationManagement />} />
        <Route path="users" element={<UserManagementPage />} />
        <Route path="users/roles" element={<UserRoleManagementPage />} />
        <Route path="users/roles/legacy" element={<UserRolesPage />} />
        <Route path="users/permissions" element={<UserModulePermissionPage />} />
        <Route path="roles" element={<RoleManagementPage />} />
        <Route path="roles/legacy" element={<RolesPage />} />
        <Route path="roles/create" element={<RoleFormPage />} />
        <Route path="roles/edit/:id" element={<RoleFormPage />} />
        <Route path="stores" element={<StoreManagementPage />} />
        <Route path="stores/create" element={<StoreFormPage />} />
        <Route path="stores/edit/:id" element={<StoreFormPage />} />
        <Route path="stores/view/:id" element={<StoreDetailPage />} />
        <Route path="stores/:storeId/admins/add" element={<StoreAdminFormPage />} />
        <Route path="stores/:storeId/admins/edit/:adminId" element={<StoreAdminFormPage />} />
        <Route path="stores/:storeId/website-config" element={<StoreWebsiteConfigPage />} />
        <Route path="coupons" element={<CouponManagement />} />
        <Route path="website-configuration" element={<WebsiteConfigurationManagement />} />
        <Route path="themes" element={<ThemeConfigurationManagement />} />
        <Route path="images" element={<BulkImageUploadPage />} />
        <Route path="settings" element={<Settings />} />
      </Route>

      {/* 404 Route */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
}

export default App;
