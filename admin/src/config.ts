// Configuration settings for the admin application
const config = {
  // API URL - can be changed based on environment
  apiUrl: import.meta.env.VITE_API_URL || 'http://localhost:5295/api',
  
  // API base URL (without the /api suffix)
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:5295',
  
  // Other configuration settings can be added here
  pageSize: 10,
  
  // Feature flags
  features: {
    enableBulkOperations: true,
    enableExport: true,
    enableImport: true,
  }
};

export default config;
