import config from '../config';

/**
 * Ensures image URLs are properly formatted
 * @param url The image URL to format
 * @returns A properly formatted URL
 */
export const getValidImageUrl = (url: string | undefined): string => {
  if (!url) return '';

  // If the URL is already absolute (starts with http:// or https://), return it as is
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }

  // Extract the filename regardless of path format
  const parts = url.split('/');
  const filename = parts[parts.length - 1];

  // Use the direct path to the image as configured in the API
  // The API is configured to serve files from multiple paths, but we'll use /uploads/images as primary
  const baseUrl = config.apiBaseUrl.replace('/api', '');
  return `${baseUrl}/uploads/images/${filename}`;
};

/**
 * Creates a fallback handler for image loading errors
 * @returns An onError handler function that tries different image paths before using a fallback SVG
 */
export const createImageErrorHandler = () => {
  return (e: React.SyntheticEvent<HTMLImageElement, Event>, imageInfo?: any) => {
    const imgElement = e.currentTarget;
    const currentSrc = imgElement.src;

    // Only log the error once to avoid console spam
    if (!imgElement.getAttribute('data-error-logged')) {
      if (imageInfo) {
        console.error('Image failed to load:', imageInfo);
      } else {
        console.error('Image failed to load:', currentSrc);
      }
      imgElement.setAttribute('data-error-logged', 'true');
    }

    // Track fallback attempts
    const fallbackAttempt = parseInt(imgElement.getAttribute('data-fallback-attempt') || '0');

    // Extract filename from current path
    const parts = currentSrc.split('/');
    const filename = parts[parts.length - 1];
    const baseUrl = config.apiBaseUrl.replace('/api', '');

    // Try different fallback strategies based on the attempt number
    if (fallbackAttempt === 0) {
      // First attempt: Try uploads/images path (used by FileUploadAdminController)
      imgElement.setAttribute('data-fallback-attempt', '1');
      imgElement.src = `${baseUrl}/uploads/images/${filename}`;
      return;
    }
    else if (fallbackAttempt === 1) {
      // Second attempt: Try direct path to upload/image folder (used by FileUploadController)
      imgElement.setAttribute('data-fallback-attempt', '2');
      imgElement.src = `${baseUrl}/upload/image/${filename}`;
      return;
    }
    else if (fallbackAttempt === 2) {
      // Third attempt: Try with www/upload/image path (for backward compatibility)
      imgElement.setAttribute('data-fallback-attempt', '3');
      imgElement.src = `${baseUrl}/uploads/images/${filename}`;
      return;
    }
    else {
      // Final fallback: Use SVG placeholder
      // Use a simple data URI for the no-image placeholder (light gray box with image icon)
      // This avoids any external HTTP requests that might fail
      const noImageSvg = `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Crect width='100' height='100' fill='%23f0f0f0'/%3E%3Cpath d='M35 40 L50 55 L65 40 M40 65 L60 65' stroke='%23999' stroke-width='2' fill='none'/%3E%3C/svg%3E`;

      imgElement.src = noImageSvg;
    }
  };
};
