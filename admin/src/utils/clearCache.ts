/**
 * Utility function to clear application cache
 * This helps resolve issues with slow typing in chat windows and other performance problems
 */
export const clearApplicationCache = () => {
  // Clear localStorage items
  localStorage.removeItem('token');
  localStorage.removeItem('user');
  
  // Clear sessionStorage items
  sessionStorage.clear();
  
  // Clear Redux store (this requires dispatch from the component)
  
  // Clear browser cache for the application domain
  if ('caches' in window) {
    caches.keys().then((keyList) => {
      return Promise.all(
        keyList.map((key) => {
          return caches.delete(key);
        })
      );
    });
  }
  
  console.log('Application cache cleared successfully');
  return true;
};

/**
 * Clear only chat-related cache
 * This is a lighter version that only clears chat data
 */
export const clearChatCache = () => {
  // Clear chat-specific localStorage items
  const localStorageKeys = Object.keys(localStorage);
  localStorageKeys.forEach(key => {
    if (key.includes('chat') || key.includes('message')) {
      localStorage.removeItem(key);
    }
  });
  
  // Clear chat-specific sessionStorage items
  const sessionStorageKeys = Object.keys(sessionStorage);
  sessionStorageKeys.forEach(key => {
    if (key.includes('chat') || key.includes('message')) {
      sessionStorage.removeItem(key);
    }
  });
  
  console.log('Chat cache cleared successfully');
  return true;
};
