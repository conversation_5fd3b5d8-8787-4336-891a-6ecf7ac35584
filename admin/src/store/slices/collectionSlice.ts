import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import api from '../../services/api';

export interface Collection {
  id: number;
  name: string;
  description: string;
  imageUrl: string;
  isActive: boolean;
  displayOrder: number;
  productCount: number;
  storeId: number;
}

interface CollectionState {
  collections: Collection[];
  loading: boolean;
  error: string | null;
  selectedCollections: number[];
}

const initialState: CollectionState = {
  collections: [],
  loading: false,
  error: null,
  selectedCollections: []
};

export const fetchCollections = createAsyncThunk(
  'collections/fetchCollections',
  async (storeId?: number, { rejectWithValue }) => {
    try {
      const url = storeId
        ? `/admin/collection?storeId=${storeId}`
        : '/admin/collection';
      const response = await api.get(url);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch collections');
    }
  }
);

export const createCollection = createAsyncThunk(
  'collections/createCollection',
  async (collection: Omit<Collection, 'id' | 'productCount'>, { rejectWithValue }) => {
    try {
      const response = await api.post('/admin/collection', collection);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create collection');
    }
  }
);

export const updateCollection = createAsyncThunk(
  'collections/updateCollection',
  async ({ id, collection }: { id: number; collection: Omit<Collection, 'id' | 'productCount' | 'storeId'> }, { rejectWithValue }) => {
    try {
      await api.put(`/admin/collection/${id}`, collection);
      return { id, ...collection };
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update collection');
    }
  }
);

export const deleteCollection = createAsyncThunk(
  'collections/deleteCollection',
  async (id: number, { rejectWithValue }) => {
    try {
      await api.delete(`/admin/collection/${id}`);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete collection');
    }
  }
);

export const bulkUpdateCollectionStatus = createAsyncThunk(
  'collections/bulkUpdateStatus',
  async ({ ids, isActive }: { ids: number[], isActive: boolean }, { rejectWithValue }) => {
    try {
      await api.put('/admin/collection/bulk-status', { collectionIds: ids, isActive });
      return { ids, isActive };
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update collections status');
    }
  }
);

export const bulkDeleteCollections = createAsyncThunk(
  'collections/bulkDelete',
  async (ids: number[], { rejectWithValue }) => {
    try {
      await api.post('/admin/collection/bulk-delete', { collectionIds: ids });
      return ids;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete collections');
    }
  }
);

const collectionSlice = createSlice({
  name: 'collections',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    selectCollection: (state, action: PayloadAction<number>) => {
      if (!state.selectedCollections.includes(action.payload)) {
        state.selectedCollections.push(action.payload);
      }
    },
    deselectCollection: (state, action: PayloadAction<number>) => {
      state.selectedCollections = state.selectedCollections.filter(id => id !== action.payload);
    },
    toggleCollectionSelection: (state, action: PayloadAction<number>) => {
      const index = state.selectedCollections.indexOf(action.payload);
      if (index === -1) {
        state.selectedCollections.push(action.payload);
      } else {
        state.selectedCollections.splice(index, 1);
      }
    },
    selectAllCollections: (state) => {
      state.selectedCollections = state.collections.map(collection => collection.id);
    },
    clearCollectionSelection: (state) => {
      state.selectedCollections = [];
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch collections
      .addCase(fetchCollections.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCollections.fulfilled, (state, action: PayloadAction<Collection[]>) => {
        state.collections = action.payload;
        state.loading = false;
        // Clear selection when collections are refreshed
        state.selectedCollections = [];
      })
      .addCase(fetchCollections.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Create collection
      .addCase(createCollection.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createCollection.fulfilled, (state, action: PayloadAction<Collection>) => {
        state.collections.push(action.payload);
        state.loading = false;
      })
      .addCase(createCollection.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Update collection
      .addCase(updateCollection.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateCollection.fulfilled, (state, action) => {
        const index = state.collections.findIndex(c => c.id === action.payload.id);
        if (index !== -1) {
          state.collections[index] = {
            ...state.collections[index],
            ...action.payload
          };
        }
        state.loading = false;
      })
      .addCase(updateCollection.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Delete collection
      .addCase(deleteCollection.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteCollection.fulfilled, (state, action: PayloadAction<number>) => {
        state.collections = state.collections.filter(c => c.id !== action.payload);
        state.loading = false;
        // Remove from selected collections if present
        state.selectedCollections = state.selectedCollections.filter(id => id !== action.payload);
      })
      .addCase(deleteCollection.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Bulk update collection status
      .addCase(bulkUpdateCollectionStatus.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(bulkUpdateCollectionStatus.fulfilled, (state, action) => {
        state.loading = false;
        const { ids, isActive } = action.payload;
        state.collections = state.collections.map(collection => {
          if (ids.includes(collection.id)) {
            return { ...collection, isActive };
          }
          return collection;
        });
        // Clear selection after bulk update
        state.selectedCollections = [];
      })
      .addCase(bulkUpdateCollectionStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Bulk delete collections
      .addCase(bulkDeleteCollections.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(bulkDeleteCollections.fulfilled, (state, action: PayloadAction<number[]>) => {
        state.loading = false;
        state.collections = state.collections.filter(c => !action.payload.includes(c.id));
        // Clear selection after bulk delete
        state.selectedCollections = [];
      })
      .addCase(bulkDeleteCollections.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  }
});

export const {
  clearError,
  selectCollection,
  deselectCollection,
  toggleCollectionSelection,
  selectAllCollections,
  clearCollectionSelection
} = collectionSlice.actions;
export default collectionSlice.reducer;
