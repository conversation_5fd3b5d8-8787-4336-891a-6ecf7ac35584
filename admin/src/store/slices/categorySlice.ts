import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import api from '../../services/api';

export interface Category {
  id: number;
  name: string;
  description: string;
  imageUrl: string;
  isActive: boolean;
  displayOrder: number;
  productCount: number;
  parentId?: number | null;
  isSale?: boolean;
  storeId: number;
  children?: Category[];
}

interface CategoryState {
  categories: Category[];
  loading: boolean;
  error: string | null;
  selectedCategories: number[];
}

const initialState: CategoryState = {
  categories: [],
  loading: false,
  error: null,
  selectedCategories: []
};

interface FetchCategoriesParams {
  storeId?: number | null;
}

export const fetchCategories = createAsyncThunk(
  'categories/fetchCategories',
  async (params: FetchCategoriesParams = {}, { rejectWithValue }) => {
    try {
      let url = '/admin/category';

      // Add query parameters if provided
      const queryParams = new URLSearchParams();
      if (params.storeId) {
        queryParams.append('storeId', params.storeId.toString());
      }

      const queryString = queryParams.toString();
      if (queryString) {
        url += `?${queryString}`;
      }

      const response = await api.get(url);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch categories');
    }
  }
);

export const createCategory = createAsyncThunk(
  'categories/createCategory',
  async (category: Omit<Category, 'id' | 'productCount' | 'children'>, { rejectWithValue }) => {
    try {
      const response = await api.post('/admin/category', category);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create category');
    }
  }
);

export const updateCategory = createAsyncThunk(
  'categories/updateCategory',
  async ({ id, category }: { id: number; category: Omit<Category, 'id' | 'productCount' | 'children' | 'storeId'> }, { rejectWithValue }) => {
    try {
      await api.put(`/admin/category/${id}`, category);
      return { id, ...category };
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update category');
    }
  }
);

export const deleteCategory = createAsyncThunk(
  'categories/deleteCategory',
  async (id: number, { rejectWithValue }) => {
    try {
      await api.delete(`/admin/category/${id}`);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete category');
    }
  }
);

export const bulkUpdateCategoryStatus = createAsyncThunk(
  'categories/bulkUpdateStatus',
  async ({ ids, isActive }: { ids: number[], isActive: boolean }, { rejectWithValue }) => {
    try {
      await api.put('/admin/category/bulk-status', { categoryIds: ids, isActive });
      return { ids, isActive };
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update categories status');
    }
  }
);

export const bulkDeleteCategories = createAsyncThunk(
  'categories/bulkDelete',
  async (ids: number[], { rejectWithValue }) => {
    try {
      await api.post('/admin/category/bulk-delete', { categoryIds: ids });
      return ids;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete categories');
    }
  }
);

const categorySlice = createSlice({
  name: 'categories',
  initialState,
  reducers: {
    selectCategory: (state, action: PayloadAction<number>) => {
      if (!state.selectedCategories.includes(action.payload)) {
        state.selectedCategories.push(action.payload);
      }
    },
    deselectCategory: (state, action: PayloadAction<number>) => {
      state.selectedCategories = state.selectedCategories.filter(id => id !== action.payload);
    },
    toggleCategorySelection: (state, action: PayloadAction<number>) => {
      const index = state.selectedCategories.indexOf(action.payload);
      if (index === -1) {
        state.selectedCategories.push(action.payload);
      } else {
        state.selectedCategories.splice(index, 1);
      }
    },
    selectAllCategories: (state) => {
      state.selectedCategories = state.categories.map(category => category.id);
    },
    clearCategorySelection: (state) => {
      state.selectedCategories = [];
    }
  },
  extraReducers: (builder) => {
    // Fetch categories
    builder.addCase(fetchCategories.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(fetchCategories.fulfilled, (state, action: PayloadAction<Category[]>) => {
      state.loading = false;
      state.categories = action.payload;
      // Clear selection when categories are refreshed
      state.selectedCategories = [];
    });
    builder.addCase(fetchCategories.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string || 'Failed to fetch categories';
    });

    // Create category
    builder.addCase(createCategory.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(createCategory.fulfilled, (state, action: PayloadAction<Category>) => {
      state.loading = false;
      state.categories.push(action.payload);
    });
    builder.addCase(createCategory.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string || 'Failed to create category';
    });

    // Update category
    builder.addCase(updateCategory.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(updateCategory.fulfilled, (state, action) => {
      state.loading = false;
      const index = state.categories.findIndex(cat => cat.id === action.payload.id);
      if (index !== -1) {
        // Preserve productCount and children from the existing category
        const productCount = state.categories[index].productCount;
        const children = state.categories[index].children;
        state.categories[index] = {
          ...action.payload,
          productCount,
          children,
          storeId: state.categories[index].storeId // Preserve storeId
        };
      }
    });
    builder.addCase(updateCategory.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string || 'Failed to update category';
    });

    // Delete category
    builder.addCase(deleteCategory.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(deleteCategory.fulfilled, (state, action: PayloadAction<number>) => {
      state.loading = false;
      state.categories = state.categories.filter(cat => cat.id !== action.payload);
      // Remove from selected categories if present
      state.selectedCategories = state.selectedCategories.filter(id => id !== action.payload);
    });
    builder.addCase(deleteCategory.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string || 'Failed to delete category';
    });

    // Bulk update category status
    builder.addCase(bulkUpdateCategoryStatus.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(bulkUpdateCategoryStatus.fulfilled, (state, action) => {
      state.loading = false;
      const { ids, isActive } = action.payload;
      state.categories = state.categories.map(category => {
        if (ids.includes(category.id)) {
          return { ...category, isActive };
        }
        return category;
      });
      // Clear selection after bulk update
      state.selectedCategories = [];
    });
    builder.addCase(bulkUpdateCategoryStatus.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string || 'Failed to update categories status';
    });

    // Bulk delete categories
    builder.addCase(bulkDeleteCategories.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(bulkDeleteCategories.fulfilled, (state, action: PayloadAction<number[]>) => {
      state.loading = false;
      state.categories = state.categories.filter(cat => !action.payload.includes(cat.id));
      // Clear selection after bulk delete
      state.selectedCategories = [];
    });
    builder.addCase(bulkDeleteCategories.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string || 'Failed to delete categories';
    });
  }
});

export const {
  selectCategory,
  deselectCategory,
  toggleCategorySelection,
  selectAllCategories,
  clearCategorySelection
} = categorySlice.actions;

export default categorySlice.reducer;
