import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import themeService from '../../services/themeService';

export interface ThemeConfiguration {
  id: number;
  name: string;
  description: string;
  isActive: boolean;
  storeId: number;
  
  // Primary Colors
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  
  // Text Colors
  textPrimaryColor: string;
  textSecondaryColor: string;
  textLightColor: string;
  
  // Background Colors
  backgroundPrimaryColor: string;
  backgroundSecondaryColor: string;
  backgroundAccentColor: string;
  
  // Button Styles
  buttonPrimaryColor: string;
  buttonSecondaryColor: string;
  buttonTextColor: string;
  buttonBorderRadius: string;
  
  // Card Styles
  cardBackgroundColor: string;
  cardBorderColor: string;
  cardBorderRadius: string;
  cardShadow: string;
  
  // Typography
  headingFontFamily: string;
  bodyFontFamily: string;
  fontBaseSize: string;
  
  // Layout
  spacingUnit: string;
  containerMaxWidth: string;
  containerPadding: string;
  
  // Header
  headerBackgroundColor: string;
  headerTextColor: string;
  headerHeight: string;
  
  // Footer
  footerBackgroundColor: string;
  footerTextColor: string;
  
  // Navigation
  navLinkColor: string;
  navLinkActiveColor: string;
  navLinkHoverColor: string;
  
  // Links
  linkColor: string;
  
  // Forms
  inputBackgroundColor: string;
  inputBorderColor: string;
  inputBorderRadius: string;
  inputFocusBorderColor: string;
  
  // Custom CSS
  customCSS: string;
  
  createdAt?: string;
  updatedAt?: string;
}

export interface StoreBasicInfo {
  id: number;
  name: string;
  domain: string;
}

export interface ThemeAssignment {
  themeId: number;
  storeId: number;
  storeName: string;
  isActive: boolean;
  assignedAt: string;
}

interface ThemeState {
  themes: ThemeConfiguration[];
  activeTheme: ThemeConfiguration | null;
  selectedTheme: ThemeConfiguration | null;
  themeAssignments: ThemeAssignment[];
  availableStores: StoreBasicInfo[];
  loading: boolean;
  error: string | null;
}

const initialState: ThemeState = {
  themes: [],
  activeTheme: null,
  selectedTheme: null,
  themeAssignments: [],
  availableStores: [],
  loading: false,
  error: null,
};

// Fetch all themes for current store
export const fetchAllThemes = createAsyncThunk(
  'theme/fetchAllThemes',
  async (storeId: number, { rejectWithValue }) => {
    try {
      const response = await themeService.getThemesByStore(storeId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || 'Failed to fetch themes');
    }
  }
);

// Fetch active theme
export const fetchActiveTheme = createAsyncThunk(
  'theme/fetchActiveTheme',
  async (storeId: number, { rejectWithValue }) => {
    try {
      const response = await themeService.getActiveTheme(storeId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || 'Failed to fetch active theme');
    }
  }
);

// Create a new theme
export const createTheme = createAsyncThunk(
  'theme/createTheme',
  async (themeData: Partial<ThemeConfiguration>, { rejectWithValue }) => {
    try {
      const response = await themeService.createTheme(themeData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || 'Failed to create theme');
    }
  }
);

// Update a theme
export const updateTheme = createAsyncThunk(
  'theme/updateTheme',
  async ({ id, themeData }: { id: number, themeData: Partial<ThemeConfiguration> }, { rejectWithValue }) => {
    try {
      await themeService.updateTheme(id, themeData);
      return { id, ...themeData };
    } catch (error: any) {
      return rejectWithValue(error.response?.data || 'Failed to update theme');
    }
  }
);

// Activate a theme
export const activateTheme = createAsyncThunk(
  'theme/activateTheme',
  async (id: number, { rejectWithValue }) => {
    try {
      await themeService.activateTheme(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || 'Failed to activate theme');
    }
  }
);

// Delete a theme
export const deleteTheme = createAsyncThunk(
  'theme/deleteTheme',
  async (id: number, { rejectWithValue }) => {
    try {
      await themeService.deleteTheme(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || 'Failed to delete theme');
    }
  }
);

// Duplicate a theme
export const duplicateTheme = createAsyncThunk(
  'theme/duplicateTheme',
  async (id: number, { rejectWithValue }) => {
    try {
      const response = await themeService.duplicateTheme(id);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || 'Failed to duplicate theme');
    }
  }
);

// Assign theme to store
export const assignThemeToStore = createAsyncThunk(
  'theme/assignThemeToStore',
  async ({ themeId, storeId }: { themeId: number, storeId: number }, { rejectWithValue }) => {
    try {
      const response = await themeService.assignThemeToStore(themeId, storeId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || 'Failed to assign theme to store');
    }
  }
);

// Fetch theme assignments
export const fetchThemeAssignments = createAsyncThunk(
  'theme/fetchThemeAssignments',
  async (themeId: number, { rejectWithValue }) => {
    try {
      const response = await themeService.getThemeAssignments(themeId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || 'Failed to fetch theme assignments');
    }
  }
);

// Fetch available stores for assignment
export const fetchAvailableStores = createAsyncThunk(
  'theme/fetchAvailableStores',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/admin/stores/basic');
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || 'Failed to fetch available stores');
    }
  }
);

// Add a function to fetch themes based on user role
export const fetchThemes = createAsyncThunk(
  'theme/fetchThemes',
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as RootState;
      const { user } = state.auth;
      
      // If super admin, get all themes
      if (user?.role === 'SuperAdmin') {
        return await themeService.getAllThemes();
      } 
      // Otherwise get themes for current store
      else if (user?.storeId) {
        return await themeService.getThemesByStore(user.storeId);
      } else {
        return rejectWithValue('User has no store assigned');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data || 'Failed to fetch themes');
    }
  }
);

const themeSlice = createSlice({
  name: 'theme',
  initialState,
  reducers: {
    setSelectedTheme: (state, action: PayloadAction<ThemeConfiguration | null>) => {
      state.selectedTheme = action.payload;
    },
    clearThemeError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch all themes
      .addCase(fetchAllThemes.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAllThemes.fulfilled, (state, action) => {
        state.loading = false;
        state.themes = action.payload;
      })
      .addCase(fetchAllThemes.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Fetch active theme
      .addCase(fetchActiveTheme.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchActiveTheme.fulfilled, (state, action) => {
        state.loading = false;
        state.activeTheme = action.payload;
      })
      .addCase(fetchActiveTheme.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Create theme
      .addCase(createTheme.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createTheme.fulfilled, (state, action) => {
        state.loading = false;
        state.themes.push(action.payload);
        state.selectedTheme = action.payload;
      })
      .addCase(createTheme.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Update theme
      .addCase(updateTheme.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateTheme.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.themes.findIndex(theme => theme.id === action.payload.id);
        if (index !== -1) {
          state.themes[index] = { ...state.themes[index], ...action.payload };
          if (state.selectedTheme?.id === action.payload.id) {
            state.selectedTheme = { ...state.selectedTheme, ...action.payload };
          }
        }
      })
      .addCase(updateTheme.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Activate theme
      .addCase(activateTheme.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(activateTheme.fulfilled, (state, action) => {
        state.loading = false;
        state.themes = state.themes.map(theme => ({
          ...theme,
          isActive: theme.id === action.payload
        }));
        const activeTheme = state.themes.find(theme => theme.id === action.payload);
        if (activeTheme) {
          state.activeTheme = activeTheme;
        }
      })
      .addCase(activateTheme.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Delete theme
      .addCase(deleteTheme.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteTheme.fulfilled, (state, action) => {
        state.loading = false;
        state.themes = state.themes.filter(theme => theme.id !== action.payload);
        if (state.selectedTheme?.id === action.payload) {
          state.selectedTheme = null;
        }
      })
      .addCase(deleteTheme.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Duplicate theme
      .addCase(duplicateTheme.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(duplicateTheme.fulfilled, (state, action) => {
        state.loading = false;
        state.themes.push(action.payload);
        state.selectedTheme = action.payload;
      })
      .addCase(duplicateTheme.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

      // Assign theme to store
      builder.addCase(assignThemeToStore.pending, (state) => {
        state.loading = true;
        state.error = null;
      });
      builder.addCase(assignThemeToStore.fulfilled, (state, action: PayloadAction<ThemeAssignment>) => {
        state.loading = false;
        // Add the new assignment if it doesn't exist
        const exists = state.themeAssignments.some(
          assignment => assignment.themeId === action.payload.themeId && 
                        assignment.storeId === action.payload.storeId
        );
        
        if (!exists) {
          state.themeAssignments.push(action.payload);
        }
      });
      builder.addCase(assignThemeToStore.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

      // Fetch theme assignments
      builder.addCase(fetchThemeAssignments.pending, (state) => {
        state.loading = true;
        state.error = null;
      });
      builder.addCase(fetchThemeAssignments.fulfilled, (state, action: PayloadAction<ThemeAssignment[]>) => {
        state.loading = false;
        state.themeAssignments = action.payload;
      });
      builder.addCase(fetchThemeAssignments.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

      // Fetch available stores
      builder.addCase(fetchAvailableStores.pending, (state) => {
        state.loading = true;
        state.error = null;
      });
      builder.addCase(fetchAvailableStores.fulfilled, (state, action: PayloadAction<StoreBasicInfo[]>) => {
        state.loading = false;
        state.availableStores = action.payload;
      });
      builder.addCase(fetchAvailableStores.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setSelectedTheme, clearThemeError } = themeSlice.actions;
export default themeSlice.reducer;
