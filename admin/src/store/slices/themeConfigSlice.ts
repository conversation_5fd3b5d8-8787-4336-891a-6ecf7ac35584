import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import {
  themeConfigService,
  ThemeConfiguration,
  CreateThemeConfigurationRequest,
  UpdateThemeConfigurationRequest
} from '../../services/themeConfigService';

interface ThemeConfigState {
  configurations: ThemeConfiguration[];
  storeConfigurations: ThemeConfiguration[];
  currentConfiguration: ThemeConfiguration | null;
  activeConfiguration: ThemeConfiguration | null;
  loading: boolean;
  error: string | null;
}

const initialState: ThemeConfigState = {
  configurations: [],
  storeConfigurations: [],
  currentConfiguration: null,
  activeConfiguration: null,
  loading: false,
  error: null
};

// Async thunks
export const fetchAllThemeConfigurations = createAsyncThunk(
  'themeConfig/fetchAllThemeConfigurations',
  async (_, { rejectWithValue }) => {
    try {
      return await themeConfigService.getAllThemeConfigurations();
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch theme configurations');
    }
  }
);

export const fetchThemeConfigurationsByStore = createAsyncThunk(
  'themeConfig/fetchThemeConfigurationsByStore',
  async (storeId: number, { rejectWithValue }) => {
    try {
      return await themeConfigService.getThemeConfigurationsByStore(storeId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch theme configurations for store');
    }
  }
);

export const fetchActiveThemeConfigurationByStore = createAsyncThunk(
  'themeConfig/fetchActiveThemeConfigurationByStore',
  async (storeId: number, { rejectWithValue }) => {
    try {
      return await themeConfigService.getActiveThemeConfigurationByStore(storeId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch active theme configuration');
    }
  }
);

export const fetchThemeConfigurationById = createAsyncThunk(
  'themeConfig/fetchThemeConfigurationById',
  async (id: number, { rejectWithValue }) => {
    try {
      return await themeConfigService.getThemeConfigurationById(id);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch theme configuration');
    }
  }
);

export const createThemeConfiguration = createAsyncThunk(
  'themeConfig/createThemeConfiguration',
  async (config: CreateThemeConfigurationRequest, { rejectWithValue }) => {
    try {
      return await themeConfigService.createThemeConfiguration(config);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create theme configuration');
    }
  }
);

export const updateThemeConfiguration = createAsyncThunk(
  'themeConfig/updateThemeConfiguration',
  async ({ id, config }: { id: number; config: UpdateThemeConfigurationRequest }, { rejectWithValue }) => {
    try {
      await themeConfigService.updateThemeConfiguration(id, config);
      return await themeConfigService.getThemeConfigurationById(id);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update theme configuration');
    }
  }
);

export const activateThemeConfiguration = createAsyncThunk(
  'themeConfig/activateThemeConfiguration',
  async (id: number, { rejectWithValue, dispatch, getState }) => {
    try {
      await themeConfigService.activateThemeConfiguration(id);
      const theme = await themeConfigService.getThemeConfigurationById(id);

      // Refresh the store configurations after activation
      dispatch(fetchThemeConfigurationsByStore(theme.storeId));

      return theme;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to activate theme configuration');
    }
  }
);

export const deleteThemeConfiguration = createAsyncThunk(
  'themeConfig/deleteThemeConfiguration',
  async (id: number, { rejectWithValue, getState, dispatch }) => {
    try {
      const state = getState() as { themeConfig: ThemeConfigState };
      const theme = state.themeConfig.configurations.find(t => t.id === id);

      if (!theme) {
        return rejectWithValue('Theme configuration not found');
      }

      const storeId = theme.storeId;
      await themeConfigService.deleteThemeConfiguration(id);

      // Refresh the store configurations after deletion
      dispatch(fetchThemeConfigurationsByStore(storeId));

      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete theme configuration');
    }
  }
);

export const duplicateThemeConfiguration = createAsyncThunk(
  'themeConfig/duplicateThemeConfiguration',
  async (id: number, { rejectWithValue, dispatch, getState }) => {
    try {
      const newTheme = await themeConfigService.duplicateThemeConfiguration(id);

      // Refresh the store configurations after duplication
      dispatch(fetchThemeConfigurationsByStore(newTheme.storeId));

      return newTheme;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to duplicate theme configuration');
    }
  }
);

const themeConfigSlice = createSlice({
  name: 'themeConfig',
  initialState,
  reducers: {
    clearCurrentConfiguration: (state) => {
      state.currentConfiguration = null;
    },
    setCurrentConfiguration: (state, action: PayloadAction<ThemeConfiguration>) => {
      state.currentConfiguration = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch all theme configurations
      .addCase(fetchAllThemeConfigurations.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAllThemeConfigurations.fulfilled, (state, action: PayloadAction<ThemeConfiguration[]>) => {
        state.configurations = action.payload;
        state.loading = false;
      })
      .addCase(fetchAllThemeConfigurations.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Fetch theme configurations by store
      .addCase(fetchThemeConfigurationsByStore.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchThemeConfigurationsByStore.fulfilled, (state, action: PayloadAction<ThemeConfiguration[]>) => {
        state.storeConfigurations = action.payload;
        state.loading = false;
      })
      .addCase(fetchThemeConfigurationsByStore.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Fetch active theme configuration by store
      .addCase(fetchActiveThemeConfigurationByStore.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchActiveThemeConfigurationByStore.fulfilled, (state, action: PayloadAction<ThemeConfiguration>) => {
        state.activeConfiguration = action.payload;
        state.loading = false;
      })
      .addCase(fetchActiveThemeConfigurationByStore.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Fetch theme configuration by ID
      .addCase(fetchThemeConfigurationById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchThemeConfigurationById.fulfilled, (state, action: PayloadAction<ThemeConfiguration>) => {
        state.currentConfiguration = action.payload;
        state.loading = false;
      })
      .addCase(fetchThemeConfigurationById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Create theme configuration
      .addCase(createThemeConfiguration.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createThemeConfiguration.fulfilled, (state, action: PayloadAction<ThemeConfiguration>) => {
        state.configurations.push(action.payload);
        state.storeConfigurations.push(action.payload);
        state.currentConfiguration = action.payload;
        state.loading = false;
      })
      .addCase(createThemeConfiguration.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Update theme configuration
      .addCase(updateThemeConfiguration.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateThemeConfiguration.fulfilled, (state, action: PayloadAction<ThemeConfiguration>) => {
        const updatedTheme = action.payload;

        // Update in configurations array
        const configIndex = state.configurations.findIndex(c => c.id === updatedTheme.id);
        if (configIndex !== -1) {
          state.configurations[configIndex] = updatedTheme;
        }

        // Update in storeConfigurations array
        const storeConfigIndex = state.storeConfigurations.findIndex(c => c.id === updatedTheme.id);
        if (storeConfigIndex !== -1) {
          state.storeConfigurations[storeConfigIndex] = updatedTheme;
        }

        // Update currentConfiguration if it's the same theme
        if (state.currentConfiguration?.id === updatedTheme.id) {
          state.currentConfiguration = updatedTheme;
        }

        // Update activeConfiguration if it's the same theme
        if (state.activeConfiguration?.id === updatedTheme.id) {
          state.activeConfiguration = updatedTheme;
        }

        state.loading = false;
      })
      .addCase(updateThemeConfiguration.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Activate theme configuration
      .addCase(activateThemeConfiguration.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(activateThemeConfiguration.fulfilled, (state, action: PayloadAction<ThemeConfiguration>) => {
        const activatedTheme = action.payload;

        // Update the active status in configurations array
        state.configurations.forEach(theme => {
          if (theme.storeId === activatedTheme.storeId) {
            theme.isActive = theme.id === activatedTheme.id;
          }
        });

        // Update the active status in storeConfigurations array
        state.storeConfigurations.forEach(theme => {
          theme.isActive = theme.id === activatedTheme.id;
        });

        // Update activeConfiguration
        state.activeConfiguration = activatedTheme;

        // Update currentConfiguration if it's the same theme
        if (state.currentConfiguration?.id === activatedTheme.id) {
          state.currentConfiguration = activatedTheme;
        }

        state.loading = false;
      })
      .addCase(activateThemeConfiguration.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Delete theme configuration
      .addCase(deleteThemeConfiguration.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteThemeConfiguration.fulfilled, (state, action: PayloadAction<number>) => {
        const deletedId = action.payload;

        // Remove from configurations array
        state.configurations = state.configurations.filter(theme => theme.id !== deletedId);

        // Remove from storeConfigurations array
        state.storeConfigurations = state.storeConfigurations.filter(theme => theme.id !== deletedId);

        // Clear currentConfiguration if it's the deleted theme
        if (state.currentConfiguration?.id === deletedId) {
          state.currentConfiguration = null;
        }

        state.loading = false;
      })
      .addCase(deleteThemeConfiguration.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Duplicate theme configuration
      .addCase(duplicateThemeConfiguration.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(duplicateThemeConfiguration.fulfilled, (state, action: PayloadAction<ThemeConfiguration>) => {
        const newTheme = action.payload;

        // Add to configurations array
        state.configurations.push(newTheme);

        // Add to storeConfigurations array
        state.storeConfigurations.push(newTheme);

        // Set as currentConfiguration
        state.currentConfiguration = newTheme;

        state.loading = false;
      })
      .addCase(duplicateThemeConfiguration.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  }
});

export const { clearCurrentConfiguration, setCurrentConfiguration } = themeConfigSlice.actions;
export default themeConfigSlice.reducer;
