import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { 
  userModulePermissionService, 
  UserModulePermission, 
  CreateUserModulePermissionRequest, 
  UpdateUserModulePermissionRequest 
} from '../../services/userModulePermissionService';

interface UserModulePermissionState {
  permissions: UserModulePermission[];
  userPermissions: Record<string, UserModulePermission[]>;
  storePermissions: Record<number, UserModulePermission[]>;
  loading: boolean;
  error: string | null;
}

const initialState: UserModulePermissionState = {
  permissions: [],
  userPermissions: {},
  storePermissions: {},
  loading: false,
  error: null,
};

export const fetchUserModulePermissions = createAsyncThunk(
  'userModulePermissions/fetchAll',
  async (_, { rejectWithValue }) => {
    try {
      return await userModulePermissionService.getUserModulePermissions();
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch permissions');
    }
  }
);

export const fetchUserPermissionsByUser = createAsyncThunk(
  'userModulePermissions/fetchByUser',
  async (userId: string, { rejectWithValue }) => {
    try {
      const permissions = await userModulePermissionService.getUserPermissionsByUser(userId);
      return { userId, permissions };
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch user permissions');
    }
  }
);

export const fetchUserPermissionsByStore = createAsyncThunk(
  'userModulePermissions/fetchByStore',
  async (storeId: number, { rejectWithValue }) => {
    try {
      const permissions = await userModulePermissionService.getUserPermissionsByStore(storeId);
      return { storeId, permissions };
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch store permissions');
    }
  }
);

export const createUserModulePermission = createAsyncThunk(
  'userModulePermissions/create',
  async (permission: CreateUserModulePermissionRequest, { rejectWithValue }) => {
    try {
      return await userModulePermissionService.createUserModulePermission(permission);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create permission');
    }
  }
);

export const updateUserModulePermission = createAsyncThunk(
  'userModulePermissions/update',
  async ({ id, permission }: { id: number, permission: UpdateUserModulePermissionRequest }, { rejectWithValue }) => {
    try {
      await userModulePermissionService.updateUserModulePermission(id, permission);
      return { id, ...permission };
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update permission');
    }
  }
);

export const deleteUserModulePermission = createAsyncThunk(
  'userModulePermissions/delete',
  async (id: number, { rejectWithValue }) => {
    try {
      await userModulePermissionService.deleteUserModulePermission(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete permission');
    }
  }
);

const userModulePermissionSlice = createSlice({
  name: 'userModulePermissions',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Fetch all permissions
      .addCase(fetchUserModulePermissions.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserModulePermissions.fulfilled, (state, action: PayloadAction<UserModulePermission[]>) => {
        state.loading = false;
        state.permissions = action.payload;
      })
      .addCase(fetchUserModulePermissions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Fetch permissions by user
      .addCase(fetchUserPermissionsByUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserPermissionsByUser.fulfilled, (state, action: PayloadAction<{ userId: string, permissions: UserModulePermission[] }>) => {
        state.loading = false;
        state.userPermissions[action.payload.userId] = action.payload.permissions;
      })
      .addCase(fetchUserPermissionsByUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Fetch permissions by store
      .addCase(fetchUserPermissionsByStore.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserPermissionsByStore.fulfilled, (state, action: PayloadAction<{ storeId: number, permissions: UserModulePermission[] }>) => {
        state.loading = false;
        state.storePermissions[action.payload.storeId] = action.payload.permissions;
      })
      .addCase(fetchUserPermissionsByStore.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Create permission
      .addCase(createUserModulePermission.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createUserModulePermission.fulfilled, (state, action: PayloadAction<UserModulePermission>) => {
        state.loading = false;
        state.permissions.push(action.payload);
        
        // Update user permissions if they exist
        if (state.userPermissions[action.payload.userId]) {
          state.userPermissions[action.payload.userId].push(action.payload);
        }
        
        // Update store permissions if they exist and the permission is store-specific
        if (action.payload.storeId && state.storePermissions[action.payload.storeId]) {
          state.storePermissions[action.payload.storeId].push(action.payload);
        }
      })
      .addCase(createUserModulePermission.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Update permission
      .addCase(updateUserModulePermission.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateUserModulePermission.fulfilled, (state, action) => {
        state.loading = false;
        
        // Update in main permissions array
        const index = state.permissions.findIndex(p => p.id === action.meta.arg.id);
        if (index !== -1) {
          state.permissions[index] = {
            ...state.permissions[index],
            ...action.meta.arg.permission,
          };
        }
        
        // Update in user permissions if they exist
        for (const userId in state.userPermissions) {
          const userIndex = state.userPermissions[userId].findIndex(p => p.id === action.meta.arg.id);
          if (userIndex !== -1) {
            state.userPermissions[userId][userIndex] = {
              ...state.userPermissions[userId][userIndex],
              ...action.meta.arg.permission,
            };
          }
        }
        
        // Update in store permissions if they exist
        for (const storeId in state.storePermissions) {
          const storeIndex = state.storePermissions[Number(storeId)].findIndex(p => p.id === action.meta.arg.id);
          if (storeIndex !== -1) {
            state.storePermissions[Number(storeId)][storeIndex] = {
              ...state.storePermissions[Number(storeId)][storeIndex],
              ...action.meta.arg.permission,
            };
          }
        }
      })
      .addCase(updateUserModulePermission.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Delete permission
      .addCase(deleteUserModulePermission.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteUserModulePermission.fulfilled, (state, action: PayloadAction<number>) => {
        state.loading = false;
        
        // Remove from main permissions array
        state.permissions = state.permissions.filter(p => p.id !== action.payload);
        
        // Remove from user permissions if they exist
        for (const userId in state.userPermissions) {
          state.userPermissions[userId] = state.userPermissions[userId].filter(p => p.id !== action.payload);
        }
        
        // Remove from store permissions if they exist
        for (const storeId in state.storePermissions) {
          state.storePermissions[Number(storeId)] = state.storePermissions[Number(storeId)].filter(p => p.id !== action.payload);
        }
      })
      .addCase(deleteUserModulePermission.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export default userModulePermissionSlice.reducer;
