import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import api from '../../services/api';

// Types
export interface InventoryItem {
  id: number;
  locationId: number;
  locationName: string;
  locationCode: string;
  stockQuantity: number;
  reorderLevel: number;
  lastRestockedAt: Date;
}

export interface Location {
  id: number;
  code: string;
  name: string;
  address: string;
  isActive: boolean;
  storeId: number;
}

export interface InventoryTransfer {
  sourceLocationId: number;
  destinationLocationId: number;
  productVariantId: number;
  quantity: number;
}

interface InventoryState {
  inventoryItems: InventoryItem[];
  locations: Location[];
  selectedItem: InventoryItem | null;
  loading: boolean;
  error: string | null;
  totalCount: number;
  totalPages: number;
  currentPage: number;
  importResult: any | null;
}

interface InventoryResponse {
  data: InventoryItem[];
  headers: {
    'x-total-count': string;
    'x-total-pages': string;
  };
}

// Initial state
const initialState: InventoryState = {
  inventoryItems: [],
  locations: [],
  selectedItem: null,
  loading: false,
  error: null,
  totalCount: 0,
  totalPages: 0,
  currentPage: 1,
  importResult: null
};

// Async thunks
export const fetchInventory = createAsyncThunk<
  InventoryResponse,
  {
    storeId?: number;
    page?: number;
    pageSize?: number;
    search?: string;
    lowStock?: boolean;
  },
  { rejectValue: string }
>(
  'inventory/fetchInventory',
  async (params, { rejectWithValue }) => {
    try {
      const { storeId, page = 1, pageSize = 10, search, lowStock } = params;

      let url = `/admin/inventory?page=${page}&pageSize=${pageSize}`;

      if (storeId) url += `&storeId=${storeId}`;
      if (search) url += `&search=${encodeURIComponent(search)}`;
      if (lowStock !== undefined) url += `&lowStock=${lowStock}`;

      const response = await api.get<InventoryItem[]>(url);

      return {
        data: response.data,
        headers: {
          'x-total-count': response.headers['x-total-count'],
          'x-total-pages': response.headers['x-total-pages']
        }
      };
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to fetch inventory items');
    }
  }
);

export const fetchInventoryItem = createAsyncThunk<InventoryItem, number, { rejectValue: string }>(
  'inventory/fetchInventoryItem',
  async (id, { rejectWithValue }) => {
    try {
      const response = await api.get<InventoryItem>(`/admin/inventory/${id}`);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to fetch inventory item');
    }
  }
);

export const fetchLocations = createAsyncThunk<Location[], number | undefined, { rejectValue: string }>(
  'inventory/fetchLocations',
  async (storeId, { rejectWithValue }) => {
    try {
      const url = storeId ? `/admin/location?storeId=${storeId}` : '/admin/location';
      const response = await api.get<Location[]>(url);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to fetch locations');
    }
  }
);

export interface UpdateInventoryItemDto {
  id: number;
  stockQuantity: number;
  reorderLevel: number;
}

export const updateInventoryItem = createAsyncThunk<void, UpdateInventoryItemDto, { rejectValue: string }>(
  'inventory/updateInventoryItem',
  async ({ id, stockQuantity, reorderLevel }, { rejectWithValue }) => {
    try {
      await api.put(`/admin/inventory/${id}`, { stockQuantity, reorderLevel });
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to update inventory item');
    }
  }
);

export const transferInventory = createAsyncThunk<void, InventoryTransfer, { rejectValue: string }>(
  'inventory/transferInventory',
  async (transferData, { rejectWithValue }) => {
    try {
      await api.post('/admin/location/transfer', transferData);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to transfer inventory');
    }
  }
);

export const importInventory = createAsyncThunk<any, { file: File, storeId?: number }, { rejectValue: string }>(
  'inventory/importInventory',
  async ({ file, storeId }, { rejectWithValue }) => {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const url = storeId ? `/admin/inventory/import?storeId=${storeId}` : '/admin/inventory/import';
      const response = await api.post(url, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to import inventory');
    }
  }
);

export const exportInventory = createAsyncThunk<boolean, number | undefined, { rejectValue: string }>(
  'inventory/exportInventory',
  async (storeId, { rejectWithValue }) => {
    try {
      const url = storeId ? `/admin/inventory/export?storeId=${storeId}` : '/admin/inventory/export';
      const response = await api.get(url, {
        responseType: 'blob'
      });

      // Create a download link and trigger the download
      const url2 = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url2;
      link.setAttribute('download', 'inventory.xlsx');
      document.body.appendChild(link);
      link.click();
      link.remove();

      return true;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to export inventory');
    }
  }
);

// Slice
const inventorySlice = createSlice({
  name: 'inventory',
  initialState,
  reducers: {
    clearInventoryError: (state) => {
      state.error = null;
    },
    setCurrentPage: (state, action: PayloadAction<number>) => {
      state.currentPage = action.payload;
    },
    clearImportResult: (state) => {
      state.importResult = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch inventory items
      .addCase(fetchInventory.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchInventory.fulfilled, (state, action) => {
        state.loading = false;
        state.inventoryItems = action.payload.data;
        state.totalCount = parseInt(action.payload.headers['x-total-count'] || '0');
        state.totalPages = parseInt(action.payload.headers['x-total-pages'] || '0');
      })
      .addCase(fetchInventory.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to fetch inventory items';
      })

      // Fetch inventory item
      .addCase(fetchInventoryItem.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchInventoryItem.fulfilled, (state, action) => {
        state.loading = false;
        state.selectedItem = action.payload;
      })
      .addCase(fetchInventoryItem.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to fetch inventory item';
      })

      // Fetch locations
      .addCase(fetchLocations.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchLocations.fulfilled, (state, action) => {
        state.loading = false;
        state.locations = action.payload;
      })
      .addCase(fetchLocations.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to fetch locations';
      })

      // Update inventory item
      .addCase(updateInventoryItem.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateInventoryItem.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(updateInventoryItem.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to update inventory item';
      })

      // Transfer inventory
      .addCase(transferInventory.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(transferInventory.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(transferInventory.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to transfer inventory';
      })

      // Import inventory
      .addCase(importInventory.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.importResult = null;
      })
      .addCase(importInventory.fulfilled, (state, action) => {
        state.loading = false;
        state.importResult = action.payload;
      })
      .addCase(importInventory.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to import inventory';
      })

      // Export inventory
      .addCase(exportInventory.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(exportInventory.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(exportInventory.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to export inventory';
      });
  }
});

export const { clearInventoryError, setCurrentPage, clearImportResult } = inventorySlice.actions;

export default inventorySlice.reducer;
