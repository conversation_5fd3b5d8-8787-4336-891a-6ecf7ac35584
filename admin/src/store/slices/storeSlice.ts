import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import api from '../../services/api';
import { RootState } from '..';

export interface Store {
  id: number;
  name: string;
  description: string | null;
  storeKey: string;
  isActive: boolean;
  primaryDomain: string | null;
  logoUrl: string | null;
  faviconUrl: string | null;
  email: string | null;
  phone: string | null;
  address: string | null;
  facebookUrl: string | null;
  instagramUrl: string | null;
  twitterUrl: string | null;
  whatsappNumber: string | null;
  createdAt: string;
  updatedAt: string;
}

interface StoreState {
  stores: Store[];
  selectedStore: Store | null;
  accessibleStores: Store[];
  loading: boolean;
  error: string | null;
}

const initialState: StoreState = {
  stores: [],
  selectedStore: null,
  accessibleStores: [],
  loading: false,
  error: null
};

// Async thunks
export const fetchAllStores = createAsyncThunk(
  'store/fetchAllStores',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/admin/store');
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.response?.data?.errorMessage || 'Failed to fetch stores');
    }
  }
);

export const fetchAccessibleStores = createAsyncThunk(
  'store/fetchAccessibleStores',
  async (_, { rejectWithValue, dispatch, getState }) => {
    try {
      // Update the endpoint path to match the StoreAdminController route
      const response = await api.get('/admin/storeAdmin/accessible-stores');

      // Get the user from the auth state
      const state = getState() as RootState;
      const { user } = state.auth;

      // If user has a default store, use it
      if (user?.defaultStore) {
        dispatch(setSelectedStore(user.defaultStore));
      }
      // If the user has only one store, select it automatically
      else if (response.data.length === 1) {
        dispatch(setSelectedStore(response.data[0]));
      } else {
        // Try to get previously selected store from localStorage
        const savedStoreId = localStorage.getItem('selectedStoreId');
        if (savedStoreId) {
          const savedStore = response.data.find((store: any) => store.id.toString() === savedStoreId);
          if (savedStore) {
            dispatch(setSelectedStore(savedStore));
          } else if (response.data.length > 0) {
            // If saved store not found, select the first one
            dispatch(setSelectedStore(response.data[0]));
          }
        } else if (response.data.length > 0) {
          // If no saved store, select the first one
          dispatch(setSelectedStore(response.data[0]));
        }
      }

      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.response?.data?.errorMessage || 'Failed to fetch accessible stores');
    }
  }
);

export const createStore = createAsyncThunk(
  'store/createStore',
  async (storeData: Partial<Store>, { rejectWithValue }) => {
    try {
      const response = await api.post('/admin/store', storeData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.response?.data?.errorMessage || 'Failed to create store');
    }
  }
);

export const updateStore = createAsyncThunk(
  'store/updateStore',
  async ({ id, storeData }: { id: number; storeData: Partial<Store> }, { rejectWithValue }) => {
    try {
      const response = await api.put(`/admin/store/${id}`, storeData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.response?.data?.errorMessage || 'Failed to update store');
    }
  }
);

export const deleteStore = createAsyncThunk(
  'store/deleteStore',
  async (id: number, { rejectWithValue }) => {
    try {
      await api.delete(`/admin/store/${id}`);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.response?.data?.errorMessage || 'Failed to delete store');
    }
  }
);

const storeSlice = createSlice({
  name: 'store',
  initialState,
  reducers: {
    setSelectedStore: (state, action: PayloadAction<Store | null>) => {
      state.selectedStore = action.payload;
      // Save selected store ID to localStorage for persistence
      if (action.payload) {
        localStorage.setItem('selectedStoreId', action.payload.id.toString());
      } else {
        localStorage.removeItem('selectedStoreId');
      }
    },
    setAccessibleStores: (state, action: PayloadAction<Store[]>) => {
      state.accessibleStores = action.payload;

      // If we have accessible stores but no selected store, select the first one
      if (action.payload.length > 0 && !state.selectedStore) {
        // Try to get previously selected store from localStorage
        const savedStoreId = localStorage.getItem('selectedStoreId');
        if (savedStoreId) {
          const savedStore = action.payload.find(store => store.id.toString() === savedStoreId);
          if (savedStore) {
            state.selectedStore = savedStore;
            return;
          }
        }
        // Default to first store if no saved store found
        state.selectedStore = action.payload[0];
        localStorage.setItem('selectedStoreId', action.payload[0].id.toString());
      }
    },
    clearStoreError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch all stores
      .addCase(fetchAllStores.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAllStores.fulfilled, (state, action) => {
        state.stores = action.payload;
        state.loading = false;
      })
      .addCase(fetchAllStores.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Fetch accessible stores
      .addCase(fetchAccessibleStores.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAccessibleStores.fulfilled, (state, action) => {
        state.accessibleStores = action.payload;

        // If we have accessible stores but no selected store, select the first one
        if (action.payload.length > 0 && !state.selectedStore) {
          // Try to get previously selected store from localStorage
          const savedStoreId = localStorage.getItem('selectedStoreId');
          if (savedStoreId) {
            const savedStore = action.payload.find(store => store.id.toString() === savedStoreId);
            if (savedStore) {
              state.selectedStore = savedStore;
            } else {
              state.selectedStore = action.payload[0];
              localStorage.setItem('selectedStoreId', action.payload[0].id.toString());
            }
          } else {
            state.selectedStore = action.payload[0];
            localStorage.setItem('selectedStoreId', action.payload[0].id.toString());
          }
        }

        state.loading = false;
      })
      .addCase(fetchAccessibleStores.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Create store
      .addCase(createStore.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createStore.fulfilled, (state, action) => {
        state.stores.push(action.payload);
        state.loading = false;
      })
      .addCase(createStore.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Update store
      .addCase(updateStore.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateStore.fulfilled, (state, action) => {
        const index = state.stores.findIndex(store => store.id === action.payload.id);
        if (index !== -1) {
          state.stores[index] = action.payload;
        }

        // Update selected store if it's the one that was updated
        if (state.selectedStore && state.selectedStore.id === action.payload.id) {
          state.selectedStore = action.payload;
        }

        // Update in accessible stores list if present
        const accessibleIndex = state.accessibleStores.findIndex(store => store.id === action.payload.id);
        if (accessibleIndex !== -1) {
          state.accessibleStores[accessibleIndex] = action.payload;
        }

        state.loading = false;
      })
      .addCase(updateStore.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Delete store
      .addCase(deleteStore.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteStore.fulfilled, (state, action) => {
        state.stores = state.stores.filter(store => store.id !== action.payload);
        state.accessibleStores = state.accessibleStores.filter(store => store.id !== action.payload);

        // If the deleted store was selected, select another one
        if (state.selectedStore && state.selectedStore.id === action.payload) {
          state.selectedStore = state.accessibleStores.length > 0 ? state.accessibleStores[0] : null;
          if (state.selectedStore) {
            localStorage.setItem('selectedStoreId', state.selectedStore.id.toString());
          } else {
            localStorage.removeItem('selectedStoreId');
          }
        }

        state.loading = false;
      })
      .addCase(deleteStore.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  }
});

export const { setSelectedStore, setAccessibleStores, clearStoreError } = storeSlice.actions;
export default storeSlice.reducer;
