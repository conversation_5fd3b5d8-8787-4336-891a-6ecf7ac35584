import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '../../services/api';

// Placeholder for the order slice
const orderSlice = createSlice({
  name: 'orders',
  initialState: {
    orders: [],
    currentOrder: null,
    loading: false,
    error: null
  },
  reducers: {},
  extraReducers: (builder) => {}
});

// Placeholder for async thunks
export const fetchOrders = createAsyncThunk(
  'orders/fetchOrders',
  async (params: any) => {
    const response = await api.get('/orders', { params });
    return response.data;
  }
);

export const fetchOrderById = createAsyncThunk(
  'orders/fetchOrderById',
  async (id: number) => {
    const response = await api.get(`/orders/${id}`);
    return response.data;
  }
);

export const updateOrderStatus = createAsyncThunk(
  'orders/updateOrderStatus',
  async ({ id, status }: { id: number; status: string }) => {
    const response = await api.put(`/orders/${id}/status`, { status });
    return response.data;
  }
);

export default orderSlice.reducer;
