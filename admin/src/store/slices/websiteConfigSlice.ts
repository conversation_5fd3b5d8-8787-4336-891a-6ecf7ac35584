import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import {
  websiteConfigService,
  WebsiteConfiguration,
  UpdateWebsiteConfigurationRequest,
  SectionVisibilityRequest
} from '../../services/websiteConfigService';
import api from '../../services/api';

export interface WebsiteBanner {
  id: number;
  imageUrl: string;
  title: string;
  subtitle: string;
  buttonText: string;
  buttonLink: string;
  displayOrder: number;
  isActive: boolean;
}

interface WebsiteConfigState {
  configurations: WebsiteConfiguration[];
  currentConfiguration: WebsiteConfiguration | null;
  banners: WebsiteBanner[];
  loading: boolean;
  error: string | null;
}

const initialState: WebsiteConfigState = {
  configurations: [],
  currentConfiguration: null,
  banners: [],
  loading: false,
  error: null
};

// Async thunks
export const fetchAllWebsiteConfigurations = createAsyncThunk(
  'websiteConfig/fetchAllWebsiteConfigurations',
  async (_, { rejectWithValue }) => {
    try {
      return await websiteConfigService.getAllWebsiteConfigurations();
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch website configurations');
    }
  }
);

export const fetchWebsiteConfigurationByStore = createAsyncThunk(
  'websiteConfig/fetchWebsiteConfigurationByStore',
  async (storeId: number, { rejectWithValue }) => {
    try {
      return await websiteConfigService.getWebsiteConfigurationByStore(storeId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch website configuration');
    }
  }
);

export const updateWebsiteConfiguration = createAsyncThunk(
  'websiteConfig/updateWebsiteConfiguration',
  async ({ storeId, config }: { storeId: number; config: UpdateWebsiteConfigurationRequest }, { rejectWithValue }) => {
    try {
      await websiteConfigService.updateWebsiteConfiguration(storeId, config);
      return await websiteConfigService.getWebsiteConfigurationByStore(storeId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update website configuration');
    }
  }
);

export const updateSectionVisibility = createAsyncThunk(
  'websiteConfig/updateSectionVisibility',
  async ({ storeId, sectionVisibility }: { storeId: number; sectionVisibility: SectionVisibilityRequest }, { rejectWithValue }) => {
    try {
      await websiteConfigService.updateSectionVisibility(storeId, sectionVisibility);
      return await websiteConfigService.getWebsiteConfigurationByStore(storeId);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update section visibility');
    }
  }
);

export const fetchWebsiteBanners = createAsyncThunk(
  'websiteConfig/fetchWebsiteBanners',
  async (storeId: number, { rejectWithValue }) => {
    try {
      const response = await api.get(`/admin/website-banners/store/${storeId}`);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch website banners');
    }
  }
);

export const createWebsiteBanner = createAsyncThunk(
  'websiteConfig/createWebsiteBanner',
  async (banner: Partial<WebsiteBanner>, { rejectWithValue }) => {
    try {
      const { storeId, ...bannerData } = banner as any;
      const response = await api.post(`/admin/website-banners/store/${storeId}`, bannerData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create website banner');
    }
  }
);

export const updateWebsiteBanner = createAsyncThunk(
  'websiteConfig/updateWebsiteBanner',
  async ({ id, banner }: { id: number; banner: Partial<WebsiteBanner> }, { rejectWithValue }) => {
    try {
      const { storeId, ...bannerData } = banner as any;
      const response = await api.put(`/admin/website-banners/${id}`, bannerData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update website banner');
    }
  }
);

export const deleteWebsiteBanner = createAsyncThunk(
  'websiteConfig/deleteWebsiteBanner',
  async ({ storeId, id }: { storeId: number; id: number }, { rejectWithValue }) => {
    try {
      await api.delete(`/admin/website-banners/${id}`);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete website banner');
    }
  }
);

export const reorderWebsiteBanners = createAsyncThunk(
  'websiteConfig/reorderWebsiteBanners',
  async ({ storeId, banners }: { storeId: number; banners: WebsiteBanner[] }, { rejectWithValue }) => {
    try {
      // Create a dictionary of banner IDs to display orders
      const bannerOrders: Record<number, number> = {};
      banners.forEach((banner, index) => {
        bannerOrders[banner.id] = index;
      });

      const response = await api.put(`/admin/website-banners/reorder`, bannerOrders);
      return banners; // Return the reordered banners
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to reorder banners');
    }
  }
);

const websiteConfigSlice = createSlice({
  name: 'websiteConfig',
  initialState,
  reducers: {
    clearCurrentConfiguration: (state) => {
      state.currentConfiguration = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch all website configurations
      .addCase(fetchAllWebsiteConfigurations.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAllWebsiteConfigurations.fulfilled, (state, action: PayloadAction<WebsiteConfiguration[]>) => {
        state.configurations = action.payload;
        state.loading = false;
      })
      .addCase(fetchAllWebsiteConfigurations.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Fetch website configuration by store
      .addCase(fetchWebsiteConfigurationByStore.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchWebsiteConfigurationByStore.fulfilled, (state, action: PayloadAction<WebsiteConfiguration>) => {
        state.currentConfiguration = action.payload;
        state.loading = false;
      })
      .addCase(fetchWebsiteConfigurationByStore.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Update website configuration
      .addCase(updateWebsiteConfiguration.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateWebsiteConfiguration.fulfilled, (state, action: PayloadAction<WebsiteConfiguration>) => {
        state.currentConfiguration = action.payload;

        // Update in configurations array if it exists
        const index = state.configurations.findIndex(c => c.id === action.payload.id);
        if (index !== -1) {
          state.configurations[index] = action.payload;
        }

        state.loading = false;
      })
      .addCase(updateWebsiteConfiguration.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Update section visibility
      .addCase(updateSectionVisibility.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateSectionVisibility.fulfilled, (state, action: PayloadAction<WebsiteConfiguration>) => {
        state.currentConfiguration = action.payload;

        // Update in configurations array if it exists
        const index = state.configurations.findIndex(c => c.id === action.payload.id);
        if (index !== -1) {
          state.configurations[index] = action.payload;
        }

        state.loading = false;
      })
      .addCase(updateSectionVisibility.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Fetch website banners
      .addCase(fetchWebsiteBanners.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchWebsiteBanners.fulfilled, (state, action) => {
        state.banners = action.payload;
        state.loading = false;
      })
      .addCase(fetchWebsiteBanners.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Create website banner
      .addCase(createWebsiteBanner.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createWebsiteBanner.fulfilled, (state, action) => {
        state.banners.push(action.payload);
        state.loading = false;
      })
      .addCase(createWebsiteBanner.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Update website banner
      .addCase(updateWebsiteBanner.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateWebsiteBanner.fulfilled, (state, action) => {
        const index = state.banners.findIndex(b => b.id === action.payload.id);
        if (index !== -1) {
          state.banners[index] = action.payload;
        }
        state.loading = false;
      })
      .addCase(updateWebsiteBanner.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Delete website banner
      .addCase(deleteWebsiteBanner.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteWebsiteBanner.fulfilled, (state, action) => {
        state.banners = state.banners.filter(b => b.id !== action.payload);
        state.loading = false;
      })
      .addCase(deleteWebsiteBanner.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Reorder website banners
      .addCase(reorderWebsiteBanners.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(reorderWebsiteBanners.fulfilled, (state, action) => {
        state.banners = action.payload;
        state.loading = false;
      })
      .addCase(reorderWebsiteBanners.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  }
});

export const { clearCurrentConfiguration } = websiteConfigSlice.actions;
export default websiteConfigSlice.reducer;
