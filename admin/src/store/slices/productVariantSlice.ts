import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '../../services/api';

// Placeholder for the product variant slice
const productVariantSlice = createSlice({
  name: 'productVariants',
  initialState: {
    variants: [],
    loading: false,
    error: null
  },
  reducers: {},
  extraReducers: (builder) => { }
});

// Placeholder for async thunks
export const fetchProductVariants = createAsyncThunk(
  'productVariants/fetchProductVariants',
  async (productId: number) => {
    const response = await api.get(`/product/${productId}/variants`);
    return response.data;
  }
);

export const createProductVariant = createAsyncThunk(
  'productVariants/createProductVariant',
  async ({ productId, variant }: { productId: number; variant: any }) => {
    const response = await api.post(`/product/${productId}/variants`, variant);
    return response.data;
  }
);

export const updateProductVariant = createAsyncThunk(
  'productVariants/updateProductVariant',
  async ({ productId, variantId, variant }: { productId: number; variantId: number; variant: any }) => {
    const response = await api.put(`/product/${productId}/variants/${variantId}`, variant);
    return response.data;
  }
);

export const deleteProductVariant = createAsyncThunk(
  'productVariants/deleteProductVariant',
  async ({ productId, variantId }: { productId: number; variantId: number }) => {
    await api.delete(`/product/${productId}/variants/${variantId}`);
    return variantId;
  }
);

export default productVariantSlice.reducer;
