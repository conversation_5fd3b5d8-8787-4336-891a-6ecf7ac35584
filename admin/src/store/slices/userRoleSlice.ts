import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import api from '../../services/api';

export interface UserRole {
  userId: string;
  userEmail: string;
  userFullName: string;
  roleIds: string[];
}

interface UserRoleState {
  userRoles: UserRole[];
  selectedUserRole: UserRole | null;
  loading: boolean;
  error: string | null;
}

const initialState: UserRoleState = {
  userRoles: [],
  selectedUserRole: null,
  loading: false,
  error: null
};

export const fetchUserRoles = createAsyncThunk(
  'userRoles/fetchUserRoles',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/admin/userrole');
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch user roles');
    }
  }
);

export const fetchUserRolesByStore = createAsyncThunk(
  'userRoles/fetchUserRolesByStore',
  async (storeId: number, { rejectWithValue }) => {
    try {
      const response = await api.get(`/admin/userrole/store/${storeId}`);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching user roles by store:', error);
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch user roles for store');
    }
  }
);

export const fetchUserRoleById = createAsyncThunk(
  'userRoles/fetchUserRoleById',
  async (userId: string, { rejectWithValue }) => {
    try {
      const response = await api.get(`/admin/userrole/${userId}`);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch user role');
    }
  }
);

export const assignRolesToUser = createAsyncThunk(
  'userRoles/assignRolesToUser',
  async (userRole: UserRole, { rejectWithValue }) => {
    try {
      const response = await api.post('/admin/userrole', {
        userId: userRole.userId,
        roleIds: userRole.roleIds
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to assign roles to user');
    }
  }
);

const userRoleSlice = createSlice({
  name: 'userRoles',
  initialState,
  reducers: {
    clearUserRoleError: (state) => {
      state.error = null;
    },
    clearSelectedUserRole: (state) => {
      state.selectedUserRole = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch user roles
      .addCase(fetchUserRoles.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserRoles.fulfilled, (state, action: PayloadAction<UserRole[]>) => {
        state.userRoles = action.payload;
        state.loading = false;
      })
      .addCase(fetchUserRoles.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Fetch user roles by store
      .addCase(fetchUserRolesByStore.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserRolesByStore.fulfilled, (state, action: PayloadAction<UserRole[]>) => {
        state.userRoles = action.payload;
        state.loading = false;
      })
      .addCase(fetchUserRolesByStore.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Fetch user role by ID
      .addCase(fetchUserRoleById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserRoleById.fulfilled, (state, action: PayloadAction<UserRole>) => {
        state.selectedUserRole = action.payload;
        state.loading = false;
      })
      .addCase(fetchUserRoleById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Assign roles to user
      .addCase(assignRolesToUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(assignRolesToUser.fulfilled, (state, action: PayloadAction<UserRole>) => {
        const index = state.userRoles.findIndex(ur => ur.userId === action.payload.userId);
        if (index !== -1) {
          state.userRoles[index] = action.payload;
        } else {
          state.userRoles.push(action.payload);
        }
        state.selectedUserRole = action.payload;
        state.loading = false;
      })
      .addCase(assignRolesToUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  }
});

export const { clearUserRoleError, clearSelectedUserRole } = userRoleSlice.actions;
export default userRoleSlice.reducer;
