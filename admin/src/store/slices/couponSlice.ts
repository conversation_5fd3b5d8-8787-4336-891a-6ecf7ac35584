import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '../../services/api';

// Placeholder for the coupon slice
const couponSlice = createSlice({
  name: 'coupons',
  initialState: {
    coupons: [],
    loading: false,
    error: null
  },
  reducers: {},
  extraReducers: (builder) => {}
});

// Placeholder for async thunks
export const fetchCoupons = createAsyncThunk(
  'coupons/fetchCoupons',
  async () => {
    const response = await api.get('/coupons');
    return response.data;
  }
);

export const createCoupon = createAsyncThunk(
  'coupons/createCoupon',
  async (coupon: any) => {
    const response = await api.post('/coupons', coupon);
    return response.data;
  }
);

export const updateCoupon = createAsyncThunk(
  'coupons/updateCoupon',
  async ({ id, coupon }: { id: number; coupon: any }) => {
    const response = await api.put(`/coupons/${id}`, coupon);
    return response.data;
  }
);

export const deleteCoupon = createAsyncThunk(
  'coupons/deleteCoupon',
  async (id: number) => {
    await api.delete(`/coupons/${id}`);
    return id;
  }
);

export default couponSlice.reducer;
