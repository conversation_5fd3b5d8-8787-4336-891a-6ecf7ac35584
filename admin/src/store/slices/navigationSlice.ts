import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import api from '../../services/api';

export interface NavigationMenu {
  id: number;
  name: string;
  url: string;
  parentId?: number | null;
  displayOrder: number;
  isActive: boolean;
  icon?: string | null;
  storeId: number;
  children: NavigationMenu[];
}

interface NavigationState {
  menus: NavigationMenu[];
  loading: boolean;
  error: string | null;
}

const initialState: NavigationState = {
  menus: [],
  loading: false,
  error: null
};

export const fetchNavigationMenus = createAsyncThunk(
  'navigation/fetchNavigationMenus',
  async (storeId?: number, { rejectWithValue }) => {
    try {
      const url = storeId
        ? `/admin/navigation?storeId=${storeId}`
        : '/admin/navigation';
      const response = await api.get(url);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch navigation menus');
    }
  }
);

export const createNavigationMenu = createAsyncThunk(
  'navigation/createNavigationMenu',
  async (menu: Omit<NavigationMenu, 'id' | 'children'>, { rejectWithValue }) => {
    try {
      const response = await api.post('/admin/navigation', menu);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create navigation menu');
    }
  }
);

export const updateNavigationMenu = createAsyncThunk(
  'navigation/updateNavigationMenu',
  async ({ id, menu }: { id: number; menu: Omit<NavigationMenu, 'id' | 'children' | 'storeId'> }, { rejectWithValue }) => {
    try {
      await api.put(`/admin/navigation/${id}`, menu);
      return { id, ...menu };
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update navigation menu');
    }
  }
);

export const deleteNavigationMenu = createAsyncThunk(
  'navigation/deleteNavigationMenu',
  async (id: number, { rejectWithValue }) => {
    try {
      await api.delete(`/admin/navigation/${id}`);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete navigation menu');
    }
  }
);

const navigationSlice = createSlice({
  name: 'navigation',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch navigation menus
      .addCase(fetchNavigationMenus.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchNavigationMenus.fulfilled, (state, action: PayloadAction<NavigationMenu[]>) => {
        state.menus = action.payload;
        state.loading = false;
      })
      .addCase(fetchNavigationMenus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Create navigation menu
      .addCase(createNavigationMenu.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createNavigationMenu.fulfilled, (state, action: PayloadAction<NavigationMenu>) => {
        state.menus.push(action.payload);
        state.loading = false;
      })
      .addCase(createNavigationMenu.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Update navigation menu
      .addCase(updateNavigationMenu.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateNavigationMenu.fulfilled, (state, action) => {
        const index = state.menus.findIndex(m => m.id === action.payload.id);
        if (index !== -1) {
          state.menus[index] = {
            ...state.menus[index],
            ...action.payload
          };
        }
        state.loading = false;
      })
      .addCase(updateNavigationMenu.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Delete navigation menu
      .addCase(deleteNavigationMenu.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteNavigationMenu.fulfilled, (state, action: PayloadAction<number>) => {
        state.menus = state.menus.filter(m => m.id !== action.payload);
        state.loading = false;
      })
      .addCase(deleteNavigationMenu.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  }
});

export const { clearError } = navigationSlice.actions;
export default navigationSlice.reducer;
