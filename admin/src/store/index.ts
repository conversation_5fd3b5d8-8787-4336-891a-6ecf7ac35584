import { configureStore } from '@reduxjs/toolkit';
import authReducer from './slices/authSlice';
import productReducer from './slices/productSlice';
import categoryReducer from './slices/categorySlice';
import collectionReducer from './slices/collectionSlice';
import navigationReducer from './slices/navigationSlice';
import userReducer from './slices/userSlice';
import couponReducer from './slices/couponSlice';
import websiteConfigReducer from './slices/websiteConfigSlice';
import themeReducer from './slices/themeSlice';
import productVariantReducer from './slices/productVariantSlice';
import orderReducer from './slices/orderSlice';
import inventoryReducer from './slices/inventorySlice';
import storeReducer from './slices/storeSlice';
import roleReducer from './slices/roleSlice';
import userRoleReducer from './slices/userRoleSlice';
import userModulePermissionReducer from './slices/userModulePermissionSlice';

export const store = configureStore({
  reducer: {
    auth: authReducer,
    products: productReducer,
    categories: categoryReducer,
    collections: collectionReducer,
    navigation: navigationReducer,
    users: userReducer,
    coupons: couponReducer,
    websiteConfig: websiteConfigReducer,
    theme: themeReducer,
    productVariants: productVariantReducer,
    orders: orderReducer,
    inventory: inventoryReducer,
    store: storeReducer,
    roles: roleReducer,
    userRoles: userRoleReducer,
    userModulePermissions: userModulePermissionReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
