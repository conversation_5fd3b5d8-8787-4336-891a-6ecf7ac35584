{"name": "admin", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite --port 3003", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.1", "@mui/lab": "^7.0.0-beta.11", "@mui/material": "^7.0.1", "@mui/x-date-pickers": "^7.28.3", "@reduxjs/toolkit": "^2.6.1", "@types/react-color": "^3.0.13", "axios": "^1.8.4", "date-fns": "^2.30.0", "formik": "^2.4.6", "notistack": "^3.0.2", "react": "^19.0.0", "react-color": "^2.19.3", "react-dom": "^19.0.0", "react-redux": "^9.2.0", "react-router-dom": "^7.4.1", "react-toastify": "^11.0.5", "yup": "^1.6.1"}, "devDependencies": {"@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.1.0"}}