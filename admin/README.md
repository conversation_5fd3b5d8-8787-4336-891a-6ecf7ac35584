# MyShop Admin Dashboard

This is the admin dashboard for the MyShop e-commerce platform. It provides a comprehensive interface for managing products, categories, collections, orders, users, and more.

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Clone the repository
2. Navigate to the admin directory
3. Install dependencies:

```bash
npm install
# or
yarn install
```

### Development

To start the development server:

```bash
npm run dev
# or
yarn dev
```

The admin dashboard will be available at http://localhost:3001.

### Building for Production

To build the application for production:

```bash
npm run build
# or
yarn build
```

## Features

- Product management
- Category management
- Collection management
- Order management
- User management
- Coupon management
- Website configuration
- Theme configuration
- Bulk image upload
- Inventory management

## Project Structure

- `src/components`: Reusable UI components
- `src/layouts`: Layout components
- `src/pages`: Page components
- `src/store`: Redux store configuration and slices
- `src/services`: API services
- `src/hooks`: Custom React hooks
- `src/utils`: Utility functions
- `src/assets`: Static assets
- `src/styles`: Global styles
- `src/types`: TypeScript type definitions
