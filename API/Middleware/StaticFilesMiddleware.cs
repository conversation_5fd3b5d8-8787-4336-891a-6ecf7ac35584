using Microsoft.AspNetCore.StaticFiles;
using Microsoft.Extensions.FileProviders;

namespace MyShop.API.Middleware
{
    public static class StaticFilesMiddlewareExtensions
    {
        public static IApplicationBuilder UseCustomStaticFiles(this IApplicationBuilder app, IWebHostEnvironment env)
        {
            // Configure the default static files middleware for wwwroot
            app.UseStaticFiles();

            // Configure additional static files middleware for the www/upload/image directory
            var uploadPath = Path.Combine(env.ContentRootPath, "www", "upload", "image");
            
            if (Directory.Exists(uploadPath))
            {
                app.UseStaticFiles(new StaticFileOptions
                {
                    FileProvider = new PhysicalFileProvider(uploadPath),
                    RequestPath = "/upload/image",
                    OnPrepareResponse = ctx =>
                    {
                        // Add cache control headers
                        ctx.Context.Response.Headers.Append("Cache-Control", "public,max-age=86400");
                        ctx.Context.Response.Headers.Append("Access-Control-Allow-Origin", "*");
                    }
                });
            }
            else
            {
                // Create the directory if it doesn't exist
                Directory.CreateDirectory(uploadPath);
                
                app.UseStaticFiles(new StaticFileOptions
                {
                    FileProvider = new PhysicalFileProvider(uploadPath),
                    RequestPath = "/upload/image",
                    OnPrepareResponse = ctx =>
                    {
                        // Add cache control headers
                        ctx.Context.Response.Headers.Append("Cache-Control", "public,max-age=86400");
                        ctx.Context.Response.Headers.Append("Access-Control-Allow-Origin", "*");
                    }
                });
            }

            // Also add support for the uploads/images path that the FileUploadController uses
            var uploadsImagesPath = Path.Combine(env.WebRootPath, "uploads", "images");
            
            if (!Directory.Exists(uploadsImagesPath))
            {
                Directory.CreateDirectory(uploadsImagesPath);
            }
            
            app.UseStaticFiles(new StaticFileOptions
            {
                FileProvider = new PhysicalFileProvider(uploadsImagesPath),
                RequestPath = "/uploads/images",
                OnPrepareResponse = ctx =>
                {
                    // Add cache control headers
                    ctx.Context.Response.Headers.Append("Cache-Control", "public,max-age=86400");
                    ctx.Context.Response.Headers.Append("Access-Control-Allow-Origin", "*");
                }
            });

            return app;
        }
    }
}
