using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MyShop.API.Data;
using MyShop.API.Data.Entities;

namespace MyShop.API.Middleware
{
    public class StoreResolutionMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<StoreResolutionMiddleware> _logger;

        public StoreResolutionMiddleware(RequestDelegate next, ILogger<StoreResolutionMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context, ApplicationDbContext dbContext)
        {
            // Skip for admin API routes
            if (context.Request.Path.StartsWithSegments("/api/admin"))
            {
                await _next(context);
                return;
            }

            Store store = null;

            // First try to get store by store key from header or query string
            string storeKey = GetStoreKeyFromRequest(context);
            if (!string.IsNullOrEmpty(storeKey))
            {
                _logger.LogInformation($"Attempting to resolve store by key: {storeKey}");
                store = await dbContext.Stores.FirstOrDefaultAsync(s => s.StoreKey == storeKey && s.IsActive);

                if (store != null)
                {
                    _logger.LogInformation($"Resolved store by key: {store.Name}");
                }
                else
                {
                    _logger.LogWarning($"No active store found with key: {storeKey}");
                }
            }

            // If store not found by key, try by domain
            if (store == null)
            {
                var host = context.Request.Host.Host;
                _logger.LogInformation($"Attempting to resolve store by host: {host}");

                store = await dbContext.Stores
                    .Include(s => s.AdditionalDomains)
                    .FirstOrDefaultAsync(s =>
                        s.PrimaryDomain == host ||
                        s.AdditionalDomains.Any(d => d.Domain == host && d.IsActive));

                if (store != null)
                {
                    _logger.LogInformation($"Resolved store by domain: {store.Name}");
                }
            }

            // If still no store found, fallback to default store
            if (store == null)
            {
                store = await dbContext.Stores.FirstOrDefaultAsync(s => s.IsActive);

                if (store == null)
                {
                    _logger.LogWarning("No active store found");
                    context.Response.StatusCode = 404;
                    await context.Response.WriteAsync("No active store found. Please contact the administrator.");
                    return;
                }

                _logger.LogInformation($"Using default store: {store.Name}");
            }

            // Add store to HttpContext items for use in controllers
            context.Items["CurrentStore"] = store;

            await _next(context);
        }

        private string GetStoreKeyFromRequest(HttpContext context)
        {
            // Try to get store key from header
            if (context.Request.Headers.TryGetValue("X-Store-Key", out var headerValues))
            {
                return headerValues.FirstOrDefault();
            }

            // Try to get store key from query string
            if (context.Request.Query.TryGetValue("storeKey", out var queryValues))
            {
                return queryValues.FirstOrDefault();
            }

            return null;
        }
    }

    // Extension method for middleware registration
    public static class StoreResolutionMiddlewareExtensions
    {
        public static IApplicationBuilder UseStoreResolution(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<StoreResolutionMiddleware>();
        }
    }
}
