using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace MyShop.API
{
    public class CreateAdminUser
    {
        public static async Task CreateAdminUserAsync(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var services = scope.ServiceProvider;
            
            try
            {
                var userManager = services.GetRequiredService<UserManager<ApplicationUser>>();
                var roleManager = services.GetRequiredService<RoleManager<IdentityRole>>();
                var context = services.GetRequiredService<ApplicationDbContext>();
                
                // Check if admin user exists
                var adminEmail = "<EMAIL>";
                var adminUser = await userManager.FindByEmailAsync(adminEmail);
                
                if (adminUser != null)
                {
                    Console.WriteLine("Admin user already exists.");
                    
                    // Ensure admin role exists
                    if (!await roleManager.RoleExistsAsync("Admin"))
                    {
                        await roleManager.CreateAsync(new IdentityRole("Admin"));
                        Console.WriteLine("Created Admin role.");
                    }
                    
                    // Ensure user has Admin role
                    if (!await userManager.IsInRoleAsync(adminUser, "Admin"))
                    {
                        await userManager.AddToRoleAsync(adminUser, "Admin");
                        Console.WriteLine("Added Admin role to existing user.");
                    }
                    
                    return;
                }
                
                // Create admin user directly in the database
                Console.WriteLine("Admin user not found. Creating new admin user directly in the database.");
                
                // Ensure Admin role exists
                if (!await roleManager.RoleExistsAsync("Admin"))
                {
                    await roleManager.CreateAsync(new IdentityRole("Admin"));
                    Console.WriteLine("Created Admin role.");
                }
                
                // Create password hash
                var passwordHasher = new PasswordHasher<ApplicationUser>();
                var newAdminUser = new ApplicationUser
                {
                    Id = Guid.NewGuid().ToString(),
                    UserName = adminEmail,
                    NormalizedUserName = adminEmail.ToUpper(),
                    Email = adminEmail,
                    NormalizedEmail = adminEmail.ToUpper(),
                    EmailConfirmed = true,
                    SecurityStamp = Guid.NewGuid().ToString(),
                    ConcurrencyStamp = Guid.NewGuid().ToString(),
                    PhoneNumberConfirmed = false,
                    TwoFactorEnabled = false,
                    LockoutEnabled = false,
                    AccessFailedCount = 0,
                    FirstName = "Admin",
                    LastName = "User",
                    UserType = UserType.Admin,
                    Address = "Admin Address",
                    City = "Admin City",
                    State = "AS",
                    ZipCode = "12345",
                    Country = "USA",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };
                
                // Set password hash
                newAdminUser.PasswordHash = passwordHasher.HashPassword(newAdminUser, "Admin123!");
                
                // Add user to database
                context.Users.Add(newAdminUser);
                await context.SaveChangesAsync();
                Console.WriteLine("Admin user created successfully.");
                
                // Add user to Admin role
                var adminRole = await roleManager.FindByNameAsync("Admin");
                if (adminRole != null)
                {
                    context.UserRoles.Add(new IdentityUserRole<string>
                    {
                        UserId = newAdminUser.Id,
                        RoleId = adminRole.Id
                    });
                    await context.SaveChangesAsync();
                    Console.WriteLine("Added Admin role to new admin user.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred while creating admin user: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
                Console.WriteLine(ex.StackTrace);
            }
        }
    }
}
