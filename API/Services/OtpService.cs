using Microsoft.EntityFrameworkCore;
using MyShop.API.Data;
using MyShop.API.Data.Entities;

namespace MyShop.API.Services
{
    public class OtpService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<OtpService> _logger;
        private readonly Random _random = new Random();

        public OtpService(ApplicationDbContext context, ILogger<OtpService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<string> GenerateOtpAsync(string? email, string? phoneNumber)
        {
            if (string.IsNullOrEmpty(email) && string.IsNullOrEmpty(phoneNumber))
            {
                throw new ArgumentException("Either email or phone number must be provided");
            }

            // Generate a 6-digit OTP
            string otp = _random.Next(100000, 999999).ToString();
            
            // Set expiration time (10 minutes from now)
            var expiresAt = DateTime.UtcNow.AddMinutes(10);

            // Create OTP verification record
            var otpVerification = new OtpVerification
            {
                Email = email,
                PhoneNumber = phoneNumber ?? string.Empty,
                Otp = otp,
                ExpiresAt = expiresAt,
                IsVerified = false,
                CreatedAt = DateTime.UtcNow
            };

            // Save to database
            _context.OtpVerifications.Add(otpVerification);
            await _context.SaveChangesAsync();

            return otp;
        }

        public async Task<bool> ValidateOtpAsync(string? email, string? phoneNumber, string otp)
        {
            if (string.IsNullOrEmpty(email) && string.IsNullOrEmpty(phoneNumber))
            {
                throw new ArgumentException("Either email or phone number must be provided");
            }

            // Find the most recent valid OTP for the given email or phone
            var otpVerification = await _context.OtpVerifications
                .Where(o => 
                    ((!string.IsNullOrEmpty(email) && o.Email == email) || 
                     (!string.IsNullOrEmpty(phoneNumber) && o.PhoneNumber == phoneNumber)) && 
                    o.Otp == otp &&
                    o.ExpiresAt > DateTime.UtcNow &&
                    !o.IsVerified)
                .OrderByDescending(o => o.CreatedAt)
                .FirstOrDefaultAsync();

            if (otpVerification == null)
            {
                return false;
            }

            // Mark OTP as verified
            otpVerification.IsVerified = true;
            await _context.SaveChangesAsync();

            return true;
        }
    }
}
