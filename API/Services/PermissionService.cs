using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MyShop.API.Services
{
    public class PermissionService
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ICurrentUserService _currentUserService;
        private readonly ApplicationDbContext _context;

        public PermissionService(
            UserManager<ApplicationUser> userManager,
            ICurrentUserService currentUserService,
            ApplicationDbContext context)
        {
            _userManager = userManager;
            _currentUserService = currentUserService;
            _context = context;
        }

        /// <summary>
        /// Check if the current user has the specified permission
        /// </summary>
        /// <param name="module">The module name (e.g., "Products", "Orders")</param>
        /// <param name="action">The action name (e.g., "View", "Create", "Edit", "Delete")</param>
        /// <param name="storeId">Optional store ID to check store-specific permissions</param>
        /// <returns>True if the user has the permission, false otherwise</returns>
        public async Task<bool> HasPermissionAsync(string module, string action, int? storeId = null)
        {
            var user = await _currentUserService.GetCurrentUserAsync();
            if (user == null)
                return false;

            // Super admins have all permissions
            var roles = await _userManager.GetRolesAsync(user);
            if (roles.Contains("SuperAdmin"))
                return true;

            // Check user-specific module permissions
            var query = _context.UserModulePermissions
                .Where(ump => ump.UserId == user.Id && ump.Module == module);

            // If storeId is provided, check store-specific permissions
            if (storeId.HasValue)
            {
                query = query.Where(ump => ump.StoreId == storeId || ump.StoreId == null);
            }
            else
            {
                query = query.Where(ump => ump.StoreId == null);
            }

            var permissions = await query.ToListAsync();

            // Check if the user has the specific action permission
            foreach (var permission in permissions)
            {
                switch (action.ToLower())
                {
                    case "view":
                        if (permission.CanView)
                            return true;
                        break;
                    case "create":
                        if (permission.CanCreate)
                            return true;
                        break;
                    case "edit":
                        if (permission.CanEdit)
                            return true;
                        break;
                    case "delete":
                        if (permission.CanDelete)
                            return true;
                        break;
                }
            }

            // If no specific permission is found, check if the user is an admin for the store
            if (storeId.HasValue)
            {
                var isStoreAdmin = await _context.StoreAdmins
                    .Where(sa => sa.UserId == user.Id && sa.StoreId == storeId)
                    .AnyAsync();

                if (isStoreAdmin)
                {
                    // Check specific permissions based on StoreAdmin flags
                    var storeAdmin = await _context.StoreAdmins
                        .FirstOrDefaultAsync(sa => sa.UserId == user.Id && sa.StoreId == storeId);

                    if (storeAdmin != null)
                    {
                        switch (module.ToLower())
                        {
                            case "products":
                                return storeAdmin.CanManageProducts;
                            case "orders":
                                return storeAdmin.CanManageOrders;
                            case "customers":
                                return storeAdmin.CanManageCustomers;
                            case "settings":
                                return storeAdmin.CanManageSettings;
                            default:
                                // For other modules, allow access if they're a store admin
                                return true;
                        }
                    }
                }
            }

            // If no specific permission is found, check if the user is an admin
            return roles.Contains("Admin");
        }

        /// <summary>
        /// Get all permissions for the current user
        /// </summary>
        /// <param name="storeId">Optional store ID to get store-specific permissions</param>
        /// <returns>List of permissions in the format "Module.Action"</returns>
        public async Task<List<string>> GetUserPermissionsAsync(int? storeId = null)
        {
            var user = await _currentUserService.GetCurrentUserAsync();
            if (user == null)
                return new List<string>();

            var permissions = new List<string>();

            // Super admins have all permissions
            var roles = await _userManager.GetRolesAsync(user);
            if (roles.Contains("SuperAdmin"))
            {
                // Return a wildcard permission for super admins
                return new List<string> { "*" };
            }

            // Get user-specific module permissions
            var query = _context.UserModulePermissions
                .Where(ump => ump.UserId == user.Id);

            // If storeId is provided, get store-specific permissions
            if (storeId.HasValue)
            {
                query = query.Where(ump => ump.StoreId == storeId || ump.StoreId == null);
            }
            else
            {
                query = query.Where(ump => ump.StoreId == null);
            }

            var userModulePermissions = await query.ToListAsync();

            // Add user-specific permissions
            foreach (var permission in userModulePermissions)
            {
                if (permission.CanView)
                    permissions.Add($"{permission.Module}.View");
                if (permission.CanCreate)
                    permissions.Add($"{permission.Module}.Create");
                if (permission.CanEdit)
                    permissions.Add($"{permission.Module}.Edit");
                if (permission.CanDelete)
                    permissions.Add($"{permission.Module}.Delete");
            }

            // If storeId is provided, add store admin permissions
            if (storeId.HasValue)
            {
                var storeAdmin = await _context.StoreAdmins
                    .FirstOrDefaultAsync(sa => sa.UserId == user.Id && sa.StoreId == storeId);

                if (storeAdmin != null)
                {
                    if (storeAdmin.CanManageProducts)
                    {
                        permissions.Add("Products.View");
                        permissions.Add("Products.Create");
                        permissions.Add("Products.Edit");
                        permissions.Add("Products.Delete");
                    }

                    if (storeAdmin.CanManageOrders)
                    {
                        permissions.Add("Orders.View");
                        permissions.Add("Orders.Create");
                        permissions.Add("Orders.Edit");
                        permissions.Add("Orders.Delete");
                    }

                    if (storeAdmin.CanManageCustomers)
                    {
                        permissions.Add("Customers.View");
                        permissions.Add("Customers.Create");
                        permissions.Add("Customers.Edit");
                        permissions.Add("Customers.Delete");
                    }

                    if (storeAdmin.CanManageSettings)
                    {
                        permissions.Add("Settings.View");
                        permissions.Add("Settings.Create");
                        permissions.Add("Settings.Edit");
                        permissions.Add("Settings.Delete");
                    }
                }
            }

            // Add role-based permissions
            if (roles.Contains("Admin"))
            {
                // Admins have access to all modules
                var modules = await GetModulesAsync();
                foreach (var module in modules)
                {
                    permissions.Add($"{module}.View");
                    permissions.Add($"{module}.Create");
                    permissions.Add($"{module}.Edit");
                    permissions.Add($"{module}.Delete");
                }
            }

            return permissions.Distinct().ToList();
        }

        /// <summary>
        /// Get all permissions for a specific role
        /// </summary>
        /// <param name="roleId">The role ID</param>
        /// <returns>List of permissions</returns>
        public async Task<List<string>> GetRolePermissionsAsync(string roleId)
        {
            // Since we're using ASP.NET Identity, roles don't have permissions
            // Return an empty list for now
            return new List<string>();
        }

        /// <summary>
        /// Get all available modules
        /// </summary>
        /// <returns>List of module names</returns>
        public Task<List<string>> GetModulesAsync()
        {
            // Return a list of standard modules
            return Task.FromResult(new List<string> {
                "Users",
                "Roles",
                "Products",
                "Orders",
                "Stores",
                "Customers",
                "Collections",
                "Categories",
                "Coupons",
                "Inventory",
                "Settings",
                "Navigation",
                "WebsiteConfiguration",
                "ThemeConfiguration"
            });
        }

        /// <summary>
        /// Check if the user is a super admin
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>True if the user is a super admin, false otherwise</returns>
        public async Task<bool> IsSuperAdminAsync(string userId)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
                return false;

            return await _userManager.IsInRoleAsync(user, "SuperAdmin");
        }

        /// <summary>
        /// Check if the user is a store admin for the specified store
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="storeId">The store ID</param>
        /// <returns>True if the user is a store admin, false otherwise</returns>
        public async Task<bool> IsStoreAdminAsync(string userId, int storeId)
        {
            // Super admins have access to all stores
            if (await IsSuperAdminAsync(userId))
                return true;

            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
                return false;

            // Check if the user is an admin
            var isAdmin = await _userManager.IsInRoleAsync(user, "Admin");
            if (!isAdmin)
                return false;

            // Check if the user is assigned to this store
            var isAssignedToStore = await _context.StoreAdmins
                .Where(sa => sa.UserId == userId && sa.StoreId == storeId)
                .AnyAsync();

            return isAssignedToStore;
        }
    }
}
