using System.Net;
using System.Net.Mail;

namespace MyShop.API.Services
{
    public class EmailService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<EmailService> _logger;

        public EmailService(IConfiguration configuration, ILogger<EmailService> logger)
        {
            _configuration = configuration;
            _logger = logger;
        }

        public async Task SendOtpEmailAsync(string email, string otp)
        {
            try
            {
              
                string smtpServer = _configuration["Email:SmtpServer"] ?? string.Empty;
                int smtpPort = Convert.ToInt32(_configuration["Email:SmtpPort"]);   
                var fromEmail = _configuration["Email:fromEmail"];
                var appPassword = _configuration["Email:Password"];
                var senderEmail = _configuration["Email:SenderEmail"];
                var senderName = _configuration["Email:SenderName"];
                var useDevelopmentMode = Convert.ToBoolean(_configuration["Email:UseDevelopmentMode"]);

                // Always log the OTP for debugging purposes
                _logger.LogInformation("OTP for {Email} is {Otp}", email, otp);

                // For development, only log the OTP instead of sending an email
                if (useDevelopmentMode)
                {
                    _logger.LogInformation("Development mode: Email sending skipped");
                    return;
                }

                // Check if email configuration is valid
                if (string.IsNullOrEmpty(smtpServer) || string.IsNullOrEmpty(fromEmail) || string.IsNullOrEmpty(appPassword))
                {
                    _logger.LogWarning("Email configuration is incomplete. Using development mode instead.");
                    return;
                }

                _logger.LogInformation("Attempting to send email using SMTP server: {SmtpServer}:{SmtpPort}", smtpServer, smtpPort);

                string subject="Your Jaipur Cotton House Login Verification Code";
                string body = $@"
                        <html>
                        <body style='font-family: Arial, sans-serif;'>
                            <div style='max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;'>
                                <h2 style='color: #ff3f6c;'>Login Verification Code</h2>
                                <p>Your verification code is:</p>
                                <h1 style='font-size: 32px; letter-spacing: 5px; text-align: center; margin: 30px 0; padding: 10px; background-color: #f5f5f5; border-radius: 5px;'>{otp}</h1>
                                <p>This code will expire in 10 minutes.</p>
                                <p>If you didn't request this code, please ignore this email.</p>
                                <p style='margin-top: 30px; font-size: 12px; color: #777;'>© {DateTime.Now.Year} Jaipur Cotton House. All rights reserved.</p>
                            </div>
                        </body>
                        </html>"
                    ;

                try
                {
                    var message = new MailMessage
                    {
                        From = new MailAddress(fromEmail),
                        To = { new MailAddress(email) },
                        Subject = subject,
                        Body = body,
                        IsBodyHtml = true
                    };

                    SmtpClient smtp = new SmtpClient(smtpServer, smtpPort)
                    {
                        EnableSsl = true,
                        Credentials = new NetworkCredential(fromEmail, appPassword)
                    };

                await smtp.SendMailAsync(message);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to send email. Falling back to development mode.");
                    // Don't rethrow the exception, just log it and continue
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SendOtpEmailAsync for {Email}", email);
                // Don't throw the exception to prevent the OTP process from failing
            }
        }
    }
}
