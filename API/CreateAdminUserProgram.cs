using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using System;
using System.IO;
using System.Threading.Tasks;

namespace MyShop.API
{
    public class CreateAdminUserProgram
    {
        public static async Task Main(string[] args)
        {
            Console.WriteLine("Starting admin user creation process...");
            
            // Build configuration
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development"}.json", optional: true)
                .AddEnvironmentVariables()
                .Build();
            
            // Setup services
            var services = new ServiceCollection();
            
            // Add DbContext
            services.AddDbContext<ApplicationDbContext>(options =>
                options.UseSqlServer(configuration.GetConnectionString("DefaultConnection")));
            
            // Add Identity
            services.AddIdentity<ApplicationUser, IdentityRole>(options =>
            {
                options.Password.RequireDigit = true;
                options.Password.RequireLowercase = true;
                options.Password.RequireNonAlphanumeric = true;
                options.Password.RequireUppercase = true;
                options.Password.RequiredLength = 8;
            })
            .AddEntityFrameworkStores<ApplicationDbContext>()
            .AddDefaultTokenProviders();
            
            // Build service provider
            var serviceProvider = services.BuildServiceProvider();
            
            // Create admin user
            await CreateAdminUser.CreateAdminUserAsync(serviceProvider);
            
            Console.WriteLine("Admin user creation process completed.");
        }
    }
}
