-- Add ProductGroups table
CREATE TABLE [ProductGroups] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NOT NULL,
    CONSTRAINT [PK_ProductGroups] PRIMARY KEY ([Id])
);

-- Add HasVariants column to Products table
ALTER TABLE [Products] ADD [HasVariants] bit NOT NULL DEFAULT 0;

-- Add ProductGroupId column to Products table
ALTER TABLE [Products] ADD [ProductGroupId] int NULL;

-- Add foreign key constraint
ALTER TABLE [Products] ADD CONSTRAINT [FK_Products_ProductGroups_ProductGroupId] 
    FOREIGN KEY ([ProductGroupId]) REFERENCES [ProductGroups] ([Id]) ON DELETE NO ACTION;

-- Create index for ProductGroupId
CREATE INDEX [IX_Products_ProductGroupId] ON [Products] ([ProductGroupId]);

-- Add ProductVariants table
CREATE TABLE [ProductVariants] (
    [Id] int NOT NULL IDENTITY,
    [SKU] nvarchar(450) NOT NULL,
    [Barcode] nvarchar(max) NOT NULL,
    [Price] decimal(18,2) NOT NULL,
    [Cost] decimal(18,2) NOT NULL,
    [StockQuantity] int NOT NULL,
    [ReorderLevel] int NOT NULL,
    [IsActive] bit NOT NULL,
    [VariantAttributes] nvarchar(max) NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NOT NULL,
    [ProductId] int NOT NULL,
    CONSTRAINT [PK_ProductVariants] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_ProductVariants_Products_ProductId] FOREIGN KEY ([ProductId]) REFERENCES [Products] ([Id]) ON DELETE CASCADE
);

-- Create unique index for SKU
CREATE UNIQUE INDEX [IX_ProductVariants_SKU] ON [ProductVariants] ([SKU]);

-- Create index for ProductId
CREATE INDEX [IX_ProductVariants_ProductId] ON [ProductVariants] ([ProductId]);
