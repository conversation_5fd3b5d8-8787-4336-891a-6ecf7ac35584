{"format": 1, "restore": {"/Users/<USER>/Desktop/JaipurCottonHouseApp/JaipurCottonHouse/API/MyShop.API.csproj": {}}, "projects": {"/Users/<USER>/Desktop/JaipurCottonHouseApp/JaipurCottonHouse/API/MyShop.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Desktop/JaipurCottonHouseApp/JaipurCottonHouse/API/MyShop.API.csproj", "projectName": "MyShop.API", "projectPath": "/Users/<USER>/Desktop/JaipurCottonHouseApp/JaipurCottonHouse/API/MyShop.API.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Desktop/JaipurCottonHouseApp/JaipurCottonHouse/API/obj/", "projectStyle": "PackageReference", "fallbackFolders": ["/usr/local/share/dotnet/sdk/NuGetFallbackFolder"], "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "EPPlus": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.3, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[8.0.3, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.3, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.3, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.3, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.404/PortableRuntimeIdentifierGraph.json"}}}}}