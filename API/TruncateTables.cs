using Microsoft.Data.SqlClient;
using System;
using System.IO;

namespace MyShop.API
{
    public class TruncateTables
    {
        public static void Main(string[] args)
        {
            string connectionString = "Server=localhost,1433;Database=ECOMDB;User Id=sa;Password=dockerStrongPwd123;TrustServerCertificate=True;MultipleActiveResultSets=true;";

            try
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    Console.WriteLine("Connected to database successfully.");

                    // Execute SQL commands to delete data
                    ExecuteCommand(connection, "DELETE FROM Inventories;");
                    ExecuteCommand(connection, "DELETE FROM ProductImages WHERE ProductVariantId IS NOT NULL;");
                    ExecuteCommand(connection, "DELETE FROM ProductVariants;");
                    ExecuteCommand(connection, "DELETE FROM ProductImages WHERE ProductId IS NOT NULL;");
                    ExecuteCommand(connection, "DELETE FROM ProductAttributes;");
                    ExecuteCommand(connection, "DELETE FROM Products;");
                    ExecuteCommand(connection, "DELETE FROM Categories;");
                    ExecuteCommand(connection, "DELETE FROM Collections;");
                    ExecuteCommand(connection, "DELETE FROM ProductGroups;");

                    Console.WriteLine("All tables truncated successfully.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner Exception: {ex.InnerException.Message}");
                }
            }
        }

        private static void ExecuteCommand(SqlConnection connection, string commandText)
        {
            try
            {
                using (SqlCommand command = new SqlCommand(commandText, connection))
                {
                    int rowsAffected = command.ExecuteNonQuery();
                    Console.WriteLine($"Command executed: {commandText} - Rows affected: {rowsAffected}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error executing command '{commandText}': {ex.Message}");
            }
        }
    }
}
