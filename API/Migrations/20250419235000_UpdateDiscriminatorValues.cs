using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MyShop.API.Migrations
{
    /// <inheritdoc />
    public partial class UpdateDiscriminatorValues : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Update existing users to have the correct discriminator
            migrationBuilder.Sql(@"
                UPDATE AspNetUsers
                SET Discriminator = 'ApplicationUser'
                WHERE Discriminator IS NULL OR Discriminator = '';
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // No down migration needed
        }
    }
}
