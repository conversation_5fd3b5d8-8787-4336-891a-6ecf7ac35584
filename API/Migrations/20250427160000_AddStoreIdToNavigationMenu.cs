using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MyShop.API.Migrations
{
    /// <inheritdoc />
    public partial class AddStoreIdToNavigationMenu : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // First, add the StoreId column with a default value of 1
            migrationBuilder.AddColumn<int>(
                name: "StoreId",
                table: "NavigationMenus",
                type: "int",
                nullable: false,
                defaultValue: 1);

            // Create an index on StoreId
            migrationBuilder.CreateIndex(
                name: "IX_NavigationMenus_StoreId",
                table: "NavigationMenus",
                column: "StoreId");

            // Add the foreign key constraint
            migrationBuilder.AddForeignKey(
                name: "FK_NavigationMenus_Stores_StoreId",
                table: "NavigationMenus",
                column: "StoreId",
                principalTable: "Stores",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Drop the foreign key constraint
            migrationBuilder.DropForeignKey(
                name: "FK_NavigationMenus_Stores_StoreId",
                table: "NavigationMenus");

            // Drop the index
            migrationBuilder.DropIndex(
                name: "IX_NavigationMenus_StoreId",
                table: "NavigationMenus");

            // Drop the column
            migrationBuilder.DropColumn(
                name: "StoreId",
                table: "NavigationMenus");
        }
    }
}
