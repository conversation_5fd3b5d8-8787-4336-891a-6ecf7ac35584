-- Create Stores table
CREATE TABLE [Stores] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NOT NULL,
    [IsActive] bit NOT NULL,
    [PrimaryDomain] nvarchar(max) NOT NULL,
    [LogoUrl] nvarchar(max) NOT NULL,
    [FaviconUrl] nvarchar(max) NOT NULL,
    [Email] nvarchar(max) NOT NULL,
    [Phone] nvarchar(max) NOT NULL,
    [Address] nvarchar(max) NOT NULL,
    [FacebookUrl] nvarchar(max) NOT NULL,
    [InstagramUrl] nvarchar(max) NOT NULL,
    [TwitterUrl] nvarchar(max) NOT NULL,
    [WhatsappNumber] nvarchar(max) NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NOT NULL,
    CONSTRAINT [PK_Stores] PRIMARY KEY ([Id])
);

-- Insert default store
INSERT INTO [dbo].[Stores] (
    [Name], 
    [Description], 
    [IsActive], 
    [PrimaryDomain], 
    [LogoUrl], 
    [FaviconUrl], 
    [Email], 
    [Phone], 
    [Address], 
    [FacebookUrl], 
    [InstagramUrl], 
    [TwitterUrl], 
    [WhatsappNumber], 
    [CreatedAt], 
    [UpdatedAt]
)
VALUES (
    'Default Store', 
    'Default store for the application', 
    1, 
    'localhost', 
    '/images/logo.png', 
    '/images/favicon.ico', 
    '<EMAIL>', 
    '1234567890', 
    '123 Main St, City, Country', 
    'https://facebook.com', 
    'https://instagram.com', 
    'https://twitter.com', 
    '1234567890', 
    GETDATE(), 
    GETDATE()
);

-- Add IsSuperAdmin column to AspNetUsers
ALTER TABLE [AspNetUsers] ADD [IsSuperAdmin] bit NULL;

-- Make admin user a super admin
UPDATE [dbo].[AspNetUsers] SET [IsSuperAdmin] = 1 WHERE [Email] = '<EMAIL>';

-- Create StoreDomains table
CREATE TABLE [StoreDomains] (
    [Id] int NOT NULL IDENTITY,
    [Domain] nvarchar(max) NOT NULL,
    [IsActive] bit NOT NULL,
    [StoreId] int NOT NULL,
    CONSTRAINT [PK_StoreDomains] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_StoreDomains_Stores_StoreId] FOREIGN KEY ([StoreId]) REFERENCES [Stores] ([Id]) ON DELETE CASCADE
);

-- Create StoreAdmins table
CREATE TABLE [StoreAdmins] (
    [Id] int NOT NULL IDENTITY,
    [UserId] nvarchar(450) NOT NULL,
    [StoreId] int NOT NULL,
    [CanManageProducts] bit NOT NULL,
    [CanManageOrders] bit NOT NULL,
    [CanManageCustomers] bit NOT NULL,
    [CanManageSettings] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NOT NULL,
    CONSTRAINT [PK_StoreAdmins] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_StoreAdmins_AspNetUsers_UserId] FOREIGN KEY ([UserId]) REFERENCES [AspNetUsers] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_StoreAdmins_Stores_StoreId] FOREIGN KEY ([StoreId]) REFERENCES [Stores] ([Id]) ON DELETE CASCADE
);

-- Create store admin for default store
INSERT INTO [dbo].[StoreAdmins] (
    [UserId], 
    [StoreId], 
    [CanManageProducts], 
    [CanManageOrders], 
    [CanManageCustomers], 
    [CanManageSettings], 
    [CreatedAt], 
    [UpdatedAt]
)
SELECT 
    [Id], 
    1, 
    1, 
    1, 
    1, 
    1, 
    GETDATE(), 
    GETDATE()
FROM [dbo].[AspNetUsers]
WHERE [Email] = '<EMAIL>';

-- Add StoreId column to all tables
ALTER TABLE [Categories] ADD [StoreId] int NOT NULL DEFAULT 1;
ALTER TABLE [Collections] ADD [StoreId] int NOT NULL DEFAULT 1;
ALTER TABLE [Coupons] ADD [StoreId] int NOT NULL DEFAULT 1;
ALTER TABLE [Locations] ADD [StoreId] int NOT NULL DEFAULT 1;
ALTER TABLE [Orders] ADD [StoreId] int NOT NULL DEFAULT 1;
ALTER TABLE [ProductGroups] ADD [StoreId] int NOT NULL DEFAULT 1;
ALTER TABLE [Products] ADD [StoreId] int NOT NULL DEFAULT 1;
ALTER TABLE [ProductTypes] ADD [StoreId] int NOT NULL DEFAULT 1;
ALTER TABLE [ThemeConfigurations] ADD [StoreId] int NOT NULL DEFAULT 1;
ALTER TABLE [WebsiteBanners] ADD [StoreId] int NOT NULL DEFAULT 1;
ALTER TABLE [WebsiteConfigurations] ADD [StoreId] int NOT NULL DEFAULT 1;

-- Create indexes
CREATE INDEX [IX_Categories_StoreId] ON [Categories] ([StoreId]);
CREATE INDEX [IX_Collections_StoreId] ON [Collections] ([StoreId]);
CREATE INDEX [IX_Coupons_StoreId] ON [Coupons] ([StoreId]);
CREATE INDEX [IX_Locations_StoreId] ON [Locations] ([StoreId]);
CREATE INDEX [IX_Orders_StoreId] ON [Orders] ([StoreId]);
CREATE INDEX [IX_ProductGroups_StoreId] ON [ProductGroups] ([StoreId]);
CREATE INDEX [IX_Products_StoreId] ON [Products] ([StoreId]);
CREATE INDEX [IX_ProductTypes_StoreId] ON [ProductTypes] ([StoreId]);
CREATE INDEX [IX_ThemeConfigurations_StoreId] ON [ThemeConfigurations] ([StoreId]);
CREATE INDEX [IX_WebsiteBanners_StoreId] ON [WebsiteBanners] ([StoreId]);
CREATE INDEX [IX_WebsiteConfigurations_StoreId] ON [WebsiteConfigurations] ([StoreId]);
CREATE INDEX [IX_StoreAdmins_StoreId] ON [StoreAdmins] ([StoreId]);
CREATE INDEX [IX_StoreAdmins_UserId] ON [StoreAdmins] ([UserId]);
CREATE INDEX [IX_StoreDomains_StoreId] ON [StoreDomains] ([StoreId]);

-- Add foreign keys
ALTER TABLE [Categories] ADD CONSTRAINT [FK_Categories_Stores_StoreId] FOREIGN KEY ([StoreId]) REFERENCES [Stores] ([Id]) ON DELETE CASCADE;
ALTER TABLE [Collections] ADD CONSTRAINT [FK_Collections_Stores_StoreId] FOREIGN KEY ([StoreId]) REFERENCES [Stores] ([Id]) ON DELETE CASCADE;
ALTER TABLE [Coupons] ADD CONSTRAINT [FK_Coupons_Stores_StoreId] FOREIGN KEY ([StoreId]) REFERENCES [Stores] ([Id]) ON DELETE CASCADE;
ALTER TABLE [Locations] ADD CONSTRAINT [FK_Locations_Stores_StoreId] FOREIGN KEY ([StoreId]) REFERENCES [Stores] ([Id]) ON DELETE CASCADE;
ALTER TABLE [Orders] ADD CONSTRAINT [FK_Orders_Stores_StoreId] FOREIGN KEY ([StoreId]) REFERENCES [Stores] ([Id]) ON DELETE CASCADE;
ALTER TABLE [ProductGroups] ADD CONSTRAINT [FK_ProductGroups_Stores_StoreId] FOREIGN KEY ([StoreId]) REFERENCES [Stores] ([Id]) ON DELETE CASCADE;
ALTER TABLE [Products] ADD CONSTRAINT [FK_Products_Stores_StoreId] FOREIGN KEY ([StoreId]) REFERENCES [Stores] ([Id]) ON DELETE CASCADE;
ALTER TABLE [ProductTypes] ADD CONSTRAINT [FK_ProductTypes_Stores_StoreId] FOREIGN KEY ([StoreId]) REFERENCES [Stores] ([Id]) ON DELETE CASCADE;
ALTER TABLE [ThemeConfigurations] ADD CONSTRAINT [FK_ThemeConfigurations_Stores_StoreId] FOREIGN KEY ([StoreId]) REFERENCES [Stores] ([Id]) ON DELETE CASCADE;
ALTER TABLE [WebsiteBanners] ADD CONSTRAINT [FK_WebsiteBanners_Stores_StoreId] FOREIGN KEY ([StoreId]) REFERENCES [Stores] ([Id]) ON DELETE CASCADE;
ALTER TABLE [WebsiteConfigurations] ADD CONSTRAINT [FK_WebsiteConfigurations_Stores_StoreId] FOREIGN KEY ([StoreId]) REFERENCES [Stores] ([Id]) ON DELETE CASCADE;
