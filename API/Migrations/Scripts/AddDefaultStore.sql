-- Insert a default store
INSERT INTO [dbo].[Stores] (
    [Name], 
    [Description], 
    [IsActive], 
    [PrimaryDomain], 
    [LogoUrl], 
    [FaviconUrl], 
    [Email], 
    [Phone], 
    [Address], 
    [FacebookUrl], 
    [InstagramUrl], 
    [TwitterUrl], 
    [WhatsappNumber], 
    [CreatedAt], 
    [UpdatedAt]
)
VALUES (
    'Default Store', 
    'Default store for the application', 
    1, 
    'localhost', 
    '/images/logo.png', 
    '/images/favicon.ico', 
    '<EMAIL>', 
    '1234567890', 
    '123 Main St, City, Country', 
    'https://facebook.com', 
    'https://instagram.com', 
    'https://twitter.com', 
    '1234567890', 
    GETDATE(), 
    GETDATE()
);

-- Get the ID of the inserted store
DECLARE @StoreId INT = SCOPE_IDENTITY();

-- Update all existing categories to use the default store
UPDATE [dbo].[Categories] SET [StoreId] = @StoreId;

-- Update all existing collections to use the default store
UPDATE [dbo].[Collections] SET [StoreId] = @StoreId;

-- Update all existing coupons to use the default store
UPDATE [dbo].[Coupons] SET [StoreId] = @StoreId;

-- Update all existing locations to use the default store
UPDATE [dbo].[Locations] SET [StoreId] = @StoreId;

-- Update all existing orders to use the default store
UPDATE [dbo].[Orders] SET [StoreId] = @StoreId;

-- Update all existing product groups to use the default store
UPDATE [dbo].[ProductGroups] SET [StoreId] = @StoreId;

-- Update all existing products to use the default store
UPDATE [dbo].[Products] SET [StoreId] = @StoreId;

-- Update all existing product types to use the default store
UPDATE [dbo].[ProductTypes] SET [StoreId] = @StoreId;

-- Update all existing theme configurations to use the default store
UPDATE [dbo].[ThemeConfigurations] SET [StoreId] = @StoreId;

-- Update all existing website banners to use the default store
UPDATE [dbo].[WebsiteBanners] SET [StoreId] = @StoreId;

-- Update all existing website configurations to use the default store
UPDATE [dbo].[WebsiteConfigurations] SET [StoreId] = @StoreId;

-- Make the admin user a super admin
UPDATE [dbo].[AspNetUsers] SET [IsSuperAdmin] = 1 WHERE [Email] = '<EMAIL>';

-- Create a store admin for the default store
INSERT INTO [dbo].[StoreAdmins] (
    [UserId], 
    [StoreId], 
    [CanManageProducts], 
    [CanManageOrders], 
    [CanManageCustomers], 
    [CanManageSettings], 
    [CreatedAt], 
    [UpdatedAt]
)
SELECT 
    [Id], 
    @StoreId, 
    1, 
    1, 
    1, 
    1, 
    GETDATE(), 
    GETDATE()
FROM [dbo].[AspNetUsers]
WHERE [Email] = '<EMAIL>';
