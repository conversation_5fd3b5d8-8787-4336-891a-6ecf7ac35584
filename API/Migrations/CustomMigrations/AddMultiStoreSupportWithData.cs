using System;
using System.IO;
using Microsoft.EntityFrameworkCore.Migrations;

namespace MyShop.API.Migrations.CustomMigrations
{
    public partial class AddMultiStoreSupportWithData : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Create Stores table
            migrationBuilder.CreateTable(
                name: "Stores",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    PrimaryDomain = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    LogoUrl = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    FaviconUrl = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Email = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Phone = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Address = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    FacebookUrl = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    InstagramUrl = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    TwitterUrl = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    WhatsappNumber = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Stores", x => x.Id);
                });
                
            // Insert default store
            migrationBuilder.Sql(@"
                INSERT INTO [dbo].[Stores] (
                    [Name], 
                    [Description], 
                    [IsActive], 
                    [PrimaryDomain], 
                    [LogoUrl], 
                    [FaviconUrl], 
                    [Email], 
                    [Phone], 
                    [Address], 
                    [FacebookUrl], 
                    [InstagramUrl], 
                    [TwitterUrl], 
                    [WhatsappNumber], 
                    [CreatedAt], 
                    [UpdatedAt]
                )
                VALUES (
                    'Default Store', 
                    'Default store for the application', 
                    1, 
                    'localhost', 
                    '/images/logo.png', 
                    '/images/favicon.ico', 
                    '<EMAIL>', 
                    '1234567890', 
                    '123 Main St, City, Country', 
                    'https://facebook.com', 
                    'https://instagram.com', 
                    'https://twitter.com', 
                    '1234567890', 
                    GETDATE(), 
                    GETDATE()
                );
            ");
            
            // Add StoreId column to all tables
            migrationBuilder.AddColumn<int>(
                name: "StoreId",
                table: "WebsiteConfigurations",
                type: "int",
                nullable: false,
                defaultValue: 1);
                
            migrationBuilder.AddColumn<int>(
                name: "StoreId",
                table: "WebsiteBanners",
                type: "int",
                nullable: false,
                defaultValue: 1);
                
            migrationBuilder.AddColumn<int>(
                name: "StoreId",
                table: "ThemeConfigurations",
                type: "int",
                nullable: false,
                defaultValue: 1);
                
            migrationBuilder.AddColumn<int>(
                name: "StoreId",
                table: "ProductTypes",
                type: "int",
                nullable: false,
                defaultValue: 1);
                
            migrationBuilder.AddColumn<int>(
                name: "StoreId",
                table: "Products",
                type: "int",
                nullable: false,
                defaultValue: 1);
                
            migrationBuilder.AddColumn<int>(
                name: "StoreId",
                table: "ProductGroups",
                type: "int",
                nullable: false,
                defaultValue: 1);
                
            migrationBuilder.AddColumn<int>(
                name: "StoreId",
                table: "Orders",
                type: "int",
                nullable: false,
                defaultValue: 1);
                
            migrationBuilder.AddColumn<int>(
                name: "StoreId",
                table: "Locations",
                type: "int",
                nullable: false,
                defaultValue: 1);
                
            migrationBuilder.AddColumn<int>(
                name: "StoreId",
                table: "Coupons",
                type: "int",
                nullable: false,
                defaultValue: 1);
                
            migrationBuilder.AddColumn<int>(
                name: "StoreId",
                table: "Collections",
                type: "int",
                nullable: false,
                defaultValue: 1);
                
            migrationBuilder.AddColumn<int>(
                name: "StoreId",
                table: "Categories",
                type: "int",
                nullable: false,
                defaultValue: 1);
                
            migrationBuilder.AddColumn<bool>(
                name: "IsSuperAdmin",
                table: "AspNetUsers",
                type: "bit",
                nullable: true);
                
            // Create StoreDomains table
            migrationBuilder.CreateTable(
                name: "StoreDomains",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Domain = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    StoreId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StoreDomains", x => x.Id);
                    table.ForeignKey(
                        name: "FK_StoreDomains_Stores_StoreId",
                        column: x => x.StoreId,
                        principalTable: "Stores",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });
                
            // Create StoreAdmins table
            migrationBuilder.CreateTable(
                name: "StoreAdmins",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    StoreId = table.Column<int>(type: "int", nullable: false),
                    CanManageProducts = table.Column<bool>(type: "bit", nullable: false),
                    CanManageOrders = table.Column<bool>(type: "bit", nullable: false),
                    CanManageCustomers = table.Column<bool>(type: "bit", nullable: false),
                    CanManageSettings = table.Column<bool>(type: "bit", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StoreAdmins", x => x.Id);
                    table.ForeignKey(
                        name: "FK_StoreAdmins_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_StoreAdmins_Stores_StoreId",
                        column: x => x.StoreId,
                        principalTable: "Stores",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });
                
            // Make admin user a super admin
            migrationBuilder.Sql("UPDATE [dbo].[AspNetUsers] SET [IsSuperAdmin] = 1 WHERE [Email] = '<EMAIL>';");
            
            // Create store admin for default store
            migrationBuilder.Sql(@"
                INSERT INTO [dbo].[StoreAdmins] (
                    [UserId], 
                    [StoreId], 
                    [CanManageProducts], 
                    [CanManageOrders], 
                    [CanManageCustomers], 
                    [CanManageSettings], 
                    [CreatedAt], 
                    [UpdatedAt]
                )
                SELECT 
                    [Id], 
                    1, 
                    1, 
                    1, 
                    1, 
                    1, 
                    GETDATE(), 
                    GETDATE()
                FROM [dbo].[AspNetUsers]
                WHERE [Email] = '<EMAIL>';
            ");
            
            // Create indexes
            migrationBuilder.CreateIndex(
                name: "IX_WebsiteConfigurations_StoreId",
                table: "WebsiteConfigurations",
                column: "StoreId");
                
            migrationBuilder.CreateIndex(
                name: "IX_WebsiteBanners_StoreId",
                table: "WebsiteBanners",
                column: "StoreId");
                
            migrationBuilder.CreateIndex(
                name: "IX_ThemeConfigurations_StoreId",
                table: "ThemeConfigurations",
                column: "StoreId");
                
            migrationBuilder.CreateIndex(
                name: "IX_ProductTypes_StoreId",
                table: "ProductTypes",
                column: "StoreId");
                
            migrationBuilder.CreateIndex(
                name: "IX_Products_StoreId",
                table: "Products",
                column: "StoreId");
                
            migrationBuilder.CreateIndex(
                name: "IX_ProductGroups_StoreId",
                table: "ProductGroups",
                column: "StoreId");
                
            migrationBuilder.CreateIndex(
                name: "IX_Orders_StoreId",
                table: "Orders",
                column: "StoreId");
                
            migrationBuilder.CreateIndex(
                name: "IX_Locations_StoreId",
                table: "Locations",
                column: "StoreId");
                
            migrationBuilder.CreateIndex(
                name: "IX_Coupons_StoreId",
                table: "Coupons",
                column: "StoreId");
                
            migrationBuilder.CreateIndex(
                name: "IX_Collections_StoreId",
                table: "Collections",
                column: "StoreId");
                
            migrationBuilder.CreateIndex(
                name: "IX_Categories_StoreId",
                table: "Categories",
                column: "StoreId");
                
            migrationBuilder.CreateIndex(
                name: "IX_StoreAdmins_StoreId",
                table: "StoreAdmins",
                column: "StoreId");
                
            migrationBuilder.CreateIndex(
                name: "IX_StoreAdmins_UserId",
                table: "StoreAdmins",
                column: "UserId");
                
            migrationBuilder.CreateIndex(
                name: "IX_StoreDomains_StoreId",
                table: "StoreDomains",
                column: "StoreId");
                
            // Add foreign keys
            migrationBuilder.AddForeignKey(
                name: "FK_Categories_Stores_StoreId",
                table: "Categories",
                column: "StoreId",
                principalTable: "Stores",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
                
            migrationBuilder.AddForeignKey(
                name: "FK_Collections_Stores_StoreId",
                table: "Collections",
                column: "StoreId",
                principalTable: "Stores",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
                
            migrationBuilder.AddForeignKey(
                name: "FK_Coupons_Stores_StoreId",
                table: "Coupons",
                column: "StoreId",
                principalTable: "Stores",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
                
            migrationBuilder.AddForeignKey(
                name: "FK_Locations_Stores_StoreId",
                table: "Locations",
                column: "StoreId",
                principalTable: "Stores",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
                
            migrationBuilder.AddForeignKey(
                name: "FK_Orders_Stores_StoreId",
                table: "Orders",
                column: "StoreId",
                principalTable: "Stores",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
                
            migrationBuilder.AddForeignKey(
                name: "FK_ProductGroups_Stores_StoreId",
                table: "ProductGroups",
                column: "StoreId",
                principalTable: "Stores",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
                
            migrationBuilder.AddForeignKey(
                name: "FK_Products_Stores_StoreId",
                table: "Products",
                column: "StoreId",
                principalTable: "Stores",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
                
            migrationBuilder.AddForeignKey(
                name: "FK_ProductTypes_Stores_StoreId",
                table: "ProductTypes",
                column: "StoreId",
                principalTable: "Stores",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
                
            migrationBuilder.AddForeignKey(
                name: "FK_ThemeConfigurations_Stores_StoreId",
                table: "ThemeConfigurations",
                column: "StoreId",
                principalTable: "Stores",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
                
            migrationBuilder.AddForeignKey(
                name: "FK_WebsiteBanners_Stores_StoreId",
                table: "WebsiteBanners",
                column: "StoreId",
                principalTable: "Stores",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
                
            migrationBuilder.AddForeignKey(
                name: "FK_WebsiteConfigurations_Stores_StoreId",
                table: "WebsiteConfigurations",
                column: "StoreId",
                principalTable: "Stores",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Categories_Stores_StoreId",
                table: "Categories");

            migrationBuilder.DropForeignKey(
                name: "FK_Collections_Stores_StoreId",
                table: "Collections");

            migrationBuilder.DropForeignKey(
                name: "FK_Coupons_Stores_StoreId",
                table: "Coupons");

            migrationBuilder.DropForeignKey(
                name: "FK_Locations_Stores_StoreId",
                table: "Locations");

            migrationBuilder.DropForeignKey(
                name: "FK_Orders_Stores_StoreId",
                table: "Orders");

            migrationBuilder.DropForeignKey(
                name: "FK_ProductGroups_Stores_StoreId",
                table: "ProductGroups");

            migrationBuilder.DropForeignKey(
                name: "FK_Products_Stores_StoreId",
                table: "Products");

            migrationBuilder.DropForeignKey(
                name: "FK_ProductTypes_Stores_StoreId",
                table: "ProductTypes");

            migrationBuilder.DropForeignKey(
                name: "FK_ThemeConfigurations_Stores_StoreId",
                table: "ThemeConfigurations");

            migrationBuilder.DropForeignKey(
                name: "FK_WebsiteBanners_Stores_StoreId",
                table: "WebsiteBanners");

            migrationBuilder.DropForeignKey(
                name: "FK_WebsiteConfigurations_Stores_StoreId",
                table: "WebsiteConfigurations");

            migrationBuilder.DropTable(
                name: "StoreAdmins");

            migrationBuilder.DropTable(
                name: "StoreDomains");

            migrationBuilder.DropTable(
                name: "Stores");

            migrationBuilder.DropIndex(
                name: "IX_WebsiteConfigurations_StoreId",
                table: "WebsiteConfigurations");

            migrationBuilder.DropIndex(
                name: "IX_WebsiteBanners_StoreId",
                table: "WebsiteBanners");

            migrationBuilder.DropIndex(
                name: "IX_ThemeConfigurations_StoreId",
                table: "ThemeConfigurations");

            migrationBuilder.DropIndex(
                name: "IX_ProductTypes_StoreId",
                table: "ProductTypes");

            migrationBuilder.DropIndex(
                name: "IX_Products_StoreId",
                table: "Products");

            migrationBuilder.DropIndex(
                name: "IX_ProductGroups_StoreId",
                table: "ProductGroups");

            migrationBuilder.DropIndex(
                name: "IX_Orders_StoreId",
                table: "Orders");

            migrationBuilder.DropIndex(
                name: "IX_Locations_StoreId",
                table: "Locations");

            migrationBuilder.DropIndex(
                name: "IX_Coupons_StoreId",
                table: "Coupons");

            migrationBuilder.DropIndex(
                name: "IX_Collections_StoreId",
                table: "Collections");

            migrationBuilder.DropIndex(
                name: "IX_Categories_StoreId",
                table: "Categories");

            migrationBuilder.DropColumn(
                name: "StoreId",
                table: "WebsiteConfigurations");

            migrationBuilder.DropColumn(
                name: "StoreId",
                table: "WebsiteBanners");

            migrationBuilder.DropColumn(
                name: "StoreId",
                table: "ThemeConfigurations");

            migrationBuilder.DropColumn(
                name: "StoreId",
                table: "ProductTypes");

            migrationBuilder.DropColumn(
                name: "StoreId",
                table: "Products");

            migrationBuilder.DropColumn(
                name: "StoreId",
                table: "ProductGroups");

            migrationBuilder.DropColumn(
                name: "StoreId",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "StoreId",
                table: "Locations");

            migrationBuilder.DropColumn(
                name: "StoreId",
                table: "Coupons");

            migrationBuilder.DropColumn(
                name: "StoreId",
                table: "Collections");

            migrationBuilder.DropColumn(
                name: "StoreId",
                table: "Categories");

            migrationBuilder.DropColumn(
                name: "IsSuperAdmin",
                table: "AspNetUsers");
        }
    }
}
