﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MyShop.API.Migrations
{
    /// <inheritdoc />
    public partial class AddThemeConfiguration : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ThemeConfigurations",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    PrimaryColor = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    SecondaryColor = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    AccentColor = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    TextPrimaryColor = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    TextSecondaryColor = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    TextLightColor = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    BackgroundPrimaryColor = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    BackgroundSecondaryColor = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    BackgroundAccentColor = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ButtonPrimaryColor = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ButtonSecondaryColor = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ButtonTextColor = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ButtonBorderRadius = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CardBackgroundColor = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CardBorderColor = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CardBorderRadius = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CardShadow = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    HeadingFontFamily = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    BodyFontFamily = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    FontBaseSize = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    SpacingUnit = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ContainerMaxWidth = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ContainerPadding = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    HeaderBackgroundColor = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    HeaderTextColor = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    HeaderHeight = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    FooterBackgroundColor = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    FooterTextColor = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    NavLinkColor = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    NavLinkActiveColor = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    NavLinkHoverColor = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    InputBackgroundColor = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    InputBorderColor = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    InputBorderRadius = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    InputFocusBorderColor = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CustomCSS = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ThemeConfigurations", x => x.Id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ThemeConfigurations");
        }
    }
}
