namespace MyShop.API.DTOs
{
    public class InventoryDTO
    {
        public int Id { get; set; }
        public int? ProductVariantId { get; set; }
        public string SKU { get; set; } = string.Empty;
        public string ProductName { get; set; } = string.Empty;
        public int Quantity { get; set; }
        public int ReorderLevel { get; set; }
        public DateTime LastRestocked { get; set; } // Maps to LastRestockedAt in the entity
        public bool IsLowStock => Quantity <= ReorderLevel;
    }

    public class UpdateInventoryDTO
    {
        public int Quantity { get; set; }
        public int ReorderLevel { get; set; }
    }

    public class InventoryImportDTO
    {
        public string SKU { get; set; } = string.Empty;
        public int Quantity { get; set; }
        public int ReorderLevel { get; set; }
    }
}
