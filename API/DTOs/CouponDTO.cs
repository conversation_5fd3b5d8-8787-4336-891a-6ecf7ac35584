using MyShop.API.Data.Entities;
using System;
using System.ComponentModel.DataAnnotations;

namespace MyShop.API.DTOs
{
    public class CouponDTO
    {
        public int Id { get; set; }
        public string Code { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public decimal Value { get; set; }
        public string Applicability { get; set; } = string.Empty;

        // Related entity IDs and names
        public int? CategoryId { get; set; }
        public string? CategoryName { get; set; }
        public int? CollectionId { get; set; }
        public string? CollectionName { get; set; }
        public int? ProductId { get; set; }
        public string? ProductName { get; set; }

        // Conditions and limits
        public decimal? MinimumPurchaseAmount { get; set; }
        public int? MinimumQuantity { get; set; }
        public decimal? MaximumDiscountAmount { get; set; }

        // Usage settings
        public bool IsActive { get; set; }
        public bool IsOneTimeUse { get; set; }
        public bool DisplayOnCartPage { get; set; }
        public int UsageLimit { get; set; }
        public int UsageCount { get; set; }

        // Validity period
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime CreatedAt { get; set; }

        // Store relationship
        public int StoreId { get; set; }

        // Calculated fields
        public decimal PotentialSavings { get; set; }
        public bool IsExpired => EndDate.HasValue && EndDate.Value < DateTime.UtcNow;
        public bool HasReachedUsageLimit => UsageLimit > 0 && UsageCount >= UsageLimit;

        public string Status => GetStatus();

        private string GetStatus()
        {
            if (!IsActive) return "Inactive";
            if (IsExpired) return "Expired";
            if (HasReachedUsageLimit) return "Usage Limit Reached";
            if (StartDate > DateTime.UtcNow) return "Scheduled";
            return "Active";
        }
    }

    public class CreateCouponDTO
    {
        [Required]
        [StringLength(20, MinimumLength = 3)]
        public string Code { get; set; } = string.Empty;

        [Required]
        public string Description { get; set; } = string.Empty;

        [Required]
        public CouponType Type { get; set; }

        [Required]
        [Range(0.01, 100)]
        public decimal Value { get; set; }

        [Required]
        public CouponApplicability Applicability { get; set; }

        public int? CategoryId { get; set; }
        public int? CollectionId { get; set; }
        public int? ProductId { get; set; }

        [Range(0, double.MaxValue)]
        public decimal? MinimumPurchaseAmount { get; set; }

        [Range(0, int.MaxValue)]
        public int? MinimumQuantity { get; set; }

        [Range(0, double.MaxValue)]
        public decimal? MaximumDiscountAmount { get; set; }

        public bool IsActive { get; set; } = true;
        public bool IsOneTimeUse { get; set; } = false;
        public bool DisplayOnCartPage { get; set; } = false;

        [Range(0, int.MaxValue)]
        public int UsageLimit { get; set; } = 0;

        [Required]
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }

        [Required]
        public int StoreId { get; set; }
    }

    public class UpdateCouponDTO
    {
        public string Description { get; set; } = string.Empty;
        public CouponType Type { get; set; }

        [Range(0.01, 100)]
        public decimal Value { get; set; }

        public CouponApplicability Applicability { get; set; }
        public int? CategoryId { get; set; }
        public int? CollectionId { get; set; }
        public int? ProductId { get; set; }

        [Range(0, double.MaxValue)]
        public decimal? MinimumPurchaseAmount { get; set; }

        [Range(0, int.MaxValue)]
        public int? MinimumQuantity { get; set; }

        [Range(0, double.MaxValue)]
        public decimal? MaximumDiscountAmount { get; set; }

        public bool IsActive { get; set; }
        public bool IsOneTimeUse { get; set; }
        public bool DisplayOnCartPage { get; set; }

        [Range(0, int.MaxValue)]
        public int UsageLimit { get; set; }

        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
    }

    public class ApplyCouponDTO
    {
        [Required]
        public string Code { get; set; } = string.Empty;

        [Range(0, double.MaxValue)]
        public decimal CartTotal { get; set; }

        [Range(0, int.MaxValue)]
        public int TotalItems { get; set; }

        public int? CategoryId { get; set; }
        public int? CollectionId { get; set; }
        public int? ProductId { get; set; }
    }

    public class CouponValidationResultDTO
    {
        public bool IsValid { get; set; }
        public string Message { get; set; } = string.Empty;
        public decimal DiscountAmount { get; set; }
        public CouponDTO? Coupon { get; set; }
    }

    public class CouponSummaryDTO
    {
        public int TotalCoupons { get; set; }
        public int ActiveCoupons { get; set; }
        public int ExpiredCoupons { get; set; }
        public int ScheduledCoupons { get; set; }
        public decimal TotalDiscountsGiven { get; set; }
        public int TotalUsageCount { get; set; }
    }

    public class PagedResult<T>
    {
        public List<T> Items { get; set; } = new List<T>();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
    }

    public class BulkStatusUpdateDTO
    {
        public List<int> CouponIds { get; set; } = new List<int>();
        public bool IsActive { get; set; }
    }

    public enum CouponApplicability
    {
        All,
        Cart,
        Category,
        Collection,
        Product,
        MinimumPurchase,
        FirstTimeUser
    }
}
