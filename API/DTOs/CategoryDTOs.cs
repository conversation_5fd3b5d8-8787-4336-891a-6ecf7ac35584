using System.Collections.Generic;

namespace MyShop.API.DTOs
{
    public class CategoryDTO
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string ImageUrl { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public int DisplayOrder { get; set; }
        public int ProductCount { get; set; }
        public int? ParentId { get; set; }
        public bool IsSale { get; set; }
        public int StoreId { get; set; }
        public List<CategoryDTO> Children { get; set; } = new List<CategoryDTO>();
    }

    public class CreateCategoryDTO
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string? ImageUrl { get; set; }
        public bool IsActive { get; set; } = true;
        public int DisplayOrder { get; set; }
        public int? ParentId { get; set; }
        public bool IsSale { get; set; } = false;
        public int StoreId { get; set; }
    }

    public class UpdateCategoryDTO
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string? ImageUrl { get; set; }
        public bool IsActive { get; set; }
        public int DisplayOrder { get; set; }
        public int? ParentId { get; set; }
        public bool IsSale { get; set; }
    }

    public class BulkCategoryIdsDTO
    {
        public List<int> CategoryIds { get; set; } = new List<int>();
    }

    public class BulkUpdateCategoryStatusDTO
    {
        public List<int> CategoryIds { get; set; } = new List<int>();
        public bool IsActive { get; set; }
    }
}
