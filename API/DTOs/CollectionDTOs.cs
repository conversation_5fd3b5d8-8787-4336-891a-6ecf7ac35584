using System.Collections.Generic;

namespace MyShop.API.DTOs
{
    public class CollectionDTO
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string ImageUrl { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public int DisplayOrder { get; set; }
        public int ProductCount { get; set; }
        public int StoreId { get; set; }
    }

    public class CreateCollectionDTO
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string? ImageUrl { get; set; }
        public bool IsActive { get; set; } = true;
        public int DisplayOrder { get; set; }
        public int StoreId { get; set; }
    }

    public class UpdateCollectionDTO
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string? ImageUrl { get; set; }
        public bool IsActive { get; set; }
        public int DisplayOrder { get; set; }
    }

    public class BulkCollectionIdsDTO
    {
        public List<int> CollectionIds { get; set; } = new List<int>();
    }

    public class BulkUpdateCollectionStatusDTO
    {
        public List<int> CollectionIds { get; set; } = new List<int>();
        public bool IsActive { get; set; }
    }
}
