namespace MyShop.API.DTOs
{
    public class ProductVariantDTO
    {
        public int Id { get; set; }
        public string SKU { get; set; } = string.Empty;
        public string Barcode { get; set; } = string.Empty;
        public decimal Price { get; set; }
        public decimal Cost { get; set; }
        public decimal MRP { get; set; }
        public int StockQuantity { get; set; } // Total stock across all locations
        public int ReorderLevel { get; set; }
        public bool IsActive { get; set; }
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;

        // Physical attributes
        public string Size { get; set; } = string.Empty;
        public decimal Weight { get; set; }
        public string WeightUnit { get; set; } = "kg";
        public decimal Length { get; set; }
        public decimal Breadth { get; set; }
        public decimal Height { get; set; }
        public string DimensionUnit { get; set; } = "cm";
        public decimal Volume { get; set; }
        public string VolumeUnit { get; set; } = "cm3";

        // Variant attributes
        public Dictionary<string, string> VariantAttributes { get; set; } = new Dictionary<string, string>();

        // Inventory items at different locations
        public List<InventoryItemDTO> InventoryItems { get; set; } = new List<InventoryItemDTO>();
    }

    public class CreateProductVariantDTO
    {
        public string? SKU { get; set; }
        public string? Barcode { get; set; }
        public decimal Price { get; set; }
        public decimal Cost { get; set; }
        public decimal MRP { get; set; }
        public int StockQuantity { get; set; }
        public int ReorderLevel { get; set; } = 10;
        public bool IsActive { get; set; } = true;
        public int ProductId { get; set; }
        public int LocationId { get; set; } = 0; // Default location ID

        // Physical attributes
        public string Size { get; set; } = string.Empty;
        public decimal Weight { get; set; }
        public string WeightUnit { get; set; } = "kg";
        public decimal Length { get; set; }
        public decimal Breadth { get; set; }
        public decimal Height { get; set; }
        public string DimensionUnit { get; set; } = "cm";
        public decimal Volume { get; set; }
        public string VolumeUnit { get; set; } = "cm3";

        // Variant attributes
        public Dictionary<string, string> VariantAttributes { get; set; } = new Dictionary<string, string>();
    }

    public class UpdateProductVariantDTO
    {
        public int Id { get; set; }
        public string? SKU { get; set; }
        public string? Barcode { get; set; }
        public decimal? Price { get; set; }
        public decimal? Cost { get; set; }
        public decimal? MRP { get; set; }
        public int? StockQuantity { get; set; }
        public int? ReorderLevel { get; set; }
        public bool? IsActive { get; set; }
        public int? LocationId { get; set; }

        // Physical attributes
        public string? Size { get; set; }
        public decimal? Weight { get; set; }
        public string? WeightUnit { get; set; }
        public decimal? Length { get; set; }
        public decimal? Breadth { get; set; }
        public decimal? Height { get; set; }
        public string? DimensionUnit { get; set; }
        public decimal? Volume { get; set; }
        public string? VolumeUnit { get; set; }

        // Variant attributes
        public Dictionary<string, string>? VariantAttributes { get; set; }
    }
}
