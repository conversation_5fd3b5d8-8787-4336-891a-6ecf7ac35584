namespace MyShop.API.DTOs
{
    public class ProductImageDTO
    {
        public int Id { get; set; }
        public string Url { get; set; } = string.Empty;
        public bool IsMain { get; set; }
        public int DisplayOrder { get; set; }
    }

    public class ProductTypeDTO
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }

    public class ProductDTO
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string ImageUrl { get; set; } = string.Empty; // Legacy support
        public string Barcode { get; set; } = string.Empty;
        public string StyleCode { get; set; } = string.Empty;
        public int ReturnExchangeCondition { get; set; } = 0;
        public string Color { get; set; } = string.Empty;
        public string HSNCode { get; set; } = string.Empty;
        public string GSTType { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public bool IsFeatured { get; set; }
        public bool HasVariants { get; set; } = false;
        public int CategoryId { get; set; }
        public string CategoryName { get; set; } = string.Empty;
        public int? ProductGroupId { get; set; }
        public string ProductGroupName { get; set; } = string.Empty;
        public int? ProductTypeId { get; set; }
        public string ProductTypeName { get; set; } = string.Empty;
        public int StockQuantity { get; set; }
        public int ReorderLevel { get; set; }
        public List<int> CollectionIds { get; set; } = new List<int>();
        public List<string> CollectionNames { get; set; } = new List<string>();
        public List<ProductImageDTO> Images { get; set; } = new List<ProductImageDTO>();
        public string MainImage { get; set; } = string.Empty; // Convenience property for the main image URL
        public Dictionary<string, string> CustomAttributes { get; set; } = new Dictionary<string, string>();
        public List<ProductVariantDTO> Variants { get; set; } = new List<ProductVariantDTO>();
        public int StoreId { get; set; }
        public string StoreName { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class CreateProductDTO
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string? Barcode { get; set; }
        public string? StyleCode { get; set; }
        public int ReturnExchangeCondition { get; set; } = 0;
        public string? Color { get; set; }
        public string? HSNCode { get; set; }
        public string? GSTType { get; set; }
        public string? ImageUrl { get; set; } // Legacy support
        public bool IsActive { get; set; } = true;
        public bool IsFeatured { get; set; } = false;
        public bool HasVariants { get; set; } = false;
        public int CategoryId { get; set; }
        public int? ProductGroupId { get; set; }
        public int? ProductTypeId { get; set; }
        public string? ProductTypeName { get; set; } // For creating new product types
        public List<int> CollectionIds { get; set; } = new List<int>();
        public List<ProductImageDTO>? Images { get; set; }
        public Dictionary<string, string>? CustomAttributes { get; set; }
        public List<CreateProductVariantDTO>? Variants { get; set; }
        public int StoreId { get; set; }
    }

    public class UpdateProductDTO
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string? Barcode { get; set; }
        public string? StyleCode { get; set; }
        public int ReturnExchangeCondition { get; set; } = 0;
        public string Color { get; set; } = string.Empty;
        public string HSNCode { get; set; } = string.Empty;
        public string GSTType { get; set; } = string.Empty;
        public string? ImageUrl { get; set; } // Legacy support
        public bool IsActive { get; set; }
        public bool IsFeatured { get; set; }
        public bool? HasVariants { get; set; }
        public int CategoryId { get; set; }
        public int? ProductGroupId { get; set; }
        public int? ProductTypeId { get; set; }
        public string? ProductTypeName { get; set; } // For creating new product types
        public int? StockQuantity { get; set; }
        public int? ReorderLevel { get; set; }
        public List<int> CollectionIds { get; set; } = new List<int>();
        public List<ProductImageDTO>? Images { get; set; }
        public Dictionary<string, string>? CustomAttributes { get; set; }
        public List<UpdateProductVariantDTO>? Variants { get; set; }
    }
}
