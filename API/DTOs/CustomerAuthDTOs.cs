namespace MyShop.API.DTOs
{
    public class RequestOtpDTO
    {
        public string? Email { get; set; }
        public string? PhoneNumber { get; set; }
    }

    public class VerifyOtpDTO
    {
        public string? Email { get; set; }
        public string? PhoneNumber { get; set; }
        public string Otp { get; set; } = string.Empty;
    }

    public class CustomerAuthResponseDTO
    {
        public bool Success { get; set; }
        public string? Token { get; set; }
        public string? UserId { get; set; }
        public string? Email { get; set; }
        public string? PhoneNumber { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public bool IsNewUser { get; set; }
        public DateTime? Expiration { get; set; }
        public string? ErrorMessage { get; set; }
        public List<string> Roles { get; set; } = new List<string>();
    }
}
