using System;
using System.Collections.Generic;

namespace MyShop.API.DTOs
{
    public class ProductVariantDto
    {
        public int Id { get; set; }
        public string SKU { get; set; } = string.Empty;
        public string Barcode { get; set; } = string.Empty;
        public decimal Price { get; set; }
        public decimal Cost { get; set; }
        public decimal MRP { get; set; }
        public int StockQuantity { get; set; }
        public int ReorderLevel { get; set; }
        public bool IsActive { get; set; }
        public Dictionary<string, string> VariantAttributes { get; set; } = new Dictionary<string, string>();

        // New fields
        public string Size { get; set; } = string.Empty;
        public decimal SellingPrice { get; set; }
        public decimal CostPrice { get; set; }
        public int Quantity { get; set; }

        // Physical attributes
        public decimal Weight { get; set; }
        public string WeightUnit { get; set; } = "kg";
        public decimal Length { get; set; }
        public decimal Breadth { get; set; }
        public decimal Height { get; set; }
        public string DimensionUnit { get; set; } = "cm";
        public decimal Volume { get; set; }
        public string VolumeUnit { get; set; } = "cm3";

        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public List<ProductImageDto> Images { get; set; } = new List<ProductImageDto>();
    }

    public class CreateProductVariantDto
    {
        public string SKU { get; set; } = string.Empty;
        public string Barcode { get; set; } = string.Empty;
        public decimal Price { get; set; }
        public decimal Cost { get; set; }
        public decimal MRP { get; set; }
        public int StockQuantity { get; set; }
        public int? ReorderLevel { get; set; }
        public bool IsActive { get; set; } = true;
        public Dictionary<string, string> VariantAttributes { get; set; } = new Dictionary<string, string>();

        // New fields
        public string Size { get; set; } = string.Empty;
        public decimal SellingPrice { get; set; }
        public decimal CostPrice { get; set; }
        public int Quantity { get; set; }

        // Physical attributes
        public decimal Weight { get; set; }
        public string WeightUnit { get; set; } = "kg";
        public decimal Length { get; set; }
        public decimal Breadth { get; set; }
        public decimal Height { get; set; }
        public string DimensionUnit { get; set; } = "cm";
        public decimal? Volume { get; set; }
        public string VolumeUnit { get; set; } = "cm3";

        public int ProductId { get; set; }
        public List<CreateProductImageDto> Images { get; set; } = new List<CreateProductImageDto>();
    }

    public class UpdateProductVariantDto
    {
        public string? SKU { get; set; }
        public string? Barcode { get; set; }
        public decimal? Price { get; set; }
        public decimal? Cost { get; set; }
        public decimal? MRP { get; set; }
        public int? StockQuantity { get; set; }
        public int? ReorderLevel { get; set; }
        public bool? IsActive { get; set; }
        public Dictionary<string, string>? VariantAttributes { get; set; }

        // New fields
        public string? Size { get; set; }
        public decimal? SellingPrice { get; set; }
        public decimal? CostPrice { get; set; }
        public int? Quantity { get; set; }

        // Physical attributes
        public decimal? Weight { get; set; }
        public string? WeightUnit { get; set; }
        public decimal? Length { get; set; }
        public decimal? Breadth { get; set; }
        public decimal? Height { get; set; }
        public string? DimensionUnit { get; set; }
        public decimal? Volume { get; set; }
        public string? VolumeUnit { get; set; }

        public List<CreateProductImageDto>? Images { get; set; }
    }
}
