using System;
using System.Collections.Generic;

namespace MyShop.API.DTOs
{
    public class StoreDTO
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string StoreKey { get; set; }
        public bool IsActive { get; set; }
        public string PrimaryDomain { get; set; }
        public List<string> AdditionalDomains { get; set; } = new List<string>();
        public string LogoUrl { get; set; }
        public string FaviconUrl { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public string Address { get; set; }
        public string FacebookUrl { get; set; }
        public string InstagramUrl { get; set; }
        public string TwitterUrl { get; set; }
        public string WhatsappNumber { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public ThemeConfigurationDTO ThemeConfiguration { get; set; }
        public WebsiteConfigurationDTO WebsiteConfiguration { get; set; }
    }

    public class CreateStoreDTO
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public bool IsActive { get; set; } = true;
        public string PrimaryDomain { get; set; }
        public List<string> AdditionalDomains { get; set; } = new List<string>();
        public string LogoUrl { get; set; } = "";
        public string FaviconUrl { get; set; } = "";
        public string Email { get; set; } = "";
        public string Phone { get; set; } = "";
        public string Address { get; set; } = "";
        public string FacebookUrl { get; set; } = "";
        public string InstagramUrl { get; set; } = "";
        public string TwitterUrl { get; set; } = "";
        public string WhatsappNumber { get; set; } = "";
    }

    public class UpdateStoreDTO
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public bool IsActive { get; set; }
        public string PrimaryDomain { get; set; }
        public List<string> AdditionalDomains { get; set; } = new List<string>();
        public string LogoUrl { get; set; }
        public string FaviconUrl { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public string Address { get; set; }
        public string FacebookUrl { get; set; }
        public string InstagramUrl { get; set; }
        public string TwitterUrl { get; set; }
        public string WhatsappNumber { get; set; }
    }

    public class StoreAdminDTO
    {
        public int Id { get; set; }
        public string UserId { get; set; }
        public string UserEmail { get; set; }
        public string UserName { get; set; }
        public int StoreId { get; set; }
        public string StoreName { get; set; }
        public bool CanManageProducts { get; set; }
        public bool CanManageOrders { get; set; }
        public bool CanManageCustomers { get; set; }
        public bool CanManageSettings { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class CreateStoreAdminDTO
    {
        public string UserEmail { get; set; }
        public int StoreId { get; set; }
        public bool CanManageProducts { get; set; } = true;
        public bool CanManageOrders { get; set; } = true;
        public bool CanManageCustomers { get; set; } = true;
        public bool CanManageSettings { get; set; } = false;
    }

    public class UpdateStoreAdminDTO
    {
        public bool CanManageProducts { get; set; }
        public bool CanManageOrders { get; set; }
        public bool CanManageCustomers { get; set; }
        public bool CanManageSettings { get; set; }
    }

    public class StoreDomainDTO
    {
        public int Id { get; set; }
        public string Domain { get; set; }
        public bool IsActive { get; set; }
        public int StoreId { get; set; }
    }

    public class CreateStoreDomainDTO
    {
        public string Domain { get; set; }
        public bool IsActive { get; set; }
        public int StoreId { get; set; }
    }
}
