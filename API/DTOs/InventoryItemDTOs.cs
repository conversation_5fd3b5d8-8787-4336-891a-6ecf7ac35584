namespace MyShop.API.DTOs
{
    public class InventoryItemDTO
    {
        public int Id { get; set; }
        public int LocationId { get; set; }
        public string LocationName { get; set; } = string.Empty;
        public string LocationCode { get; set; } = string.Empty;
        public int StockQuantity { get; set; }
        public int ReorderLevel { get; set; }
        public DateTime LastRestockedAt { get; set; }
    }

    public class CreateInventoryItemDTO
    {
        public int LocationId { get; set; }
        public int? ProductId { get; set; }
        public int? ProductVariantId { get; set; }
        public int StockQuantity { get; set; }
        public int ReorderLevel { get; set; } = 10;
    }

    public class UpdateInventoryItemDTO
    {
        public int StockQuantity { get; set; }
        public int ReorderLevel { get; set; }
    }
}
