using System;
using System.Collections.Generic;

namespace MyShop.API.DTOs
{
    public class AdminResponseDTO
    {
        public bool Success { get; set; }
        public string Token { get; set; }
        public string UserId { get; set; }
        public string Email { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public List<string> Roles { get; set; } = new List<string>();
        public DateTime? Expiration { get; set; }
        public string ErrorMessage { get; set; }
        public List<StoreDTO> AccessibleStores { get; set; } = new List<StoreDTO>();
    }
}
