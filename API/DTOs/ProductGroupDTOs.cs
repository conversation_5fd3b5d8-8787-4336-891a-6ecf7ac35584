using System.Collections.Generic;

namespace MyShop.API.DTOs
{
    public class ProductGroupDTO
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public List<ProductDTO> Products { get; set; } = new List<ProductDTO>();
    }

    public class CreateProductGroupDTO
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public List<int> ProductIds { get; set; } = new List<int>();
    }

    public class UpdateProductGroupDTO
    {
        public string? Name { get; set; }
        public string? Description { get; set; }
        public List<int>? ProductIds { get; set; }
    }
}
