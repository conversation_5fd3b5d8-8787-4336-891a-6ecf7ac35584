using System;

namespace MyShop.API.DTOs
{
    public class ThemeConfigurationDTO
    {
        public int Id { get; set; }
        public int StoreId { get; set; }
        public string? StoreName { get; set; }
        public string? LogoUrl { get; set; }
        public string? FaviconUrl { get; set; }

        // Link Color (for user preference)
        public string LinkColor { get; set; } = "#03a685"; // Green for links

        // Theme Name and Description
        public string Name { get; set; } = "Default";
        public string Description { get; set; } = "Default theme configuration";
        public bool IsActive { get; set; } = true;

        // Primary Colors
        public string PrimaryColor { get; set; } = "#ff3f6c"; // Default pink
        public string SecondaryColor { get; set; } = "#282c3f"; // Dark gray
        public string AccentColor { get; set; } = "#03a685"; // Green for success/positive elements

        // Text Colors
        public string TextPrimaryColor { get; set; } = "#282c3f"; // Dark gray for main text
        public string TextSecondaryColor { get; set; } = "#94969f"; // Light gray for secondary text
        public string TextLightColor { get; set; } = "#ffffff"; // White text for dark backgrounds

        // Background Colors
        public string BackgroundPrimaryColor { get; set; } = "#ffffff"; // White
        public string BackgroundSecondaryColor { get; set; } = "#f5f5f6"; // Light gray
        public string BackgroundAccentColor { get; set; } = "#fff5f5"; // Very light pink

        // Button Styles
        public string ButtonPrimaryColor { get; set; } = "#ff3f6c"; // Pink
        public string ButtonSecondaryColor { get; set; } = "#ffffff"; // White
        public string ButtonTextColor { get; set; } = "#ffffff"; // White
        public string ButtonBorderRadius { get; set; } = "4px"; // Slightly rounded corners

        // Card Styles
        public string CardBackgroundColor { get; set; } = "#ffffff"; // White
        public string CardBorderColor { get; set; } = "#e0e0e0"; // Light gray
        public string CardBorderRadius { get; set; } = "8px"; // Rounded corners
        public string CardShadow { get; set; } = "0 2px 8px rgba(0,0,0,0.1)"; // Subtle shadow

        // Typography
        public string HeadingFontFamily { get; set; } = "'Poppins', sans-serif";
        public string BodyFontFamily { get; set; } = "'Roboto', sans-serif";
        public string FontBaseSize { get; set; } = "16px"; // Base font size

        // Spacing
        public string SpacingUnit { get; set; } = "8px"; // Base spacing unit
        public string ContainerMaxWidth { get; set; } = "1200px"; // Maximum width for content containers
        public string ContainerPadding { get; set; } = "16px"; // Padding for containers

        // Header Styles
        public string HeaderBackgroundColor { get; set; } = "#ffffff"; // White
        public string HeaderTextColor { get; set; } = "#282c3f"; // Dark gray
        public string HeaderHeight { get; set; } = "64px"; // Header height

        // Footer Styles
        public string FooterBackgroundColor { get; set; } = "#f5f5f6"; // Light gray
        public string FooterTextColor { get; set; } = "#282c3f"; // Dark gray

        // Navigation Styles
        public string NavLinkColor { get; set; } = "#282c3f"; // Dark gray
        public string NavLinkActiveColor { get; set; } = "#ff3f6c"; // Pink
        public string NavLinkHoverColor { get; set; } = "#ff3f6c"; // Pink

        // Form Styles
        public string InputBackgroundColor { get; set; } = "#ffffff"; // White
        public string InputBorderColor { get; set; } = "#d4d5d9"; // Light gray
        public string InputBorderRadius { get; set; } = "4px"; // Slightly rounded corners
        public string InputFocusBorderColor { get; set; } = "#ff3f6c"; // Pink

        // Custom CSS
        public string CustomCSS { get; set; } = ""; // For any additional custom CSS

        // Timestamps
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class CreateThemeConfigurationDTO
    {
        public int StoreId { get; set; }
        // Theme Name and Description
        public string Name { get; set; } = "Default";
        public string Description { get; set; } = "Default theme configuration";
        public bool IsActive { get; set; } = true;

        // Primary Colors
        public string PrimaryColor { get; set; } = "#ff3f6c"; // Default pink
        public string SecondaryColor { get; set; } = "#282c3f"; // Dark gray
        public string AccentColor { get; set; } = "#03a685"; // Green for success/positive elements

        // Text Colors
        public string TextPrimaryColor { get; set; } = "#282c3f"; // Dark gray for main text
        public string TextSecondaryColor { get; set; } = "#94969f"; // Light gray for secondary text
        public string TextLightColor { get; set; } = "#ffffff"; // White text for dark backgrounds

        // Background Colors
        public string BackgroundPrimaryColor { get; set; } = "#ffffff"; // White
        public string BackgroundSecondaryColor { get; set; } = "#f5f5f6"; // Light gray
        public string BackgroundAccentColor { get; set; } = "#fff5f5"; // Very light pink

        // Button Styles
        public string ButtonPrimaryColor { get; set; } = "#ff3f6c"; // Pink
        public string ButtonSecondaryColor { get; set; } = "#ffffff"; // White
        public string ButtonTextColor { get; set; } = "#ffffff"; // White
        public string ButtonBorderRadius { get; set; } = "4px"; // Slightly rounded corners

        // Card Styles
        public string CardBackgroundColor { get; set; } = "#ffffff"; // White
        public string CardBorderColor { get; set; } = "#e0e0e0"; // Light gray
        public string CardBorderRadius { get; set; } = "8px"; // Rounded corners
        public string CardShadow { get; set; } = "0 2px 8px rgba(0,0,0,0.1)"; // Subtle shadow

        // Typography
        public string HeadingFontFamily { get; set; } = "'Poppins', sans-serif";
        public string BodyFontFamily { get; set; } = "'Roboto', sans-serif";
        public string FontBaseSize { get; set; } = "16px"; // Base font size

        // Spacing
        public string SpacingUnit { get; set; } = "8px"; // Base spacing unit
        public string ContainerMaxWidth { get; set; } = "1200px"; // Maximum width for content containers
        public string ContainerPadding { get; set; } = "16px"; // Padding for containers

        // Header Styles
        public string HeaderBackgroundColor { get; set; } = "#ffffff"; // White
        public string HeaderTextColor { get; set; } = "#282c3f"; // Dark gray
        public string HeaderHeight { get; set; } = "64px"; // Header height

        // Footer Styles
        public string FooterBackgroundColor { get; set; } = "#f5f5f6"; // Light gray
        public string FooterTextColor { get; set; } = "#282c3f"; // Dark gray

        // Navigation Styles
        public string NavLinkColor { get; set; } = "#282c3f"; // Dark gray
        public string NavLinkActiveColor { get; set; } = "#ff3f6c"; // Pink
        public string NavLinkHoverColor { get; set; } = "#ff3f6c"; // Pink

        // Form Styles
        public string InputBackgroundColor { get; set; } = "#ffffff"; // White
        public string InputBorderColor { get; set; } = "#d4d5d9"; // Light gray
        public string InputBorderRadius { get; set; } = "4px"; // Slightly rounded corners
        public string InputFocusBorderColor { get; set; } = "#ff3f6c"; // Pink

        // Custom CSS
        public string CustomCSS { get; set; } = ""; // For any additional custom CSS
    }

    public class UpdateThemeConfigurationDTO
    {
        // Theme Name and Description
        public string Name { get; set; } = "Default";
        public string Description { get; set; } = "Default theme configuration";
        public bool IsActive { get; set; } = true;

        // Primary Colors
        public string PrimaryColor { get; set; } = "#ff3f6c"; // Default pink
        public string SecondaryColor { get; set; } = "#282c3f"; // Dark gray
        public string AccentColor { get; set; } = "#03a685"; // Green for success/positive elements

        // Text Colors
        public string TextPrimaryColor { get; set; } = "#282c3f"; // Dark gray for main text
        public string TextSecondaryColor { get; set; } = "#94969f"; // Light gray for secondary text
        public string TextLightColor { get; set; } = "#ffffff"; // White text for dark backgrounds

        // Background Colors
        public string BackgroundPrimaryColor { get; set; } = "#ffffff"; // White
        public string BackgroundSecondaryColor { get; set; } = "#f5f5f6"; // Light gray
        public string BackgroundAccentColor { get; set; } = "#fff5f5"; // Very light pink

        // Button Styles
        public string ButtonPrimaryColor { get; set; } = "#ff3f6c"; // Pink
        public string ButtonSecondaryColor { get; set; } = "#ffffff"; // White
        public string ButtonTextColor { get; set; } = "#ffffff"; // White
        public string ButtonBorderRadius { get; set; } = "4px"; // Slightly rounded corners

        // Card Styles
        public string CardBackgroundColor { get; set; } = "#ffffff"; // White
        public string CardBorderColor { get; set; } = "#e0e0e0"; // Light gray
        public string CardBorderRadius { get; set; } = "8px"; // Rounded corners
        public string CardShadow { get; set; } = "0 2px 8px rgba(0,0,0,0.1)"; // Subtle shadow

        // Typography
        public string HeadingFontFamily { get; set; } = "'Poppins', sans-serif";
        public string BodyFontFamily { get; set; } = "'Roboto', sans-serif";
        public string FontBaseSize { get; set; } = "16px"; // Base font size

        // Spacing
        public string SpacingUnit { get; set; } = "8px"; // Base spacing unit
        public string ContainerMaxWidth { get; set; } = "1200px"; // Maximum width for content containers
        public string ContainerPadding { get; set; } = "16px"; // Padding for containers

        // Header Styles
        public string HeaderBackgroundColor { get; set; } = "#ffffff"; // White
        public string HeaderTextColor { get; set; } = "#282c3f"; // Dark gray
        public string HeaderHeight { get; set; } = "64px"; // Header height

        // Footer Styles
        public string FooterBackgroundColor { get; set; } = "#f5f5f6"; // Light gray
        public string FooterTextColor { get; set; } = "#282c3f"; // Dark gray

        // Navigation Styles
        public string NavLinkColor { get; set; } = "#282c3f"; // Dark gray
        public string NavLinkActiveColor { get; set; } = "#ff3f6c"; // Pink
        public string NavLinkHoverColor { get; set; } = "#ff3f6c"; // Pink

        // Form Styles
        public string InputBackgroundColor { get; set; } = "#ffffff"; // White
        public string InputBorderColor { get; set; } = "#d4d5d9"; // Light gray
        public string InputBorderRadius { get; set; } = "4px"; // Slightly rounded corners
        public string InputFocusBorderColor { get; set; } = "#ff3f6c"; // Pink

        // Custom CSS
        public string CustomCSS { get; set; } = ""; // For any additional custom CSS
    }
}
