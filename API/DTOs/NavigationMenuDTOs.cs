namespace MyShop.API.DTOs
{
    public class NavigationMenuDTO
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
        public int? ParentId { get; set; }
        public int DisplayOrder { get; set; }
        public bool IsActive { get; set; }
        public string? Icon { get; set; }
        public int StoreId { get; set; }
        public List<NavigationMenuDTO> Children { get; set; } = new List<NavigationMenuDTO>();
    }

    public class CreateNavigationMenuDTO
    {
        public string Name { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
        public int? ParentId { get; set; }
        public int DisplayOrder { get; set; }
        public bool IsActive { get; set; } = true;
        public string? Icon { get; set; }
        public int StoreId { get; set; }
    }

    public class UpdateNavigationMenuDTO
    {
        public string Name { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
        public int? ParentId { get; set; }
        public int DisplayOrder { get; set; }
        public bool IsActive { get; set; }
        public string? Icon { get; set; }
    }
}
