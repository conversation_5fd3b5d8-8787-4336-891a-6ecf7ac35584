-- Fix the discriminator column for existing users
UPDATE AspNetUsers
SET Discriminator = 'ApplicationUser'
WHERE Discriminator IS NULL OR Discriminator = '';

-- Add a default constraint to ensure new users have the correct discriminator
IF NOT EXISTS (SELECT * FROM sys.default_constraints WHERE name = 'DF_AspNetUsers_Discriminator')
BEGIN
    ALTER TABLE AspNetUsers
    ADD CONSTRAINT DF_AspNetUsers_Discriminator
    DEFAULT 'ApplicationUser' FOR Discriminator;
END
