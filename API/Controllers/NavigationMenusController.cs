using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using MyShop.API.DTOs;

namespace MyShop.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class NavigationMenusController : StoreAwareController
    {
        private readonly ApplicationDbContext _context;

        public NavigationMenusController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: api/NavigationMenus
        [HttpGet]
        public async Task<ActionResult<IEnumerable<NavigationMenuDTO>>> GetNavigationMenus(
            [FromQuery] bool? isActive = true)
        {
            var storeId = GetCurrentStoreId();

            var query = _context.NavigationMenus
                .Include(n => n.Children)
                .Where(n => n.ParentId == null && n.StoreId == storeId)
                .AsQueryable();

            if (isActive.HasValue)
            {
                query = query.Where(n => n.IsActive == isActive.Value);
            }

            var menus = await query
                .OrderBy(n => n.DisplayOrder)
                .ToListAsync();

            var menuDtos = new List<NavigationMenuDTO>();

            // Process each menu
            foreach (var menu in menus)
            {
                var menuDto = MapToNavigationMenuDTO(menu);

                // Special handling for category and collection menus
                if (menu.Name.Equals("Categories", StringComparison.OrdinalIgnoreCase))
                {
                    // Add parent categories as children
                    var parentCategories = await _context.Categories
                        .Include(c => c.Children)
                        .Where(c => c.IsActive && c.ParentId == null && c.StoreId == storeId)
                        .OrderBy(c => c.DisplayOrder)
                        .ToListAsync();

                    foreach (var category in parentCategories)
                    {
                        var categoryMenu = new NavigationMenuDTO
                        {
                            Id = -category.Id, // Negative ID to indicate it's a category
                            Name = category.Name,
                            Url = $"/category/{category.Id}",
                            ParentId = menuDto.Id,
                            DisplayOrder = category.DisplayOrder,
                            IsActive = category.IsActive,
                            Icon = category.IsSale ? "offer" : null,
                            Children = new List<NavigationMenuDTO>()
                        };

                        // Add child categories
                        foreach (var childCategory in category.Children.Where(c => c.IsActive).OrderBy(c => c.DisplayOrder))
                        {
                            var childCategoryMenu = new NavigationMenuDTO
                            {
                                Id = -childCategory.Id, // Negative ID to indicate it's a category
                                Name = childCategory.Name,
                                Url = $"/category/{childCategory.Id}",
                                ParentId = categoryMenu.Id,
                                DisplayOrder = childCategory.DisplayOrder,
                                IsActive = childCategory.IsActive,
                                Icon = childCategory.IsSale ? "offer" : null,
                                Children = new List<NavigationMenuDTO>()
                            };

                            categoryMenu.Children.Add(childCategoryMenu);
                        }

                        menuDto.Children.Add(categoryMenu);
                    }
                }
                else if (menu.Name.Equals("Collections", StringComparison.OrdinalIgnoreCase))
                {
                    // Add collections as children
                    var collections = await _context.Collections
                        .Where(c => c.IsActive && c.StoreId == storeId)
                        .OrderBy(c => c.DisplayOrder)
                        .ToListAsync();

                    foreach (var collection in collections)
                    {
                        var collectionMenu = new NavigationMenuDTO
                        {
                            Id = -1000 - collection.Id, // Negative ID with offset to indicate it's a collection
                            Name = collection.Name,
                            Url = $"/collection/{collection.Id}",
                            ParentId = menuDto.Id,
                            DisplayOrder = collection.DisplayOrder,
                            IsActive = collection.IsActive,
                            Icon = collection.Name.Contains("Sale", StringComparison.OrdinalIgnoreCase) ? "offer" : null,
                            Children = new List<NavigationMenuDTO>()
                        };

                        menuDto.Children.Add(collectionMenu);
                    }
                }

                menuDtos.Add(menuDto);
            }

            return Ok(menuDtos);
        }

        // GET: api/NavigationMenus/5
        [HttpGet("{id}")]
        public async Task<ActionResult<NavigationMenuDTO>> GetNavigationMenu(int id)
        {
            var storeId = GetCurrentStoreId();

            var menu = await _context.NavigationMenus
                .Include(n => n.Children)
                .FirstOrDefaultAsync(n => n.Id == id && n.StoreId == storeId);

            if (menu == null)
            {
                return NotFound();
            }

            var menuDto = MapToNavigationMenuDTO(menu);

            return Ok(menuDto);
        }

        private NavigationMenuDTO MapToNavigationMenuDTO(NavigationMenu menu)
        {
            var dto = new NavigationMenuDTO
            {
                Id = menu.Id,
                Name = menu.Name,
                Url = menu.Url,
                ParentId = menu.ParentId,
                DisplayOrder = menu.DisplayOrder,
                IsActive = menu.IsActive,
                Icon = menu.Icon,
                StoreId = menu.StoreId,
                Children = new List<NavigationMenuDTO>()
            };

            if (menu.Children != null && menu.Children.Any())
            {
                foreach (var child in menu.Children.Where(c => c.IsActive).OrderBy(c => c.DisplayOrder))
                {
                    dto.Children.Add(MapToNavigationMenuDTO(child));
                }
            }

            return dto;
        }
    }
}
