using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using MyShop.API.DTOs;
using System.Net;

namespace MyShop.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ProductController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<ProductController> _logger;

        public ProductController(ApplicationDbContext context, ILogger<ProductController> logger)
        {
            _context = context;
            _logger = logger;
        }

        // GET: api/Product
        [HttpGet]
        public async Task<ActionResult<IEnumerable<ProductDTO>>> GetProducts(
            [FromQuery] string? search = null,
            [FromQuery] int? categoryId = null,
            [FromQuery] int? collectionId = null,
            [FromQuery] bool? isFeatured = null,
            [FromQuery] bool? isActive = true,
            [FromQuery] decimal? minPrice = null,
            [FromQuery] decimal? maxPrice = null,
            [FromQuery] string? sortBy = "name",
            [FromQuery] string? sortOrder = "asc",
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                var query = _context.Products
                    .Include(p => p.Category)
                    .Include(p => p.ProductType)
                    .Include(p => p.Variants)
                        .ThenInclude(v => v.InventoryItems)
                    .Include(p => p.Collections)
                    .Include(p => p.Images)
                    .AsQueryable();

                // Apply filters
                if (!string.IsNullOrEmpty(search))
                {
                    query = query.Where(p => p.Name.Contains(search) ||
                                           p.Description.Contains(search));
                }

                if (categoryId.HasValue)
                {
                    query = query.Where(p => p.CategoryId == categoryId.Value);
                }

                if (collectionId.HasValue)
                {
                    query = query.Where(p => p.Collections.Any(c => c.Id == collectionId.Value));
                }

                if (isFeatured.HasValue)
                {
                    query = query.Where(p => p.IsFeatured == isFeatured.Value);
                }

                if (isActive.HasValue)
                {
                    query = query.Where(p => p.IsActive == isActive.Value);
                }

                // Apply price filtering on variants
                if (minPrice.HasValue || maxPrice.HasValue)
                {
                    query = query.Where(p => p.Variants.Any(v =>
                        (!minPrice.HasValue || v.Price >= minPrice.Value) &&
                        (!maxPrice.HasValue || v.Price <= maxPrice.Value)));
                }

                // Apply sorting
                query = ApplySorting(query, sortBy, sortOrder);

                // Apply pagination
                var totalCount = await query.CountAsync();
                var totalPages = (int)Math.Ceiling(totalCount / (double)pageSize);

                var products = await query
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var productDtos = products.Select(p => new ProductDTO
                {
                    Id = p.Id,
                    Name = p.Name,
                    Description = p.Description,
                    StyleCode = p.StyleCode,
                    ReturnExchangeCondition = p.ReturnExchangeCondition,
                    Color = p.Color,
                    HSNCode = p.HSNCode,
                    GSTType = p.GSTType,
                    HasVariants = p.HasVariants,
                    ProductGroupId = p.ProductGroupId,
                    ProductGroupName = p.ProductGroup?.Name ?? string.Empty,
                    ProductTypeId = p.ProductTypeId,
                    ProductTypeName = p.ProductType?.Name ?? string.Empty,
                    ImageUrl = p.ImageUrl,
                    Barcode = p.Barcode,
                    IsActive = p.IsActive,
                    IsFeatured = p.IsFeatured,
                    CategoryId = p.CategoryId,
                    CategoryName = p.Category.Name,
                    StockQuantity = p.Variants.Any() ? p.Variants.Sum(v => v.InventoryItems.Sum(ii => ii.StockQuantity)) : 0,
                    ReorderLevel = p.Variants.Any() ? p.Variants.Min(v => v.InventoryItems.Any() ? v.InventoryItems.Min(ii => ii.ReorderLevel) : 0) : 0,
                    CollectionIds = p.Collections.Select(c => c.Id).ToList(),
                    CollectionNames = p.Collections.Select(c => c.Name).ToList(),
                    Variants = p.Variants.Select(v => new ProductVariantDTO
                    {
                        Id = v.Id,
                        SKU = v.SKU,
                        Barcode = v.Barcode,
                        Price = v.Price,
                        Cost = v.Cost,
                        MRP = v.MRP,
                        StockQuantity = v.InventoryItems.Sum(ii => ii.StockQuantity),
                        IsActive = v.IsActive,
                        ProductId = v.ProductId,
                        ProductName = p.Name,
                        // Physical attributes
                        Size = v.Size,
                        Weight = v.Weight,
                        WeightUnit = v.WeightUnit,
                        Length = v.Length,
                        Breadth = v.Breadth,
                        Height = v.Height,
                        DimensionUnit = v.DimensionUnit,
                        Volume = v.Volume,
                        VolumeUnit = v.VolumeUnit
                    }).ToList(),
                    Images = p.Images.Select(i => new ProductImageDTO
                    {
                        Id = i.Id,
                        Url = i.ImageUrl,
                        IsMain = i.IsMain,
                        DisplayOrder = i.DisplayOrder
                    }).ToList(),
                    MainImage = p.Images.FirstOrDefault(i => i.IsMain)?.ImageUrl ?? p.ImageUrl,
                    CustomAttributes = !string.IsNullOrEmpty(p.CustomAttributes) ?
                        System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(p.CustomAttributes) ??
                        new Dictionary<string, string>() :
                        new Dictionary<string, string>()
                }).ToList();

                Response.Headers.Append("X-Total-Count", totalCount.ToString());
                Response.Headers.Append("X-Total-Pages", totalPages.ToString());

                return Ok(productDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving products");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error retrieving products");
            }
        }

        private IQueryable<Product> ApplySorting(IQueryable<Product> query, string? sortBy, string? sortOrder)
        {
            var isDescending = sortOrder?.ToLower() == "desc";

            return sortBy?.ToLower() switch
            {
                "date" => isDescending ? query.OrderByDescending(p => p.CreatedAt) : query.OrderBy(p => p.CreatedAt),
                "category" => isDescending ? query.OrderByDescending(p => p.Category.Name) : query.OrderBy(p => p.Category.Name),
                "price" => isDescending
                    ? query.OrderByDescending(p => p.Variants.Any() ? p.Variants.Min(v => v.Price) : decimal.MaxValue)
                    : query.OrderBy(p => p.Variants.Any() ? p.Variants.Min(v => v.Price) : decimal.MaxValue),
                _ => isDescending ? query.OrderByDescending(p => p.Name) : query.OrderBy(p => p.Name) // Default to name
            };
        }

        // GET: api/Product/NewArrivals
        [HttpGet("NewArrivals")]
        public async Task<ActionResult<IEnumerable<ProductDTO>>> GetNewArrivals([FromQuery] int maxItems = 10)
        {
            try
            {
                // Get products created in the last 30 days
                var thirtyDaysAgo = DateTime.UtcNow.AddDays(-30);

                var query = _context.Products
                    .Include(p => p.Category)
                    .Include(p => p.ProductType)
                    .Include(p => p.Variants)
                        .ThenInclude(v => v.InventoryItems)
                    .Include(p => p.Collections)
                    .Include(p => p.Images)
                    .Where(p => p.IsActive && p.CreatedAt >= thirtyDaysAgo)
                    .OrderByDescending(p => p.CreatedAt)
                    .Take(maxItems);

                var products = await query.ToListAsync();

                var productDtos = products.Select(p => new ProductDTO
                {
                    Id = p.Id,
                    Name = p.Name,
                    Description = p.Description,
                    StyleCode = p.StyleCode,
                    Color = p.Color,
                    HasVariants = p.HasVariants,
                    ProductTypeId = p.ProductTypeId,
                    ProductTypeName = p.ProductType?.Name ?? string.Empty,
                    ImageUrl = p.ImageUrl,
                    IsActive = p.IsActive,
                    IsFeatured = p.IsFeatured,
                    CategoryId = p.CategoryId,
                    CategoryName = p.Category.Name,
                    StockQuantity = p.Variants.Any() ? p.Variants.Sum(v => v.InventoryItems.Sum(ii => ii.StockQuantity)) : 0,
                    CollectionIds = p.Collections.Select(c => c.Id).ToList(),
                    CollectionNames = p.Collections.Select(c => c.Name).ToList(),
                    Variants = p.Variants.Select(v => new ProductVariantDTO
                    {
                        Id = v.Id,
                        SKU = v.SKU,
                        Price = v.Price,
                        Cost = v.Cost,
                        MRP = v.MRP,
                        StockQuantity = v.InventoryItems.Sum(ii => ii.StockQuantity),
                        IsActive = v.IsActive,
                        ProductId = v.ProductId,
                        ProductName = p.Name,
                        Size = v.Size
                    }).ToList(),
                    Images = p.Images.Select(i => new ProductImageDTO
                    {
                        Id = i.Id,
                        Url = i.ImageUrl,
                        IsMain = i.IsMain,
                        DisplayOrder = i.DisplayOrder
                    }).ToList(),
                    MainImage = p.Images.FirstOrDefault(i => i.IsMain)?.ImageUrl ?? p.ImageUrl
                }).ToList();

                return Ok(productDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving new arrivals");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error retrieving new arrivals");
            }
        }

        // GET: api/Product/BestSelling
        [HttpGet("BestSelling")]
        public async Task<ActionResult<IEnumerable<ProductDTO>>> GetBestSelling([FromQuery] int maxItems = 10)
        {
            try
            {
                // Get products with the most order items
                var bestSellingProductIds = await _context.OrderItems
                    .GroupBy(oi => oi.ProductId)
                    .Select(g => new { ProductId = g.Key, Count = g.Count() })
                    .OrderByDescending(x => x.Count)
                    .Take(maxItems)
                    .Select(x => x.ProductId)
                    .ToListAsync();

                // If we don't have enough best-selling products, supplement with featured products
                if (bestSellingProductIds.Count < maxItems)
                {
                    var featuredProductIds = await _context.Products
                        .Where(p => p.IsActive && p.IsFeatured && !bestSellingProductIds.Contains(p.Id))
                        .OrderByDescending(p => p.CreatedAt)
                        .Take(maxItems - bestSellingProductIds.Count)
                        .Select(p => p.Id)
                        .ToListAsync();

                    bestSellingProductIds.AddRange(featuredProductIds);
                }

                // Get the full product details
                var products = await _context.Products
                    .Include(p => p.Category)
                    .Include(p => p.ProductType)
                    .Include(p => p.Variants)
                        .ThenInclude(v => v.InventoryItems)
                    .Include(p => p.Collections)
                    .Include(p => p.Images)
                    .Where(p => p.IsActive && bestSellingProductIds.Contains(p.Id))
                    .ToListAsync();

                // Order the products by the order of bestSellingProductIds
                products = products
                    .OrderBy(p => bestSellingProductIds.IndexOf(p.Id))
                    .ToList();

                var productDtos = products.Select(p => new ProductDTO
                {
                    Id = p.Id,
                    Name = p.Name,
                    Description = p.Description,
                    StyleCode = p.StyleCode,
                    Color = p.Color,
                    HasVariants = p.HasVariants,
                    ProductTypeId = p.ProductTypeId,
                    ProductTypeName = p.ProductType?.Name ?? string.Empty,
                    ImageUrl = p.ImageUrl,
                    IsActive = p.IsActive,
                    IsFeatured = p.IsFeatured,
                    CategoryId = p.CategoryId,
                    CategoryName = p.Category.Name,
                    StockQuantity = p.Variants.Any() ? p.Variants.Sum(v => v.InventoryItems.Sum(ii => ii.StockQuantity)) : 0,
                    CollectionIds = p.Collections.Select(c => c.Id).ToList(),
                    CollectionNames = p.Collections.Select(c => c.Name).ToList(),
                    Variants = p.Variants.Select(v => new ProductVariantDTO
                    {
                        Id = v.Id,
                        SKU = v.SKU,
                        Price = v.Price,
                        Cost = v.Cost,
                        MRP = v.MRP,
                        StockQuantity = v.InventoryItems.Sum(ii => ii.StockQuantity),
                        IsActive = v.IsActive,
                        ProductId = v.ProductId,
                        ProductName = p.Name,
                        Size = v.Size
                    }).ToList(),
                    Images = p.Images.Select(i => new ProductImageDTO
                    {
                        Id = i.Id,
                        Url = i.ImageUrl,
                        IsMain = i.IsMain,
                        DisplayOrder = i.DisplayOrder
                    }).ToList(),
                    MainImage = p.Images.FirstOrDefault(i => i.IsMain)?.ImageUrl ?? p.ImageUrl
                }).ToList();

                return Ok(productDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving best selling products");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error retrieving best selling products");
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<ProductDTO>> GetProduct(int id)
        {
            try
            {
                var product = await _context.Products
                    .Include(p => p.Category)
                    .Include(p => p.ProductType)
                    .Include(p => p.Variants)
                        .ThenInclude(v => v.InventoryItems)
                    .Include(p => p.Variants)
                        .ThenInclude(v => v.InventoryItems)
                            .ThenInclude(ii => ii.Location)
                    .Include(p => p.Collections)
                    .Include(p => p.Images)
                    .Include(p => p.Attributes)
                    .FirstOrDefaultAsync(p => p.Id == id);

                if (product == null)
                {
                    return NotFound();
                }

                var productDto = new ProductDTO
                {
                    Id = product.Id,
                    Name = product.Name,
                    Description = product.Description,
                    ImageUrl = product.ImageUrl,
                    Barcode = product.Barcode,
                    StyleCode = product.StyleCode,
                    ReturnExchangeCondition = product.ReturnExchangeCondition,
                    Color = product.Color,
                    HSNCode = product.HSNCode,
                    GSTType = product.GSTType,
                    IsActive = product.IsActive,
                    IsFeatured = product.IsFeatured,
                    HasVariants = product.HasVariants,
                    CategoryId = product.CategoryId,
                    CategoryName = product.Category.Name,
                    ProductTypeId = product.ProductTypeId,
                    ProductTypeName = product.ProductType?.Name ?? string.Empty,
                    StockQuantity = product.Variants.Any() ? product.Variants.Sum(v => v.InventoryItems.Any() ? v.InventoryItems.Sum(ii => ii.StockQuantity) : 0) : 0,
                    ReorderLevel = product.Variants.Any() ? product.Variants.Min(v => v.InventoryItems.Any() ? v.InventoryItems.Min(ii => ii.ReorderLevel) : 0) : 0,
                    CollectionIds = product.Collections.Select(c => c.Id).ToList(),
                    CollectionNames = product.Collections.Select(c => c.Name).ToList(),
                    Images = product.Images.Select(i => new ProductImageDTO
                    {
                        Id = i.Id,
                        Url = i.ImageUrl,
                        IsMain = i.IsMain,
                        DisplayOrder = i.DisplayOrder
                    }).ToList(),
                    Variants = product.Variants.Select(v => new ProductVariantDTO
                    {
                        Id = v.Id,
                        SKU = v.SKU,
                        Barcode = v.Barcode,
                        Price = v.Price,
                        Cost = v.Cost,
                        MRP = v.MRP,
                        StockQuantity = v.InventoryItems.Any() ? v.InventoryItems.Sum(ii => ii.StockQuantity) : 0,
                        IsActive = v.IsActive,
                        ProductId = v.ProductId,
                        ProductName = product.Name,
                        // Physical attributes
                        Size = v.Size,
                        Weight = v.Weight,
                        WeightUnit = v.WeightUnit,
                        Length = v.Length,
                        Breadth = v.Breadth,
                        Height = v.Height,
                        DimensionUnit = v.DimensionUnit,
                        Volume = v.Volume,
                        VolumeUnit = v.VolumeUnit,
                        // Variant attributes
                        VariantAttributes = new Dictionary<string, string> { { "Size", v.Size }, { "Color", product.Color ?? "" } },
                        // Inventory items
                        InventoryItems = v.InventoryItems.Select(ii => new InventoryItemDTO
                        {
                            Id = ii.Id,
                            LocationId = ii.LocationId,
                            LocationName = ii.Location.Name,
                            LocationCode = ii.Location.Code,
                            StockQuantity = ii.StockQuantity,
                            ReorderLevel = ii.ReorderLevel,
                            LastRestockedAt = ii.LastRestockedAt
                        }).ToList()
                    }).ToList(),
                    MainImage = product.Images.FirstOrDefault(i => i.IsMain)?.ImageUrl ?? product.Images.FirstOrDefault()?.ImageUrl ?? product.ImageUrl,
                    CustomAttributes = product.Attributes.ToDictionary(a => a.Key, a => a.Value)
                };

                return Ok(productDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving product");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error retrieving product");
            }
        }

     
       

        // GET: api/Product/filters
        [HttpGet("filters")]
        public async Task<ActionResult<object>> GetProductFilters([FromQuery] int? categoryId = null, [FromQuery] int? collectionId = null)
        {
            try
            {
                var query = _context.Products
                    .Include(p => p.ProductType)
                    .Include(p => p.Variants)
                    .Include(p => p.Collections)
                    .AsQueryable();

                // Apply filters if provided
                if (categoryId.HasValue)
                {
                    query = query.Where(p => p.CategoryId == categoryId.Value);
                }

                if (collectionId.HasValue)
                {
                    query = query.Where(p => p.Collections.Any(c => c.Id == collectionId.Value));
                }

                // Get all active products
                query = query.Where(p => p.IsActive);

                // Get product types
                var productTypes = await query
                    .Where(p => p.ProductType != null)
                    .Select(p => new { Id = p.ProductType.Id, Name = p.ProductType.Name })
                    .Distinct()
                    .OrderBy(pt => pt.Name)
                    .ToListAsync();

                // Get sizes from variants
                var sizes = await query
                    .SelectMany(p => p.Variants)
                    .Where(v => !string.IsNullOrEmpty(v.Size))
                    .Select(v => v.Size)
                    .Distinct()
                    .OrderBy(s => s)
                    .ToListAsync();

                // Get colors
                var colors = await query
                    .Where(p => !string.IsNullOrEmpty(p.Color))
                    .Select(p => p.Color)
                    .Distinct()
                    .OrderBy(c => c)
                    .ToListAsync();

                // Return all filter values
                return Ok(new
                {
                    productTypes,
                    sizes,
                    colors
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting product filters");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error getting product filters");
            }
        }

        // GET: api/Product/types
        [HttpGet("types")]
        public async Task<ActionResult<IEnumerable<ProductTypeDTO>>> GetProductTypes()
        {
            try
            {
                var productTypes = await _context.ProductTypes
                    .OrderBy(pt => pt.Name)
                    .ToListAsync();

                var productTypeDtos = productTypes.Select(pt => new ProductTypeDTO
                {
                    Id = pt.Id,
                    Name = pt.Name,
                    Description = pt.Description
                }).ToList();

                return Ok(productTypeDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting product types");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error getting product types");
            }
        }

        // GET: api/Product/{productId}/variants
        [HttpGet("{productId}/variants")]
        public async Task<ActionResult<IEnumerable<ProductVariantDTO>>> GetProductVariants(int productId)
        {
            try
            {
                var product = await _context.Products.FindAsync(productId);
                if (product == null)
                {
                    return NotFound("Product not found");
                }

                var variants = await _context.ProductVariants
                    .Include(pv => pv.Product)
                    .Include(pv => pv.InventoryItems)
                        .ThenInclude(ii => ii.Location)
                    .Where(pv => pv.ProductId == productId)
                    .ToListAsync();

                var variantDtos = variants.Select(v => new ProductVariantDTO
                {
                    Id = v.Id,
                    SKU = v.SKU,
                    Barcode = v.Barcode,
                    Price = v.Price,
                    Cost = v.Cost,
                    MRP = v.MRP,
                    StockQuantity = v.InventoryItems.Sum(ii => ii.StockQuantity), // Sum of all inventory items
                    IsActive = v.IsActive,
                    ProductId = v.ProductId,
                    ProductName = v.Product.Name,
                    // Physical attributes
                    Size = v.Size,
                    Weight = v.Weight,
                    WeightUnit = v.WeightUnit,
                    Length = v.Length,
                    Breadth = v.Breadth,
                    Height = v.Height,
                    DimensionUnit = v.DimensionUnit,
                    Volume = v.Volume,
                    VolumeUnit = v.VolumeUnit,
                    // Variant attributes
                    VariantAttributes = new Dictionary<string, string> { { "Size", v.Size }, { "Color", v.Product.Color ?? "" } },
                    // Inventory items
                    InventoryItems = v.InventoryItems.Select(ii => new InventoryItemDTO
                    {
                        Id = ii.Id,
                        LocationId = ii.LocationId,
                        LocationName = ii.Location.Name,
                        LocationCode = ii.Location.Code,
                        StockQuantity = ii.StockQuantity,
                        ReorderLevel = ii.ReorderLevel,
                        LastRestockedAt = ii.LastRestockedAt
                    }).ToList()
                }).ToList();

                return Ok(variantDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting product variants");
                return StatusCode(500, "An error occurred while retrieving product variants");
            }
        }

        // GET: api/Product/variant/{id}
        [HttpGet("variant/{id}")]
        public async Task<ActionResult<ProductVariantDTO>> GetProductVariant(int id)
        {
            try
            {
                var variant = await _context.ProductVariants
                    .Include(pv => pv.Product)
                    .Include(pv => pv.InventoryItems)
                        .ThenInclude(ii => ii.Location)
                    .FirstOrDefaultAsync(pv => pv.Id == id);

                if (variant == null)
                {
                    return NotFound();
                }

                // Calculate total stock quantity across all locations
                int totalStockQuantity = variant.InventoryItems.Sum(ii => ii.StockQuantity);

                var variantDto = new ProductVariantDTO
                {
                    Id = variant.Id,
                    SKU = variant.SKU,
                    Barcode = variant.Barcode,
                    Price = variant.Price,
                    Cost = variant.Cost,
                    MRP = variant.MRP,
                    StockQuantity = totalStockQuantity, // Sum of all inventory items
                    IsActive = variant.IsActive,
                    ProductId = variant.ProductId,
                    ProductName = variant.Product.Name,
                    // Physical attributes
                    Size = variant.Size,
                    Weight = variant.Weight,
                    WeightUnit = variant.WeightUnit,
                    Length = variant.Length,
                    Breadth = variant.Breadth,
                    Height = variant.Height,
                    DimensionUnit = variant.DimensionUnit,
                    Volume = variant.Volume,
                    VolumeUnit = variant.VolumeUnit,
                    // Variant attributes
                    VariantAttributes = new Dictionary<string, string> { { "Size", variant.Size }, { "Color", variant.Product.Color ?? "" } },
                    // Inventory items
                    InventoryItems = variant.InventoryItems.Select(ii => new InventoryItemDTO
                    {
                        Id = ii.Id,
                        LocationId = ii.LocationId,
                        LocationName = ii.Location.Name,
                        LocationCode = ii.Location.Code,
                        StockQuantity = ii.StockQuantity,
                        ReorderLevel = ii.ReorderLevel,
                        LastRestockedAt = ii.LastRestockedAt
                    }).ToList()
                };

                return Ok(variantDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting product variant with ID {Id}", id);
                return StatusCode(500, "An error occurred while retrieving the product variant");
            }
        }

           }
}
