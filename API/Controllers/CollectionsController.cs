using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using MyShop.API.DTOs;
using MyShop.API.Services;
using System.IO;

namespace MyShop.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CollectionsController : StoreAwareController
    {
        private readonly ApplicationDbContext _context;
        private readonly IWebHostEnvironment _environment;
        private readonly ILogger<CollectionsController> _logger;

        public CollectionsController(ApplicationDbContext context, IWebHostEnvironment environment, ILogger<CollectionsController> logger)
        {
            _context = context;
            _environment = environment;
            _logger = logger;
        }

        // GET: api/Collections
        [HttpGet]
        public async Task<ActionResult<IEnumerable<CollectionDTO>>> GetCollections(
            [FromQuery] bool? isActive = true)
        {
            var storeId = GetCurrentStoreId();

            var query = _context.Collections
                .Include(c => c.Products)
                .Where(c => c.StoreId == storeId)
                .AsQueryable();

            if (isActive.HasValue)
            {
                query = query.Where(c => c.IsActive == isActive.Value);
            }

            var collections = await query
                .OrderBy(c => c.DisplayOrder)
                .ToListAsync();

            var collectionDtos = collections.Select(c => new CollectionDTO
            {
                Id = c.Id,
                Name = c.Name,
                Description = c.Description,
                ImageUrl = c.ImageUrl,
                IsActive = c.IsActive,
                DisplayOrder = c.DisplayOrder,
                ProductCount = c.Products.Count,
                StoreId = c.StoreId
            }).ToList();

            return Ok(collectionDtos);
        }

        // GET: api/Collections/5
        [HttpGet("{id}")]
        public async Task<ActionResult<CollectionDTO>> GetCollection(int id)
        {
            var storeId = GetCurrentStoreId();

            var collection = await _context.Collections
                .Include(c => c.Products)
                .FirstOrDefaultAsync(c => c.Id == id && c.StoreId == storeId);

            if (collection == null)
            {
                return NotFound();
            }

            var collectionDto = new CollectionDTO
            {
                Id = collection.Id,
                Name = collection.Name,
                Description = collection.Description,
                ImageUrl = collection.ImageUrl,
                IsActive = collection.IsActive,
                DisplayOrder = collection.DisplayOrder,
                ProductCount = collection.Products.Count,
                StoreId = collection.StoreId
            };

            return Ok(collectionDto);
        }

       

        // PUT: api/Collections/5
        [HttpPut("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> UpdateCollection(int id, UpdateCollectionDTO collectionDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var storeId = GetCurrentStoreId();

            var collection = await _context.Collections.FirstOrDefaultAsync(c => c.Id == id && c.StoreId == storeId);
            if (collection == null)
            {
                return NotFound();
            }

            collection.Name = collectionDto.Name;
            collection.Description = collectionDto.Description;
            if (!string.IsNullOrEmpty(collectionDto.ImageUrl))
            {
                collection.ImageUrl = collectionDto.ImageUrl;
            }
            collection.IsActive = collectionDto.IsActive;
            collection.DisplayOrder = collectionDto.DisplayOrder;
            collection.UpdatedAt = DateTime.UtcNow;

            _context.Entry(collection).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!CollectionExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // DELETE: api/Collections/5
        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteCollection(int id)
        {
            var storeId = GetCurrentStoreId();

            var collection = await _context.Collections
                .Include(c => c.Products)
                .FirstOrDefaultAsync(c => c.Id == id && c.StoreId == storeId);

            if (collection == null)
            {
                return NotFound();
            }

            // Remove the collection from all products
            foreach (var product in collection.Products.ToList())
            {
                collection.Products.Remove(product);
            }

            // Delete the image file if it exists
            if (!string.IsNullOrEmpty(collection.ImageUrl))
            {
                try
                {
                    // Extract filename from URL
                    var uri = new Uri(collection.ImageUrl);
                    var filename = Path.GetFileName(uri.LocalPath);
                    var imagePath = Path.Combine(_environment.WebRootPath, "uploads", "images", filename);

                    if (System.IO.File.Exists(imagePath))
                    {
                        System.IO.File.Delete(imagePath);
                        _logger.LogInformation("Deleted image file for collection {CollectionId}: {ImagePath}", id, imagePath);
                    }
                }
                catch (Exception ex)
                {
                    // Log the error but continue with collection deletion
                    _logger.LogError(ex, "Error deleting image file for collection {CollectionId}", id);
                }
            }

            _context.Collections.Remove(collection);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool CollectionExists(int id)
        {
            return _context.Collections.Any(e => e.Id == id);
        }
    }
}
