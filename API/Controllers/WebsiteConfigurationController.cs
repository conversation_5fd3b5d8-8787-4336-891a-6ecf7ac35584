using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using MyShop.API.DTOs;

namespace MyShop.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class WebsiteConfigurationController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<WebsiteConfigurationController> _logger;

        public WebsiteConfigurationController(ApplicationDbContext context, ILogger<WebsiteConfigurationController> logger)
        {
            _context = context;
            _logger = logger;
        }

        // GET: api/WebsiteConfiguration
        [HttpGet]
        public async Task<ActionResult<WebsiteConfigurationDTO>> GetWebsiteConfiguration()
        {
            var config = await _context.WebsiteConfigurations.FirstOrDefaultAsync();

            if (config == null)
            {
                // Create default configuration if none exists
                config = new WebsiteConfiguration();
                _context.WebsiteConfigurations.Add(config);
                await _context.SaveChangesAsync();
            }

            var configDto = new WebsiteConfigurationDTO
            {
                Id = config.Id,
                WebsiteTitle = config.WebsiteTitle,
                LogoUrl = config.LogoUrl,
                MetaDescription = config.MetaDescription,
                MetaKeywords = config.MetaKeywords,
                AnnouncementText = config.AnnouncementText,
                ShowAnnouncement = config.ShowAnnouncement,
                Phone = config.Phone,
                Email = config.Email,
                Address = config.Address,
                FacebookUrl = config.FacebookUrl,
                InstagramUrl = config.InstagramUrl,
                TwitterUrl = config.TwitterUrl,
                WhatsappNumber = config.WhatsappNumber,
                YoutubeUrl = config.YoutubeUrl,
                ShowBannerSection = config.ShowBannerSection,
                ShowCategorySection = config.ShowCategorySection,
                ShowNewArrivalsSection = config.ShowNewArrivalsSection,
                ShowCollectionSection = config.ShowCollectionSection,
                ShowBestSellingSection = config.ShowBestSellingSection,
                BannerTitle = config.BannerTitle,
                BannerSubtitle = config.BannerSubtitle,
                BannerButtonText = config.BannerButtonText,
                BannerButtonLink = config.BannerButtonLink,
                CategorySectionTitle = config.CategorySectionTitle,
                NewArrivalsSectionTitle = config.NewArrivalsSectionTitle,
                CollectionSectionTitle = config.CollectionSectionTitle,
                BestSellingSectionTitle = config.BestSellingSectionTitle
            };

            return Ok(configDto);
        }

  }
}
