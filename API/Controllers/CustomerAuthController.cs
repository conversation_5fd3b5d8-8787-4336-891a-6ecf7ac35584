using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using MyShop.API.DTOs;
using MyShop.API.Services;
using System.Net;

namespace MyShop.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CustomerAuthController : ControllerBase
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly OtpService _otpService;
        private readonly EmailService _emailService;
        private readonly JwtTokenService _jwtTokenService;
        private readonly ILogger<CustomerAuthController> _logger;
        private readonly ApplicationDbContext _context;

        public CustomerAuthController(
            UserManager<ApplicationUser> userManager,
            OtpService otpService,
            EmailService emailService,
            JwtTokenService jwtTokenService,
            ILogger<CustomerAuthController> logger,
            ApplicationDbContext context)
        {
            _userManager = userManager;
            _otpService = otpService;
            _emailService = emailService;
            _jwtTokenService = jwtTokenService;
            _logger = logger;
            _context = context;
        }

        [HttpPost("request-otp")]
        public async Task<IActionResult> RequestOtp([FromBody] RequestOtpDTO model)
        {
            try
            {
                if (string.IsNullOrEmpty(model.Email) && string.IsNullOrEmpty(model.PhoneNumber))
                {
                    return BadRequest(new CustomerAuthResponseDTO
                    {
                        Success = false,
                        ErrorMessage = "Either email or phone number is required"
                    });
                }

                // Generate OTP
                string otp = await _otpService.GenerateOtpAsync(model.Email, model.PhoneNumber);

                // Send OTP via email if email is provided
                if (!string.IsNullOrEmpty(model.Email))
                {
                    await _emailService.SendOtpEmailAsync(model.Email, otp);
                }

                // TODO: Send OTP via WhatsApp if phone number is provided (future integration)

                return Ok(new CustomerAuthResponseDTO
                {
                    Success = true
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error requesting OTP");
                return StatusCode((int)HttpStatusCode.InternalServerError, new CustomerAuthResponseDTO
                {
                    Success = false,
                    ErrorMessage = "An error occurred while processing your request"
                });
            }
        }

        [HttpPost("verify-otp")]
        public async Task<IActionResult> VerifyOtp([FromBody] VerifyOtpDTO model)
        {
            try
            {
                if (string.IsNullOrEmpty(model.Email) && string.IsNullOrEmpty(model.PhoneNumber))
                {
                    return BadRequest(new CustomerAuthResponseDTO
                    {
                        Success = false,
                        ErrorMessage = "Either email or phone number is required"
                    });
                }

                if (string.IsNullOrEmpty(model.Otp))
                {
                    return BadRequest(new CustomerAuthResponseDTO
                    {
                        Success = false,
                        ErrorMessage = "OTP is required"
                    });
                }

                // Validate OTP
                bool isValid = await _otpService.ValidateOtpAsync(model.Email, model.PhoneNumber, model.Otp);
                if (!isValid)
                {
                    return BadRequest(new CustomerAuthResponseDTO
                    {
                        Success = false,
                        ErrorMessage = "Invalid or expired OTP"
                    });
                }

                // Find or create user
                ApplicationUser user;
                bool isNewUser = false;

                if (!string.IsNullOrEmpty(model.Email))
                {
                    user = await _userManager.FindByEmailAsync(model.Email);
                    if (user == null)
                    {
                        // Create new user
                        user = new ApplicationUser
                        {
                            UserName = model.Email,
                            Email = model.Email,
                            PhoneNumber = model.PhoneNumber,
                            EmailConfirmed = true,
                            UserType = UserType.Customer,
                            LastLoginDate = DateTime.UtcNow,
                            LastLoginIp = HttpContext.Connection.RemoteIpAddress?.ToString()
                        };

                        var result = await _userManager.CreateAsync(user);
                        if (!result.Succeeded)
                        {
                            return BadRequest(new CustomerAuthResponseDTO
                            {
                                Success = false,
                                ErrorMessage = string.Join(", ", result.Errors.Select(e => e.Description))
                            });
                        }

                        // Add to Customer role
                        await _userManager.AddToRoleAsync(user, "Customer");
                        isNewUser = true;
                    }
                    else
                    {
                        // Update existing user
                        user.LastLoginDate = DateTime.UtcNow;
                        user.LastLoginIp = HttpContext.Connection.RemoteIpAddress?.ToString();
                        if (!string.IsNullOrEmpty(model.PhoneNumber) && string.IsNullOrEmpty(user.PhoneNumber))
                        {
                            user.PhoneNumber = model.PhoneNumber;
                        }
                        await _userManager.UpdateAsync(user);
                    }
                }
                else if (!string.IsNullOrEmpty(model.PhoneNumber))
                {
                    // Find user by phone number
                    user = await _userManager.Users.FirstOrDefaultAsync(u => u.PhoneNumber == model.PhoneNumber);
                    if (user == null)
                    {
                        // Create new user with phone number
                        string username = $"user_{Guid.NewGuid().ToString().Substring(0, 8)}";
                        user = new ApplicationUser
                        {
                            UserName = username,
                            PhoneNumber = model.PhoneNumber,
                            UserType = UserType.Customer,
                            LastLoginDate = DateTime.UtcNow,
                            LastLoginIp = HttpContext.Connection.RemoteIpAddress?.ToString()
                        };

                        var result = await _userManager.CreateAsync(user);
                        if (!result.Succeeded)
                        {
                            return BadRequest(new CustomerAuthResponseDTO
                            {
                                Success = false,
                                ErrorMessage = string.Join(", ", result.Errors.Select(e => e.Description))
                            });
                        }

                        // Add to Customer role
                        await _userManager.AddToRoleAsync(user, "Customer");
                        isNewUser = true;
                    }
                    else
                    {
                        // Update existing user
                        user.LastLoginDate = DateTime.UtcNow;
                        user.LastLoginIp = HttpContext.Connection.RemoteIpAddress?.ToString();
                        await _userManager.UpdateAsync(user);
                    }
                }
                else
                {
                    return BadRequest(new CustomerAuthResponseDTO
                    {
                        Success = false,
                        ErrorMessage = "Either email or phone number is required"
                    });
                }

                // Generate JWT token
                var (token, expiration) = await _jwtTokenService.GenerateJwtToken(user);
                var roles = await _userManager.GetRolesAsync(user);

                return Ok(new CustomerAuthResponseDTO
                {
                    Success = true,
                    Token = token,
                    UserId = user.Id,
                    Email = user.Email,
                    PhoneNumber = user.PhoneNumber,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    IsNewUser = isNewUser,
                    Expiration = expiration,
                    Roles = roles.ToList(),
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying OTP");
                return StatusCode((int)HttpStatusCode.InternalServerError, new CustomerAuthResponseDTO
                {
                    Success = false,
                    ErrorMessage = "An error occurred while processing your request"
                });
            }
        }
    }
}
