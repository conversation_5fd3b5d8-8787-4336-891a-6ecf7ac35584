using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using MyShop.API.DTOs;

namespace MyShop.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class WebsiteBannersController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<WebsiteBannersController> _logger;

        public WebsiteBannersController(ApplicationDbContext context, ILogger<WebsiteBannersController> logger)
        {
            _context = context;
            _logger = logger;
        }

        // GET: api/WebsiteBanners
        [HttpGet]
        public async Task<ActionResult<IEnumerable<WebsiteBannerDTO>>> GetWebsiteBanners([FromQuery] bool? isActive = null)
        {
            var query = _context.WebsiteBanners.AsQueryable();
            
            if (isActive.HasValue)
            {
                query = query.Where(b => b.IsActive == isActive.Value);
            }
            
            var banners = await query.OrderBy(b => b.DisplayOrder).ToListAsync();
            
            var bannerDtos = banners.Select(b => new WebsiteBannerDTO
            {
                Id = b.Id,
                ImageUrl = b.ImageUrl,
                Title = b.Title,
                Subtitle = b.Subtitle,
                ButtonText = b.ButtonText,
                ButtonLink = b.ButtonLink,
                DisplayOrder = b.DisplayOrder,
                IsActive = b.IsActive
            }).ToList();
            
            return Ok(bannerDtos);
        }

        // GET: api/WebsiteBanners/5
        [HttpGet("{id}")]
        public async Task<ActionResult<WebsiteBannerDTO>> GetWebsiteBanner(int id)
        {
            var banner = await _context.WebsiteBanners.FindAsync(id);
            
            if (banner == null)
            {
                return NotFound();
            }
            
            var bannerDto = new WebsiteBannerDTO
            {
                Id = banner.Id,
                ImageUrl = banner.ImageUrl,
                Title = banner.Title,
                Subtitle = banner.Subtitle,
                ButtonText = banner.ButtonText,
                ButtonLink = banner.ButtonLink,
                DisplayOrder = banner.DisplayOrder,
                IsActive = banner.IsActive
            };
            
            return Ok(bannerDto);
        }

        }
}
