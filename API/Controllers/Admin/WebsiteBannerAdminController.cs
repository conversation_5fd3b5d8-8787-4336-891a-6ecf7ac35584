using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MyShop.API.Attributes;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using MyShop.API.DTOs;
using MyShop.API.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MyShop.API.Controllers.Admin
{
    [Route("api/admin/website-banners")]
    [ApiController]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public class WebsiteBannerAdminController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILogger<WebsiteBannerAdminController> _logger;
        private readonly ICurrentUserService _currentUserService;

        public WebsiteBannerAdminController(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            ILogger<WebsiteBannerAdminController> logger,
            ICurrentUserService currentUserService)
        {
            _context = context;
            _userManager = userManager;
            _logger = logger;
            _currentUserService = currentUserService;
        }

        // GET: api/admin/website-banners/store/{storeId}
        [HttpGet("store/{storeId}")]
        [Permission("WebsiteConfiguration", "View")]
        public async Task<ActionResult<IEnumerable<WebsiteBannerDTO>>> GetBannersByStore(int storeId, [FromQuery] bool? isActive = null)
        {
            try
            {
                if (!await CanAccessStoreAsync(storeId))
                    return Forbid();

                var query = _context.WebsiteBanners
                    .Where(b => b.StoreId == storeId);

                if (isActive.HasValue)
                {
                    query = query.Where(b => b.IsActive == isActive.Value);
                }

                var banners = await query
                    .OrderBy(b => b.DisplayOrder)
                    .ToListAsync();

                return Ok(banners.Select(MapToDTO));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting banners for store {StoreId}", storeId);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // GET: api/admin/website-banners/{id}
        [HttpGet("{id}")]
        [Permission("WebsiteConfiguration", "View")]
        public async Task<ActionResult<WebsiteBannerDTO>> GetBanner(int id)
        {
            try
            {
                var banner = await _context.WebsiteBanners
                    .Include(b => b.Store)
                    .FirstOrDefaultAsync(b => b.Id == id);

                if (banner == null)
                    return NotFound();

                if (!await CanAccessStoreAsync(banner.StoreId))
                    return Forbid();

                return Ok(MapToDTO(banner));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting banner {BannerId}", id);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // POST: api/admin/website-banners/store/{storeId}
        [HttpPost("store/{storeId}")]
        [Permission("WebsiteConfiguration", "Create")]
        public async Task<ActionResult<WebsiteBannerDTO>> CreateBanner(int storeId, CreateWebsiteBannerDTO bannerDto)
        {
            try
            {
                if (!await CanAccessStoreAsync(storeId))
                    return Forbid();

                // Check if store exists
                var store = await _context.Stores.FindAsync(storeId);
                if (store == null)
                    return NotFound("Store not found");

                var banner = new WebsiteBanner
                {
                    StoreId = storeId,
                    ImageUrl = bannerDto.ImageUrl,
                    Title = bannerDto.Title,
                    Subtitle = bannerDto.Subtitle,
                    ButtonText = bannerDto.ButtonText,
                    ButtonLink = bannerDto.ButtonLink,
                    DisplayOrder = bannerDto.DisplayOrder,
                    IsActive = bannerDto.IsActive,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.WebsiteBanners.Add(banner);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetBanner), new { id = banner.Id }, MapToDTO(banner));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating banner for store {StoreId}", storeId);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // PUT: api/admin/website-banners/{id}
        [HttpPut("{id}")]
        [Permission("WebsiteConfiguration", "Edit")]
        public async Task<IActionResult> UpdateBanner(int id, UpdateWebsiteBannerDTO bannerDto)
        {
            try
            {
                var banner = await _context.WebsiteBanners.FindAsync(id);
                if (banner == null)
                    return NotFound();

                if (!await CanAccessStoreAsync(banner.StoreId))
                    return Forbid();

                // Update properties
                banner.ImageUrl = bannerDto.ImageUrl;
                banner.Title = bannerDto.Title;
                banner.Subtitle = bannerDto.Subtitle;
                banner.ButtonText = bannerDto.ButtonText;
                banner.ButtonLink = bannerDto.ButtonLink;
                banner.DisplayOrder = bannerDto.DisplayOrder;
                banner.IsActive = bannerDto.IsActive;
                banner.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating banner {BannerId}", id);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // DELETE: api/admin/website-banners/{id}
        [HttpDelete("{id}")]
        [Permission("WebsiteConfiguration", "Delete")]
        public async Task<IActionResult> DeleteBanner(int id)
        {
            try
            {
                var banner = await _context.WebsiteBanners.FindAsync(id);
                if (banner == null)
                    return NotFound();

                if (!await CanAccessStoreAsync(banner.StoreId))
                    return Forbid();

                _context.WebsiteBanners.Remove(banner);
                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting banner {BannerId}", id);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // PUT: api/admin/website-banners/reorder
        [HttpPut("reorder")]
        [Permission("WebsiteConfiguration", "Edit")]
        public async Task<IActionResult> ReorderBanners([FromBody] Dictionary<int, int> bannerOrders)
        {
            try
            {
                foreach (var kvp in bannerOrders)
                {
                    var bannerId = kvp.Key;
                    var newOrder = kvp.Value;

                    var banner = await _context.WebsiteBanners.FindAsync(bannerId);
                    if (banner == null)
                        continue;

                    if (!await CanAccessStoreAsync(banner.StoreId))
                        return Forbid();

                    banner.DisplayOrder = newOrder;
                    banner.UpdatedAt = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reordering banners");
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // Helper methods
        private async Task<bool> CanAccessStoreAsync(int storeId)
        {
            var currentUser = await _currentUserService.GetCurrentUserAsync();
            if (currentUser == null)
                return false;

            // Super admins can access all stores
            if (await _userManager.IsInRoleAsync(currentUser, "SuperAdmin"))
                return true;

            // Check if user is an admin of the store
            return await _context.StoreAdmins
                .AnyAsync(sa => sa.UserId == currentUser.Id && sa.StoreId == storeId);
        }

        private WebsiteBannerDTO MapToDTO(WebsiteBanner banner)
        {
            return new WebsiteBannerDTO
            {
                Id = banner.Id,
                ImageUrl = banner.ImageUrl,
                Title = banner.Title,
                Subtitle = banner.Subtitle,
                ButtonText = banner.ButtonText,
                ButtonLink = banner.ButtonLink,
                DisplayOrder = banner.DisplayOrder,
                IsActive = banner.IsActive
            };
        }
    }
}
