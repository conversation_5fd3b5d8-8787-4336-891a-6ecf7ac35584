using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using MyShop.API.DTOs;
using OfficeOpenXml;
using System.Net;

namespace MyShop.API.Controllers.Admin
{
    [Route("api/admin/[controller]")]
    [ApiController]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public class InventoryController : AdminController
    {
        public InventoryController(
            UserManager<ApplicationUser> userManager,
            ApplicationDbContext context,
            ILogger<InventoryController> logger)
            : base(userManager, context, logger)
        {
        }

        // GET: api/admin/inventory
        [HttpGet]
        public async Task<ActionResult<IEnumerable<InventoryItemDTO>>> GetInventory(
            [FromQuery] int? storeId = null,
            [FromQuery] bool? lowStock = null,
            [FromQuery] string? search = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                // Get accessible store IDs for the current user
                var accessibleStoreIds = await GetUserStoreIdsAsync();
                if (accessibleStoreIds.Length == 0)
                {
                    return Forbid();
                }

                // If storeId is specified, check if user has access to it
                if (storeId.HasValue && !accessibleStoreIds.Contains(storeId.Value))
                {
                    return Forbid();
                }

                // Start with all inventory items
                var query = _context.InventoryItems
                    .Include(i => i.ProductVariant)
                        .ThenInclude(pv => pv.Product)
                    .Include(i => i.Location)
                    .AsQueryable();

                // Filter by store
                if (storeId.HasValue)
                {
                    query = query.Where(i => i.Location.StoreId == storeId.Value);
                }
                else
                {
                    // Filter by accessible stores
                    query = query.Where(i => accessibleStoreIds.Contains(i.Location.StoreId));
                }

                // Apply filters
                if (lowStock.HasValue && lowStock.Value)
                {
                    query = query.Where(i => i.StockQuantity <= i.ReorderLevel);
                }

                if (!string.IsNullOrEmpty(search))
                {
                    query = query.Where(i => 
                        i.ProductVariant.Product.Name.Contains(search) || 
                        i.ProductVariant.SKU.Contains(search) ||
                        i.Location.Name.Contains(search));
                }

                // Apply pagination
                var totalCount = await query.CountAsync();
                var totalPages = (int)Math.Ceiling(totalCount / (double)pageSize);

                var inventoryItems = await query
                    .OrderBy(i => i.ProductVariant.Product.Name)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var inventoryDtos = inventoryItems.Select(i => new InventoryItemDTO
                {
                    Id = i.Id,
                    LocationId = i.LocationId,
                    LocationName = i.Location.Name,
                    LocationCode = i.Location.Code,
                    StockQuantity = i.StockQuantity,
                    ReorderLevel = i.ReorderLevel,
                    LastRestockedAt = i.LastRestockedAt
                }).ToList();

                Response.Headers.Append("X-Total-Count", totalCount.ToString());
                Response.Headers.Append("X-Total-Pages", totalPages.ToString());

                return Ok(inventoryDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving inventory");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error retrieving inventory");
            }
        }

        // GET: api/admin/inventory/5
        [HttpGet("{id}")]
        public async Task<ActionResult<InventoryItemDTO>> GetInventoryItem(int id)
        {
            try
            {
                var inventoryItem = await _context.InventoryItems
                    .Include(i => i.ProductVariant)
                        .ThenInclude(pv => pv.Product)
                    .Include(i => i.Location)
                    .FirstOrDefaultAsync(i => i.Id == id);

                if (inventoryItem == null)
                {
                    return NotFound();
                }

                // Check if user has access to the store
                if (!await CanAccessStoreAsync(inventoryItem.Location.StoreId))
                {
                    return Forbid();
                }

                var inventoryDto = new InventoryItemDTO
                {
                    Id = inventoryItem.Id,
                    LocationId = inventoryItem.LocationId,
                    LocationName = inventoryItem.Location.Name,
                    LocationCode = inventoryItem.Location.Code,
                    StockQuantity = inventoryItem.StockQuantity,
                    ReorderLevel = inventoryItem.ReorderLevel,
                    LastRestockedAt = inventoryItem.LastRestockedAt
                };

                return Ok(inventoryDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving inventory item");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error retrieving inventory item");
            }
        }

        // PUT: api/admin/inventory/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateInventoryItem(int id, UpdateInventoryItemDTO updateDto)
        {
            try
            {
                var inventoryItem = await _context.InventoryItems
                    .Include(i => i.Location)
                    .FirstOrDefaultAsync(i => i.Id == id);
                    
                if (inventoryItem == null)
                {
                    return NotFound();
                }

                // Check if user has access to the store
                if (!await CanAccessStoreAsync(inventoryItem.Location.StoreId))
                {
                    return Forbid();
                }

                inventoryItem.StockQuantity = updateDto.StockQuantity;
                inventoryItem.ReorderLevel = updateDto.ReorderLevel;
                inventoryItem.LastRestockedAt = DateTime.UtcNow;
                inventoryItem.UpdatedAt = DateTime.UtcNow;

                _context.Entry(inventoryItem).State = EntityState.Modified;
                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating inventory item");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error updating inventory item");
            }
        }

        // GET: api/admin/inventory/export
        [HttpGet("export")]
        public async Task<IActionResult> ExportInventory([FromQuery] int? storeId = null)
        {
            try
            {
                // Get accessible store IDs for the current user
                var accessibleStoreIds = await GetUserStoreIdsAsync();
                if (accessibleStoreIds.Length == 0)
                {
                    return Forbid();
                }

                // If storeId is specified, check if user has access to it
                if (storeId.HasValue && !accessibleStoreIds.Contains(storeId.Value))
                {
                    return Forbid();
                }

                // Start with all inventory items
                var query = _context.InventoryItems
                    .Include(i => i.ProductVariant)
                        .ThenInclude(pv => pv.Product)
                    .Include(i => i.Location)
                    .AsQueryable();

                // Filter by store
                if (storeId.HasValue)
                {
                    query = query.Where(i => i.Location.StoreId == storeId.Value);
                }
                else
                {
                    // Filter by accessible stores
                    query = query.Where(i => accessibleStoreIds.Contains(i.Location.StoreId));
                }

                var inventoryItems = await query
                    .OrderBy(i => i.ProductVariant.Product.Name)
                    .ToListAsync();

                if (inventoryItems.Count == 0)
                {
                    return NotFound("No inventory data found");
                }

                // Set the license context
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("Inventory");

                // Add headers
                worksheet.Cells[1, 1].Value = "Product Name";
                worksheet.Cells[1, 2].Value = "SKU";
                worksheet.Cells[1, 3].Value = "Location";
                worksheet.Cells[1, 4].Value = "Store";
                worksheet.Cells[1, 5].Value = "Stock Quantity";
                worksheet.Cells[1, 6].Value = "Reorder Level";
                worksheet.Cells[1, 7].Value = "Last Restocked";

                // Add data
                for (int i = 0; i < inventoryItems.Count; i++)
                {
                    var item = inventoryItems[i];
                    int row = i + 2;

                    worksheet.Cells[row, 1].Value = item.ProductVariant?.Product?.Name ?? "Unknown Product";
                    worksheet.Cells[row, 2].Value = item.ProductVariant?.SKU ?? "Unknown SKU";
                    worksheet.Cells[row, 3].Value = item.Location?.Name ?? "Unknown Location";
                    worksheet.Cells[row, 4].Value = item.Location?.Store?.Name ?? "Unknown Store";
                    worksheet.Cells[row, 5].Value = item.StockQuantity;
                    worksheet.Cells[row, 6].Value = item.ReorderLevel;
                    worksheet.Cells[row, 7].Value = item.LastRestockedAt.ToString("yyyy-MM-dd HH:mm");
                }

                // Auto-fit columns
                worksheet.Cells.AutoFitColumns();

                // Generate file
                var fileBytes = package.GetAsByteArray();
                var fileName = $"Inventory_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";

                return File(fileBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting inventory");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error exporting inventory");
            }
        }

        // POST: api/admin/inventory/import
        [HttpPost("import")]
        public async Task<IActionResult> ImportInventory(IFormFile file, [FromQuery] int? storeId = null)
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest("No file uploaded");
            }

            try
            {
                // Get accessible store IDs for the current user
                var accessibleStoreIds = await GetUserStoreIdsAsync();
                if (accessibleStoreIds.Length == 0)
                {
                    return Forbid();
                }

                // If storeId is specified, check if user has access to it
                if (storeId.HasValue && !accessibleStoreIds.Contains(storeId.Value))
                {
                    return Forbid();
                }

                // Set the license context
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

                using var stream = new MemoryStream();
                await file.CopyToAsync(stream);
                using var package = new ExcelPackage(stream);
                var worksheet = package.Workbook.Worksheets[0];
                var rowCount = worksheet.Dimension.Rows;

                if (rowCount <= 1)
                {
                    return BadRequest("File contains no data");
                }

                var updatedCount = 0;
                var errors = new List<string>();

                for (int row = 2; row <= rowCount; row++)
                {
                    var sku = worksheet.Cells[row, 2].Value?.ToString();
                    var locationName = worksheet.Cells[row, 3].Value?.ToString();
                    var stockQuantityStr = worksheet.Cells[row, 5].Value?.ToString();
                    var reorderLevelStr = worksheet.Cells[row, 6].Value?.ToString();

                    if (string.IsNullOrEmpty(sku) || string.IsNullOrEmpty(locationName) || string.IsNullOrEmpty(stockQuantityStr))
                    {
                        errors.Add($"Row {row}: Missing required data");
                        continue;
                    }

                    if (!int.TryParse(stockQuantityStr, out int stockQuantity) || stockQuantity < 0)
                    {
                        errors.Add($"Row {row}: Invalid stock quantity");
                        continue;
                    }

                    int reorderLevel = 10; // Default value
                    if (!string.IsNullOrEmpty(reorderLevelStr) && int.TryParse(reorderLevelStr, out int parsedReorderLevel))
                    {
                        reorderLevel = parsedReorderLevel;
                    }

                    // Find the product variant by SKU
                    var variant = await _context.ProductVariants
                        .FirstOrDefaultAsync(v => v.SKU == sku);

                    if (variant == null)
                    {
                        errors.Add($"Row {row}: SKU '{sku}' not found");
                        continue;
                    }

                    // Find the location by name
                    var location = await _context.Locations
                        .FirstOrDefaultAsync(l => l.Name == locationName);

                    if (location == null)
                    {
                        errors.Add($"Row {row}: Location '{locationName}' not found");
                        continue;
                    }

                    // Check if user has access to the store
                    if (!await CanAccessStoreAsync(location.StoreId))
                    {
                        errors.Add($"Row {row}: You don't have access to the store for location '{locationName}'");
                        continue;
                    }

                    // Find the inventory item
                    var inventoryItem = await _context.InventoryItems
                        .FirstOrDefaultAsync(i => i.ProductVariantId == variant.Id && i.LocationId == location.Id);

                    if (inventoryItem == null)
                    {
                        // Create new inventory item
                        inventoryItem = new InventoryItem
                        {
                            ProductVariantId = variant.Id,
                            LocationId = location.Id,
                            StockQuantity = stockQuantity,
                            ReorderLevel = reorderLevel,
                            LastRestockedAt = DateTime.UtcNow,
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow
                        };
                        _context.InventoryItems.Add(inventoryItem);
                    }
                    else
                    {
                        // Update existing inventory item
                        inventoryItem.StockQuantity = stockQuantity;
                        inventoryItem.ReorderLevel = reorderLevel;
                        inventoryItem.LastRestockedAt = DateTime.UtcNow;
                        inventoryItem.UpdatedAt = DateTime.UtcNow;
                        _context.Entry(inventoryItem).State = EntityState.Modified;
                    }

                    updatedCount++;
                }

                await _context.SaveChangesAsync();

                return Ok(new
                {
                    UpdatedCount = updatedCount,
                    Errors = errors.Count > 0 ? errors : null
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error importing inventory");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error importing inventory");
            }
        }
    }
}
