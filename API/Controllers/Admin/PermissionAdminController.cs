using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MyShop.API.Controllers.Admin
{
    [Route("api/admin/permission")]
    [ApiController]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public class PermissionController : ControllerBase
    {
        private readonly RoleManager<IdentityRole> _roleManager;
        private readonly UserManager<ApplicationUser> _userManager;

        public PermissionController(
            RoleManager<IdentityRole> roleManager,
            UserManager<ApplicationUser> userManager)
        {
            _roleManager = roleManager;
            _userManager = userManager;
        }

        // GET: api/admin/permission
        [HttpGet]
        public ActionResult<IEnumerable<object>> GetPermissions()
        {
            // Since we're using ASP.NET Identity, we don't have custom permissions
            // Return an empty list for now
            return new List<object>();
        }

        // GET: api/admin/permission/modules
        [HttpGet("modules")]
        public ActionResult<IEnumerable<string>> GetModules()
        {
            // Since we're using ASP.NET Identity, we don't have custom modules
            // Return a list of standard modules
            return new List<string> { "Users", "Roles", "Products", "Orders", "Stores" };
        }

        // GET: api/admin/permission/user
        [HttpGet("user")]
        public async Task<ActionResult<IEnumerable<string>>> GetUserPermissions()
        {
            // Get the current user
            var userId = User.FindFirst("sub")?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized();
            }

            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                return NotFound();
            }

            // Return the user's roles as permissions
            var roles = await _userManager.GetRolesAsync(user);
            return roles.ToList();
        }

        // GET: api/admin/permission/role/{roleId}
        [HttpGet("role/{roleId}")]
        public async Task<ActionResult<IEnumerable<string>>> GetRolePermissions(string roleId)
        {
            var role = await _roleManager.FindByIdAsync(roleId);
            if (role == null)
            {
                return NotFound();
            }

            // Since we're using ASP.NET Identity, roles don't have permissions
            // Return an empty list for now
            return new List<string>();
        }
    }
}
