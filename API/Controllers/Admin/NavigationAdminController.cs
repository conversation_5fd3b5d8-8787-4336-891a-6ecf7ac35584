using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using MyShop.API.DTOs;
using MyShop.API.Services;
using System.IO;

namespace MyShop.API.Controllers.Admin
{
    [Route("api/admin/navigation")]
    [ApiController]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public class NavigationController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<NavigationController> _logger;
        private readonly ICurrentUserService _currentUserService;

        public NavigationController(
            ApplicationDbContext context,
            ILogger<NavigationController> logger,
            ICurrentUserService currentUserService)
        {
            _context = context;
            _logger = logger;
            _currentUserService = currentUserService;
        }

        // GET: api/admin/navigation
        [HttpGet]
        public async Task<ActionResult<IEnumerable<NavigationMenuDTO>>> GetNavigationMenus(
            [FromQuery] int? storeId = null,
            [FromQuery] bool? isActive = null)
        {
            try
            {
                var currentUser = await _currentUserService.GetCurrentUserAsync();
                if (currentUser == null)
                    return Unauthorized();

                IQueryable<NavigationMenu> query = _context.NavigationMenus
                    .AsQueryable();

                // Filter by store if specified or if user is not super admin
                if (storeId.HasValue)
                {
                    query = query.Where(n => n.StoreId == storeId.Value);
                }
                else if (!currentUser.IsSuperAdmin)
                {
                    // Regular admin can only see navigation menus from stores they have access to
                    var accessibleStoreIds = await _context.StoreAdmins
                        .Where(sa => sa.UserId == currentUser.Id)
                        .Select(sa => sa.StoreId)
                        .ToListAsync();

                    query = query.Where(n => accessibleStoreIds.Contains(n.StoreId));
                }

                // Apply additional filters
                if (isActive.HasValue)
                {
                    query = query.Where(n => n.IsActive == isActive.Value);
                }

                var menus = await query
                    .OrderBy(n => n.DisplayOrder)
                    .ToListAsync();

                var menuDtos = menus.Select(n => new NavigationMenuDTO
                {
                    Id = n.Id,
                    Name = n.Name,
                    Url = n.Url,
                    ParentId = n.ParentId,
                    DisplayOrder = n.DisplayOrder,
                    IsActive = n.IsActive,
                    Icon = n.Icon,
                    StoreId = n.StoreId,
                    Children = new List<NavigationMenuDTO>()
                }).ToList();

                // Build the hierarchy
                var rootMenus = menuDtos.Where(n => n.ParentId == null).ToList();
                var childMenus = menuDtos.Where(n => n.ParentId != null).ToList();

                foreach (var childMenu in childMenus)
                {
                    var parent = menuDtos.FirstOrDefault(n => n.Id == childMenu.ParentId);
                    if (parent != null)
                    {
                        parent.Children.Add(childMenu);
                    }
                }

                return Ok(menuDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting navigation menus");
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // GET: api/admin/navigation/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<NavigationMenuDTO>> GetNavigationMenu(int id)
        {
            try
            {
                var currentUser = await _currentUserService.GetCurrentUserAsync();
                if (currentUser == null)
                    return Unauthorized();

                var menu = await _context.NavigationMenus
                    .Include(n => n.Children)
                    .FirstOrDefaultAsync(n => n.Id == id);

                if (menu == null)
                    return NotFound();

                // Check if user has access to this store
                if (!currentUser.IsSuperAdmin)
                {
                    var hasAccess = await _context.StoreAdmins
                        .AnyAsync(sa => sa.UserId == currentUser.Id && sa.StoreId == menu.StoreId);

                    if (!hasAccess)
                        return Forbid();
                }

                var menuDto = new NavigationMenuDTO
                {
                    Id = menu.Id,
                    Name = menu.Name,
                    Url = menu.Url,
                    ParentId = menu.ParentId,
                    DisplayOrder = menu.DisplayOrder,
                    IsActive = menu.IsActive,
                    Icon = menu.Icon,
                    StoreId = menu.StoreId,
                    Children = menu.Children.Select(c => new NavigationMenuDTO
                    {
                        Id = c.Id,
                        Name = c.Name,
                        Url = c.Url,
                        ParentId = c.ParentId,
                        DisplayOrder = c.DisplayOrder,
                        IsActive = c.IsActive,
                        Icon = c.Icon,
                        StoreId = c.StoreId,
                        Children = new List<NavigationMenuDTO>()
                    }).ToList()
                };

                return Ok(menuDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting navigation menu {NavigationMenuId}", id);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // POST: api/admin/navigation
        [HttpPost]
        public async Task<ActionResult<NavigationMenuDTO>> CreateNavigationMenu(CreateNavigationMenuDTO menuDto)
        {
            try
            {
                if (!ModelState.IsValid)
                    return BadRequest(ModelState);

                var currentUser = await _currentUserService.GetCurrentUserAsync();
                if (currentUser == null)
                    return Unauthorized();

                // Validate store access
                if (!await HasStoreAccessAsync(menuDto.StoreId))
                    return Forbid();

                // Check if parent exists
                if (menuDto.ParentId.HasValue)
                {
                    var parent = await _context.NavigationMenus.FindAsync(menuDto.ParentId.Value);
                    if (parent == null)
                    {
                        return BadRequest("Invalid parent ID");
                    }
                }

                var menu = new NavigationMenu
                {
                    Name = menuDto.Name,
                    Url = menuDto.Url,
                    ParentId = menuDto.ParentId,
                    DisplayOrder = menuDto.DisplayOrder,
                    IsActive = menuDto.IsActive,
                    Icon = menuDto.Icon,
                    StoreId = menuDto.StoreId,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.NavigationMenus.Add(menu);
                await _context.SaveChangesAsync();

                var createdMenuDto = new NavigationMenuDTO
                {
                    Id = menu.Id,
                    Name = menu.Name,
                    Url = menu.Url,
                    ParentId = menu.ParentId,
                    DisplayOrder = menu.DisplayOrder,
                    IsActive = menu.IsActive,
                    Icon = menu.Icon,
                    StoreId = menu.StoreId,
                    Children = new List<NavigationMenuDTO>()
                };

                return CreatedAtAction(nameof(GetNavigationMenu), new { id = menu.Id }, createdMenuDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating navigation menu");
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // PUT: api/admin/navigation/{id}
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateNavigationMenu(int id, UpdateNavigationMenuDTO menuDto)
        {
            try
            {
                if (!ModelState.IsValid)
                    return BadRequest(ModelState);

                var menu = await _context.NavigationMenus.FindAsync(id);
                if (menu == null)
                    return NotFound();

                // Validate store access
                if (!await HasStoreAccessAsync(menu.StoreId))
                    return Forbid();

                // Check if parent exists and is not the menu itself or its child
                if (menuDto.ParentId.HasValue)
                {
                    if (menuDto.ParentId.Value == id)
                    {
                        return BadRequest("A menu cannot be its own parent");
                    }

                    var parent = await _context.NavigationMenus.FindAsync(menuDto.ParentId.Value);
                    if (parent == null)
                    {
                        return BadRequest("Invalid parent ID");
                    }

                    // Check if the new parent is not a child of this menu
                    var isChildOfThisMenu = await IsChildOf(menuDto.ParentId.Value, id);
                    if (isChildOfThisMenu)
                    {
                        return BadRequest("Cannot set a child menu as parent");
                    }
                }

                menu.Name = menuDto.Name;
                menu.Url = menuDto.Url;
                menu.ParentId = menuDto.ParentId;
                menu.DisplayOrder = menuDto.DisplayOrder;
                menu.IsActive = menuDto.IsActive;
                menu.Icon = menuDto.Icon;
                menu.UpdatedAt = DateTime.UtcNow;

                _context.Entry(menu).State = EntityState.Modified;

                try
                {
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!NavigationMenuExists(id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating navigation menu {NavigationMenuId}", id);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // DELETE: api/admin/navigation/{id}
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteNavigationMenu(int id)
        {
            try
            {
                var menu = await _context.NavigationMenus
                    .Include(n => n.Children)
                    .FirstOrDefaultAsync(n => n.Id == id);

                if (menu == null)
                    return NotFound();

                // Validate store access
                if (!await HasStoreAccessAsync(menu.StoreId))
                    return Forbid();

                // Check if menu has children
                if (menu.Children.Any())
                {
                    return BadRequest("Cannot delete menu with children. Please delete or reassign children first.");
                }

                _context.NavigationMenus.Remove(menu);
                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting navigation menu {NavigationMenuId}", id);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        private bool NavigationMenuExists(int id)
        {
            return _context.NavigationMenus.Any(e => e.Id == id);
        }

        private async Task<bool> IsChildOf(int menuId, int potentialParentId)
        {
            var menu = await _context.NavigationMenus.FindAsync(menuId);
            if (menu == null || !menu.ParentId.HasValue)
            {
                return false;
            }

            if (menu.ParentId.Value == potentialParentId)
            {
                return true;
            }

            return await IsChildOf(menu.ParentId.Value, potentialParentId);
        }

        private async Task<bool> HasStoreAccessAsync(int storeId)
        {
            var currentUser = await _currentUserService.GetCurrentUserAsync();
            if (currentUser == null)
                return false;

            if (currentUser.IsSuperAdmin)
                return true;

            return await _context.StoreAdmins
                .AnyAsync(sa => sa.UserId == currentUser.Id && sa.StoreId == storeId);
        }
    }
}
