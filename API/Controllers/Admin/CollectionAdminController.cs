using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using MyShop.API.DTOs;
using MyShop.API.Services;
using System.IO;
using System.Collections.Generic;

namespace MyShop.API.Controllers.Admin
{
    [Route("api/admin/collection")]
    [ApiController]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public class CollectionController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly IWebHostEnvironment _environment;
        private readonly ILogger<CollectionController> _logger;
        private readonly ICurrentUserService _currentUserService;

        public CollectionController(
            ApplicationDbContext context,
            IWebHostEnvironment environment,
            ILogger<CollectionController> logger,
            ICurrentUserService currentUserService)
        {
            _context = context;
            _environment = environment;
            _logger = logger;
            _currentUserService = currentUserService;
        }

        // GET: api/admin/collection
        [HttpGet]
        public async Task<ActionResult<IEnumerable<CollectionDTO>>> GetCollections(
            [FromQuery] int? storeId = null,
            [FromQuery] bool? isActive = null)
        {
            try
            {
                var currentUser = await _currentUserService.GetCurrentUserAsync();
                if (currentUser == null)
                    return Unauthorized();

                IQueryable<Collection> query = _context.Collections
                    .Include(c => c.Products)
                    .AsQueryable();

                // Filter by store if specified or if user is not super admin
                if (storeId.HasValue)
                {
                    query = query.Where(c => c.StoreId == storeId.Value);
                }
                else if (!currentUser.IsSuperAdmin)
                {
                    // Regular admin can only see collections from stores they have access to
                    var accessibleStoreIds = await _context.StoreAdmins
                        .Where(sa => sa.UserId == currentUser.Id)
                        .Select(sa => sa.StoreId)
                        .ToListAsync();

                    query = query.Where(c => accessibleStoreIds.Contains(c.StoreId));
                }

                // Apply additional filters
                if (isActive.HasValue)
                {
                    query = query.Where(c => c.IsActive == isActive.Value);
                }

                var collections = await query
                    .OrderBy(c => c.StoreId)
                    .ThenBy(c => c.DisplayOrder)
                    .ToListAsync();

                var collectionDtos = collections.Select(c => new CollectionDTO
                {
                    Id = c.Id,
                    Name = c.Name,
                    Description = c.Description,
                    ImageUrl = c.ImageUrl,
                    IsActive = c.IsActive,
                    DisplayOrder = c.DisplayOrder,
                    ProductCount = c.Products.Count,
                    StoreId = c.StoreId
                }).ToList();

                return Ok(collectionDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting collections");
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // GET: api/admin/collection/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<CollectionDTO>> GetCollection(int id)
        {
            try
            {
                var currentUser = await _currentUserService.GetCurrentUserAsync();
                if (currentUser == null)
                    return Unauthorized();

                var collection = await _context.Collections
                    .Include(c => c.Products)
                    .FirstOrDefaultAsync(c => c.Id == id);

                if (collection == null)
                    return NotFound();

                // Check if user has access to this store
                if (!currentUser.IsSuperAdmin)
                {
                    var hasAccess = await _context.StoreAdmins
                        .AnyAsync(sa => sa.UserId == currentUser.Id && sa.StoreId == collection.StoreId);

                    if (!hasAccess)
                        return Forbid();
                }

                var collectionDto = new CollectionDTO
                {
                    Id = collection.Id,
                    Name = collection.Name,
                    Description = collection.Description,
                    ImageUrl = collection.ImageUrl,
                    IsActive = collection.IsActive,
                    DisplayOrder = collection.DisplayOrder,
                    ProductCount = collection.Products.Count,
                    StoreId = collection.StoreId
                };

                return Ok(collectionDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting collection {CollectionId}", id);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // POST: api/admin/collection
        [HttpPost]
        public async Task<ActionResult<CollectionDTO>> CreateCollection(CreateCollectionDTO collectionDto)
        {
            try
            {
                if (!ModelState.IsValid)
                    return BadRequest(ModelState);

                var currentUser = await _currentUserService.GetCurrentUserAsync();
                if (currentUser == null)
                    return Unauthorized();

                // Validate store access
                if (!await HasStoreAccessAsync(collectionDto.StoreId))
                    return Forbid();

                var collection = new Collection
                {
                    Name = collectionDto.Name,
                    Description = collectionDto.Description,
                    ImageUrl = collectionDto.ImageUrl ?? string.Empty,
                    IsActive = collectionDto.IsActive,
                    DisplayOrder = collectionDto.DisplayOrder,
                    StoreId = collectionDto.StoreId,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.Collections.Add(collection);
                await _context.SaveChangesAsync();

                var createdCollectionDto = new CollectionDTO
                {
                    Id = collection.Id,
                    Name = collection.Name,
                    Description = collection.Description,
                    ImageUrl = collection.ImageUrl,
                    IsActive = collection.IsActive,
                    DisplayOrder = collection.DisplayOrder,
                    ProductCount = 0,
                    StoreId = collection.StoreId
                };

                return CreatedAtAction(nameof(GetCollection), new { id = collection.Id }, createdCollectionDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating collection");
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // PUT: api/admin/collection/{id}
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateCollection(int id, UpdateCollectionDTO collectionDto)
        {
            try
            {
                if (!ModelState.IsValid)
                    return BadRequest(ModelState);

                var collection = await _context.Collections.FindAsync(id);
                if (collection == null)
                    return NotFound();

                // Validate store access
                if (!await HasStoreAccessAsync(collection.StoreId))
                    return Forbid();

                collection.Name = collectionDto.Name;
                collection.Description = collectionDto.Description;
                if (!string.IsNullOrEmpty(collectionDto.ImageUrl))
                {
                    collection.ImageUrl = collectionDto.ImageUrl;
                }
                collection.IsActive = collectionDto.IsActive;
                collection.DisplayOrder = collectionDto.DisplayOrder;
                collection.UpdatedAt = DateTime.UtcNow;

                _context.Entry(collection).State = EntityState.Modified;

                try
                {
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!CollectionExists(id))
                        return NotFound();
                    else
                        throw;
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating collection {CollectionId}", id);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // DELETE: api/admin/collection/{id}
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteCollection(int id)
        {
            try
            {
                var collection = await _context.Collections
                    .Include(c => c.Products)
                    .FirstOrDefaultAsync(c => c.Id == id);

                if (collection == null)
                    return NotFound();

                // Validate store access
                if (!await HasStoreAccessAsync(collection.StoreId))
                    return Forbid();

                // Remove the collection from all products
                foreach (var product in collection.Products.ToList())
                {
                    collection.Products.Remove(product);
                }

                // Delete the image file if it exists
                if (!string.IsNullOrEmpty(collection.ImageUrl))
                {
                    try
                    {
                        // Extract filename from URL
                        var uri = new Uri(collection.ImageUrl);
                        var filename = Path.GetFileName(uri.LocalPath);
                        var imagePath = Path.Combine(_environment.WebRootPath, "uploads", "images", filename);

                        if (System.IO.File.Exists(imagePath))
                        {
                            System.IO.File.Delete(imagePath);
                            _logger.LogInformation("Deleted image file for collection {CollectionId}: {ImagePath}", id, imagePath);
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log the error but continue with collection deletion
                        _logger.LogError(ex, "Error deleting image file for collection {CollectionId}", id);
                    }
                }

                _context.Collections.Remove(collection);
                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting collection {CollectionId}", id);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // POST: api/admin/collection/bulk-delete
        [HttpPost("bulk-delete")]
        public async Task<IActionResult> BulkDeleteCollections([FromBody] BulkCollectionIdsDTO dto)
        {
            try
            {
                if (dto.CollectionIds == null || dto.CollectionIds.Count == 0)
                    return BadRequest("No collection IDs provided");

                var currentUser = await _currentUserService.GetCurrentUserAsync();
                if (currentUser == null)
                    return Unauthorized();

                // Get collections to delete
                var collectionsToDelete = await _context.Collections
                    .Where(c => dto.CollectionIds.Contains(c.Id))
                    .Include(c => c.Products)
                    .ToListAsync();

                if (collectionsToDelete.Count == 0)
                    return NotFound("None of the specified collections were found");

                // Check store access for each collection
                foreach (var collection in collectionsToDelete)
                {
                    if (!await HasStoreAccessAsync(collection.StoreId))
                        return Forbid();
                }

                // Remove collections from products
                foreach (var collection in collectionsToDelete)
                {
                    foreach (var product in collection.Products.ToList())
                    {
                        collection.Products.Remove(product);
                    }
                }

                // Delete image files
                foreach (var collection in collectionsToDelete)
                {
                    if (!string.IsNullOrEmpty(collection.ImageUrl))
                    {
                        try
                        {
                            // Extract filename from URL
                            var uri = new Uri(collection.ImageUrl);
                            var filename = Path.GetFileName(uri.LocalPath);
                            var imagePath = Path.Combine(_environment.WebRootPath, "uploads", "images", filename);

                            if (System.IO.File.Exists(imagePath))
                            {
                                System.IO.File.Delete(imagePath);
                                _logger.LogInformation("Deleted image file for collection {CollectionId}: {ImagePath}", collection.Id, imagePath);
                            }
                        }
                        catch (Exception ex)
                        {
                            // Log the error but continue with collection deletion
                            _logger.LogError(ex, "Error deleting image file for collection {CollectionId}", collection.Id);
                        }
                    }
                }

                // Delete collections
                _context.Collections.RemoveRange(collectionsToDelete);
                await _context.SaveChangesAsync();

                return Ok(new { message = $"Successfully deleted {collectionsToDelete.Count} collections" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error bulk deleting collections");
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // PUT: api/admin/collection/bulk-status
        [HttpPut("bulk-status")]
        public async Task<IActionResult> BulkUpdateCollectionStatus([FromBody] BulkUpdateCollectionStatusDTO dto)
        {
            try
            {
                if (dto.CollectionIds == null || dto.CollectionIds.Count == 0)
                    return BadRequest("No collection IDs provided");

                var currentUser = await _currentUserService.GetCurrentUserAsync();
                if (currentUser == null)
                    return Unauthorized();

                // Get collections to update
                var collectionsToUpdate = await _context.Collections
                    .Where(c => dto.CollectionIds.Contains(c.Id))
                    .ToListAsync();

                if (collectionsToUpdate.Count == 0)
                    return NotFound("None of the specified collections were found");

                // Check store access for each collection
                foreach (var collection in collectionsToUpdate)
                {
                    if (!await HasStoreAccessAsync(collection.StoreId))
                        return Forbid();
                }

                // Update status
                foreach (var collection in collectionsToUpdate)
                {
                    collection.IsActive = dto.IsActive;
                    collection.UpdatedAt = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();

                return Ok(new { message = $"Successfully updated status for {collectionsToUpdate.Count} collections" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error bulk updating collection status");
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        private bool CollectionExists(int id)
        {
            return _context.Collections.Any(e => e.Id == id);
        }

        private async Task<bool> HasStoreAccessAsync(int storeId)
        {
            var currentUser = await _currentUserService.GetCurrentUserAsync();
            if (currentUser == null)
                return false;

            if (currentUser.IsSuperAdmin)
                return true;

            return await _context.StoreAdmins
                .AnyAsync(sa => sa.UserId == currentUser.Id && sa.StoreId == storeId);
        }
    }
}
