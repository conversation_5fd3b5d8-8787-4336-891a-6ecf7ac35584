using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using System.Net;

namespace MyShop.API.Controllers.Admin
{
    [Route("api/admin/[controller]")]
    [ApiController]
    public class FileUploadAdminController : AdminController
    {
        private readonly IWebHostEnvironment _environment;
        // Use 'new' keyword to explicitly hide the base class _logger
        private readonly new ILogger<FileUploadAdminController> _logger;

        public FileUploadAdminController(
            UserManager<ApplicationUser> userManager,
            ApplicationDbContext context,
            IWebHostEnvironment environment,
            ILogger<FileUploadAdminController> logger)
            : base(userManager, context, logger)
        {
            _environment = environment;
            _logger = logger;
        }

        [HttpPost("images")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> UploadImage(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest("No file uploaded");
                }

                // Validate file type
                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".webp" };
                var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
                if (!allowedExtensions.Contains(fileExtension))
                {
                    return BadRequest("Invalid file type. Only image files are allowed.");
                }

                // Create uploads directory if it doesn't exist
                var uploadsFolder = Path.Combine(_environment.WebRootPath, "uploads", "images");
                if (!Directory.Exists(uploadsFolder))
                {
                    Directory.CreateDirectory(uploadsFolder);
                }

                // Generate a unique filename
                var uniqueFileName = $"{Guid.NewGuid()}{fileExtension}";
                var filePath = Path.Combine(uploadsFolder, uniqueFileName);

                // Save the file
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                // Return the URL to the uploaded file
                var baseUrl = $"{Request.Scheme}://{Request.Host}";
                var fileUrl = $"{baseUrl}/uploads/images/{uniqueFileName}";

                return Ok(new { url = fileUrl });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading image");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error uploading image");
            }
        }

        [HttpPost("bulk-images")]
        // [Authorize(Roles = "Admin")]
        public async Task<IActionResult> UploadMultipleImages()
        {
            try
            {
                IFormFileCollection files;
                try
                {
                    files = Request.Form.Files;
                    _logger.LogInformation("UploadMultipleImages called with {FileCount} files", files.Count);

                    if (files == null || files.Count == 0)
                    {
                        _logger.LogWarning("No files uploaded");
                        return BadRequest("No files uploaded");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error accessing Request.Form.Files: {ErrorMessage}", ex.Message);
                    return StatusCode((int)HttpStatusCode.InternalServerError, $"Error accessing files: {ex.Message}");
                }

                var uploadedUrls = new List<string>();
                var allowedExtensions = new List<string> { ".jpg", ".jpeg", ".png", ".gif", ".webp" };
                var uploadsFolder = Path.Combine(_environment.WebRootPath, "uploads", "images");

                if (!Directory.Exists(uploadsFolder))
                {
                    Directory.CreateDirectory(uploadsFolder);
                }

                foreach (var file in files)
                {
                    if (file.Length == 0)
                    {
                        continue;
                    }

                    var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
                    if (!allowedExtensions.Contains(fileExtension))
                    {
                        continue;
                    }

                    var uniqueFileName = $"{Guid.NewGuid()}{fileExtension}";
                    var filePath = Path.Combine(uploadsFolder, uniqueFileName);

                    using (var stream = new FileStream(filePath, FileMode.Create))
                    {
                        await file.CopyToAsync(stream);
                    }

                    var baseUrl = $"{Request.Scheme}://{Request.Host}";
                    var fileUrl = $"{baseUrl}/uploads/images/{uniqueFileName}";
                    uploadedUrls.Add(fileUrl);
                }

                return Ok(new { urls = uploadedUrls });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading multiple images: {ErrorMessage}", ex.Message);
                if (ex.InnerException != null)
                {
                    _logger.LogError("Inner exception: {InnerErrorMessage}", ex.InnerException.Message);
                }
                _logger.LogError("Stack trace: {StackTrace}", ex.StackTrace);
                return StatusCode((int)HttpStatusCode.InternalServerError, $"Error uploading images: {ex.Message}");
            }
        }

        [HttpGet("images")]
        public IActionResult GetImages([FromQuery] int page = 1, [FromQuery] int pageSize = 20)
        {
            try
            {
                // Validate parameters
                if (page < 1) page = 1;
                if (pageSize < 1 || pageSize > 100) pageSize = 20;

                var uploadsFolder = Path.Combine(_environment.WebRootPath, "uploads", "images");

                if (!Directory.Exists(uploadsFolder))
                {
                    return Ok(new {
                        images = new List<object>(),
                        totalCount = 0,
                        currentPage = page,
                        pageSize = pageSize,
                        totalPages = 0
                    });
                }

                // Get all image files
                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".webp" };
                var imageFiles = Directory.GetFiles(uploadsFolder)
                    .Where(file => allowedExtensions.Contains(Path.GetExtension(file).ToLowerInvariant()))
                    .OrderByDescending(f => new FileInfo(f).CreationTime)
                    .ToList();

                // Calculate pagination
                var totalCount = imageFiles.Count;
                var totalPages = (int)Math.Ceiling(totalCount / (double)pageSize);
                var paginatedFiles = imageFiles
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                // Create image objects with metadata
                var baseUrl = $"{Request.Scheme}://{Request.Host}";
                var images = paginatedFiles.Select(file =>
                {
                    var fileInfo = new FileInfo(file);
                    var fileName = Path.GetFileName(file);
                    return new
                    {
                        url = $"{baseUrl}/uploads/images/{fileName}",
                        name = fileName,
                        uploadedAt = fileInfo.CreationTime,
                        size = fileInfo.Length,
                        extension = Path.GetExtension(file).ToLowerInvariant()
                    };
                }).ToList();

                return Ok(new
                {
                    images,
                    totalCount,
                    currentPage = page,
                    pageSize,
                    totalPages
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting images");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error getting images");
            }
        }

        [HttpDelete("images/{fileName}")]
        [Authorize(Roles = "Admin")]
        public IActionResult DeleteImage(string fileName)
        {
            try
            {
                if (string.IsNullOrEmpty(fileName))
                {
                    return BadRequest("Filename is required");
                }

                // Prevent directory traversal attacks
                fileName = Path.GetFileName(fileName);
                var filePath = Path.Combine(_environment.WebRootPath, "uploads", "images", fileName);

                if (!System.IO.File.Exists(filePath))
                {
                    return NotFound("File not found");
                }

                System.IO.File.Delete(filePath);
                return Ok(new { message = "File deleted successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting image");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error deleting image");
            }
        }
    }
}
