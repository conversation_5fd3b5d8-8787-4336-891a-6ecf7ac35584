using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using MyShop.API.DTOs;

namespace MyShop.API.Controllers.Admin
{
    [Route("api/admin/[controller]")]
    [ApiController]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public class StoreController : AdminController
    {
        public StoreController(
            UserManager<ApplicationUser> userManager,
            ApplicationDbContext context,
            ILogger<StoreController> logger)
            : base(userManager, context, logger)
        {
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<StoreDTO>>> GetStores()
        {
            try
            {
                var storeIds = await GetUserStoreIdsAsync();

                var stores = await _context.Stores
                    .Include(s => s.AdditionalDomains)
                    .Where(s => storeIds.Contains(s.Id))
                    .ToListAsync();

                return Ok(stores.Select(s => MapToDTO(s)));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting stores");
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetStore(int id)
        {
            try
            {
                var accessCheck = await CheckStoreAccessAsync(id);
                if (accessCheck != null)
                    return accessCheck;

                var store = await _context.Stores
                    .Include(s => s.AdditionalDomains)
                    .Include(s => s.ThemeConfiguration)
                    .FirstOrDefaultAsync(s => s.Id == id);

                if (store == null)
                    return NotFound();

                return Ok(MapToDTO(store));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting store {StoreId}", id);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        [HttpPost]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<ActionResult<StoreDTO>> CreateStore(CreateStoreDTO dto)
        {
            try
            {
                // Generate a random store key (10 characters)
                string storeKey = GenerateStoreKey();

                var store = new Store
                {
                    Name = dto.Name,
                    Description = dto.Description,
                    StoreKey = storeKey,
                    PrimaryDomain = dto.PrimaryDomain,
                    IsActive = dto.IsActive,
                    LogoUrl = dto.LogoUrl,
                    FaviconUrl = dto.FaviconUrl,
                    Email = dto.Email,
                    Phone = dto.Phone,
                    Address = dto.Address,
                    FacebookUrl = dto.FacebookUrl,
                    InstagramUrl = dto.InstagramUrl,
                    TwitterUrl = dto.TwitterUrl,
                    WhatsappNumber = dto.WhatsappNumber,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.Stores.Add(store);
                await _context.SaveChangesAsync();

                // Create default theme configuration
                var themeConfig = new ThemeConfiguration
                {
                    StoreId = store.Id,
                    Name = "Default",
                    Description = "Default theme configuration",
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.ThemeConfigurations.Add(themeConfig);

                // Create additional domains if provided
                if (dto.AdditionalDomains != null && dto.AdditionalDomains.Any())
                {
                    foreach (var domain in dto.AdditionalDomains)
                    {
                        _context.StoreDomains.Add(new StoreDomain
                        {
                            Domain = domain,
                            IsActive = true,
                            StoreId = store.Id
                        });
                    }
                }

                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetStore), new { id = store.Id }, MapToDTO(store));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating store");
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateStore(int id, UpdateStoreDTO dto)
        {
            try
            {
                var accessCheck = await CheckStoreAccessAsync(id);
                if (accessCheck != null)
                    return accessCheck;

                var store = await _context.Stores.FindAsync(id);

                if (store == null)
                    return NotFound();

                // Only super admin can change certain properties
                if (await IsSuperAdminAsync())
                {
                    store.Name = dto.Name;
                    store.PrimaryDomain = dto.PrimaryDomain;
                    store.IsActive = dto.IsActive;
                }

                // Properties that store admin can change
                store.Description = dto.Description;
                store.LogoUrl = dto.LogoUrl;
                store.FaviconUrl = dto.FaviconUrl;
                store.Email = dto.Email;
                store.Phone = dto.Phone;
                store.Address = dto.Address;
                store.FacebookUrl = dto.FacebookUrl;
                store.InstagramUrl = dto.InstagramUrl;
                store.TwitterUrl = dto.TwitterUrl;
                store.WhatsappNumber = dto.WhatsappNumber;
                store.UpdatedAt = DateTime.UtcNow;

                // Update additional domains if super admin
                if (await IsSuperAdminAsync() && dto.AdditionalDomains != null)
                {
                    // Get existing domains
                    var existingDomains = await _context.StoreDomains
                        .Where(d => d.StoreId == id)
                        .ToListAsync();

                    // Remove domains that are not in the new list
                    foreach (var domain in existingDomains)
                    {
                        if (!dto.AdditionalDomains.Contains(domain.Domain))
                        {
                            _context.StoreDomains.Remove(domain);
                        }
                    }

                    // Add new domains
                    foreach (var domain in dto.AdditionalDomains)
                    {
                        if (!existingDomains.Any(d => d.Domain == domain))
                        {
                            _context.StoreDomains.Add(new StoreDomain
                            {
                                Domain = domain,
                                IsActive = true,
                                StoreId = id
                            });
                        }
                    }
                }

                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating store {StoreId}", id);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> DeleteStore(int id)
        {
            try
            {
                var store = await _context.Stores.FindAsync(id);

                if (store == null)
                    return NotFound();

                // Check if this is the last active store
                var activeStoreCount = await _context.Stores.CountAsync(s => s.IsActive);

                if (activeStoreCount <= 1 && store.IsActive)
                    return BadRequest("Cannot delete the last active store");

                _context.Stores.Remove(store);
                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting store {StoreId}", id);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        private string GenerateStoreKey()
        {
            // Generate a random 10-character alphanumeric key
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            var random = new Random();
            var key = new string(Enumerable.Repeat(chars, 10)
                .Select(s => s[random.Next(s.Length)]).ToArray());

            // Check if the key already exists
            while (_context.Stores.Any(s => s.StoreKey == key))
            {
                // Generate a new key if it already exists
                key = new string(Enumerable.Repeat(chars, 10)
                    .Select(s => s[random.Next(s.Length)]).ToArray());
            }

            return key;
        }

        private StoreDTO MapToDTO(Store store)
        {
            return new StoreDTO
            {
                Id = store.Id,
                Name = store.Name,
                Description = store.Description,
                StoreKey = store.StoreKey,
                PrimaryDomain = store.PrimaryDomain,
                IsActive = store.IsActive,
                LogoUrl = store.LogoUrl,
                FaviconUrl = store.FaviconUrl,
                Email = store.Email,
                Phone = store.Phone,
                Address = store.Address,
                FacebookUrl = store.FacebookUrl,
                InstagramUrl = store.InstagramUrl,
                TwitterUrl = store.TwitterUrl,
                WhatsappNumber = store.WhatsappNumber,
                AdditionalDomains = store.AdditionalDomains?.Select(d => d.Domain).ToList() ?? new List<string>(),
                CreatedAt = store.CreatedAt,
                UpdatedAt = store.UpdatedAt,
                ThemeConfiguration = store.ThemeConfiguration != null ?
                    new ThemeConfigurationDTO
                    {
                        Id = store.ThemeConfiguration.Id,
                        StoreId = store.ThemeConfiguration.StoreId,
                        Name = store.ThemeConfiguration.Name,
                        Description = store.ThemeConfiguration.Description,
                        IsActive = store.ThemeConfiguration.IsActive,
                        PrimaryColor = store.ThemeConfiguration.PrimaryColor,
                        SecondaryColor = store.ThemeConfiguration.SecondaryColor,
                        AccentColor = store.ThemeConfiguration.AccentColor,
                        TextPrimaryColor = store.ThemeConfiguration.TextPrimaryColor,
                        TextSecondaryColor = store.ThemeConfiguration.TextSecondaryColor,
                        TextLightColor = store.ThemeConfiguration.TextLightColor,
                        BackgroundPrimaryColor = store.ThemeConfiguration.BackgroundPrimaryColor,
                        BackgroundSecondaryColor = store.ThemeConfiguration.BackgroundSecondaryColor,
                        BackgroundAccentColor = store.ThemeConfiguration.BackgroundAccentColor,
                        ButtonPrimaryColor = store.ThemeConfiguration.ButtonPrimaryColor,
                        ButtonSecondaryColor = store.ThemeConfiguration.ButtonSecondaryColor,
                        ButtonTextColor = store.ThemeConfiguration.ButtonTextColor,
                        ButtonBorderRadius = store.ThemeConfiguration.ButtonBorderRadius,
                        CardBackgroundColor = store.ThemeConfiguration.CardBackgroundColor,
                        CardBorderColor = store.ThemeConfiguration.CardBorderColor,
                        CardBorderRadius = store.ThemeConfiguration.CardBorderRadius,
                        CardShadow = store.ThemeConfiguration.CardShadow,
                        HeadingFontFamily = store.ThemeConfiguration.HeadingFontFamily,
                        BodyFontFamily = store.ThemeConfiguration.BodyFontFamily,
                        FontBaseSize = store.ThemeConfiguration.FontBaseSize,
                        SpacingUnit = store.ThemeConfiguration.SpacingUnit,
                        ContainerMaxWidth = store.ThemeConfiguration.ContainerMaxWidth,
                        ContainerPadding = store.ThemeConfiguration.ContainerPadding,
                        HeaderBackgroundColor = store.ThemeConfiguration.HeaderBackgroundColor,
                        HeaderTextColor = store.ThemeConfiguration.HeaderTextColor,
                        HeaderHeight = store.ThemeConfiguration.HeaderHeight,
                        FooterBackgroundColor = store.ThemeConfiguration.FooterBackgroundColor,
                        FooterTextColor = store.ThemeConfiguration.FooterTextColor,
                        NavLinkColor = store.ThemeConfiguration.NavLinkColor,
                        NavLinkActiveColor = store.ThemeConfiguration.NavLinkActiveColor,
                        NavLinkHoverColor = store.ThemeConfiguration.NavLinkHoverColor,
                        InputBackgroundColor = store.ThemeConfiguration.InputBackgroundColor,
                        InputBorderColor = store.ThemeConfiguration.InputBorderColor,
                        InputBorderRadius = store.ThemeConfiguration.InputBorderRadius,
                        InputFocusBorderColor = store.ThemeConfiguration.InputFocusBorderColor,
                        CustomCSS = store.ThemeConfiguration.CustomCSS
                    } : null
            };
        }
    }
}
