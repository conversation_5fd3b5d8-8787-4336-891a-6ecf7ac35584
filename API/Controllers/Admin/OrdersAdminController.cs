using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using MyShop.API.DTOs;
using System.Net;

namespace MyShop.API.Controllers.Admin
{
    [Route("api/admin/orders")]
    [ApiController]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public class OrdersAdminController : AdminController
    {
        public OrdersAdminController(
            UserManager<ApplicationUser> userManager,
            ApplicationDbContext context,
            ILogger<OrdersAdminController> logger)
            : base(userManager, context, logger)
        {
        }

        // GET: api/admin/orders
        [HttpGet]
        public async Task<ActionResult<IEnumerable<OrderDTO>>> GetOrders(
            [FromQuery] int? storeId = null,
            [FromQuery] string? status = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] string? search = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                // Get accessible store IDs for the current user
                var accessibleStoreIds = await GetUserStoreIdsAsync();
                if (accessibleStoreIds.Length == 0)
                {
                    return Forbid();
                }

                // If storeId is specified, check if user has access to it
                if (storeId.HasValue && !accessibleStoreIds.Contains(storeId.Value))
                {
                    return Forbid();
                }

                var query = _context.Orders
                    .Include(o => o.OrderItems)
                        .ThenInclude(oi => oi.Product)
                    .Include(o => o.OrderItems)
                    .AsQueryable();

                // Filter by store if specified
                if (storeId.HasValue)
                {
                    query = query.Where(o => o.StoreId == storeId.Value);
                }
                else
                {
                    // Filter by accessible stores
                    query = query.Where(o => accessibleStoreIds.Contains(o.StoreId));
                }

                // Apply filters
                if (!string.IsNullOrEmpty(status) && Enum.TryParse<OrderStatus>(status, true, out var orderStatus))
                {
                    query = query.Where(o => o.Status == orderStatus);
                }

                if (fromDate.HasValue)
                {
                    query = query.Where(o => o.CreatedAt >= fromDate.Value);
                }

                if (toDate.HasValue)
                {
                    // Add one day to include the end date
                    var endDate = toDate.Value.AddDays(1);
                    query = query.Where(o => o.CreatedAt < endDate);
                }

                if (!string.IsNullOrEmpty(search))
                {
                    query = query.Where(o =>
                        o.OrderNumber.Contains(search) ||
                        o.CustomerName.Contains(search) ||
                        o.CustomerEmail.Contains(search) ||
                        o.CustomerPhone.Contains(search));
                }

                // Get total count for pagination
                var totalCount = await query.CountAsync();

                // Apply pagination
                var orders = await query
                    .OrderByDescending(o => o.CreatedAt)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                // Map to DTOs
                var orderDtos = orders.Select(o => new OrderDTO
                {
                    Id = o.Id,
                    OrderNumber = o.OrderNumber,
                    CustomerName = o.CustomerName,
                    CustomerEmail = o.CustomerEmail,
                    CustomerPhone = o.CustomerPhone,
                    Status = o.Status.ToString(),
                    TotalAmount = o.TotalAmount,
                    ShippingAmount = o.ShippingCost,
                    TaxAmount = o.TaxAmount,
                    DiscountAmount = o.DiscountAmount,
                    CouponCode = o.CouponCode,
                    PaymentMethod = o.PaymentMethod,
                    //PaymentStatus = ,
                    ShippingAddress = o.ShippingAddress??"",
                    BillingAddress = o.BillingAddress??"",
                    Notes = o.Notes??"",
                    CreatedAt = o.CreatedAt,
                    UpdatedAt = o.UpdatedAt,
                    StoreId = o.StoreId,
                    OrderItems = o.OrderItems.Select(oi => new OrderItemDTO
                    {
                        Id = oi.Id,
                        ProductId = oi.ProductId,
                        ProductName = oi.Product?.Name ?? "Unknown Product",
                        Quantity = oi.Quantity,
                        UnitPrice = oi.UnitPrice,
                        TotalPrice = oi.TotalPrice
                    }).ToList()
                }).ToList();

                // Add pagination headers
                Response.Headers.Add("X-Total-Count", totalCount.ToString());
                Response.Headers.Add("X-Total-Pages", Math.Ceiling((double)totalCount / pageSize).ToString());

                return Ok(orderDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving orders");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error retrieving orders");
            }
        }

        // GET: api/admin/orders/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<OrderDTO>> GetOrder(int id)
        {
            try
            {
                var order = await _context.Orders
                    .Include(o => o.OrderItems)
                        .ThenInclude(oi => oi.Product)
                    .Include(o => o.OrderItems)
                    .FirstOrDefaultAsync(o => o.Id == id);

                if (order == null)
                {
                    return NotFound();
                }

                // Check if user has access to the store
                if (!await CanAccessStoreAsync(order.StoreId))
                {
                    return Forbid();
                }

                var orderDto = new OrderDTO
                {
                    Id = order.Id,
                    OrderNumber = order.OrderNumber,
                    CustomerName = order.CustomerName,
                    CustomerEmail = order.CustomerEmail,
                    CustomerPhone = order.CustomerPhone,
                    Status = order.Status.ToString(),
                    TotalAmount = order.TotalAmount,
                    ShippingAmount = order.ShippingCost,
                    TaxAmount = order.TaxAmount,
                    DiscountAmount = order.DiscountAmount,
                    CouponCode = order.CouponCode,
                    PaymentMethod = order.PaymentMethod,
                    //PaymentStatus = "",
                    ShippingAddress = order.ShippingAddress,
                    BillingAddress = order.BillingAddress,
                    Notes = order.Notes,
                    CreatedAt = order.CreatedAt,
                    UpdatedAt = order.UpdatedAt,
                    StoreId = order.StoreId,
                    OrderItems = order.OrderItems.Select(oi => new OrderItemDTO
                    {
                        Id = oi.Id,
                        ProductId = oi.ProductId,
                        ProductName = oi.Product?.Name ?? "Unknown Product",
                        Quantity = oi.Quantity,
                        UnitPrice = oi.UnitPrice,
                        TotalPrice = oi.TotalPrice
                    }).ToList()
                };

                return Ok(orderDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving order");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error retrieving order");
            }
        }

        // PUT: api/admin/orders/{id}/status
        [HttpPut("{id}/status")]
        public async Task<IActionResult> UpdateOrderStatus(int id, [FromBody] UpdateOrderStatusDTO updateDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var order = await _context.Orders.FindAsync(id);
                if (order == null)
                {
                    return NotFound();
                }

                // Check if user has access to the store
                if (!await CanAccessStoreAsync(order.StoreId))
                {
                    return Forbid();
                }

               
                    order.Status = OrderStatus.Delivered;
                    order.UpdatedAt = DateTime.UtcNow;

                    // // Add notes if provided
                    // if (!string.IsNullOrEmpty(updateDto.Notes))
                    // {
                    //     order.Notes = "";
                    // }

                    _context.Entry(order).State = EntityState.Modified;
                    await _context.SaveChangesAsync();

                    return NoContent();
              
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating order status");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error updating order status");
            }
        }

        // PUT: api/admin/orders/{id}/payment-status
        [HttpPut("{id}/payment-status")]
        public async Task<IActionResult> UpdatePaymentStatus(int id, [FromBody] UpdateOrderStatusDTO updateDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var order = await _context.Orders.FindAsync(id);
                if (order == null)
                {
                    return NotFound();
                }

                // Check if user has access to the store
                if (!await CanAccessStoreAsync(order.StoreId))
                {
                    return Forbid();
                }

                // Update payment status
                // if (Enum.TryParse<PaymentStatus>(updateDto.PaymentStatus, true, out var paymentStatus))
                // {
                    order.PaymentStatus = PaymentStatus.Paid; // paymentStatus;
                    order.UpdatedAt = DateTime.UtcNow;

                    // Add notes if provided
                    // if (!string.IsNullOrEmpty(updateDto.Notes))
                    // {
                    //     order.Notes = string.IsNullOrEmpty(order.Notes)
                    //         ? updateDto.Notes
                    //         : $"{order.Notes}\n{DateTime.UtcNow:yyyy-MM-dd HH:mm}: {updateDto.Notes}";
                    // }

                    _context.Entry(order).State = EntityState.Modified;
                    await _context.SaveChangesAsync();

                    return NoContent();
                // }
                // else
                // {
                    // return BadRequest("Invalid payment status");
                // }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating payment status");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error updating payment status");
            }
        }

        // GET: api/admin/orders/stats
        [HttpGet("stats")]
        public async Task<ActionResult<OrderStatsDTO>> GetOrderStats(
            [FromQuery] int? storeId = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                // Get accessible store IDs for the current user
                var accessibleStoreIds = await GetUserStoreIdsAsync();
                if (accessibleStoreIds.Length == 0)
                {
                    return Forbid();
                }

                // If storeId is specified, check if user has access to it
                if (storeId.HasValue && !accessibleStoreIds.Contains(storeId.Value))
                {
                    return Forbid();
                }

                var query = _context.Orders.AsQueryable();

                // Filter by store if specified
                if (storeId.HasValue)
                {
                    query = query.Where(o => o.StoreId == storeId.Value);
                }
                else
                {
                    // Filter by accessible stores
                    query = query.Where(o => accessibleStoreIds.Contains(o.StoreId));
                }

                // Apply date filters
                if (fromDate.HasValue)
                {
                    query = query.Where(o => o.CreatedAt >= fromDate.Value);
                }

                if (toDate.HasValue)
                {
                    // Add one day to include the end date
                    var endDate = toDate.Value.AddDays(1);
                    query = query.Where(o => o.CreatedAt < endDate);
                }

                // Calculate stats
                var totalOrders = await query.CountAsync();
                var totalRevenue = await query.SumAsync(o => o.TotalAmount);
                var totalPending = await query.CountAsync(o => o.Status == OrderStatus.Pending);
                var totalProcessing = await query.CountAsync(o => o.Status == OrderStatus.Processing);
                var totalShipped = await query.CountAsync(o => o.Status == OrderStatus.Shipped);
                var totalDelivered = await query.CountAsync(o => o.Status == OrderStatus.Delivered);
                var totalCancelled = await query.CountAsync(o => o.Status == OrderStatus.Cancelled);
                var totalRefunded = await query.CountAsync(o => o.Status == OrderStatus.Refunded);

                var stats = new OrderStatsDTO
                {
                    TotalOrders = totalOrders,
                    TotalRevenue = totalRevenue,
                    PendingOrders = totalPending,
                    ProcessingOrders = totalProcessing,
                    ShippedOrders = totalShipped,
                    DeliveredOrders = totalDelivered,
                    CancelledOrders = totalCancelled,
                    RefundedOrders = totalRefunded
                };

                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving order stats");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error retrieving order stats");
            }
        }
    }
}
