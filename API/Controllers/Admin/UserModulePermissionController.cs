using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyShop.API.Attributes;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using MyShop.API.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MyShop.API.Controllers.Admin
{
    [Route("api/admin/user-permissions")]
    [ApiController]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public class UserModulePermissionController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly PermissionService _permissionService;
        private readonly ICurrentUserService _currentUserService;

        public UserModulePermissionController(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            PermissionService permissionService,
            ICurrentUserService currentUserService)
        {
            _context = context;
            _userManager = userManager;
            _permissionService = permissionService;
            _currentUserService = currentUserService;
        }

        // GET: api/admin/user-permissions
        [HttpGet]
        [Permission("Users", "View")]
        public async Task<ActionResult<IEnumerable<object>>> GetUserPermissions()
        {
            var currentUser = await _currentUserService.GetCurrentUserAsync();
            if (currentUser == null)
                return Unauthorized();

            // Super admins can see all user permissions
            var isSuperAdmin = await _userManager.IsInRoleAsync(currentUser, "SuperAdmin");

            var query = _context.UserModulePermissions.AsQueryable();
            
            // If not super admin, only show permissions for users in stores they manage
            if (!isSuperAdmin)
            {
                var managedStoreIds = await _context.StoreAdmins
                    .Where(sa => sa.UserId == currentUser.Id && sa.CanManageSettings)
                    .Select(sa => sa.StoreId)
                    .ToListAsync();

                query = query.Where(ump => 
                    ump.UserId == currentUser.Id || // Their own permissions
                    (ump.StoreId.HasValue && managedStoreIds.Contains(ump.StoreId.Value)) // Permissions for users in stores they manage
                );
            }

            var permissions = await query
                .Include(ump => ump.User)
                .Include(ump => ump.Store)
                .Select(ump => new
                {
                    Id = ump.Id,
                    UserId = ump.UserId,
                    UserName = ump.User.UserName,
                    UserFullName = ump.User.FirstName + " " + ump.User.LastName,
                    Module = ump.Module,
                    CanView = ump.CanView,
                    CanCreate = ump.CanCreate,
                    CanEdit = ump.CanEdit,
                    CanDelete = ump.CanDelete,
                    StoreId = ump.StoreId,
                    StoreName = ump.Store != null ? ump.Store.Name : null,
                    CreatedAt = ump.CreatedAt,
                    UpdatedAt = ump.UpdatedAt
                })
                .ToListAsync();

            return Ok(permissions);
        }

        // GET: api/admin/user-permissions/user/{userId}
        [HttpGet("user/{userId}")]
        [Permission("Users", "View")]
        public async Task<ActionResult<IEnumerable<object>>> GetUserPermissionsByUser(string userId)
        {
            var currentUser = await _currentUserService.GetCurrentUserAsync();
            if (currentUser == null)
                return Unauthorized();

            // Check if the user exists
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
                return NotFound("User not found");

            // Super admins can see all user permissions
            var isSuperAdmin = await _userManager.IsInRoleAsync(currentUser, "SuperAdmin");

            // If not super admin, check if they can manage this user
            if (!isSuperAdmin && currentUser.Id != userId)
            {
                // Check if the user is in a store they manage
                var userStoreIds = await _context.StoreAdmins
                    .Where(sa => sa.UserId == userId)
                    .Select(sa => sa.StoreId)
                    .ToListAsync();

                var managedStoreIds = await _context.StoreAdmins
                    .Where(sa => sa.UserId == currentUser.Id && sa.CanManageSettings)
                    .Select(sa => sa.StoreId)
                    .ToListAsync();

                // If there's no overlap in stores, they can't manage this user
                if (!userStoreIds.Intersect(managedStoreIds).Any())
                    return Forbid();
            }

            var permissions = await _context.UserModulePermissions
                .Where(ump => ump.UserId == userId)
                .Include(ump => ump.Store)
                .Select(ump => new
                {
                    Id = ump.Id,
                    UserId = ump.UserId,
                    Module = ump.Module,
                    CanView = ump.CanView,
                    CanCreate = ump.CanCreate,
                    CanEdit = ump.CanEdit,
                    CanDelete = ump.CanDelete,
                    StoreId = ump.StoreId,
                    StoreName = ump.Store != null ? ump.Store.Name : null,
                    CreatedAt = ump.CreatedAt,
                    UpdatedAt = ump.UpdatedAt
                })
                .ToListAsync();

            return Ok(permissions);
        }

        // GET: api/admin/user-permissions/store/{storeId}
        [HttpGet("store/{storeId}")]
        [Permission("Users", "View", true)]
        public async Task<ActionResult<IEnumerable<object>>> GetUserPermissionsByStore(int storeId)
        {
            var currentUser = await _currentUserService.GetCurrentUserAsync();
            if (currentUser == null)
                return Unauthorized();

            // Check if the store exists
            var store = await _context.Stores.FindAsync(storeId);
            if (store == null)
                return NotFound("Store not found");

            // Super admins can see all store permissions
            var isSuperAdmin = await _userManager.IsInRoleAsync(currentUser, "SuperAdmin");

            // If not super admin, check if they can manage this store
            if (!isSuperAdmin)
            {
                var canManageStore = await _context.StoreAdmins
                    .AnyAsync(sa => sa.UserId == currentUser.Id && sa.StoreId == storeId && sa.CanManageSettings);

                if (!canManageStore)
                    return Forbid();
            }

            var permissions = await _context.UserModulePermissions
                .Where(ump => ump.StoreId == storeId)
                .Include(ump => ump.User)
                .Select(ump => new
                {
                    Id = ump.Id,
                    UserId = ump.UserId,
                    UserName = ump.User.UserName,
                    UserFullName = ump.User.FirstName + " " + ump.User.LastName,
                    Module = ump.Module,
                    CanView = ump.CanView,
                    CanCreate = ump.CanCreate,
                    CanEdit = ump.CanEdit,
                    CanDelete = ump.CanDelete,
                    StoreId = ump.StoreId,
                    CreatedAt = ump.CreatedAt,
                    UpdatedAt = ump.UpdatedAt
                })
                .ToListAsync();

            return Ok(permissions);
        }

        // POST: api/admin/user-permissions
        [HttpPost]
        [Permission("Users", "Create")]
        public async Task<ActionResult<object>> CreateUserPermission(UserModulePermissionDTO dto)
        {
            var currentUser = await _currentUserService.GetCurrentUserAsync();
            if (currentUser == null)
                return Unauthorized();

            // Check if the user exists
            var user = await _userManager.FindByIdAsync(dto.UserId);
            if (user == null)
                return NotFound("User not found");

            // Super admins can create permissions for any user
            var isSuperAdmin = await _userManager.IsInRoleAsync(currentUser, "SuperAdmin");

            // If store ID is provided, check if the store exists
            if (dto.StoreId.HasValue)
            {
                var store = await _context.Stores.FindAsync(dto.StoreId.Value);
                if (store == null)
                    return NotFound("Store not found");

                // If not super admin, check if they can manage this store
                if (!isSuperAdmin)
                {
                    var canManageStore = await _context.StoreAdmins
                        .AnyAsync(sa => sa.UserId == currentUser.Id && sa.StoreId == dto.StoreId.Value && sa.CanManageSettings);

                    if (!canManageStore)
                        return Forbid();
                }
            }
            else
            {
                // Only super admins can create global permissions
                if (!isSuperAdmin)
                    return Forbid();
            }

            // Check if the permission already exists
            var existingPermission = await _context.UserModulePermissions
                .FirstOrDefaultAsync(ump => 
                    ump.UserId == dto.UserId && 
                    ump.Module == dto.Module && 
                    ump.StoreId == dto.StoreId);

            if (existingPermission != null)
                return Conflict("Permission already exists");

            // Create the permission
            var permission = new UserModulePermission
            {
                UserId = dto.UserId,
                Module = dto.Module,
                CanView = dto.CanView,
                CanCreate = dto.CanCreate,
                CanEdit = dto.CanEdit,
                CanDelete = dto.CanDelete,
                StoreId = dto.StoreId,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.UserModulePermissions.Add(permission);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetUserPermissionsByUser), new { userId = dto.UserId }, new
            {
                Id = permission.Id,
                UserId = permission.UserId,
                Module = permission.Module,
                CanView = permission.CanView,
                CanCreate = permission.CanCreate,
                CanEdit = permission.CanEdit,
                CanDelete = permission.CanDelete,
                StoreId = permission.StoreId,
                CreatedAt = permission.CreatedAt,
                UpdatedAt = permission.UpdatedAt
            });
        }

        // PUT: api/admin/user-permissions/{id}
        [HttpPut("{id}")]
        [Permission("Users", "Edit")]
        public async Task<IActionResult> UpdateUserPermission(int id, UserModulePermissionDTO dto)
        {
            var currentUser = await _currentUserService.GetCurrentUserAsync();
            if (currentUser == null)
                return Unauthorized();

            var permission = await _context.UserModulePermissions.FindAsync(id);
            if (permission == null)
                return NotFound();

            // Super admins can update any permission
            var isSuperAdmin = await _userManager.IsInRoleAsync(currentUser, "SuperAdmin");

            // If not super admin, check if they can manage this permission
            if (!isSuperAdmin)
            {
                // If it's a store-specific permission, check if they can manage the store
                if (permission.StoreId.HasValue)
                {
                    var canManageStore = await _context.StoreAdmins
                        .AnyAsync(sa => sa.UserId == currentUser.Id && sa.StoreId == permission.StoreId.Value && sa.CanManageSettings);

                    if (!canManageStore)
                        return Forbid();
                }
                else
                {
                    // Only super admins can update global permissions
                    return Forbid();
                }
            }

            // Update the permission
            permission.CanView = dto.CanView;
            permission.CanCreate = dto.CanCreate;
            permission.CanEdit = dto.CanEdit;
            permission.CanDelete = dto.CanDelete;
            permission.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            return NoContent();
        }

        // DELETE: api/admin/user-permissions/{id}
        [HttpDelete("{id}")]
        [Permission("Users", "Delete")]
        public async Task<IActionResult> DeleteUserPermission(int id)
        {
            var currentUser = await _currentUserService.GetCurrentUserAsync();
            if (currentUser == null)
                return Unauthorized();

            var permission = await _context.UserModulePermissions.FindAsync(id);
            if (permission == null)
                return NotFound();

            // Super admins can delete any permission
            var isSuperAdmin = await _userManager.IsInRoleAsync(currentUser, "SuperAdmin");

            // If not super admin, check if they can manage this permission
            if (!isSuperAdmin)
            {
                // If it's a store-specific permission, check if they can manage the store
                if (permission.StoreId.HasValue)
                {
                    var canManageStore = await _context.StoreAdmins
                        .AnyAsync(sa => sa.UserId == currentUser.Id && sa.StoreId == permission.StoreId.Value && sa.CanManageSettings);

                    if (!canManageStore)
                        return Forbid();
                }
                else
                {
                    // Only super admins can delete global permissions
                    return Forbid();
                }
            }

            _context.UserModulePermissions.Remove(permission);
            await _context.SaveChangesAsync();

            return NoContent();
        }
    }

    public class UserModulePermissionDTO
    {
        public string UserId { get; set; }
        public string Module { get; set; }
        public bool CanView { get; set; }
        public bool CanCreate { get; set; }
        public bool CanEdit { get; set; }
        public bool CanDelete { get; set; }
        public int? StoreId { get; set; }
    }
}
