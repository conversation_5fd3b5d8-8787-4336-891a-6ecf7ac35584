using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using MyShop.API.DTOs;
using System.Net;

namespace MyShop.API.Controllers
{
    [Route("api/admin/[controller]")]
    [ApiController]
    [Authorize(Roles = "Admin")]
    public class UserManagementController : ControllerBase
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly RoleManager<IdentityRole> _roleManager;
        private readonly ApplicationDbContext _context;
        private readonly ILogger<UserManagementController> _logger;

        public UserManagementController(
            UserManager<ApplicationUser> userManager,
            RoleManager<IdentityRole> roleManager,
            ApplicationDbContext context,
            ILogger<UserManagementController> logger)
        {
            _userManager = userManager;
            _roleManager = roleManager;
            _context = context;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> GetUsers()
        {
            try
            {
                // Get all admin and staff users (not customers)
                var users = await _userManager.Users
                    .Where(u => u.UserType == UserType.Admin || u.UserType == UserType.Staff)
                    .ToListAsync();

                // Get all store assignments for these users
                var userStoreMap = await _context.StoreAdmins
                    .Where(sa => users.Select(u => u.Id).Contains(sa.UserId))
                    .GroupBy(sa => sa.UserId)
                    .ToDictionaryAsync(g => g.Key, g => g.Select(sa => sa.StoreId).ToList());

                var userDtos = new List<UserDTO>();
                foreach (var user in users)
                {
                    var roles = await _userManager.GetRolesAsync(user);
                    userStoreMap.TryGetValue(user.Id, out var storeIds);

                    userDtos.Add(new UserDTO
                    {
                        Id = user.Id,
                        Email = user.Email,
                        FirstName = user.FirstName,
                        LastName = user.LastName,
                        PhoneNumber = user.PhoneNumber,
                        UserType = user.UserType,
                        Roles = roles.ToList(),
                        CreatedAt = user.CreatedAt,
                        LastLoginDate = user.LastLoginDate,
                        StoreIds = storeIds ?? new List<int>()
                    });
                }

                return Ok(userDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting users");
                return StatusCode((int)HttpStatusCode.InternalServerError, "An error occurred while processing your request");
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetUser(string id)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(id);
                if (user == null)
                {
                    return NotFound();
                }

                var roles = await _userManager.GetRolesAsync(user);

                // Get user's store IDs
                var storeIds = await _context.StoreAdmins
                    .Where(sa => sa.UserId == user.Id)
                    .Select(sa => sa.StoreId)
                    .ToListAsync();

                var userDto = new UserDTO
                {
                    Id = user.Id,
                    Email = user.Email,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    PhoneNumber = user.PhoneNumber,
                    UserType = user.UserType,
                    Roles = roles.ToList(),
                    CreatedAt = user.CreatedAt,
                    LastLoginDate = user.LastLoginDate,
                    StoreIds = storeIds
                };

                return Ok(userDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user {UserId}", id);
                return StatusCode((int)HttpStatusCode.InternalServerError, "An error occurred while processing your request");
            }
        }

        [HttpPost]
        public async Task<IActionResult> CreateUser([FromBody] CreateUserDTO model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Check if email already exists
                var existingUser = await _userManager.FindByEmailAsync(model.Email);
                if (existingUser != null)
                {
                    return BadRequest("Email already exists");
                }

                // Create user
                var user = new ApplicationUser
                {
                    UserName = model.Email,
                    Email = model.Email,
                    FirstName = model.FirstName,
                    LastName = model.LastName,
                    PhoneNumber = model.PhoneNumber,
                    UserType = model.UserType,
                    EmailConfirmed = true
                };

                var result = await _userManager.CreateAsync(user, model.Password);
                if (!result.Succeeded)
                {
                    return BadRequest(string.Join(", ", result.Errors.Select(e => e.Description)));
                }

                // Add roles
                foreach (var role in model.Roles)
                {
                    if (await _roleManager.RoleExistsAsync(role))
                    {
                        await _userManager.AddToRoleAsync(user, role);
                    }
                }

                // Add store assignments if provided
                if (model.StoreIds != null && model.StoreIds.Any())
                {
                    foreach (var storeId in model.StoreIds)
                    {
                        var storeAdmin = new StoreAdmin
                        {
                            UserId = user.Id,
                            StoreId = storeId
                        };
                        _context.StoreAdmins.Add(storeAdmin);
                    }
                    await _context.SaveChangesAsync();
                }

                // Return created user
                var roles = await _userManager.GetRolesAsync(user);
                var storeIds = model.StoreIds ?? new List<int>();

                var userDto = new UserDTO
                {
                    Id = user.Id,
                    Email = user.Email,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    PhoneNumber = user.PhoneNumber,
                    UserType = user.UserType,
                    Roles = roles.ToList(),
                    CreatedAt = user.CreatedAt,
                    LastLoginDate = user.LastLoginDate,
                    StoreIds = storeIds
                };

                return CreatedAtAction(nameof(GetUser), new { id = user.Id }, userDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating user");
                return StatusCode((int)HttpStatusCode.InternalServerError, "An error occurred while processing your request");
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateUser(string id, [FromBody] UpdateUserDTO model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var user = await _userManager.FindByIdAsync(id);
                if (user == null)
                {
                    return NotFound();
                }

                // Update user properties
                user.FirstName = model.FirstName;
                user.LastName = model.LastName;
                user.PhoneNumber = model.PhoneNumber;
                user.UserType = model.UserType;
                user.UpdatedAt = DateTime.UtcNow;

                var result = await _userManager.UpdateAsync(user);
                if (!result.Succeeded)
                {
                    return BadRequest(string.Join(", ", result.Errors.Select(e => e.Description)));
                }

                // Update password if provided
                if (!string.IsNullOrEmpty(model.NewPassword))
                {
                    var token = await _userManager.GeneratePasswordResetTokenAsync(user);
                    result = await _userManager.ResetPasswordAsync(user, token, model.NewPassword);
                    if (!result.Succeeded)
                    {
                        return BadRequest(string.Join(", ", result.Errors.Select(e => e.Description)));
                    }
                }

                // Update roles
                var currentRoles = await _userManager.GetRolesAsync(user);
                var rolesToRemove = currentRoles.Except(model.Roles).ToList();
                var rolesToAdd = model.Roles.Except(currentRoles).ToList();

                if (rolesToRemove.Any())
                {
                    result = await _userManager.RemoveFromRolesAsync(user, rolesToRemove);
                    if (!result.Succeeded)
                    {
                        return BadRequest(string.Join(", ", result.Errors.Select(e => e.Description)));
                    }
                }

                if (rolesToAdd.Any())
                {
                    result = await _userManager.AddToRolesAsync(user, rolesToAdd);
                    if (!result.Succeeded)
                    {
                        return BadRequest(string.Join(", ", result.Errors.Select(e => e.Description)));
                    }
                }

                // Update store assignments if provided
                if (model.StoreIds != null)
                {
                    // Remove existing store assignments
                    var existingStoreAdmins = await _context.StoreAdmins
                        .Where(sa => sa.UserId == user.Id)
                        .ToListAsync();

                    if (existingStoreAdmins.Any())
                    {
                        _context.StoreAdmins.RemoveRange(existingStoreAdmins);
                    }

                    // Add new store assignments
                    if (model.StoreIds.Any())
                    {
                        foreach (var storeId in model.StoreIds)
                        {
                            var storeAdmin = new StoreAdmin
                            {
                                UserId = user.Id,
                                StoreId = storeId
                            };
                            _context.StoreAdmins.Add(storeAdmin);
                        }
                    }

                    await _context.SaveChangesAsync();
                }

                // Get user's store IDs
                var storeIds = await _context.StoreAdmins
                    .Where(sa => sa.UserId == user.Id)
                    .Select(sa => sa.StoreId)
                    .ToListAsync();

                // Return updated user
                var updatedRoles = await _userManager.GetRolesAsync(user);
                var userDto = new UserDTO
                {
                    Id = user.Id,
                    Email = user.Email,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    PhoneNumber = user.PhoneNumber,
                    UserType = user.UserType,
                    Roles = updatedRoles.ToList(),
                    CreatedAt = user.CreatedAt,
                    LastLoginDate = user.LastLoginDate,
                    StoreIds = storeIds
                };

                return Ok(userDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user {UserId}", id);
                return StatusCode((int)HttpStatusCode.InternalServerError, "An error occurred while processing your request");
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteUser(string id)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(id);
                if (user == null)
                {
                    return NotFound();
                }

                // Prevent deleting the last admin
                if (user.UserType == UserType.Admin)
                {
                    var adminCount = await _userManager.GetUsersInRoleAsync("Admin");
                    if (adminCount.Count <= 1)
                    {
                        return BadRequest("Cannot delete the last admin user");
                    }
                }

                var result = await _userManager.DeleteAsync(user);
                if (!result.Succeeded)
                {
                    return BadRequest(string.Join(", ", result.Errors.Select(e => e.Description)));
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user {UserId}", id);
                return StatusCode((int)HttpStatusCode.InternalServerError, "An error occurred while processing your request");
            }
        }
    }
}
