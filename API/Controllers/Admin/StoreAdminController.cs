using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using MyShop.API.DTOs;

namespace MyShop.API.Controllers.Admin
{
    [Route("api/admin/[controller]")]
    [ApiController]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public class StoreAdminController : AdminController
    {
        public StoreAdminController(
            UserManager<ApplicationUser> userManager,
            ApplicationDbContext context,
            ILogger<StoreAdminController> logger)
            : base(userManager, context, logger)
        {
        }

        [HttpGet("accessible-stores")]
        public async Task<ActionResult<IEnumerable<StoreDTO>>> GetAccessibleStores()
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null)
                {
                    return Unauthorized();
                }

                // Get accessible store IDs for the current user
                var accessibleStoreIds = await GetUserStoreIdsAsync();
                if (accessibleStoreIds.Length == 0)
                {
                    return Ok(new List<StoreDTO>());
                }

                var stores = await _context.Stores
                    .Where(s => accessibleStoreIds.Contains(s.Id))
                    .Select(s => new StoreDTO
                    {
                        Id = s.Id,
                        Name = s.Name,
                        StoreKey = s.StoreKey,
                        IsActive = s.IsActive
                    })
                    .ToListAsync();



                return Ok(stores);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting accessible stores");
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        [HttpGet("store/{storeId}")]
        public async Task<IActionResult> GetStoreAdmins(int storeId)
        {
            try
            {
                var accessCheck = await CheckStoreAccessAsync(storeId);
                if (accessCheck != null)
                    return accessCheck;

                var storeAdmins = await _context.StoreAdmins
                    .Include(sa => sa.User)
                    .Include(sa => sa.Store)
                    .Where(sa => sa.StoreId == storeId)
                    .ToListAsync();

                return Ok(storeAdmins.Select(sa => MapToDTO(sa)));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting store admins for store {StoreId}", storeId);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetStoreAdmin(int id)
        {
            try
            {
                var storeAdmin = await _context.StoreAdmins
                    .Include(sa => sa.User)
                    .Include(sa => sa.Store)
                    .FirstOrDefaultAsync(sa => sa.Id == id);

                if (storeAdmin == null)
                    return NotFound();

                var accessCheck = await CheckStoreAccessAsync(storeAdmin.StoreId);
                if (accessCheck != null)
                    return accessCheck;

                return Ok(MapToDTO(storeAdmin));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting store admin {Id}", id);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        [HttpPost]
        public async Task<IActionResult> CreateStoreAdmin(CreateStoreAdminDTO dto)
        {
            try
            {
                var accessCheck = await CheckStoreAccessAsync(dto.StoreId);
                if (accessCheck != null)
                    return accessCheck;

                // Check if store exists
                var store = await _context.Stores.FindAsync(dto.StoreId);
                if (store == null)
                    return BadRequest("Store not found");

                // Check if user exists
                var user = await _userManager.FindByEmailAsync(dto.UserEmail);

                if (user == null)
                {
                    // Create new user
                    user = new ApplicationUser
                    {
                        UserName = dto.UserEmail,
                        Email = dto.UserEmail,
                        EmailConfirmed = true,
                        UserType = UserType.Admin,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };

                    var result = await _userManager.CreateAsync(user, "Admin123!");  // Default password

                    if (!result.Succeeded)
                        return BadRequest(result.Errors.Select(e => e.Description));

                    await _userManager.AddToRoleAsync(user, "Admin");
                }

                // Check if user is already admin for this store
                var existingAdmin = await _context.StoreAdmins
                    .FirstOrDefaultAsync(sa => sa.UserId == user.Id && sa.StoreId == dto.StoreId);

                if (existingAdmin != null)
                    return BadRequest("User is already an admin for this store");

                var storeAdmin = new StoreAdmin
                {
                    UserId = user.Id,
                    StoreId = dto.StoreId,
                    CanManageProducts = dto.CanManageProducts,
                    CanManageOrders = dto.CanManageOrders,
                    CanManageCustomers = dto.CanManageCustomers,
                    CanManageSettings = dto.CanManageSettings,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.StoreAdmins.Add(storeAdmin);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetStoreAdmin), new { id = storeAdmin.Id }, MapToDTO(storeAdmin));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating store admin");
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateStoreAdmin(int id, UpdateStoreAdminDTO dto)
        {
            try
            {
                var storeAdmin = await _context.StoreAdmins.FindAsync(id);

                if (storeAdmin == null)
                    return NotFound();

                var accessCheck = await CheckStoreAccessAsync(storeAdmin.StoreId);
                if (accessCheck != null)
                    return accessCheck;

                storeAdmin.CanManageProducts = dto.CanManageProducts;
                storeAdmin.CanManageOrders = dto.CanManageOrders;
                storeAdmin.CanManageCustomers = dto.CanManageCustomers;
                storeAdmin.CanManageSettings = dto.CanManageSettings;
                storeAdmin.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating store admin {Id}", id);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteStoreAdmin(int id)
        {
            try
            {
                var storeAdmin = await _context.StoreAdmins.FindAsync(id);

                if (storeAdmin == null)
                    return NotFound();

                var accessCheck = await CheckStoreAccessAsync(storeAdmin.StoreId);
                if (accessCheck != null)
                    return accessCheck;

                _context.StoreAdmins.Remove(storeAdmin);
                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting store admin {Id}", id);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        private StoreAdminDTO MapToDTO(StoreAdmin storeAdmin)
        {
            return new StoreAdminDTO
            {
                Id = storeAdmin.Id,
                UserId = storeAdmin.UserId,
                UserEmail = storeAdmin.User?.Email,
                UserName = $"{storeAdmin.User?.FirstName} {storeAdmin.User?.LastName}".Trim(),
                StoreId = storeAdmin.StoreId,
                StoreName = storeAdmin.Store?.Name,
                CanManageProducts = storeAdmin.CanManageProducts,
                CanManageOrders = storeAdmin.CanManageOrders,
                CanManageCustomers = storeAdmin.CanManageCustomers,
                CanManageSettings = storeAdmin.CanManageSettings,
                CreatedAt = storeAdmin.CreatedAt,
                UpdatedAt = storeAdmin.UpdatedAt
            };
        }
    }
}
