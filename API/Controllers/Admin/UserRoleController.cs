using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MyShop.API.Controllers.Admin
{
    [Route("api/admin/userrole")]
    [ApiController]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public class UserRoleController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly RoleManager<IdentityRole> _roleManager;

        public UserRoleController(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            RoleManager<IdentityRole> roleManager)
        {
            _context = context;
            _userManager = userManager;
            _roleManager = roleManager;
        }

        // GET: api/admin/userrole
        [HttpGet]
        public async Task<ActionResult<IEnumerable<object>>> GetUserRoles()
        {
            var users = await _userManager.Users.ToListAsync();

            var userRoles = new List<object>();
            foreach (var user in users)
            {
                var roles = await _userManager.GetRolesAsync(user);
                if (roles.Any())
                {
                    userRoles.Add(new
                    {
                        UserId = user.Id,
                        UserEmail = user.Email,
                        UserFullName = $"{user.FirstName} {user.LastName}",
                        RoleIds = roles.ToList()
                    });
                }
            }

            return userRoles;
        }

        // GET: api/admin/userrole/{userId}
        [HttpGet("{userId}")]
        public async Task<ActionResult<object>> GetUserRole(string userId)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                return NotFound();
            }

            var roles = await _userManager.GetRolesAsync(user);

            return new
            {
                UserId = user.Id,
                UserEmail = user.Email,
                UserFullName = $"{user.FirstName} {user.LastName}",
                RoleIds = roles.ToList()
            };
        }

        // POST: api/admin/userrole
        [HttpPost]
        public async Task<ActionResult<object>> AssignRolesToUser(UserRoleAssignDto userRoleDto)
        {
            var user = await _userManager.FindByIdAsync(userRoleDto.UserId);
            if (user == null)
            {
                return NotFound("User not found");
            }

            // Validate roles
            var roleIds = userRoleDto.Roles ?? new List<string>();
            var validRoles = new List<string>();

            foreach (var roleId in roleIds)
            {
                var role = await _roleManager.FindByNameAsync(roleId);
                if (role != null)
                {
                    validRoles.Add(roleId);
                }
            }

            if (roleIds.Count != validRoles.Count)
            {
                return BadRequest("One or more role names are invalid");
            }

            // Get current roles
            var currentRoles = await _userManager.GetRolesAsync(user);

            // Remove roles that are not in the new list
            var rolesToRemove = currentRoles.Except(roleIds).ToList();
            if (rolesToRemove.Any())
            {
                await _userManager.RemoveFromRolesAsync(user, rolesToRemove);
            }

            // Add roles that are not in the current list
            var rolesToAdd = roleIds.Except(currentRoles).ToList();
            if (rolesToAdd.Any())
            {
                await _userManager.AddToRolesAsync(user, rolesToAdd);
            }

            // Get updated roles
            var updatedRoles = await _userManager.GetRolesAsync(user);

            return new
            {
                UserId = user.Id,
                UserEmail = user.Email,
                UserFullName = $"{user.FirstName} {user.LastName}",
                RoleIds = updatedRoles.ToList()
            };
        }

        // GET: api/admin/userrole/store/{storeId}
        [HttpGet("store/{storeId}")]
        public async Task<ActionResult<IEnumerable<object>>> GetUserRolesByStore(int storeId)
        {
            // Check if the current user has access to this store
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return Unauthorized();
            }

            // Super admins can see all users in any store
            var isSuperAdmin = await _userManager.IsInRoleAsync(currentUser, "SuperAdmin");
            
            // If not super admin, check if they can access this store
            if (!isSuperAdmin)
            {
                var hasAccess = await _context.StoreAdmins
                    .AnyAsync(sa => sa.UserId == currentUser.Id && sa.StoreId == storeId);
                
                if (!hasAccess)
                {
                    return Forbid();
                }
            }

            // Get store admins for this store
            var storeAdmins = await _context.StoreAdmins
                .Where(sa => sa.StoreId == storeId)
                .Select(sa => sa.UserId)
                .ToListAsync();

            var userRoles = new List<object>();

            // Get all users who are assigned to this store
            foreach (var userId in storeAdmins)
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user != null)
                {
                    var roles = await _userManager.GetRolesAsync(user);

                    userRoles.Add(new
                    {
                        UserId = user.Id,
                        UserEmail = user.Email,
                        UserFullName = $"{user.FirstName} {user.LastName}",
                        RoleIds = roles.ToList()
                    });
                }
            }

            return userRoles;
        }
    }

    public class UserRoleAssignDto
    {
        public string UserId { get; set; }
        public List<string> Roles { get; set; }
    }
}
