using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System.Drawing;
using System.Net;

namespace MyShop.API.Controllers.Admin
{
   [Route("api/admin/[controller]")]
    [ApiController]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public class ProductImportExportController : AdminController
    {
       public ProductImportExportController(
            UserManager<ApplicationUser> userManager,
            ApplicationDbContext context,
            ILogger<ProductController> logger)
            : base(userManager, context, logger)
        {
        }


        [HttpGet("export-template")]
        public IActionResult ExportTemplate()
        {
            try
            {
                using (var package = new ExcelPackage())
                {
                    var worksheet = package.Workbook.Worksheets.Add("Products");

                    // Define headers
                    var headers = new List<string>
                    {
                        "Name*", "Description*", "Price*", "SKU*", "Barcode", "Weight", "WeightUnit",
                        "Cost", "CategoryId*", "IsActive", "IsFeatured", "StockQuantity*", "MainImageUrl",
                        "Fabric", "Type", "CustomAttributes"
                    };

                    // Apply headers
                    for (int i = 0; i < headers.Count; i++)
                    {
                        worksheet.Cells[1, i + 1].Value = headers[i];
                        worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                        worksheet.Cells[1, i + 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
                        worksheet.Cells[1, i + 1].Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                    }

                    // Add sample data
                    worksheet.Cells[2, 1].Value = "Sample Product";
                    worksheet.Cells[2, 2].Value = "This is a sample product description";
                    worksheet.Cells[2, 3].Value = 19.99;
                    worksheet.Cells[2, 4].Value = "PROD-001";
                    worksheet.Cells[2, 5].Value = "123456789012";
                    worksheet.Cells[2, 6].Value = 0.5;
                    worksheet.Cells[2, 7].Value = "kg";
                    worksheet.Cells[2, 8].Value = 10.00;
                    worksheet.Cells[2, 9].Value = 1; // CategoryId
                    worksheet.Cells[2, 10].Value = true;
                    worksheet.Cells[2, 11].Value = false;
                    worksheet.Cells[2, 12].Value = 100;
                    worksheet.Cells[2, 13].Value = "https://example.com/image.jpg";
                    worksheet.Cells[2, 14].Value = "Cotton";
                    worksheet.Cells[2, 15].Value = "Kurta Set";
                    worksheet.Cells[2, 16].Value = "{\"Color\":\"Blue\",\"Size\":\"M\",\"Material\":\"Premium Cotton\"}";


                    // Add notes
                    worksheet.Cells[4, 1].Value = "Notes:";
                    worksheet.Cells[4, 1].Style.Font.Bold = true;
                    worksheet.Cells[5, 1].Value = "1. Fields marked with * are required";
                    worksheet.Cells[6, 1].Value = "2. CategoryId must be a valid category ID from the system";
                    worksheet.Cells[7, 1].Value = "3. IsActive and IsFeatured should be TRUE or FALSE";
                    worksheet.Cells[8, 1].Value = "4. Price, Weight, and Cost should be numeric values";
                    worksheet.Cells[9, 1].Value = "5. StockQuantity should be a whole number";
                    worksheet.Cells[10, 1].Value = "6. Fabric and Type are custom attributes for easy access";
                    worksheet.Cells[11, 1].Value = "7. CustomAttributes can be a JSON string with additional properties";
                    worksheet.Cells[12, 1].Value = "8. Products will be automatically assigned to your store";

                    // Auto-fit columns
                    worksheet.Cells.AutoFitColumns();

                    // Return the Excel file
                    var stream = new MemoryStream(package.GetAsByteArray());
                    return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "product_import_template.xlsx");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting product template");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error exporting product template");
            }
        }

        [HttpGet("export")]
        public async Task<IActionResult> ExportProducts([FromQuery] int? storeId = null)
        {
            try
            {
                _logger.LogInformation("ExportProducts called with storeId: {StoreId}", storeId);

                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    _logger.LogWarning("No authenticated user found when exporting products");
                    return Unauthorized();
                }

                // Get query for products
                var query = _context.Products
                    .Include(p => p.Category)
                    .Include(p => p.Variants)
                        .ThenInclude(v => v.InventoryItems)
                    .Include(p => p.Images)
                    .Include(p => p.Store)
                    .AsQueryable();

                // Filter by store access
                if (storeId.HasValue)
                {
                    // Check if user has access to the specified store
                    if (!await CanAccessStoreAsync(storeId.Value))
                    {
                        _logger.LogWarning("User {UserId} attempted to export products from unauthorized store {StoreId}",
                            currentUser.Id, storeId.Value);
                        return Forbid();
                    }

                    // Filter products by the specified store
                    query = query.Where(p => p.StoreId == storeId.Value);
                    _logger.LogInformation("Filtering exported products for store {StoreId}", storeId.Value);
                }
                else
                {
                    // If no specific store is requested, filter by all stores the user has access to
                    var accessibleStoreIds = await GetUserStoreIdsAsync();

                    if (accessibleStoreIds.Length == 0)
                    {
                        _logger.LogWarning("User {UserId} has no accessible stores", currentUser.Id);
                        return Ok(new List<Product>()); // Return empty list if user has no accessible stores
                    }

                    query = query.Where(p => accessibleStoreIds.Contains(p.StoreId));
                    _logger.LogInformation("Filtering exported products for {StoreCount} accessible stores", accessibleStoreIds.Length);
                }

                var products = await query.ToListAsync();

                using (var package = new ExcelPackage())
                {
                    var worksheet = package.Workbook.Worksheets.Add("Products");

                    // Define headers
                    var headers = new List<string>
                    {
                        "Id", "Name", "Description", "Price", "SKU", "Barcode", "Weight", "WeightUnit",
                        "Cost", "CategoryId", "CategoryName", "IsActive", "IsFeatured",
                        "StockQuantity", "MainImageUrl", "CreatedAt", "UpdatedAt", "CustomAttributes",
                        "StoreId", "StoreName"
                    };

                    // Apply headers
                    for (int i = 0; i < headers.Count; i++)
                    {
                        worksheet.Cells[1, i + 1].Value = headers[i];
                        worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                        worksheet.Cells[1, i + 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
                        worksheet.Cells[1, i + 1].Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                    }

                    // Add data
                    for (int i = 0; i < products.Count; i++)
                    {
                        var product = products[i];
                        var row = i + 2;

                        worksheet.Cells[row, 1].Value = product.Id;
                        worksheet.Cells[row, 2].Value = product.Name;
                        worksheet.Cells[row, 3].Value = product.Description;
                        worksheet.Cells[row, 6].Value = product.Barcode;
                        worksheet.Cells[row, 10].Value = product.CategoryId;
                        worksheet.Cells[row, 11].Value = product.Category?.Name;
                        worksheet.Cells[row, 12].Value = product.IsActive;
                        worksheet.Cells[row, 13].Value = product.IsFeatured;
                        int totalStock = 0;
                        foreach (var variant in product.Variants)
                        {
                            foreach (var item in variant.InventoryItems)
                            {
                                totalStock += item.StockQuantity;
                            }
                        }
                        worksheet.Cells[row, 14].Value = totalStock;

                        // Get main image URL
                        var mainImage = product.Images.FirstOrDefault(i => i.IsMain);
                        worksheet.Cells[row, 15].Value = mainImage?.ImageUrl ?? product.ImageUrl;

                        worksheet.Cells[row, 16].Value = product.CreatedAt;
                        worksheet.Cells[row, 17].Value = product.UpdatedAt;
                        worksheet.Cells[row, 18].Value = product.CustomAttributes;
                        worksheet.Cells[row, 19].Value = product.StoreId;
                        worksheet.Cells[row, 20].Value = product.Store?.Name ?? "Unknown";
                    }

                    // Auto-fit columns
                    worksheet.Cells.AutoFitColumns();

                    // Return the Excel file
                    var stream = new MemoryStream(package.GetAsByteArray());
                    return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"products_export_{DateTime.Now:yyyyMMdd}.xlsx");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting products");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error exporting products");
            }
        }

        [HttpPost("import")]
        public async Task<IActionResult> ImportProducts(IFormFile file, [FromQuery] int? storeId = null)
        {
            try
            {
                _logger.LogInformation("ImportProducts called with storeId: {StoreId}", storeId);

                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    _logger.LogWarning("No authenticated user found when importing products");
                    return Unauthorized();
                }

                // Determine which store to use for imported products
                int targetStoreId;

                if (storeId.HasValue)
                {
                    // Check if user has access to the specified store
                    if (!await CanAccessStoreAsync(storeId.Value))
                    {
                        _logger.LogWarning("User {UserId} attempted to import products to unauthorized store {StoreId}",
                            currentUser.Id, storeId.Value);
                        return Forbid();
                    }
                    targetStoreId = storeId.Value;
                }
                else
                {
                    // If no specific store is requested, use the first accessible store
                    var accessibleStoreIds = await GetUserStoreIdsAsync();

                    if (accessibleStoreIds.Length == 0)
                    {
                        _logger.LogWarning("User {UserId} has no accessible stores for importing products", currentUser.Id);
                        return BadRequest("You don't have access to any stores. Please contact an administrator.");
                    }

                    targetStoreId = accessibleStoreIds[0];
                    _logger.LogInformation("Using store {StoreId} for imported products", targetStoreId);
                }

                if (file == null || file.Length == 0)
                {
                    return BadRequest("No file uploaded");
                }

                // Validate file type
                var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
                if (fileExtension != ".xlsx")
                {
                    return BadRequest("Invalid file type. Only Excel (.xlsx) files are allowed.");
                }

                using (var stream = new MemoryStream())
                {
                    await file.CopyToAsync(stream);
                    using (var package = new ExcelPackage(stream))
                    {
                        var worksheet = package.Workbook.Worksheets[0];
                        var rowCount = worksheet.Dimension.Rows;

                        var results = new List<object>();
                        var errors = new List<string>();
                        var successCount = 0;

                        // Start from row 2 (skip header)
                        for (int row = 2; row <= rowCount; row++)
                        {
                            try
                            {
                                // Skip empty rows
                                if (worksheet.Cells[row, 1].Value == null)
                                {
                                    continue;
                                }

                                // Extract data from Excel
                                var name = worksheet.Cells[row, 1].Value?.ToString();
                                var description = worksheet.Cells[row, 2].Value?.ToString();
                                var priceStr = worksheet.Cells[row, 3].Value?.ToString();
                                var sku = worksheet.Cells[row, 4].Value?.ToString();
                                var barcode = worksheet.Cells[row, 5].Value?.ToString();
                                var weightStr = worksheet.Cells[row, 6].Value?.ToString();
                                var weightUnit = worksheet.Cells[row, 7].Value?.ToString();
                                var costStr = worksheet.Cells[row, 8].Value?.ToString();
                                var categoryIdStr = worksheet.Cells[row, 9].Value?.ToString();
                                var isActiveStr = worksheet.Cells[row, 10].Value?.ToString();
                                var isFeaturedStr = worksheet.Cells[row, 11].Value?.ToString();
                                var stockQuantityStr = worksheet.Cells[row, 12].Value?.ToString();
                                var imageUrl = worksheet.Cells[row, 13].Value?.ToString();
                                var fabric = worksheet.Cells[row, 14].Value?.ToString();
                                var type = worksheet.Cells[row, 15].Value?.ToString();
                                var customAttributesJson = worksheet.Cells[row, 16].Value?.ToString();

                                // Validate required fields
                                if (string.IsNullOrEmpty(name) || string.IsNullOrEmpty(description) ||
                                    string.IsNullOrEmpty(priceStr) || string.IsNullOrEmpty(sku) ||
                                    string.IsNullOrEmpty(categoryIdStr) || string.IsNullOrEmpty(stockQuantityStr))
                                {
                                    errors.Add($"Row {row}: Missing required fields");
                                    continue;
                                }

                                // Parse numeric values
                                if (!decimal.TryParse(priceStr, out decimal price))
                                {
                                    errors.Add($"Row {row}: Invalid price format");
                                    continue;
                                }

                                if (!int.TryParse(categoryIdStr, out int categoryId))
                                {
                                    errors.Add($"Row {row}: Invalid category ID");
                                    continue;
                                }

                                if (!int.TryParse(stockQuantityStr, out int stockQuantity))
                                {
                                    errors.Add($"Row {row}: Invalid stock quantity");
                                    continue;
                                }

                                decimal weight = 0;
                                if (!string.IsNullOrEmpty(weightStr) && !decimal.TryParse(weightStr, out weight))
                                {
                                    errors.Add($"Row {row}: Invalid weight format");
                                    continue;
                                }

                                decimal cost = 0;
                                if (!string.IsNullOrEmpty(costStr) && !decimal.TryParse(costStr, out cost))
                                {
                                    errors.Add($"Row {row}: Invalid cost format");
                                    continue;
                                }

                                bool isActive = true;
                                if (!string.IsNullOrEmpty(isActiveStr) && !bool.TryParse(isActiveStr, out isActive))
                                {
                                    errors.Add($"Row {row}: Invalid IsActive value (should be TRUE or FALSE)");
                                    continue;
                                }

                                bool isFeatured = false;
                                if (!string.IsNullOrEmpty(isFeaturedStr) && !bool.TryParse(isFeaturedStr, out isFeatured))
                                {
                                    errors.Add($"Row {row}: Invalid IsFeatured value (should be TRUE or FALSE)");
                                    continue;
                                }

                                // Validate category exists
                                var categoryExists = await _context.Categories.AnyAsync(c => c.Id == categoryId);
                                if (!categoryExists)
                                {
                                    errors.Add($"Row {row}: Category with ID {categoryId} does not exist");
                                    continue;
                                }

                                // Check if product with SKU already exists
                                var existingProduct = await _context.ProductVariants.FirstOrDefaultAsync(x=> x.SKU==sku);
                                if (existingProduct != null)
                                {
                                    errors.Add($"Row {row}: Product with SKU {sku} already exists");
                                    continue;
                                }

                                // Create new product
                                var product = new Product
                                {
                                    Name = name,
                                    Description = description,
                                    Barcode = barcode ?? string.Empty,
                                    CategoryId = categoryId,
                                    IsActive = isActive,
                                    IsFeatured = isFeatured,
                                    CustomAttributes = GetCustomAttributesJson(fabric, type, customAttributesJson),
                                    ImageUrl = imageUrl ?? string.Empty,
                                    StoreId = targetStoreId, // Set the store ID based on user's access
                                    CreatedAt = DateTime.UtcNow,
                                    UpdatedAt = DateTime.UtcNow
                                };

                                // Add product to database
                                _context.Products.Add(product);
                                await _context.SaveChangesAsync();

                                // Create default variant
                                var variant = new ProductVariant
                                {
                                    ProductId = product.Id,
                                    SKU = sku,
                                    Price = price,
                                    Cost = cost,
                                    MRP = price * 1.2m, // Default MRP
                                    IsActive = true,
                                    Weight = 0.5m,
                                    WeightUnit = "kg"
                                };

                                _context.ProductVariants.Add(variant);
                                await _context.SaveChangesAsync(); // Save to get the variant ID

                                // Create inventory record
                                // Create inventory item for the default location
                                var defaultLocation = await _context.Locations.FirstOrDefaultAsync();
                                if (defaultLocation == null)
                                {
                                    // Create a default location if none exists
                                    defaultLocation = new Location
                                    {
                                        Code = "MAIN",
                                        Name = "Main Warehouse",
                                        Address = "Main Address",
                                        IsActive = true
                                    };
                                    _context.Locations.Add(defaultLocation);
                                    await _context.SaveChangesAsync();
                                }

                                var inventoryItem = new InventoryItem
                                {
                                    LocationId = defaultLocation.Id,
                                    ProductVariantId = variant.Id,
                                    StockQuantity = stockQuantity,
                                    ReorderLevel = 10, // Default value
                                    LastRestockedAt = DateTime.UtcNow,
                                    CreatedAt = DateTime.UtcNow,
                                    UpdatedAt = DateTime.UtcNow
                                };

                                _context.InventoryItems.Add(inventoryItem);
                                await _context.SaveChangesAsync();

                                // Add main image if provided
                                if (!string.IsNullOrEmpty(imageUrl))
                                {
                                    var productImage = new ProductImage
                                    {
                                        ProductId = product.Id,
                                        ImageUrl = imageUrl,
                                        IsMain = true,
                                        DisplayOrder = 0
                                    };

                                    _context.ProductImages.Add(productImage);
                                    await _context.SaveChangesAsync();
                                }

                                successCount++;
                                results.Add(new { Id = product.Id, Name = product.Name, SKU = sku });
                            }
                            catch (Exception ex)
                            {
                                errors.Add($"Row {row}: {ex.Message}");
                            }
                        }

                        return Ok(new
                        {
                            Success = successCount > 0,
                            Message = $"Imported {successCount} products successfully",
                            ImportedProducts = results,
                            Errors = errors
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error importing products");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error importing products");
            }
        }

        private static string GetCustomAttributesJson(string? fabric, string? type, string? existingJson)
        {
            var attributes = new Dictionary<string, string>();

            // Try to parse existing JSON if provided
            if (!string.IsNullOrEmpty(existingJson))
            {
                try
                {
                    var parsedAttributes = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(existingJson);
                    if (parsedAttributes != null)
                    {
                        attributes = parsedAttributes;
                    }
                }
                catch
                {
                    // If parsing fails, just ignore and create a new dictionary
                }
            }

            // Add or update fabric and type if provided
            if (!string.IsNullOrEmpty(fabric))
            {
                attributes["Fabric"] = fabric;
            }

            if (!string.IsNullOrEmpty(type))
            {
                attributes["Type"] = type;
            }

            // Serialize back to JSON
            return System.Text.Json.JsonSerializer.Serialize(attributes);
        }
    }
}
