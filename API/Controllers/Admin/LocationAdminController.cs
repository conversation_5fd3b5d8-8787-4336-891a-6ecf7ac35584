using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using MyShop.API.DTOs;
using System.Net;

namespace MyShop.API.Controllers.Admin
{
    [Route("api/admin/[controller]")]
    [ApiController]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public class LocationController : AdminController
    {
        public LocationController(
            UserManager<ApplicationUser> userManager,
            ApplicationDbContext context,
            ILogger<LocationController> logger)
            : base(userManager, context, logger)
        {
        }

        // GET: api/admin/location
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Location>>> GetLocations([FromQuery] int? storeId = null)
        {
            try
            {
                // Get accessible store IDs for the current user
                var accessibleStoreIds = await GetUserStoreIdsAsync();
                if (accessibleStoreIds.Length == 0)
                {
                    return Forbid();
                }

                // If storeId is specified, check if user has access to it
                if (storeId.HasValue && !accessibleStoreIds.Contains(storeId.Value))
                {
                    return Forbid();
                }

                // Start with all locations
                var query = _context.Locations.AsQueryable();

                // Filter by store
                if (storeId.HasValue)
                {
                    query = query.Where(l => l.StoreId == storeId.Value);
                }
                else
                {
                    // Filter by accessible stores
                    query = query.Where(l => accessibleStoreIds.Contains(l.StoreId));
                }

                var locations = await query.ToListAsync();
                return Ok(locations);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving locations");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error retrieving locations");
            }
        }

        // GET: api/admin/location/5
        [HttpGet("{id}")]
        public async Task<ActionResult<Location>> GetLocation(int id)
        {
            try
            {
                var location = await _context.Locations.FindAsync(id);

                if (location == null)
                {
                    return NotFound();
                }

                // Check if user has access to the store
                if (!await CanAccessStoreAsync(location.StoreId))
                {
                    return Forbid();
                }

                return Ok(location);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving location");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error retrieving location");
            }
        }

        // POST: api/admin/location
        [HttpPost]
        public async Task<ActionResult<Location>> CreateLocation(Location location)
        {
            try
            {
                // Check if user has access to the store
                if (!await CanAccessStoreAsync(location.StoreId))
                {
                    return Forbid();
                }

                location.CreatedAt = DateTime.UtcNow;
                location.UpdatedAt = DateTime.UtcNow;
                
                _context.Locations.Add(location);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetLocation), new { id = location.Id }, location);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating location");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error creating location");
            }
        }

        // PUT: api/admin/location/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateLocation(int id, Location location)
        {
            if (id != location.Id)
            {
                return BadRequest();
            }

            try
            {
                var existingLocation = await _context.Locations.FindAsync(id);
                if (existingLocation == null)
                {
                    return NotFound();
                }

                // Check if user has access to the store
                if (!await CanAccessStoreAsync(existingLocation.StoreId))
                {
                    return Forbid();
                }

                // Update properties
                existingLocation.Name = location.Name;
                existingLocation.Code = location.Code;
                existingLocation.Address = location.Address;
                existingLocation.IsActive = location.IsActive;
                existingLocation.UpdatedAt = DateTime.UtcNow;

                // Don't allow changing the store ID
                // existingLocation.StoreId = location.StoreId;

                await _context.SaveChangesAsync();
                return NoContent();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!LocationExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating location");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error updating location");
            }
        }

        // DELETE: api/admin/location/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteLocation(int id)
        {
            try
            {
                var location = await _context.Locations.FindAsync(id);
                if (location == null)
                {
                    return NotFound();
                }

                // Check if user has access to the store
                if (!await CanAccessStoreAsync(location.StoreId))
                {
                    return Forbid();
                }

                // Check if this location has inventory items
                var hasInventoryItems = await _context.InventoryItems.AnyAsync(i => i.LocationId == id);
                if (hasInventoryItems)
                {
                    return BadRequest("Cannot delete location with inventory items. Transfer inventory to another location first.");
                }

                _context.Locations.Remove(location);
                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting location");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error deleting location");
            }
        }

        // GET: api/admin/location/5/inventory
        [HttpGet("{locationId}/inventory")]
        public async Task<ActionResult<IEnumerable<InventoryItemDTO>>> GetLocationInventory(int locationId)
        {
            try
            {
                var location = await _context.Locations.FindAsync(locationId);
                if (location == null)
                {
                    return NotFound("Location not found");
                }

                // Check if user has access to the store
                if (!await CanAccessStoreAsync(location.StoreId))
                {
                    return Forbid();
                }

                var inventoryItems = await _context.InventoryItems
                    .Include(i => i.ProductVariant)
                        .ThenInclude(pv => pv.Product)
                    .Where(i => i.LocationId == locationId)
                    .ToListAsync();

                var inventoryItemDtos = inventoryItems.Select(i => new InventoryItemDTO
                {
                    Id = i.Id,
                    LocationId = i.LocationId,
                    LocationName = location.Name,
                    LocationCode = location.Code,
                    StockQuantity = i.StockQuantity,
                    ReorderLevel = i.ReorderLevel,
                    LastRestockedAt = i.LastRestockedAt
                }).ToList();

                return Ok(inventoryItemDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving location inventory");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error retrieving location inventory");
            }
        }

        // POST: api/admin/location/transfer
        [HttpPost("transfer")]
        public async Task<IActionResult> TransferInventory(InventoryTransferDTO transferDto)
        {
            try
            {
                // Validate source and destination locations
                var sourceLocation = await _context.Locations.FindAsync(transferDto.SourceLocationId);
                if (sourceLocation == null)
                {
                    return NotFound("Source location not found");
                }

                var destinationLocation = await _context.Locations.FindAsync(transferDto.DestinationLocationId);
                if (destinationLocation == null)
                {
                    return NotFound("Destination location not found");
                }

                // Check if user has access to both stores
                if (!await CanAccessStoreAsync(sourceLocation.StoreId) || !await CanAccessStoreAsync(destinationLocation.StoreId))
                {
                    return Forbid();
                }

                // Find the source inventory item
                var sourceInventoryItem = await _context.InventoryItems
                    .FirstOrDefaultAsync(i => i.LocationId == transferDto.SourceLocationId && 
                                             i.ProductVariantId == transferDto.ProductVariantId);
                
                if (sourceInventoryItem == null)
                {
                    return NotFound("Source inventory item not found");
                }

                // Check if there's enough quantity to transfer
                if (sourceInventoryItem.StockQuantity < transferDto.Quantity)
                {
                    return BadRequest("Not enough quantity available to transfer");
                }

                // Find or create destination inventory item
                var destinationInventoryItem = await _context.InventoryItems
                    .FirstOrDefaultAsync(i => i.LocationId == transferDto.DestinationLocationId && 
                                             i.ProductVariantId == transferDto.ProductVariantId);

                if (destinationInventoryItem == null)
                {
                    // Create new inventory item at destination
                    destinationInventoryItem = new InventoryItem
                    {
                        LocationId = transferDto.DestinationLocationId,
                        ProductVariantId = transferDto.ProductVariantId,
                        StockQuantity = 0,
                        ReorderLevel = sourceInventoryItem.ReorderLevel,
                        LastRestockedAt = DateTime.UtcNow,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };
                    _context.InventoryItems.Add(destinationInventoryItem);
                }

                // Update quantities
                sourceInventoryItem.StockQuantity -= transferDto.Quantity;
                sourceInventoryItem.UpdatedAt = DateTime.UtcNow;

                destinationInventoryItem.StockQuantity += transferDto.Quantity;
                destinationInventoryItem.LastRestockedAt = DateTime.UtcNow;
                destinationInventoryItem.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return Ok(new { Message = "Inventory transferred successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error transferring inventory");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error transferring inventory");
            }
        }

        private bool LocationExists(int id)
        {
            return _context.Locations.Any(e => e.Id == id);
        }
    }
}
