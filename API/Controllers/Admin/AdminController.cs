using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyShop.API.Data;
using MyShop.API.Data.Entities;

namespace MyShop.API.Controllers.Admin
{
    [Route("api/admin/[controller]")]
    [ApiController]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public abstract class AdminController : ControllerBase
    {
        protected readonly UserManager<ApplicationUser> _userManager;
        protected readonly ApplicationDbContext _context;
        protected readonly ILogger _logger;

        public AdminController(UserManager<ApplicationUser> userManager, ApplicationDbContext context, ILogger logger)
        {
            _userManager = userManager;
            _context = context;
            _logger = logger;
        }

        protected async Task<bool> IsSuperAdminAsync()
        {
            var user = await _userManager.GetUserAsync(User);
            return user != null && (user.IsSuperAdmin || await _userManager.IsInRoleAsync(user, "SuperAdmin"));
        }

        protected async Task<ApplicationUser> GetCurrentUserAsync()
        {
            return await _userManager.GetUserAsync(User);
        }

        protected async Task<int[]> GetUserStoreIdsAsync()
        {
            var user = await _userManager.GetUserAsync(User);

            if (user == null)
            {
                _logger.LogWarning("No authenticated user found when trying to get store IDs");
                return Array.Empty<int>();
            }

            // Super admin can access all stores
            if (user.IsSuperAdmin || await _userManager.IsInRoleAsync(user, "SuperAdmin"))
            {
                var allStoreIds = await _context.Stores.Select(s => s.Id).ToArrayAsync();
                _logger.LogInformation("User {UserId} is a super admin, has access to {StoreCount} stores", user.Id, allStoreIds.Length);
                return allStoreIds;
            }

            // Store admin can only access their assigned stores
            var storeIds = await _context.StoreAdmins
                .Where(sa => sa.UserId == user.Id)
                .Select(sa => sa.StoreId)
                .ToArrayAsync();

            _logger.LogInformation("User {UserId} has access to {StoreCount} stores", user.Id, storeIds.Length);
            return storeIds;
        }

        protected async Task<bool> CanAccessStoreAsync(int storeId)
        {
            try
            {
                // First check if the store exists
                var storeExists = await _context.Stores.AnyAsync(s => s.Id == storeId);
                if (!storeExists)
                {
                    _logger.LogWarning("Attempted to access non-existent store with ID {StoreId}", storeId);
                    return false;
                }

                var user = await _userManager.GetUserAsync(User);
                if (user == null)
                {
                    _logger.LogWarning("No authenticated user found when checking store access");
                    return false;
                }

                // Super admin can access all stores
                if (user.IsSuperAdmin || await _userManager.IsInRoleAsync(user, "SuperAdmin"))
                {
                    _logger.LogInformation("Super admin {UserId} granted access to store {StoreId}", user.Id, storeId);
                    return true;
                }

                // Check if user is admin for this store
                var hasAccess = await _context.StoreAdmins
                    .AnyAsync(sa => sa.UserId == user.Id && sa.StoreId == storeId);

                if (!hasAccess)
                {
                    _logger.LogWarning("User {UserId} attempted to access unauthorized store {StoreId}", user.Id, storeId);
                }
                else
                {
                    _logger.LogInformation("User {UserId} granted access to store {StoreId}", user.Id, storeId);
                }

                return hasAccess;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking store access for storeId: {StoreId}", storeId);
                return false;
            }
        }

        protected IQueryable<T> FilterByUserStores<T>(IQueryable<T> query, int[] storeIds) where T : class, IStoreEntity
        {
            return query.Where(e => storeIds.Contains(e.StoreId));
        }

        protected async Task<IActionResult> CheckStoreAccessAsync(int storeId)
        {
            if (!await CanAccessStoreAsync(storeId))
            {
                _logger.LogWarning("Access denied to store {StoreId}", storeId);
                return Forbid();
            }

            return null;
        }
    }
}
