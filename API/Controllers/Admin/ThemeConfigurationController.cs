using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using MyShop.API.DTOs;

namespace MyShop.API.Controllers.Admin
{
    [Route("api/admin/[controller]")]
    [ApiController]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public class ThemeConfigurationController : AdminController
    {
        public ThemeConfigurationController(
            UserManager<ApplicationUser> userManager,
            ApplicationDbContext context,
            ILogger<ThemeConfigurationController> logger)
            : base(userManager, context, logger)
        {
        }

        [HttpGet("store/{storeId}")]
        public async Task<ActionResult<IEnumerable<ThemeConfigurationDTO>>> GetThemeConfigurations(int storeId)
        {
            try
            {
                if (!await CanAccessStoreAsync(storeId))
                    return Forbid();

                var themes = await _context.ThemeConfigurations
                    .Where(t => t.StoreId == storeId)
                    .ToListAsync();

                return Ok(themes.Select(t => MapToDTO(t)));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting theme configurations for store {StoreId}", storeId);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<ThemeConfigurationDTO>> GetThemeConfiguration(int id)
        {
            try
            {
                var theme = await _context.ThemeConfigurations.FindAsync(id);

                if (theme == null)
                    return NotFound();

                if (!await CanAccessStoreAsync(theme.StoreId))
                    return Forbid();

                return MapToDTO(theme);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting theme configuration {Id}", id);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        [HttpGet("store/{storeId}/active")]
        public async Task<ActionResult<ThemeConfigurationDTO>> GetActiveThemeConfiguration(int storeId)
        {
            try
            {
                if (!await CanAccessStoreAsync(storeId))
                    return Forbid();

                var theme = await _context.ThemeConfigurations
                    .FirstOrDefaultAsync(t => t.StoreId == storeId && t.IsActive);

                if (theme == null)
                    return NotFound();

                return MapToDTO(theme);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active theme configuration for store {StoreId}", storeId);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        [HttpPost]
        public async Task<ActionResult<ThemeConfigurationDTO>> CreateThemeConfiguration(CreateThemeConfigurationDTO dto)
        {
            try
            {
                if (!await CanAccessStoreAsync(dto.StoreId))
                    return Forbid();

                // Check if store exists
                var store = await _context.Stores.FindAsync(dto.StoreId);
                if (store == null)
                    return BadRequest("Store not found");

                // If this is the first theme for the store, make it active
                var existingThemesCount = await _context.ThemeConfigurations
                    .CountAsync(t => t.StoreId == dto.StoreId);

                var isFirstTheme = existingThemesCount == 0;

                var theme = new ThemeConfiguration
                {
                    StoreId = dto.StoreId,
                    Name = dto.Name,
                    Description = dto.Description,
                    IsActive = isFirstTheme || dto.IsActive,
                    PrimaryColor = dto.PrimaryColor,
                    SecondaryColor = dto.SecondaryColor,
                    AccentColor = dto.AccentColor,
                    TextPrimaryColor = dto.TextPrimaryColor,
                    TextSecondaryColor = dto.TextSecondaryColor,
                    TextLightColor = dto.TextLightColor,
                    BackgroundPrimaryColor = dto.BackgroundPrimaryColor,
                    BackgroundSecondaryColor = dto.BackgroundSecondaryColor,
                    BackgroundAccentColor = dto.BackgroundAccentColor,
                    ButtonPrimaryColor = dto.ButtonPrimaryColor,
                    ButtonSecondaryColor = dto.ButtonSecondaryColor,
                    ButtonTextColor = dto.ButtonTextColor,
                    ButtonBorderRadius = dto.ButtonBorderRadius,
                    CardBackgroundColor = dto.CardBackgroundColor,
                    CardBorderColor = dto.CardBorderColor,
                    CardBorderRadius = dto.CardBorderRadius,
                    CardShadow = dto.CardShadow,
                    HeadingFontFamily = dto.HeadingFontFamily,
                    BodyFontFamily = dto.BodyFontFamily,
                    FontBaseSize = dto.FontBaseSize,
                    SpacingUnit = dto.SpacingUnit,
                    ContainerMaxWidth = dto.ContainerMaxWidth,
                    ContainerPadding = dto.ContainerPadding,
                    HeaderBackgroundColor = dto.HeaderBackgroundColor,
                    HeaderTextColor = dto.HeaderTextColor,
                    HeaderHeight = dto.HeaderHeight,
                    FooterBackgroundColor = dto.FooterBackgroundColor,
                    FooterTextColor = dto.FooterTextColor,
                    NavLinkColor = dto.NavLinkColor,
                    NavLinkActiveColor = dto.NavLinkActiveColor,
                    NavLinkHoverColor = dto.NavLinkHoverColor,
                    InputBackgroundColor = dto.InputBackgroundColor,
                    InputBorderColor = dto.InputBorderColor,
                    InputBorderRadius = dto.InputBorderRadius,
                    InputFocusBorderColor = dto.InputFocusBorderColor,
                    CustomCSS = dto.CustomCSS,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.ThemeConfigurations.Add(theme);

                // If this theme is active, deactivate all other themes for this store
                if (theme.IsActive)
                {
                    var otherThemes = await _context.ThemeConfigurations
                        .Where(t => t.StoreId == dto.StoreId && t.IsActive)
                        .ToListAsync();

                    foreach (var otherTheme in otherThemes)
                    {
                        otherTheme.IsActive = false;
                        otherTheme.UpdatedAt = DateTime.UtcNow;
                    }
                }

                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetThemeConfiguration), new { id = theme.Id }, MapToDTO(theme));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating theme configuration");
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateThemeConfiguration(int id, UpdateThemeConfigurationDTO dto)
        {
            try
            {
                var theme = await _context.ThemeConfigurations.FindAsync(id);

                if (theme == null)
                    return NotFound();

                if (!await CanAccessStoreAsync(theme.StoreId))
                    return Forbid();

                // Update theme properties
                theme.Name = dto.Name;
                theme.Description = dto.Description;
                theme.PrimaryColor = dto.PrimaryColor;
                theme.SecondaryColor = dto.SecondaryColor;
                theme.AccentColor = dto.AccentColor;
                theme.TextPrimaryColor = dto.TextPrimaryColor;
                theme.TextSecondaryColor = dto.TextSecondaryColor;
                theme.TextLightColor = dto.TextLightColor;
                theme.BackgroundPrimaryColor = dto.BackgroundPrimaryColor;
                theme.BackgroundSecondaryColor = dto.BackgroundSecondaryColor;
                theme.BackgroundAccentColor = dto.BackgroundAccentColor;
                theme.ButtonPrimaryColor = dto.ButtonPrimaryColor;
                theme.ButtonSecondaryColor = dto.ButtonSecondaryColor;
                theme.ButtonTextColor = dto.ButtonTextColor;
                theme.ButtonBorderRadius = dto.ButtonBorderRadius;
                theme.CardBackgroundColor = dto.CardBackgroundColor;
                theme.CardBorderColor = dto.CardBorderColor;
                theme.CardBorderRadius = dto.CardBorderRadius;
                theme.CardShadow = dto.CardShadow;
                theme.HeadingFontFamily = dto.HeadingFontFamily;
                theme.BodyFontFamily = dto.BodyFontFamily;
                theme.FontBaseSize = dto.FontBaseSize;
                theme.SpacingUnit = dto.SpacingUnit;
                theme.ContainerMaxWidth = dto.ContainerMaxWidth;
                theme.ContainerPadding = dto.ContainerPadding;
                theme.HeaderBackgroundColor = dto.HeaderBackgroundColor;
                theme.HeaderTextColor = dto.HeaderTextColor;
                theme.HeaderHeight = dto.HeaderHeight;
                theme.FooterBackgroundColor = dto.FooterBackgroundColor;
                theme.FooterTextColor = dto.FooterTextColor;
                theme.NavLinkColor = dto.NavLinkColor;
                theme.NavLinkActiveColor = dto.NavLinkActiveColor;
                theme.NavLinkHoverColor = dto.NavLinkHoverColor;
                theme.InputBackgroundColor = dto.InputBackgroundColor;
                theme.InputBorderColor = dto.InputBorderColor;
                theme.InputBorderRadius = dto.InputBorderRadius;
                theme.InputFocusBorderColor = dto.InputFocusBorderColor;
                theme.CustomCSS = dto.CustomCSS;
                theme.UpdatedAt = DateTime.UtcNow;

                // Handle activation status change
                if (dto.IsActive && !theme.IsActive)
                {
                    // Activating this theme, deactivate all others
                    var otherThemes = await _context.ThemeConfigurations
                        .Where(t => t.StoreId == theme.StoreId && t.Id != theme.Id && t.IsActive)
                        .ToListAsync();

                    foreach (var otherTheme in otherThemes)
                    {
                        otherTheme.IsActive = false;
                        otherTheme.UpdatedAt = DateTime.UtcNow;
                    }

                    theme.IsActive = true;
                }
                else if (!dto.IsActive && theme.IsActive)
                {
                    // Check if this is the only active theme
                    var activeThemesCount = await _context.ThemeConfigurations
                        .CountAsync(t => t.StoreId == theme.StoreId && t.IsActive);

                    if (activeThemesCount <= 1)
                    {
                        return BadRequest("Cannot deactivate the only active theme");
                    }

                    theme.IsActive = false;
                }

                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating theme configuration {Id}", id);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        [HttpPost("{id}/activate")]
        public async Task<IActionResult> ActivateThemeConfiguration(int id)
        {
            try
            {
                var theme = await _context.ThemeConfigurations.FindAsync(id);

                if (theme == null)
                    return NotFound();

                if (!await CanAccessStoreAsync(theme.StoreId))
                    return Forbid();

                if (theme.IsActive)
                    return NoContent(); // Already active

                // Deactivate all other themes for this store
                var otherThemes = await _context.ThemeConfigurations
                    .Where(t => t.StoreId == theme.StoreId && t.Id != theme.Id && t.IsActive)
                    .ToListAsync();

                foreach (var otherTheme in otherThemes)
                {
                    otherTheme.IsActive = false;
                    otherTheme.UpdatedAt = DateTime.UtcNow;
                }

                theme.IsActive = true;
                theme.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error activating theme configuration {Id}", id);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteThemeConfiguration(int id)
        {
            try
            {
                var theme = await _context.ThemeConfigurations.FindAsync(id);

                if (theme == null)
                    return NotFound();

                if (!await CanAccessStoreAsync(theme.StoreId))
                    return Forbid();

                // Cannot delete active theme
                if (theme.IsActive)
                    return BadRequest("Cannot delete the active theme");

                _context.ThemeConfigurations.Remove(theme);
                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting theme configuration {Id}", id);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        [HttpPost("{id}/duplicate")]
        public async Task<ActionResult<ThemeConfigurationDTO>> DuplicateThemeConfiguration(int id)
        {
            try
            {
                var theme = await _context.ThemeConfigurations.FindAsync(id);

                if (theme == null)
                    return NotFound();

                if (!await CanAccessStoreAsync(theme.StoreId))
                    return Forbid();

                var newTheme = new ThemeConfiguration
                {
                    StoreId = theme.StoreId,
                    Name = $"{theme.Name} (Copy)",
                    Description = theme.Description,
                    IsActive = false,
                    PrimaryColor = theme.PrimaryColor,
                    SecondaryColor = theme.SecondaryColor,
                    AccentColor = theme.AccentColor,
                    TextPrimaryColor = theme.TextPrimaryColor,
                    TextSecondaryColor = theme.TextSecondaryColor,
                    TextLightColor = theme.TextLightColor,
                    BackgroundPrimaryColor = theme.BackgroundPrimaryColor,
                    BackgroundSecondaryColor = theme.BackgroundSecondaryColor,
                    BackgroundAccentColor = theme.BackgroundAccentColor,
                    ButtonPrimaryColor = theme.ButtonPrimaryColor,
                    ButtonSecondaryColor = theme.ButtonSecondaryColor,
                    ButtonTextColor = theme.ButtonTextColor,
                    ButtonBorderRadius = theme.ButtonBorderRadius,
                    CardBackgroundColor = theme.CardBackgroundColor,
                    CardBorderColor = theme.CardBorderColor,
                    CardBorderRadius = theme.CardBorderRadius,
                    CardShadow = theme.CardShadow,
                    HeadingFontFamily = theme.HeadingFontFamily,
                    BodyFontFamily = theme.BodyFontFamily,
                    FontBaseSize = theme.FontBaseSize,
                    SpacingUnit = theme.SpacingUnit,
                    ContainerMaxWidth = theme.ContainerMaxWidth,
                    ContainerPadding = theme.ContainerPadding,
                    HeaderBackgroundColor = theme.HeaderBackgroundColor,
                    HeaderTextColor = theme.HeaderTextColor,
                    HeaderHeight = theme.HeaderHeight,
                    FooterBackgroundColor = theme.FooterBackgroundColor,
                    FooterTextColor = theme.FooterTextColor,
                    NavLinkColor = theme.NavLinkColor,
                    NavLinkActiveColor = theme.NavLinkActiveColor,
                    NavLinkHoverColor = theme.NavLinkHoverColor,
                    InputBackgroundColor = theme.InputBackgroundColor,
                    InputBorderColor = theme.InputBorderColor,
                    InputBorderRadius = theme.InputBorderRadius,
                    InputFocusBorderColor = theme.InputFocusBorderColor,
                    CustomCSS = theme.CustomCSS,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.ThemeConfigurations.Add(newTheme);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetThemeConfiguration), new { id = newTheme.Id }, MapToDTO(newTheme));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error duplicating theme configuration {Id}", id);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        private ThemeConfigurationDTO MapToDTO(ThemeConfiguration theme)
        {
            return new ThemeConfigurationDTO
            {
                Id = theme.Id,
                StoreId = theme.StoreId,
                Name = theme.Name,
                Description = theme.Description,
                IsActive = theme.IsActive,
                PrimaryColor = theme.PrimaryColor,
                SecondaryColor = theme.SecondaryColor,
                AccentColor = theme.AccentColor,
                TextPrimaryColor = theme.TextPrimaryColor,
                TextSecondaryColor = theme.TextSecondaryColor,
                TextLightColor = theme.TextLightColor,
                BackgroundPrimaryColor = theme.BackgroundPrimaryColor,
                BackgroundSecondaryColor = theme.BackgroundSecondaryColor,
                BackgroundAccentColor = theme.BackgroundAccentColor,
                ButtonPrimaryColor = theme.ButtonPrimaryColor,
                ButtonSecondaryColor = theme.ButtonSecondaryColor,
                ButtonTextColor = theme.ButtonTextColor,
                ButtonBorderRadius = theme.ButtonBorderRadius,
                CardBackgroundColor = theme.CardBackgroundColor,
                CardBorderColor = theme.CardBorderColor,
                CardBorderRadius = theme.CardBorderRadius,
                CardShadow = theme.CardShadow,
                HeadingFontFamily = theme.HeadingFontFamily,
                BodyFontFamily = theme.BodyFontFamily,
                FontBaseSize = theme.FontBaseSize,
                SpacingUnit = theme.SpacingUnit,
                ContainerMaxWidth = theme.ContainerMaxWidth,
                ContainerPadding = theme.ContainerPadding,
                HeaderBackgroundColor = theme.HeaderBackgroundColor,
                HeaderTextColor = theme.HeaderTextColor,
                HeaderHeight = theme.HeaderHeight,
                FooterBackgroundColor = theme.FooterBackgroundColor,
                FooterTextColor = theme.FooterTextColor,
                NavLinkColor = theme.NavLinkColor,
                NavLinkActiveColor = theme.NavLinkActiveColor,
                NavLinkHoverColor = theme.NavLinkHoverColor,
                InputBackgroundColor = theme.InputBackgroundColor,
                InputBorderColor = theme.InputBorderColor,
                InputBorderRadius = theme.InputBorderRadius,
                InputFocusBorderColor = theme.InputFocusBorderColor,
                CustomCSS = theme.CustomCSS,
                CreatedAt = theme.CreatedAt,
                UpdatedAt = theme.UpdatedAt
            };
        }
    }
}
