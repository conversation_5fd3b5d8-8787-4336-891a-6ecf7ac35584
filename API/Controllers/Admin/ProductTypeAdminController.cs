using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using MyShop.API.DTOs;

namespace MyShop.API.Controllers.Admin
{
    [Route("api/[controller]")]
    [ApiController]
    public class ProductTypeController : AdminController
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<ProductTypeController> _logger;

        public ProductTypeController(UserManager<ApplicationUser> userManager, ApplicationDbContext context, ILogger<ProductTypeController> logger)
            : base(userManager, context, logger)
        {
            _context = context;
            _logger = logger;
        }

        // GET: api/ProductType
        [HttpGet]
        public async Task<ActionResult<IEnumerable<ProductTypeDTO>>> GetProductTypes()
        {
            try
            {
                var productTypes = await _context.ProductTypes
                    .OrderBy(pt => pt.Name)
                    .ToListAsync();

                var productTypeDtos = productTypes.Select(pt => new ProductTypeDTO
                {
                    Id = pt.Id,
                    Name = pt.Name,
                    Description = pt.Description
                }).ToList();

                return Ok(productTypeDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting product types");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error getting product types");
            }
        }

        // GET: api/ProductType/5
        [HttpGet("{id}")]
        public async Task<ActionResult<ProductTypeDTO>> GetProductType(int id)
        {
            try
            {
                var productType = await _context.ProductTypes.FindAsync(id);

                if (productType == null)
                {
                    return NotFound();
                }

                var productTypeDto = new ProductTypeDTO
                {
                    Id = productType.Id,
                    Name = productType.Name,
                    Description = productType.Description
                };

                return Ok(productTypeDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting product type");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error getting product type");
            }
        }

        // POST: api/ProductType
        [HttpPost]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ProductTypeDTO>> CreateProductType(ProductTypeDTO productTypeDto)
        {
            try
            {
                var productType = new ProductType
                {
                    Name = productTypeDto.Name,
                    Description = productTypeDto.Description,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.ProductTypes.Add(productType);
                await _context.SaveChangesAsync();

                productTypeDto.Id = productType.Id;

                return CreatedAtAction(nameof(GetProductType), new { id = productType.Id }, productTypeDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating product type");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error creating product type");
            }
        }

        // PUT: api/ProductType/5
        [HttpPut("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> UpdateProductType(int id, ProductTypeDTO productTypeDto)
        {
            if (id != productTypeDto.Id)
            {
                return BadRequest();
            }

            try
            {
                var productType = await _context.ProductTypes.FindAsync(id);

                if (productType == null)
                {
                    return NotFound();
                }

                productType.Name = productTypeDto.Name;
                productType.Description = productTypeDto.Description;
                productType.UpdatedAt = DateTime.UtcNow;

                _context.Entry(productType).State = EntityState.Modified;
                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!ProductTypeExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating product type");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error updating product type");
            }
        }

        // DELETE: api/ProductType/5
        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteProductType(int id)
        {
            try
            {
                var productType = await _context.ProductTypes.FindAsync(id);
                if (productType == null)
                {
                    return NotFound();
                }

                // Check if any products are using this product type
                var productsUsingType = await _context.Products
                    .Where(p => p.ProductTypeId == id)
                    .AnyAsync();

                if (productsUsingType)
                {
                    return BadRequest("Cannot delete product type because it is being used by one or more products");
                }

                _context.ProductTypes.Remove(productType);
                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting product type");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error deleting product type");
            }
        }

        private bool ProductTypeExists(int id)
        {
            return _context.ProductTypes.Any(e => e.Id == id);
        }
    }
}
