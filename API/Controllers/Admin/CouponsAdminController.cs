using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using MyShop.API.DTOs;
using System.Net;

namespace MyShop.API.Controllers.Admin
{
    [Route("api/admin/coupons")]
    [ApiController]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public class CouponsAdminController : AdminController
    {
        public CouponsAdminController(
            UserManager<ApplicationUser> userManager,
            ApplicationDbContext context,
            ILogger<CouponsAdminController> logger)
            : base(userManager, context, logger)
        {
        }

        // GET: api/admin/coupons
        [HttpGet]
        public async Task<ActionResult<PagedResult<CouponDTO>>> GetCoupons(
            [FromQuery] int? storeId = null,
            [FromQuery] string? searchTerm = null,
            [FromQuery] string? status = null,
            [FromQuery] string? type = null,
            [FromQuery] string? applicability = null,
            [FromQuery] bool? displayOnCartPage = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10,
            [FromQuery] string sortBy = "CreatedAt",
            [FromQuery] string sortOrder = "desc")
        {
            try
            {
                // Get accessible store IDs for the current user
                var accessibleStoreIds = await GetUserStoreIdsAsync();
                if (accessibleStoreIds.Length == 0)
                {
                    return Forbid();
                }

                // If storeId is specified, check if user has access to it
                if (storeId.HasValue && !accessibleStoreIds.Contains(storeId.Value))
                {
                    return Forbid();
                }

                // Start with all coupons
                IQueryable<Coupon> query = _context.Coupons
                    .Include(c => c.Category)
                    .Include(c => c.Collection)
                    .Include(c => c.Product);

                // Filter by store if specified
                if (storeId.HasValue)
                {
                    query = query.Where(c => c.StoreId == storeId.Value);
                }
                else
                {
                    // Filter by accessible stores
                    query = query.Where(c => accessibleStoreIds.Contains(c.StoreId));
                }

                // Apply search filter
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    query = query.Where(c =>
                        c.Code.Contains(searchTerm) ||
                        c.Description.Contains(searchTerm));
                }

                // Apply status filter
                if (!string.IsNullOrEmpty(status))
                {
                    var now = DateTime.UtcNow;
                    switch (status.ToLower())
                    {
                        case "active":
                            query = query.Where(c =>
                                c.IsActive &&
                                c.StartDate <= now &&
                                (!c.EndDate.HasValue || c.EndDate >= now) &&
                                (c.UsageLimit == 0 || c.UsageCount < c.UsageLimit));
                            break;
                        case "inactive":
                            query = query.Where(c => !c.IsActive);
                            break;
                        case "expired":
                            query = query.Where(c => c.EndDate.HasValue && c.EndDate < now);
                            break;
                        case "scheduled":
                            query = query.Where(c => c.StartDate > now);
                            break;
                        case "exhausted":
                            query = query.Where(c => c.UsageLimit > 0 && c.UsageCount >= c.UsageLimit);
                            break;
                    }
                }

                // Apply type filter
                if (!string.IsNullOrEmpty(type) && Enum.TryParse<CouponType>(type, true, out var couponType))
                {
                    query = query.Where(c => c.Type == couponType);
                }

                // Apply applicability filter
                if (!string.IsNullOrEmpty(applicability) && Enum.TryParse<CouponApplicability>(applicability, true, out var couponApplicability))
                {
                    query = query.Where(c => c.Applicability == couponApplicability);
                }

                // Apply display on cart page filter
                if (displayOnCartPage.HasValue)
                {
                    query = query.Where(c => c.DisplayOnCartPage == displayOnCartPage.Value);
                }

                // Get total count for pagination
                var totalCount = await query.CountAsync();

                // Apply sorting
                query = ApplySorting(query, sortBy, sortOrder);

                // Apply pagination
                var coupons = await query
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                // Map to DTOs
                var couponDtos = coupons.Select(c => new CouponDTO
                {
                    Id = c.Id,
                    Code = c.Code,
                    Description = c.Description,
                    Type = c.Type.ToString(),
                    Value = c.Value,
                    Applicability = c.Applicability.ToString(),
                    CategoryId = c.CategoryId,
                    CategoryName = c.Category?.Name,
                    CollectionId = c.CollectionId,
                    CollectionName = c.Collection?.Name,
                    ProductId = c.ProductId,
                    ProductName = c.Product?.Name,
                    MinimumPurchaseAmount = c.MinimumPurchaseAmount,
                    MaximumDiscountAmount = c.MaximumDiscountAmount,
                    MinimumQuantity = c.MinimumQuantity,
                    UsageLimit = c.UsageLimit,
                    UsageCount = c.UsageCount,
                    DisplayOnCartPage = c.DisplayOnCartPage,
                    StartDate = c.StartDate,
                    EndDate = c.EndDate,
                    IsActive = c.IsActive,
                    IsOneTimeUse = c.IsOneTimeUse,
                    StoreId = c.StoreId
                }).ToList();

                // Calculate total pages
                var totalPages = (int)Math.Ceiling(totalCount / (double)pageSize);

                // Create paged result
                var result = new PagedResult<CouponDTO>
                {
                    Items = couponDtos,
                    TotalCount = totalCount,
                    Page = page,
                    PageSize = pageSize,
                    TotalPages = totalPages
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving coupons");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error retrieving coupons");
            }
        }

        // GET: api/admin/coupons/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<CouponDTO>> GetCoupon(int id)
        {
            try
            {
                var coupon = await _context.Coupons
                    .Include(c => c.Category)
                    .Include(c => c.Collection)
                    .Include(c => c.Product)
                    .FirstOrDefaultAsync(c => c.Id == id);

                if (coupon == null)
                {
                    return NotFound();
                }

                // Check if user has access to the store
                if (!await CanAccessStoreAsync(coupon.StoreId))
                {
                    return Forbid();
                }

                var couponDto = new CouponDTO
                {
                    Id = coupon.Id,
                    Code = coupon.Code,
                    Description = coupon.Description,
                    Type = coupon.Type.ToString(),
                    Value = coupon.Value,
                    Applicability = coupon.Applicability.ToString(),
                    CategoryId = coupon.CategoryId,
                    CategoryName = coupon.Category?.Name,
                    CollectionId = coupon.CollectionId,
                    CollectionName = coupon.Collection?.Name,
                    ProductId = coupon.ProductId,
                    ProductName = coupon.Product?.Name,
                    MinimumPurchaseAmount = coupon.MinimumPurchaseAmount,
                    MaximumDiscountAmount = coupon.MaximumDiscountAmount,
                    MinimumQuantity = coupon.MinimumQuantity,
                    UsageLimit = coupon.UsageLimit,
                    UsageCount = coupon.UsageCount,
                    DisplayOnCartPage = coupon.DisplayOnCartPage,
                    StartDate = coupon.StartDate,
                    EndDate = coupon.EndDate,
                    IsActive = coupon.IsActive,
                    IsOneTimeUse = coupon.IsOneTimeUse,
                    StoreId = coupon.StoreId
                };

                return Ok(couponDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving coupon");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error retrieving coupon");
            }
        }

        // POST: api/admin/coupons
        [HttpPost]
        public async Task<ActionResult<CouponDTO>> CreateCoupon(CreateCouponDTO couponDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Check if user has access to the store
                if (!await CanAccessStoreAsync(couponDto.StoreId))
                {
                    return Forbid();
                }

                // Check if code already exists
                var codeExists = await _context.Coupons.AnyAsync(c => c.Code == couponDto.Code && c.StoreId == couponDto.StoreId);
                if (codeExists)
                {
                    return BadRequest("Coupon code already exists");
                }

                // Validate references
                if (couponDto.Applicability == CouponApplicability.Category && !couponDto.CategoryId.HasValue)
                {
                    return BadRequest("Category ID is required for category-specific coupons");
                }
                else if (couponDto.Applicability == CouponApplicability.Collection && !couponDto.CollectionId.HasValue)
                {
                    return BadRequest("Collection ID is required for collection-specific coupons");
                }
                else if (couponDto.Applicability == CouponApplicability.Product && !couponDto.ProductId.HasValue)
                {
                    return BadRequest("Product ID is required for product-specific coupons");
                }

                var coupon = new Coupon
                {
                    Code = couponDto.Code.ToUpper(),
                    Description = couponDto.Description,
                    Type = couponDto.Type,
                    Value = couponDto.Value,
                    Applicability = couponDto.Applicability,
                    CategoryId = couponDto.CategoryId,
                    CollectionId = couponDto.CollectionId,
                    ProductId = couponDto.ProductId,
                    MinimumPurchaseAmount = couponDto.MinimumPurchaseAmount,
                    MaximumDiscountAmount = couponDto.MaximumDiscountAmount,
                    MinimumQuantity = couponDto.MinimumQuantity,
                    UsageLimit = couponDto.UsageLimit,
                    UsageCount = 0,
                    DisplayOnCartPage = couponDto.DisplayOnCartPage,
                    StartDate = couponDto.StartDate,
                    EndDate = couponDto.EndDate,
                    IsActive = couponDto.IsActive,
                    IsOneTimeUse = couponDto.IsOneTimeUse,
                    StoreId = couponDto.StoreId,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.Coupons.Add(coupon);
                await _context.SaveChangesAsync();

                var createdCouponDto = new CouponDTO
                {
                    Id = coupon.Id,
                    Code = coupon.Code,
                    Description = coupon.Description,
                    Type = coupon.Type.ToString(),
                    Value = coupon.Value,
                    Applicability = coupon.Applicability.ToString(),
                    CategoryId = coupon.CategoryId,
                    CollectionId = coupon.CollectionId,
                    ProductId = coupon.ProductId,
                    MinimumPurchaseAmount = coupon.MinimumPurchaseAmount,
                    MaximumDiscountAmount = coupon.MaximumDiscountAmount,
                    MinimumQuantity = coupon.MinimumQuantity,
                    UsageLimit = coupon.UsageLimit,
                    UsageCount = coupon.UsageCount,
                    DisplayOnCartPage = coupon.DisplayOnCartPage,
                    StartDate = coupon.StartDate,
                    EndDate = coupon.EndDate,
                    IsActive = coupon.IsActive,
                    IsOneTimeUse = coupon.IsOneTimeUse,
                    StoreId = coupon.StoreId
                };

                return CreatedAtAction(nameof(GetCoupon), new { id = coupon.Id }, createdCouponDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating coupon");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error creating coupon");
            }
        }

        // PUT: api/admin/coupons/{id}
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateCoupon(int id, UpdateCouponDTO couponDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var coupon = await _context.Coupons.FindAsync(id);
                if (coupon == null)
                {
                    return NotFound();
                }

                // Check if user has access to the store
                if (!await CanAccessStoreAsync(coupon.StoreId))
                {
                    return Forbid();
                }

                // Validate references
                if (couponDto.Applicability == CouponApplicability.Category && !couponDto.CategoryId.HasValue)
                {
                    return BadRequest("Category ID is required for category-specific coupons");
                }
                else if (couponDto.Applicability == CouponApplicability.Collection && !couponDto.CollectionId.HasValue)
                {
                    return BadRequest("Collection ID is required for collection-specific coupons");
                }
                else if (couponDto.Applicability == CouponApplicability.Product && !couponDto.ProductId.HasValue)
                {
                    return BadRequest("Product ID is required for product-specific coupons");
                }

                // Update properties
                coupon.Description = couponDto.Description;
                coupon.Type = couponDto.Type;
                coupon.Value = couponDto.Value;
                coupon.Applicability = couponDto.Applicability;
                coupon.CategoryId = couponDto.CategoryId;
                coupon.CollectionId = couponDto.CollectionId;
                coupon.ProductId = couponDto.ProductId;
                coupon.MinimumPurchaseAmount = couponDto.MinimumPurchaseAmount;
                coupon.MaximumDiscountAmount = couponDto.MaximumDiscountAmount;
                coupon.MinimumQuantity = couponDto.MinimumQuantity;
                coupon.UsageLimit = couponDto.UsageLimit;
                coupon.DisplayOnCartPage = couponDto.DisplayOnCartPage;
                coupon.StartDate = couponDto.StartDate;
                coupon.EndDate = couponDto.EndDate;
                coupon.IsActive = couponDto.IsActive;
                coupon.IsOneTimeUse = couponDto.IsOneTimeUse;
                coupon.UpdatedAt = DateTime.UtcNow;

                _context.Entry(coupon).State = EntityState.Modified;
                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating coupon");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error updating coupon");
            }
        }

        // DELETE: api/admin/coupons/{id}
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteCoupon(int id)
        {
            try
            {
                var coupon = await _context.Coupons.FindAsync(id);
                if (coupon == null)
                {
                    return NotFound();
                }

                // Check if user has access to the store
                if (!await CanAccessStoreAsync(coupon.StoreId))
                {
                    return Forbid();
                }

                _context.Coupons.Remove(coupon);
                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting coupon");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error deleting coupon");
            }
        }

        // PUT: api/admin/coupons/bulk-status-update
        [HttpPut("bulk-status-update")]
        public async Task<IActionResult> BulkStatusUpdate(BulkStatusUpdateDTO updateDto)
        {
            try
            {
                if (updateDto.CouponIds == null || updateDto.CouponIds.Count == 0)
                {
                    return BadRequest("No coupon IDs provided");
                }

                // Get coupons
                var coupons = await _context.Coupons
                    .Where(c => updateDto.CouponIds.Contains(c.Id))
                    .ToListAsync();

                if (coupons.Count == 0)
                {
                    return NotFound("No coupons found with the provided IDs");
                }

                // Check if user has access to all stores
                foreach (var coupon in coupons)
                {
                    if (!await CanAccessStoreAsync(coupon.StoreId))
                    {
                        return Forbid();
                    }
                }

                // Update status
                foreach (var coupon in coupons)
                {
                    coupon.IsActive = updateDto.IsActive;
                    coupon.UpdatedAt = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating coupon statuses");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error updating coupon statuses");
            }
        }

        // GET: api/admin/coupons/stats
        [HttpGet("stats")]
        public async Task<ActionResult<CouponSummaryDTO>> GetCouponStats([FromQuery] int? storeId = null)
        {
            try
            {
                // Get accessible store IDs for the current user
                var accessibleStoreIds = await GetUserStoreIdsAsync();
                if (accessibleStoreIds.Length == 0)
                {
                    return Forbid();
                }

                // If storeId is specified, check if user has access to it
                if (storeId.HasValue && !accessibleStoreIds.Contains(storeId.Value))
                {
                    return Forbid();
                }

                // Start with all coupons
                IQueryable<Coupon> query = _context.Coupons;

                // Filter by store if specified
                if (storeId.HasValue)
                {
                    query = query.Where(c => c.StoreId == storeId.Value);
                }
                else
                {
                    // Filter by accessible stores
                    query = query.Where(c => accessibleStoreIds.Contains(c.StoreId));
                }

                var now = DateTime.UtcNow;
                var allCoupons = await query.ToListAsync();

                var summary = new CouponSummaryDTO
                {
                    TotalCoupons = allCoupons.Count,
                    ActiveCoupons = allCoupons.Count(c =>
                        c.IsActive &&
                        c.StartDate <= now &&
                        (!c.EndDate.HasValue || c.EndDate >= now) &&
                        (c.UsageLimit == 0 || c.UsageCount < c.UsageLimit)),
                    ExpiredCoupons = allCoupons.Count(c => c.EndDate.HasValue && c.EndDate < now),
                    ScheduledCoupons = allCoupons.Count(c => c.StartDate > now),
                    TotalUsageCount = allCoupons.Sum(c => c.UsageCount)
                };

                return Ok(summary);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving coupon stats");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error retrieving coupon stats");
            }
        }

        private IQueryable<Coupon> ApplySorting(IQueryable<Coupon> query, string sortBy, string sortOrder)
        {
            var isAscending = string.Equals(sortOrder, "asc", StringComparison.OrdinalIgnoreCase);

            return sortBy.ToLower() switch
            {
                "code" => isAscending ? query.OrderBy(c => c.Code) : query.OrderByDescending(c => c.Code),
                "value" => isAscending ? query.OrderBy(c => c.Value) : query.OrderByDescending(c => c.Value),
                "startdate" => isAscending ? query.OrderBy(c => c.StartDate) : query.OrderByDescending(c => c.StartDate),
                "enddate" => isAscending ? query.OrderBy(c => c.EndDate) : query.OrderByDescending(c => c.EndDate),
                "usagecount" => isAscending ? query.OrderBy(c => c.UsageCount) : query.OrderByDescending(c => c.UsageCount),
                "createdat" => isAscending ? query.OrderBy(c => c.CreatedAt) : query.OrderByDescending(c => c.CreatedAt),
                _ => isAscending ? query.OrderBy(c => c.CreatedAt) : query.OrderByDescending(c => c.CreatedAt)
            };
        }
    }
}
