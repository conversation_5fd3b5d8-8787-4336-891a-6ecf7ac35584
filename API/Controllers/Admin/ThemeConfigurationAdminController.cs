using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MyShop.API.Attributes;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using MyShop.API.DTOs;
using MyShop.API.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MyShop.API.Controllers.Admin
{
    [Route("api/admin/theme-configuration")]
    [ApiController]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public class ThemeConfigurationAdminController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILogger<ThemeConfigurationAdminController> _logger;
        private readonly ICurrentUserService _currentUserService;

        public ThemeConfigurationAdminController(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            ILogger<ThemeConfigurationAdminController> logger,
            ICurrentUserService currentUserService)
        {
            _context = context;
            _userManager = userManager;
            _logger = logger;
            _currentUserService = currentUserService;
        }

        // GET: api/admin/theme-configuration
        [HttpGet]
        [Permission("ThemeConfiguration", "View")]
        public async Task<ActionResult<IEnumerable<ThemeConfigurationDTO>>> GetAllThemeConfigurations()
        {
            try
            {
                var currentUser = await _currentUserService.GetCurrentUserAsync();
                if (currentUser == null)
                    return Unauthorized();

                // Check if user is super admin
                var isSuperAdmin = await _userManager.IsInRoleAsync(currentUser, "SuperAdmin");

                IQueryable<ThemeConfiguration> query = _context.ThemeConfigurations
                    .Include(tc => tc.Store);

                // If not super admin, only return configurations for stores the user has access to
                if (!isSuperAdmin)
                {
                    var accessibleStoreIds = await _context.StoreAdmins
                        .Where(sa => sa.UserId == currentUser.Id)
                        .Select(sa => sa.StoreId)
                        .ToListAsync();

                    query = query.Where(tc => accessibleStoreIds.Contains(tc.StoreId));
                }

                var configurations = await query.ToListAsync();

                return Ok(configurations.Select(MapToDTO));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all theme configurations");
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // GET: api/admin/theme-configuration/store/{storeId}
        [HttpGet("store/{storeId}")]
        [Permission("ThemeConfiguration", "View")]
        public async Task<ActionResult<IEnumerable<ThemeConfigurationDTO>>> GetThemeConfigurationsByStore(int storeId)
        {
            try
            {
                if (!await CanAccessStoreAsync(storeId))
                    return Forbid();

                var themes = await _context.ThemeConfigurations
                    .Include(tc => tc.Store)
                    .Where(tc => tc.StoreId == storeId)
                    .ToListAsync();

                return Ok(themes.Select(MapToDTO));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting theme configurations for store {StoreId}", storeId);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // GET: api/admin/theme-configuration/store/{storeId}/active
        [HttpGet("store/{storeId}/active")]
        [Permission("ThemeConfiguration", "View")]
        public async Task<ActionResult<ThemeConfigurationDTO>> GetActiveThemeConfigurationByStore(int storeId)
        {
            try
            {
                if (!await CanAccessStoreAsync(storeId))
                    return Forbid();

                var theme = await _context.ThemeConfigurations
                    .Include(tc => tc.Store)
                    .FirstOrDefaultAsync(tc => tc.StoreId == storeId && tc.IsActive);

                if (theme == null)
                {
                    // Create default theme if none exists
                    var store = await _context.Stores.FindAsync(storeId);
                    if (store == null)
                        return NotFound("Store not found");

                    theme = new ThemeConfiguration
                    {
                        StoreId = storeId,
                        Name = $"{store.Name} Default Theme",
                        Description = "Default theme configuration",
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };

                    _context.ThemeConfigurations.Add(theme);
                    await _context.SaveChangesAsync();
                }

                return Ok(MapToDTO(theme));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active theme configuration for store {StoreId}", storeId);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // GET: api/admin/theme-configuration/{id}
        [HttpGet("{id}")]
        [Permission("ThemeConfiguration", "View")]
        public async Task<ActionResult<ThemeConfigurationDTO>> GetThemeConfiguration(int id)
        {
            try
            {
                var theme = await _context.ThemeConfigurations
                    .Include(tc => tc.Store)
                    .FirstOrDefaultAsync(tc => tc.Id == id);

                if (theme == null)
                    return NotFound();

                if (!await CanAccessStoreAsync(theme.StoreId))
                    return Forbid();

                return Ok(MapToDTO(theme));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting theme configuration {Id}", id);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // POST: api/admin/theme-configuration
        [HttpPost]
        [Permission("ThemeConfiguration", "Create")]
        public async Task<ActionResult<ThemeConfigurationDTO>> CreateThemeConfiguration(CreateThemeConfigurationDTO dto)
        {
            try
            {
                if (!await CanAccessStoreAsync(dto.StoreId))
                    return Forbid();

                // Check if store exists
                var store = await _context.Stores.FindAsync(dto.StoreId);
                if (store == null)
                    return BadRequest("Store not found");

                // If this is the first theme for the store, make it active
                var existingThemesCount = await _context.ThemeConfigurations
                    .CountAsync(t => t.StoreId == dto.StoreId);

                var isFirstTheme = existingThemesCount == 0;

                var theme = new ThemeConfiguration
                {
                    StoreId = dto.StoreId,
                    Name = dto.Name,
                    Description = dto.Description,
                    IsActive = isFirstTheme || dto.IsActive,
                    PrimaryColor = dto.PrimaryColor,
                    SecondaryColor = dto.SecondaryColor,
                    AccentColor = dto.AccentColor,
                    TextPrimaryColor = dto.TextPrimaryColor,
                    TextSecondaryColor = dto.TextSecondaryColor,
                    TextLightColor = dto.TextLightColor,
                    BackgroundPrimaryColor = dto.BackgroundPrimaryColor,
                    BackgroundSecondaryColor = dto.BackgroundSecondaryColor,
                    BackgroundAccentColor = dto.BackgroundAccentColor,
                    ButtonPrimaryColor = dto.ButtonPrimaryColor,
                    ButtonSecondaryColor = dto.ButtonSecondaryColor,
                    ButtonTextColor = dto.ButtonTextColor,
                    ButtonBorderRadius = dto.ButtonBorderRadius,
                    CardBackgroundColor = dto.CardBackgroundColor,
                    CardBorderColor = dto.CardBorderColor,
                    CardBorderRadius = dto.CardBorderRadius,
                    CardShadow = dto.CardShadow,
                    HeadingFontFamily = dto.HeadingFontFamily,
                    BodyFontFamily = dto.BodyFontFamily,
                    FontBaseSize = dto.FontBaseSize,
                    SpacingUnit = dto.SpacingUnit,
                    ContainerMaxWidth = dto.ContainerMaxWidth,
                    ContainerPadding = dto.ContainerPadding,
                    HeaderBackgroundColor = dto.HeaderBackgroundColor,
                    HeaderTextColor = dto.HeaderTextColor,
                    HeaderHeight = dto.HeaderHeight,
                    FooterBackgroundColor = dto.FooterBackgroundColor,
                    FooterTextColor = dto.FooterTextColor,
                    NavLinkColor = dto.NavLinkColor,
                    NavLinkActiveColor = dto.NavLinkActiveColor,
                    NavLinkHoverColor = dto.NavLinkHoverColor,
                    InputBackgroundColor = dto.InputBackgroundColor,
                    InputBorderColor = dto.InputBorderColor,
                    InputBorderRadius = dto.InputBorderRadius,
                    InputFocusBorderColor = dto.InputFocusBorderColor,
                    CustomCSS = dto.CustomCSS,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.ThemeConfigurations.Add(theme);

                // If this theme is active, deactivate all other themes for this store
                if (theme.IsActive)
                {
                    var otherThemes = await _context.ThemeConfigurations
                        .Where(t => t.StoreId == dto.StoreId && t.IsActive)
                        .ToListAsync();

                    foreach (var otherTheme in otherThemes)
                    {
                        otherTheme.IsActive = false;
                        otherTheme.UpdatedAt = DateTime.UtcNow;
                    }
                }

                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetThemeConfiguration), new { id = theme.Id }, MapToDTO(theme));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating theme configuration for store {StoreId}", dto.StoreId);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // PUT: api/admin/theme-configuration/{id}
        [HttpPut("{id}")]
        [Permission("ThemeConfiguration", "Edit")]
        public async Task<IActionResult> UpdateThemeConfiguration(int id, UpdateThemeConfigurationDTO dto)
        {
            try
            {
                var theme = await _context.ThemeConfigurations.FindAsync(id);
                if (theme == null)
                    return NotFound();

                if (!await CanAccessStoreAsync(theme.StoreId))
                    return Forbid();

                // Update properties
                theme.Name = dto.Name;
                theme.Description = dto.Description;
                theme.PrimaryColor = dto.PrimaryColor;
                theme.SecondaryColor = dto.SecondaryColor;
                theme.AccentColor = dto.AccentColor;
                theme.TextPrimaryColor = dto.TextPrimaryColor;
                theme.TextSecondaryColor = dto.TextSecondaryColor;
                theme.TextLightColor = dto.TextLightColor;
                theme.BackgroundPrimaryColor = dto.BackgroundPrimaryColor;
                theme.BackgroundSecondaryColor = dto.BackgroundSecondaryColor;
                theme.BackgroundAccentColor = dto.BackgroundAccentColor;
                theme.ButtonPrimaryColor = dto.ButtonPrimaryColor;
                theme.ButtonSecondaryColor = dto.ButtonSecondaryColor;
                theme.ButtonTextColor = dto.ButtonTextColor;
                theme.ButtonBorderRadius = dto.ButtonBorderRadius;
                theme.CardBackgroundColor = dto.CardBackgroundColor;
                theme.CardBorderColor = dto.CardBorderColor;
                theme.CardBorderRadius = dto.CardBorderRadius;
                theme.CardShadow = dto.CardShadow;
                theme.HeadingFontFamily = dto.HeadingFontFamily;
                theme.BodyFontFamily = dto.BodyFontFamily;
                theme.FontBaseSize = dto.FontBaseSize;
                theme.SpacingUnit = dto.SpacingUnit;
                theme.ContainerMaxWidth = dto.ContainerMaxWidth;
                theme.ContainerPadding = dto.ContainerPadding;
                theme.HeaderBackgroundColor = dto.HeaderBackgroundColor;
                theme.HeaderTextColor = dto.HeaderTextColor;
                theme.HeaderHeight = dto.HeaderHeight;
                theme.FooterBackgroundColor = dto.FooterBackgroundColor;
                theme.FooterTextColor = dto.FooterTextColor;
                theme.NavLinkColor = dto.NavLinkColor;
                theme.NavLinkActiveColor = dto.NavLinkActiveColor;
                theme.NavLinkHoverColor = dto.NavLinkHoverColor;
                theme.InputBackgroundColor = dto.InputBackgroundColor;
                theme.InputBorderColor = dto.InputBorderColor;
                theme.InputBorderRadius = dto.InputBorderRadius;
                theme.InputFocusBorderColor = dto.InputFocusBorderColor;
                theme.CustomCSS = dto.CustomCSS;
                theme.UpdatedAt = DateTime.UtcNow;

                // If this theme is being activated, deactivate all other themes for this store
                if (dto.IsActive && !theme.IsActive)
                {
                    var otherThemes = await _context.ThemeConfigurations
                        .Where(t => t.StoreId == theme.StoreId && t.Id != theme.Id && t.IsActive)
                        .ToListAsync();

                    foreach (var otherTheme in otherThemes)
                    {
                        otherTheme.IsActive = false;
                        otherTheme.UpdatedAt = DateTime.UtcNow;
                    }

                    theme.IsActive = true;
                }

                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating theme configuration {Id}", id);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // POST: api/admin/theme-configuration/{id}/activate
        [HttpPost("{id}/activate")]
        [Permission("ThemeConfiguration", "Edit")]
        public async Task<IActionResult> ActivateThemeConfiguration(int id)
        {
            try
            {
                var theme = await _context.ThemeConfigurations.FindAsync(id);

                if (theme == null)
                    return NotFound();

                if (!await CanAccessStoreAsync(theme.StoreId))
                    return Forbid();

                if (theme.IsActive)
                    return NoContent(); // Already active

                // Deactivate all other themes for this store
                var otherThemes = await _context.ThemeConfigurations
                    .Where(t => t.StoreId == theme.StoreId && t.Id != theme.Id && t.IsActive)
                    .ToListAsync();

                foreach (var otherTheme in otherThemes)
                {
                    otherTheme.IsActive = false;
                    otherTheme.UpdatedAt = DateTime.UtcNow;
                }

                theme.IsActive = true;
                theme.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error activating theme configuration {Id}", id);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // DELETE: api/admin/theme-configuration/{id}
        [HttpDelete("{id}")]
        [Permission("ThemeConfiguration", "Delete")]
        public async Task<IActionResult> DeleteThemeConfiguration(int id)
        {
            try
            {
                var theme = await _context.ThemeConfigurations.FindAsync(id);
                if (theme == null)
                    return NotFound();

                if (!await CanAccessStoreAsync(theme.StoreId))
                    return Forbid();

                // Don't allow deleting the active theme
                if (theme.IsActive)
                    return BadRequest("Cannot delete the active theme. Please activate another theme first.");

                _context.ThemeConfigurations.Remove(theme);
                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting theme configuration {Id}", id);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // POST: api/admin/theme-configuration/{id}/duplicate
        [HttpPost("{id}/duplicate")]
        [Permission("ThemeConfiguration", "Create")]
        public async Task<ActionResult<ThemeConfigurationDTO>> DuplicateThemeConfiguration(int id)
        {
            try
            {
                var theme = await _context.ThemeConfigurations.FindAsync(id);
                if (theme == null)
                    return NotFound();

                if (!await CanAccessStoreAsync(theme.StoreId))
                    return Forbid();

                var newTheme = new ThemeConfiguration
                {
                    StoreId = theme.StoreId,
                    Name = $"{theme.Name} (Copy)",
                    Description = theme.Description,
                    IsActive = false,
                    PrimaryColor = theme.PrimaryColor,
                    SecondaryColor = theme.SecondaryColor,
                    AccentColor = theme.AccentColor,
                    TextPrimaryColor = theme.TextPrimaryColor,
                    TextSecondaryColor = theme.TextSecondaryColor,
                    TextLightColor = theme.TextLightColor,
                    BackgroundPrimaryColor = theme.BackgroundPrimaryColor,
                    BackgroundSecondaryColor = theme.BackgroundSecondaryColor,
                    BackgroundAccentColor = theme.BackgroundAccentColor,
                    ButtonPrimaryColor = theme.ButtonPrimaryColor,
                    ButtonSecondaryColor = theme.ButtonSecondaryColor,
                    ButtonTextColor = theme.ButtonTextColor,
                    ButtonBorderRadius = theme.ButtonBorderRadius,
                    CardBackgroundColor = theme.CardBackgroundColor,
                    CardBorderColor = theme.CardBorderColor,
                    CardBorderRadius = theme.CardBorderRadius,
                    CardShadow = theme.CardShadow,
                    HeadingFontFamily = theme.HeadingFontFamily,
                    BodyFontFamily = theme.BodyFontFamily,
                    FontBaseSize = theme.FontBaseSize,
                    SpacingUnit = theme.SpacingUnit,
                    ContainerMaxWidth = theme.ContainerMaxWidth,
                    ContainerPadding = theme.ContainerPadding,
                    HeaderBackgroundColor = theme.HeaderBackgroundColor,
                    HeaderTextColor = theme.HeaderTextColor,
                    HeaderHeight = theme.HeaderHeight,
                    FooterBackgroundColor = theme.FooterBackgroundColor,
                    FooterTextColor = theme.FooterTextColor,
                    NavLinkColor = theme.NavLinkColor,
                    NavLinkActiveColor = theme.NavLinkActiveColor,
                    NavLinkHoverColor = theme.NavLinkHoverColor,
                    InputBackgroundColor = theme.InputBackgroundColor,
                    InputBorderColor = theme.InputBorderColor,
                    InputBorderRadius = theme.InputBorderRadius,
                    InputFocusBorderColor = theme.InputFocusBorderColor,
                    CustomCSS = theme.CustomCSS,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.ThemeConfigurations.Add(newTheme);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetThemeConfiguration), new { id = newTheme.Id }, MapToDTO(newTheme));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error duplicating theme configuration {Id}", id);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // GET: api/admin/theme-configuration/{id}/assignments
        [HttpGet("{id}/assignments")]
        [Permission("ThemeConfiguration", "View")]
        public async Task<ActionResult<IEnumerable<object>>> GetThemeAssignments(int id)
        {
            try
            {
                var theme = await _context.ThemeConfigurations
                    .Include(tc => tc.Store)
                    .FirstOrDefaultAsync(tc => tc.Id == id);
                
                if (theme == null)
                    return NotFound();

                if (!await CanAccessStoreAsync(theme.StoreId))
                    return Forbid();

                // Since we're not implementing theme assignments, return an empty list
                // or basic store information instead
                var storeInfo = new
                {
                    Id = theme.StoreId,
                    Name = theme.Store?.Name,
                    IsActive = theme.IsActive,
                    AssignedAt = theme.CreatedAt
                };

                return Ok(new[] { storeInfo });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting theme information for theme {ThemeId}", id);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // Helper methods
        private async Task<bool> CanAccessStoreAsync(int storeId)
        {
            var currentUser = await _currentUserService.GetCurrentUserAsync();
            if (currentUser == null)
                return false;

            // Super admins can access all stores
            if (await _userManager.IsInRoleAsync(currentUser, "SuperAdmin"))
                return true;

            // Check if user is an admin of the store
            return await _context.StoreAdmins
                .AnyAsync(sa => sa.UserId == currentUser.Id && sa.StoreId == storeId);
        }

        private ThemeConfigurationDTO MapToDTO(ThemeConfiguration theme)
        {
            return new ThemeConfigurationDTO
            {
                Id = theme.Id,
                StoreId = theme.StoreId,
                StoreName = theme.Store?.Name,
                Name = theme.Name,
                Description = theme.Description,
                IsActive = theme.IsActive,
                PrimaryColor = theme.PrimaryColor,
                SecondaryColor = theme.SecondaryColor,
                AccentColor = theme.AccentColor,
                TextPrimaryColor = theme.TextPrimaryColor,
                TextSecondaryColor = theme.TextSecondaryColor,
                TextLightColor = theme.TextLightColor,
                BackgroundPrimaryColor = theme.BackgroundPrimaryColor,
                BackgroundSecondaryColor = theme.BackgroundSecondaryColor,
                BackgroundAccentColor = theme.BackgroundAccentColor,
                ButtonPrimaryColor = theme.ButtonPrimaryColor,
                ButtonSecondaryColor = theme.ButtonSecondaryColor,
                ButtonTextColor = theme.ButtonTextColor,
                ButtonBorderRadius = theme.ButtonBorderRadius,
                CardBackgroundColor = theme.CardBackgroundColor,
                CardBorderColor = theme.CardBorderColor,
                CardBorderRadius = theme.CardBorderRadius,
                CardShadow = theme.CardShadow,
                HeadingFontFamily = theme.HeadingFontFamily,
                BodyFontFamily = theme.BodyFontFamily,
                FontBaseSize = theme.FontBaseSize,
                SpacingUnit = theme.SpacingUnit,
                ContainerMaxWidth = theme.ContainerMaxWidth,
                ContainerPadding = theme.ContainerPadding,
                HeaderBackgroundColor = theme.HeaderBackgroundColor,
                HeaderTextColor = theme.HeaderTextColor,
                HeaderHeight = theme.HeaderHeight,
                FooterBackgroundColor = theme.FooterBackgroundColor,
                FooterTextColor = theme.FooterTextColor,
                NavLinkColor = theme.NavLinkColor,
                NavLinkActiveColor = theme.NavLinkActiveColor,
                NavLinkHoverColor = theme.NavLinkHoverColor,
                InputBackgroundColor = theme.InputBackgroundColor,
                InputBorderColor = theme.InputBorderColor,
                InputBorderRadius = theme.InputBorderRadius,
                InputFocusBorderColor = theme.InputFocusBorderColor,
                CustomCSS = theme.CustomCSS,
                CreatedAt = theme.CreatedAt,
                UpdatedAt = theme.UpdatedAt
            };
        }
    }
}
