using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using MyShop.API.DTOs;

namespace MyShop.API.Controllers.Admin
{
    [Route("api/admin/[controller]")]
    [ApiController]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public class ProductController : AdminController
    {
        public ProductController(
            UserManager<ApplicationUser> userManager,
            ApplicationDbContext context,
            ILogger<ProductController> logger)
            : base(userManager, context, logger)
        {
        }

        // GET: api/Product
        [HttpGet]
        public async Task<ActionResult<IEnumerable<ProductDTO>>> GetProducts(
            [FromQuery] int? storeId = null,
            [FromQuery] string? search = null,
            [FromQuery] int? categoryId = null,
            [FromQuery] int? collectionId = null,
            [FromQuery] bool? isFeatured = null,
            [FromQuery] bool? isActive = true,
            [FromQuery] decimal? minPrice = null,
            [FromQuery] decimal? maxPrice = null,
            [FromQuery] string? sortBy = "name",
            [FromQuery] string? sortOrder = "asc",
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                _logger.LogInformation("GetProducts called with storeId: {StoreId}", storeId);

                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    _logger.LogWarning("No authenticated user found when retrieving products");
                    return Unauthorized();
                }

                var query = _context.Products
                    .Include(p => p.Category)
                    .Include(p => p.ProductType)
                    .Include(p => p.Variants)
                        .ThenInclude(v => v.InventoryItems)
                    .Include(p => p.Collections)
                    .Include(p => p.Images)
                    .AsQueryable();

                // Filter by store access
                if (storeId.HasValue)
                {
                    // Check if user has access to the specified store
                    if (!await CanAccessStoreAsync(storeId.Value))
                    {
                        _logger.LogWarning("User {UserId} attempted to access products from unauthorized store {StoreId}",
                            currentUser.Id, storeId.Value);
                        return Forbid();
                    }

                    // Filter products by the specified store
                    query = query.Where(p => p.StoreId == storeId.Value);
                    _logger.LogInformation("Filtering products for store {StoreId}", storeId.Value);
                }
                else
                {
                    // If no specific store is requested, filter by all stores the user has access to
                    var accessibleStoreIds = await GetUserStoreIdsAsync();

                    if (accessibleStoreIds.Length == 0)
                    {
                        _logger.LogWarning("User {UserId} has no accessible stores", currentUser.Id);
                        return Ok(new List<ProductDTO>()); // Return empty list if user has no accessible stores
                    }

                    query = query.Where(p => accessibleStoreIds.Contains(p.StoreId));
                    _logger.LogInformation("Filtering products for {StoreCount} accessible stores", accessibleStoreIds.Length);
                }

                // Apply filters
                if (!string.IsNullOrEmpty(search))
                {
                    query = query.Where(p => p.Name.Contains(search) ||
                                           p.Description.Contains(search));
                }

                if (categoryId.HasValue)
                {
                    query = query.Where(p => p.CategoryId == categoryId.Value);
                }

                if (collectionId.HasValue)
                {
                    query = query.Where(p => p.Collections.Any(c => c.Id == collectionId.Value));
                }

                if (isFeatured.HasValue)
                {
                    query = query.Where(p => p.IsFeatured == isFeatured.Value);
                }

                if (isActive.HasValue)
                {
                    query = query.Where(p => p.IsActive == isActive.Value);
                }

                // Apply price filtering on variants
                if (minPrice.HasValue || maxPrice.HasValue)
                {
                    query = query.Where(p => p.Variants.Any(v =>
                        (!minPrice.HasValue || v.Price >= minPrice.Value) &&
                        (!maxPrice.HasValue || v.Price <= maxPrice.Value)));
                }

                // Apply sorting
                query = ApplySorting(query, sortBy, sortOrder);

                // Apply pagination
                var totalCount = await query.CountAsync();
                var totalPages = (int)Math.Ceiling(totalCount / (double)pageSize);

                var products = await query
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var productDtos = products.Select(p => new ProductDTO
                {
                    Id = p.Id,
                    Name = p.Name,
                    Description = p.Description,
                    StyleCode = p.StyleCode,
                    ReturnExchangeCondition = p.ReturnExchangeCondition,
                    Color = p.Color,
                    HSNCode = p.HSNCode,
                    GSTType = p.GSTType,
                    HasVariants = p.HasVariants,
                    ProductGroupId = p.ProductGroupId,
                    ProductGroupName = p.ProductGroup?.Name ?? string.Empty,
                    ProductTypeId = p.ProductTypeId,
                    ProductTypeName = p.ProductType?.Name ?? string.Empty,
                    ImageUrl = p.ImageUrl,
                    Barcode = p.Barcode,
                    IsActive = p.IsActive,
                    IsFeatured = p.IsFeatured,
                    CategoryId = p.CategoryId,
                    CategoryName = p.Category.Name,
                    StockQuantity = p.Variants.Any() ? p.Variants.Sum(v => v.InventoryItems.Sum(ii => ii.StockQuantity)) : 0,
                    ReorderLevel = p.Variants.Any() ? p.Variants.Min(v => v.InventoryItems.Any() ? v.InventoryItems.Min(ii => ii.ReorderLevel) : 0) : 0,
                    CollectionIds = p.Collections.Select(c => c.Id).ToList(),
                    CollectionNames = p.Collections.Select(c => c.Name).ToList(),
                    Variants = p.Variants.Select(v => new ProductVariantDTO
                    {
                        Id = v.Id,
                        SKU = v.SKU,
                        Barcode = v.Barcode,
                        Price = v.Price,
                        Cost = v.Cost,
                        MRP = v.MRP,
                        StockQuantity = v.InventoryItems.Sum(ii => ii.StockQuantity),
                        IsActive = v.IsActive,
                        ProductId = v.ProductId,
                        ProductName = p.Name,
                        // Physical attributes
                        Size = v.Size,
                        Weight = v.Weight,
                        WeightUnit = v.WeightUnit,
                        Length = v.Length,
                        Breadth = v.Breadth,
                        Height = v.Height,
                        DimensionUnit = v.DimensionUnit,
                        Volume = v.Volume,
                        VolumeUnit = v.VolumeUnit
                    }).ToList(),
                    Images = p.Images.Select(i => new ProductImageDTO
                    {
                        Id = i.Id,
                        Url = i.ImageUrl,
                        IsMain = i.IsMain,
                        DisplayOrder = i.DisplayOrder
                    }).ToList(),
                    MainImage = p.Images.FirstOrDefault(i => i.IsMain)?.ImageUrl ?? p.ImageUrl,
                    CustomAttributes = !string.IsNullOrEmpty(p.CustomAttributes) ?
                        System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(p.CustomAttributes) ??
                        new Dictionary<string, string>() :
                        new Dictionary<string, string>()
                }).ToList();

                Response.Headers.Append("X-Total-Count", totalCount.ToString());
                Response.Headers.Append("X-Total-Pages", totalPages.ToString());

                return Ok(productDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving products");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error retrieving products");
            }
        }

        private IQueryable<Product> ApplySorting(IQueryable<Product> query, string? sortBy, string? sortOrder)
        {
            var isDescending = sortOrder?.ToLower() == "desc";

            return sortBy?.ToLower() switch
            {
                "date" => isDescending ? query.OrderByDescending(p => p.CreatedAt) : query.OrderBy(p => p.CreatedAt),
                "category" => isDescending ? query.OrderByDescending(p => p.Category.Name) : query.OrderBy(p => p.Category.Name),
                "price" => isDescending
                    ? query.OrderByDescending(p => p.Variants.Any() ? p.Variants.Min(v => v.Price) : decimal.MaxValue)
                    : query.OrderBy(p => p.Variants.Any() ? p.Variants.Min(v => v.Price) : decimal.MaxValue),
                _ => isDescending ? query.OrderByDescending(p => p.Name) : query.OrderBy(p => p.Name) // Default to name
            };
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<ProductDTO>> GetProduct(int id)
        {
            try
            {
                _logger.LogInformation("GetProduct called with id: {Id}", id);

                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    _logger.LogWarning("No authenticated user found when retrieving product");
                    return Unauthorized();
                }

                var product = await _context.Products
                    .Include(p => p.Category)
                    .Include(p => p.ProductType)
                    .Include(p => p.Variants)
                        .ThenInclude(v => v.InventoryItems)
                    .Include(p => p.Variants)
                        .ThenInclude(v => v.InventoryItems)
                            .ThenInclude(ii => ii.Location)
                    .Include(p => p.Collections)
                    .Include(p => p.Images)
                    .Include(p => p.Attributes)
                    .FirstOrDefaultAsync(p => p.Id == id);

                if (product == null)
                {
                    return NotFound();
                }

                // Check if user has access to the product's store
                if (!await CanAccessStoreAsync(product.StoreId))
                {
                    _logger.LogWarning("User {UserId} attempted to access product from unauthorized store {StoreId}",
                        currentUser.Id, product.StoreId);
                    return Forbid();
                }

                var productDto = new ProductDTO
                {
                    Id = product.Id,
                    Name = product.Name,
                    Description = product.Description,
                    ImageUrl = product.ImageUrl,
                    Barcode = product.Barcode,
                    StyleCode = product.StyleCode,
                    ReturnExchangeCondition = product.ReturnExchangeCondition,
                    Color = product.Color,
                    HSNCode = product.HSNCode,
                    GSTType = product.GSTType,
                    IsActive = product.IsActive,
                    IsFeatured = product.IsFeatured,
                    HasVariants = product.HasVariants,
                    CategoryId = product.CategoryId,
                    CategoryName = product.Category.Name,
                    ProductTypeId = product.ProductTypeId,
                    ProductTypeName = product.ProductType?.Name ?? string.Empty,
                    StockQuantity = product.Variants.Any() ? product.Variants.Sum(v => v.InventoryItems.Any() ? v.InventoryItems.Sum(ii => ii.StockQuantity) : 0) : 0,
                    ReorderLevel = product.Variants.Any() ? product.Variants.Min(v => v.InventoryItems.Any() ? v.InventoryItems.Min(ii => ii.ReorderLevel) : 0) : 0,
                    CollectionIds = product.Collections.Select(c => c.Id).ToList(),
                    CollectionNames = product.Collections.Select(c => c.Name).ToList(),
                    Images = product.Images.Select(i => new ProductImageDTO
                    {
                        Id = i.Id,
                        Url = i.ImageUrl,
                        IsMain = i.IsMain,
                        DisplayOrder = i.DisplayOrder
                    }).ToList(),
                    Variants = product.Variants.Select(v => new ProductVariantDTO
                    {
                        Id = v.Id,
                        SKU = v.SKU,
                        Barcode = v.Barcode,
                        Price = v.Price,
                        Cost = v.Cost,
                        MRP = v.MRP,
                        StockQuantity = v.InventoryItems.Any() ? v.InventoryItems.Sum(ii => ii.StockQuantity) : 0,
                        IsActive = v.IsActive,
                        ProductId = v.ProductId,
                        ProductName = product.Name,
                        // Physical attributes
                        Size = v.Size,
                        Weight = v.Weight,
                        WeightUnit = v.WeightUnit,
                        Length = v.Length,
                        Breadth = v.Breadth,
                        Height = v.Height,
                        DimensionUnit = v.DimensionUnit,
                        Volume = v.Volume,
                        VolumeUnit = v.VolumeUnit,
                        // Variant attributes
                        VariantAttributes = new Dictionary<string, string> { { "Size", v.Size }, { "Color", product.Color ?? "" } },
                        // Inventory items
                        InventoryItems = v.InventoryItems.Select(ii => new InventoryItemDTO
                        {
                            Id = ii.Id,
                            LocationId = ii.LocationId,
                            LocationName = ii.Location.Name,
                            LocationCode = ii.Location.Code,
                            StockQuantity = ii.StockQuantity,
                            ReorderLevel = ii.ReorderLevel,
                            LastRestockedAt = ii.LastRestockedAt
                        }).ToList()
                    }).ToList(),
                    MainImage = product.Images.FirstOrDefault(i => i.IsMain)?.ImageUrl ?? product.Images.FirstOrDefault()?.ImageUrl ?? product.ImageUrl,
                    CustomAttributes = product.Attributes.ToDictionary(a => a.Key, a => a.Value)
                };

                return Ok(productDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving product");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error retrieving product");
            }
        }



           // POST: api/Product
        [HttpPost]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ProductDTO>> CreateProduct(CreateProductDTO productDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Check if category exists
                var category = await _context.Categories.FindAsync(productDto.CategoryId);
                if (category == null)
                {
                    return BadRequest("Invalid category ID");
                }

                // Check if collections exist
                if (productDto.CollectionIds != null && productDto.CollectionIds.Any())
                {
                    var existingCollectionIds = await _context.Collections
                        .Where(c => productDto.CollectionIds.Contains(c.Id))
                        .Select(c => c.Id)
                        .ToListAsync();

                    if (existingCollectionIds.Count != productDto.CollectionIds.Count)
                    {
                        return BadRequest("One or more collection IDs are invalid");
                    }
                }

                // Check if product type exists or create a new one if name is provided
                int? productTypeId = productDto.ProductTypeId;
                if (productTypeId == null && !string.IsNullOrEmpty(productDto.ProductTypeName))
                {
                    // Check if a product type with this name already exists
                    var existingProductType = await _context.ProductTypes
                        .FirstOrDefaultAsync(pt => pt.Name.ToLower() == productDto.ProductTypeName.ToLower());

                    if (existingProductType != null)
                    {
                        productTypeId = existingProductType.Id;
                    }
                    else
                    {
                        // Create a new product type
                        var newProductType = new ProductType
                        {
                            Name = productDto.ProductTypeName,
                            Description = string.Empty,
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow
                        };

                        _context.ProductTypes.Add(newProductType);
                        await _context.SaveChangesAsync();

                        productTypeId = newProductType.Id;
                    }
                }

                // Check if user has access to the specified store
                if (!await CanAccessStoreAsync(productDto.StoreId))
                {
                    var currentUser = await _userManager.GetUserAsync(User);
                    _logger.LogWarning("User {UserId} attempted to create product for unauthorized store {StoreId}",
                        currentUser?.Id, productDto.StoreId);
                    return Forbid();
                }

                // Use the store ID from the DTO
                int storeId = productDto.StoreId;

                var product = new Product
                {
                    Name = productDto.Name,
                    Description = productDto.Description,
                    ImageUrl = productDto.ImageUrl ?? string.Empty,
                    Barcode = productDto.Barcode ?? string.Empty,
                    StyleCode = productDto.StyleCode ?? string.Empty,
                    ReturnExchangeCondition = productDto.ReturnExchangeCondition,
                    Color = productDto.Color ?? string.Empty,
                    HSNCode = productDto.HSNCode ?? string.Empty,
                    GSTType = productDto.GSTType ?? string.Empty,
                    IsActive = productDto.IsActive,
                    IsFeatured = productDto.IsFeatured,
                    HasVariants = productDto.HasVariants,
                    CategoryId = productDto.CategoryId,
                    ProductGroupId = productDto.ProductGroupId,
                    ProductTypeId = productTypeId,
                    StoreId = storeId,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                // Add collections
                if (productDto.CollectionIds != null && productDto.CollectionIds.Any())
                {
                    var collections = await _context.Collections
                        .Where(c => productDto.CollectionIds.Contains(c.Id))
                        .ToListAsync();

                    foreach (var collection in collections)
                    {
                        product.Collections.Add(collection);
                    }
                }

                _context.Products.Add(product);
                await _context.SaveChangesAsync();

                // Add custom attributes
                if (productDto.CustomAttributes != null && productDto.CustomAttributes.Any())
                {
                    foreach (var attr in productDto.CustomAttributes)
                    {
                        _context.ProductAttributes.Add(new ProductAttribute
                        {
                            ProductId = product.Id,
                            Key = attr.Key,
                            Value = attr.Value
                        });
                    }
                    await _context.SaveChangesAsync();
                }

                // Add images
                if (productDto.Images != null && productDto.Images.Any())
                {
                    foreach (var image in productDto.Images)
                    {
                        _context.ProductImages.Add(new ProductImage
                        {
                            ProductId = product.Id,
                            ImageUrl = image.Url,
                            IsMain = image.IsMain,
                            DisplayOrder = image.DisplayOrder
                        });
                    }
                    await _context.SaveChangesAsync();
                }

                if (productDto.Variants != null && productDto.Variants.Any())
                {
                    foreach (var variantDto in productDto.Variants)
                    {
                        var variant = new ProductVariant
                        {
                            ProductId = product.Id,
                            SKU = variantDto.SKU ?? $"SKU-{product.Id}-{Guid.NewGuid().ToString()[..8]}",
                            Barcode = variantDto.Barcode,
                            Price = variantDto.Price,
                            Cost = variantDto.Cost,
                            StockQuantity = variantDto.StockQuantity,
                            IsActive = variantDto.IsActive
                        };

                        _context.ProductVariants.Add(variant);
                        await _context.SaveChangesAsync();

                        // Always use default location (ID: 1) for inventory
                        const int locationId = 1;

                        // Create inventory item for the default location
                        var inventoryItem = new InventoryItem
                        {
                            LocationId = locationId,
                            ProductVariantId = variant.Id,
                            StockQuantity = variant.StockQuantity,
                            LastRestockedAt = DateTime.UtcNow,
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow
                        };

                        _context.InventoryItems.Add(inventoryItem);
                    }
                    await _context.SaveChangesAsync();
                }

                // Legacy code for handling images has been moved above
                // If no images were added above but ImageUrl is set, create a main image
                if (productDto.Images == null && !string.IsNullOrEmpty(productDto.ImageUrl))
                {
                    var mainImage = new ProductImage
                    {
                        ProductId = product.Id,
                        ImageUrl = productDto.ImageUrl,
                        IsMain = true,
                        DisplayOrder = 0
                    };

                    _context.ProductImages.Add(mainImage);
                    await _context.SaveChangesAsync();
                }

                // Variant handling has been moved above

                await _context.SaveChangesAsync();

                // Reload product with relationships
                product = await _context.Products
                    .Include(p => p.Category)
                    .Include(p => p.ProductType)
                    .Include(p => p.Variants)
                        .ThenInclude(v => v.InventoryItems)
                    .Include(p => p.Collections)
                    .Include(p => p.Images)
                    .Include(p => p.Variants)
                    .FirstOrDefaultAsync(p => p.Id == product.Id);

                var createdProductDto = new ProductDTO
                {
                    Id = product.Id,
                    Name = product.Name,
                    Description = product.Description,
                    ImageUrl = product.ImageUrl,
                    Barcode = product.Barcode,
                    IsActive = product.IsActive,
                    IsFeatured = product.IsFeatured,
                    HasVariants = product.HasVariants,
                    CategoryId = product.CategoryId,
                    CategoryName = product.Category.Name,
                    ProductTypeId = product.ProductTypeId,
                    ProductTypeName = product.ProductType?.Name ?? string.Empty,
                    StockQuantity = product.Variants.Any() ? product.Variants.Sum(v => v.InventoryItems.Sum(ii => ii.StockQuantity)) : 0,
                    ReorderLevel = product.Variants.Any() ? product.Variants.Min(v => v.InventoryItems.Any() ? v.InventoryItems.Min(ii => ii.ReorderLevel) : 0) : 0,
                    CollectionIds = product.Collections.Select(c => c.Id).ToList(),
                    CollectionNames = product.Collections.Select(c => c.Name).ToList(),
                    Images = product.Images.Select(i => new ProductImageDTO
                    {
                        Id = i.Id,
                        Url = i.ImageUrl,
                        IsMain = i.IsMain,
                        DisplayOrder = i.DisplayOrder
                    }).ToList(),
                    Variants = product.Variants.Select(v => new ProductVariantDTO
                    {
                        Id = v.Id,
                        SKU = v.SKU,
                        Barcode = v.Barcode,
                        Price = v.Price,
                        Cost = v.Cost,
                        StockQuantity = v.StockQuantity,
                        IsActive = v.IsActive,
                        ProductId = v.ProductId,
                        ProductName = product.Name
                    }).ToList()
                };

                return CreatedAtAction(nameof(GetProduct), new { id = product.Id }, createdProductDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating product");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error creating product");
            }
        }

        // PUT: api/Product/5
        [HttpPut("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> UpdateProduct(int id, UpdateProductDTO productDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var product = await _context.Products
                    .Include(p => p.Collections)
                    .Include(p => p.Images)
                    .Include(p => p.ProductType)
                    .FirstOrDefaultAsync(p => p.Id == id);

                if (product == null)
                {
                    return NotFound();
                }

                // Check if user has access to the product's store
                if (!await CanAccessStoreAsync(product.StoreId))
                {
                    var currentUser = await _userManager.GetUserAsync(User);
                    _logger.LogWarning("User {UserId} attempted to update product from unauthorized store {StoreId}",
                        currentUser?.Id, product.StoreId);
                    return Forbid();
                }

                // Check if category exists
                var category = await _context.Categories.FindAsync(productDto.CategoryId);
                if (category == null)
                {
                    return BadRequest("Invalid category ID");
                }

                // Update product properties
                product.Name = productDto.Name;
                product.Description = productDto.Description;

                if (!string.IsNullOrEmpty(productDto.StyleCode))
                {
                    product.StyleCode = productDto.StyleCode;
                }

                product.ReturnExchangeCondition = productDto.ReturnExchangeCondition;

                if (!string.IsNullOrEmpty(productDto.Color))
                {
                    product.Color = productDto.Color;
                }

                if (!string.IsNullOrEmpty(productDto.HSNCode))
                {
                    product.HSNCode = productDto.HSNCode;
                }

                if (!string.IsNullOrEmpty(productDto.GSTType))
                {
                    product.GSTType = productDto.GSTType;
                }

                if (!string.IsNullOrEmpty(productDto.Barcode))
                {
                    product.Barcode = productDto.Barcode;
                }

                if (!string.IsNullOrEmpty(productDto.ImageUrl))
                {
                    product.ImageUrl = productDto.ImageUrl;
                }
                product.IsActive = productDto.IsActive;
                product.IsFeatured = productDto.IsFeatured;
                product.CategoryId = productDto.CategoryId;
                product.ProductGroupId = productDto.ProductGroupId;

                // Handle product type
                if (productDto.ProductTypeId.HasValue)
                {
                    // Check if product type exists
                    var productType = await _context.ProductTypes.FindAsync(productDto.ProductTypeId.Value);
                    if (productType == null)
                    {
                        return BadRequest("Invalid product type ID");
                    }
                    product.ProductTypeId = productDto.ProductTypeId;
                }
                else if (!string.IsNullOrEmpty(productDto.ProductTypeName))
                {
                    // Check if a product type with this name already exists
                    var existingProductType = await _context.ProductTypes
                        .FirstOrDefaultAsync(pt => pt.Name.ToLower() == productDto.ProductTypeName.ToLower());

                    if (existingProductType != null)
                    {
                        product.ProductTypeId = existingProductType.Id;
                    }
                    else
                    {
                        // Create a new product type
                        var newProductType = new ProductType
                        {
                            Name = productDto.ProductTypeName,
                            Description = string.Empty,
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow
                        };

                        _context.ProductTypes.Add(newProductType);
                        await _context.SaveChangesAsync();

                        product.ProductTypeId = newProductType.Id;
                    }
                }

                // Update custom attributes if provided
                if (productDto.CustomAttributes != null)
                {
                    product.CustomAttributes = System.Text.Json.JsonSerializer.Serialize(productDto.CustomAttributes);
                }

                product.UpdatedAt = DateTime.UtcNow;

                // Update collections
                product.Collections.Clear();
                if (productDto.CollectionIds != null && productDto.CollectionIds.Any())
                {
                    var collections = await _context.Collections
                        .Where(c => productDto.CollectionIds.Contains(c.Id))
                        .ToListAsync();

                    foreach (var collection in collections)
                    {
                        product.Collections.Add(collection);
                    }
                }

                // Update inventory if needed
                if (productDto.StockQuantity.HasValue || productDto.ReorderLevel.HasValue)
                {
                    // Find the default variant for this product
                    var defaultVariant = await _context.ProductVariants
                        .FirstOrDefaultAsync(v => v.ProductId == id);

                    if (defaultVariant != null)
                    {
                        // Find the inventory items for this variant
                        var inventoryItems = await _context.InventoryItems
                            .Where(i => i.ProductVariantId == defaultVariant.Id)
                            .ToListAsync();

                        if (inventoryItems.Any())
                        {
                            // Update inventory items
                            // For simplicity, we'll update the first inventory item with the new stock quantity
                            var mainInventoryItem = inventoryItems.First();
                            if (productDto.StockQuantity.HasValue)
                            {
                                mainInventoryItem.StockQuantity = productDto.StockQuantity.Value;
                            }
                            if (productDto.ReorderLevel.HasValue)
                            {
                                mainInventoryItem.ReorderLevel = productDto.ReorderLevel.Value;
                            }
                            mainInventoryItem.LastRestockedAt = DateTime.UtcNow;
                            mainInventoryItem.UpdatedAt = DateTime.UtcNow;
                            _context.Entry(mainInventoryItem).State = EntityState.Modified;
                        }
                    }
                }

                // Update product images if specified
                if (productDto.Images != null && productDto.Images.Any())
                {
                    // Remove existing images
                    _context.ProductImages.RemoveRange(product.Images);
                    await _context.SaveChangesAsync();

                    // Add new images
                    var productImages = productDto.Images.Select((image, index) => new ProductImage
                    {
                        ProductId = product.Id,
                        ImageUrl = image.Url,
                        IsMain = image.IsMain,
                        DisplayOrder = image.DisplayOrder != 0 ? image.DisplayOrder : index
                    }).ToList();

                    // Ensure at least one image is marked as main
                    if (!productImages.Any(pi => pi.IsMain) && productImages.Any())
                    {
                        productImages.First().IsMain = true;
                    }

                    _context.ProductImages.AddRange(productImages);
                }
                // If no images provided but ImageUrl is set and no existing images, create a main image
                else if (!string.IsNullOrEmpty(productDto.ImageUrl) && !product.Images.Any())
                {
                    var mainImage = new ProductImage
                    {
                        ProductId = product.Id,
                        ImageUrl = productDto.ImageUrl,
                        IsMain = true,
                        DisplayOrder = 0
                    };

                    _context.ProductImages.Add(mainImage);
                }

                // Handle variants if provided
                if (productDto.Variants != null && productDto.Variants.Any())
                {
                    // Get existing variants
                    var existingVariants = await _context.ProductVariants
                        .Include(v => v.InventoryItems)
                        .Where(v => v.ProductId == id)
                        .ToListAsync();

                    // Update existing variants
                    foreach (var variantDto in productDto.Variants)
                    {
                        // Find the existing variant by ID
                        var existingVariant = existingVariants.FirstOrDefault(v => v.Id == variantDto.Id);
                        if (existingVariant != null)
                        {
                            // Update variant properties
                            if (!string.IsNullOrEmpty(variantDto.SKU))
                            {
                                existingVariant.SKU = variantDto.SKU;
                            }

                            if (!string.IsNullOrEmpty(variantDto.Barcode))
                            {
                                existingVariant.Barcode = variantDto.Barcode;
                            }

                            if (variantDto.Price.HasValue)
                            {
                                existingVariant.Price = variantDto.Price.Value;
                            }

                            if (variantDto.Cost.HasValue)
                            {
                                existingVariant.Cost = variantDto.Cost.Value;
                            }

                            if (variantDto.MRP.HasValue)
                            {
                                existingVariant.MRP = variantDto.MRP.Value;
                            }

                            if (variantDto.StockQuantity.HasValue)
                            {
                                existingVariant.StockQuantity = variantDto.StockQuantity.Value;

                                // Also update the inventory items
                                if (existingVariant.InventoryItems != null && existingVariant.InventoryItems.Any())
                                {
                                    foreach (var item in existingVariant.InventoryItems)
                                    {
                                        item.StockQuantity = variantDto.StockQuantity.Value;
                                        item.UpdatedAt = DateTime.UtcNow;
                                        _context.Entry(item).State = EntityState.Modified;
                                    }
                                }
                                else
                                {


                                    var inventoryItem = new InventoryItem
                                    {
                                        LocationId = 1,
                                        ProductVariantId = existingVariant.Id,
                                        StockQuantity = variantDto.StockQuantity.Value,
                                        ReorderLevel = variantDto.ReorderLevel ?? 10,
                                        LastRestockedAt = DateTime.UtcNow,
                                        CreatedAt = DateTime.UtcNow,
                                        UpdatedAt = DateTime.UtcNow
                                    };

                                    _context.InventoryItems.Add(inventoryItem);
                                }
                            }

                            if (variantDto.ReorderLevel.HasValue)
                            {
                                // Also update the inventory items
                                if (existingVariant.InventoryItems != null && existingVariant.InventoryItems.Any())
                                {
                                    foreach (var item in existingVariant.InventoryItems)
                                    {
                                        item.ReorderLevel = variantDto.ReorderLevel.Value;
                                        item.UpdatedAt = DateTime.UtcNow;
                                        _context.Entry(item).State = EntityState.Modified;
                                    }
                                }
                            }

                            if (variantDto.IsActive.HasValue)
                            {
                                existingVariant.IsActive = variantDto.IsActive.Value;
                            }

                            // Update physical attributes
                            if (variantDto.Size != null)
                            {
                                existingVariant.Size = variantDto.Size;
                            }

                            if (variantDto.Weight.HasValue)
                            {
                                existingVariant.Weight = variantDto.Weight.Value;
                            }

                            if (variantDto.WeightUnit != null)
                            {
                                existingVariant.WeightUnit = variantDto.WeightUnit;
                            }

                            if (variantDto.Length.HasValue)
                            {
                                existingVariant.Length = variantDto.Length.Value;
                            }

                            if (variantDto.Breadth.HasValue)
                            {
                                existingVariant.Breadth = variantDto.Breadth.Value;
                            }

                            if (variantDto.Height.HasValue)
                            {
                                existingVariant.Height = variantDto.Height.Value;
                            }

                            if (variantDto.DimensionUnit != null)
                            {
                                existingVariant.DimensionUnit = variantDto.DimensionUnit;
                            }

                            if (variantDto.Volume.HasValue)
                            {
                                existingVariant.Volume = variantDto.Volume.Value;
                            }

                            if (variantDto.VolumeUnit != null)
                            {
                                existingVariant.VolumeUnit = variantDto.VolumeUnit;
                            }

                            existingVariant.UpdatedAt = DateTime.UtcNow;
                            _context.Entry(existingVariant).State = EntityState.Modified;
                        }
                        // If variant doesn't exist, create a new one
                        else if (variantDto.Id == 0)
                        {
                            // Create new variant
                            var newVariant = new ProductVariant
                            {
                                ProductId = product.Id,
                                SKU = variantDto.SKU ?? $"SKU-{product.Id}-{Guid.NewGuid().ToString()[..8]}",
                                Barcode = variantDto.Barcode ?? string.Empty,
                                Price = variantDto.Price ?? 0,
                                Cost = variantDto.Cost ?? 0,
                                MRP = variantDto.MRP ?? 0,
                                StockQuantity = variantDto.StockQuantity ?? 0,
                                IsActive = variantDto.IsActive ?? true,
                                Size = variantDto.Size ?? string.Empty,
                                Weight = variantDto.Weight ?? 0,
                                WeightUnit = variantDto.WeightUnit ?? string.Empty,
                                Length = variantDto.Length ?? 0,
                                Breadth = variantDto.Breadth ?? 0,
                                Height = variantDto.Height ?? 0,
                                DimensionUnit = variantDto.DimensionUnit ?? string.Empty,
                                Volume = variantDto.Volume ?? 0,
                                VolumeUnit = variantDto.VolumeUnit ?? string.Empty,
                                CreatedAt = DateTime.UtcNow,
                                UpdatedAt = DateTime.UtcNow
                            };

                            _context.ProductVariants.Add(newVariant);
                            await _context.SaveChangesAsync();

                            // Always use default location (ID: 1) for inventory
                            const int locationId = 1;

                            // Create inventory item for the selected location
                            var inventoryItem = new InventoryItem
                            {
                                LocationId = locationId,
                                ProductVariantId = newVariant.Id,
                                StockQuantity = newVariant.StockQuantity,
                                LastRestockedAt = DateTime.UtcNow,
                                CreatedAt = DateTime.UtcNow,
                                UpdatedAt = DateTime.UtcNow
                            };

                            _context.InventoryItems.Add(inventoryItem);
                        }
                    }
                }

                _context.Entry(product).State = EntityState.Modified;

                await _context.SaveChangesAsync();

                // Reload product with all relationships for response
                product = await _context.Products
                    .Include(p => p.Category)
                    .Include(p => p.ProductType)
                    .Include(p => p.Variants)
                        .ThenInclude(v => v.InventoryItems)
                    .Include(p => p.Collections)
                    .Include(p => p.Images)
                    .Include(p => p.Variants)
                    .FirstOrDefaultAsync(p => p.Id == id);

                var updatedProductDto = new ProductDTO
                {
                    Id = product.Id,
                    Name = product.Name,
                    Description = product.Description,
                    ImageUrl = product.ImageUrl,
                    Barcode = product.Barcode,
                    IsActive = product.IsActive,
                    IsFeatured = product.IsFeatured,
                    HasVariants = product.HasVariants,
                    CategoryId = product.CategoryId,
                    CategoryName = product.Category.Name,
                    ProductTypeId = product.ProductTypeId,
                    ProductTypeName = product.ProductType?.Name ?? string.Empty,
                    StockQuantity = product.Variants.Any() ? product.Variants.Sum(v => v.InventoryItems.Sum(ii => ii.StockQuantity)) : 0,
                    ReorderLevel = product.Variants.Any() ? product.Variants.Min(v => v.InventoryItems.Any() ? v.InventoryItems.Min(ii => ii.ReorderLevel) : 0) : 0,
                    CollectionIds = product.Collections.Select(c => c.Id).ToList(),
                    CollectionNames = product.Collections.Select(c => c.Name).ToList(),
                    Images = product.Images.Select(i => new ProductImageDTO
                    {
                        Id = i.Id,
                        Url = i.ImageUrl,
                        IsMain = i.IsMain,
                        DisplayOrder = i.DisplayOrder
                    }).ToList(),
                    Variants = product.Variants.Select(v => new ProductVariantDTO
                    {
                        Id = v.Id,
                        SKU = v.SKU,
                        Barcode = v.Barcode,
                        Price = v.Price,
                        Cost = v.Cost,
                        MRP = v.MRP,
                        StockQuantity = v.InventoryItems.Sum(ii => ii.StockQuantity),
                        IsActive = v.IsActive,
                        ProductId = v.ProductId,
                        ProductName = product.Name,
                        // Physical attributes
                        Size = v.Size,
                        Weight = v.Weight,
                        WeightUnit = v.WeightUnit,
                        Length = v.Length,
                        Breadth = v.Breadth,
                        Height = v.Height,
                        DimensionUnit = v.DimensionUnit,
                        Volume = v.Volume,
                        VolumeUnit = v.VolumeUnit,
                        // Inventory items
                        InventoryItems = v.InventoryItems.Select(ii => new InventoryItemDTO
                        {
                            Id = ii.Id,
                            LocationId = ii.LocationId,
                            LocationName = ii.Location != null ? ii.Location.Name : "",
                            LocationCode = ii.Location != null ? ii.Location.Code : "",
                            StockQuantity = ii.StockQuantity,
                            ReorderLevel = ii.ReorderLevel,
                            LastRestockedAt = ii.LastRestockedAt
                        }).ToList()
                    }).ToList()
                };

                return Ok(updatedProductDto);
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!ProductExists(id))
                {
                    return NotFound();
                }
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating product");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error updating product");
            }
        }

        // DELETE: api/Product/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteProduct(int id)
        {
            try
            {
                _logger.LogInformation("DeleteProduct called with id: {Id}", id);

                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    _logger.LogWarning("No authenticated user found when deleting product");
                    return Unauthorized();
                }

                var product = await _context.Products.FindAsync(id);
                if (product == null)
                {
                    return NotFound();
                }

                // Check if user has access to the product's store
                if (!await CanAccessStoreAsync(product.StoreId))
                {
                    _logger.LogWarning("User {UserId} attempted to delete product from unauthorized store {StoreId}",
                        currentUser.Id, product.StoreId);
                    return Forbid();
                }

                _context.Products.Remove(product);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Product {Id} deleted successfully", id);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting product {Id}", id);
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error deleting product");
            }
        }

         private bool ProductExists(int id)
        {
            return _context.Products.Any(e => e.Id == id);
        }

 // POST: api/Product/{productId}/variant
        [HttpPost("{productId}/variant")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ProductVariantDTO>> CreateProductVariant(int productId, CreateProductVariantDTO variantDto)
        {
            try
            {
                // Check if product exists
                var product = await _context.Products.FindAsync(productId);
                if (product == null)
                {
                    return BadRequest("Product not found");
                }

                // Check if user has access to the product's store
                if (!await CanAccessStoreAsync(product.StoreId))
                {
                    var currentUser = await _userManager.GetUserAsync(User);
                    _logger.LogWarning("User {UserId} attempted to create variant for product from unauthorized store {StoreId}",
                        currentUser?.Id, product.StoreId);
                    return Forbid();
                }

                // Check if SKU is unique
                if (!string.IsNullOrEmpty(variantDto.SKU) && await _context.ProductVariants.AnyAsync(pv => pv.SKU == variantDto.SKU))
                {
                    return BadRequest("SKU must be unique");
                }

                // Create variant
                var variant = new ProductVariant
                {
                    SKU = variantDto.SKU ?? $"SKU-{Guid.NewGuid().ToString().Substring(0, 8)}",
                    Barcode = variantDto.Barcode ?? string.Empty,
                    Price = variantDto.Price,
                    Cost = variantDto.Cost,
                    MRP = variantDto.MRP,
                    StockQuantity = variantDto.StockQuantity,
                    IsActive = variantDto.IsActive,
                    ProductId = productId,
                    // Physical attributes
                    Size = variantDto.Size,
                    Weight = variantDto.Weight,
                    WeightUnit = variantDto.WeightUnit,
                    Length = variantDto.Length,
                    Breadth = variantDto.Breadth,
                    Height = variantDto.Height,
                    DimensionUnit = variantDto.DimensionUnit,
                    Volume = variantDto.Volume,
                    VolumeUnit = variantDto.VolumeUnit,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.ProductVariants.Add(variant);
                await _context.SaveChangesAsync();

                // Update product to indicate it has variants
                if (!product.HasVariants)
                {
                    product.HasVariants = true;
                    product.UpdatedAt = DateTime.UtcNow;
                    await _context.SaveChangesAsync();
                }

                // Always use default location (ID: 1) for inventory
                const int locationId = 1;

                // Create inventory item for the selected location
                var inventoryItem = new InventoryItem
                {
                    LocationId = locationId,
                    ProductVariantId = variant.Id,
                    StockQuantity = variantDto.StockQuantity,
                    ReorderLevel = variantDto.ReorderLevel,
                    LastRestockedAt = DateTime.UtcNow,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.InventoryItems.Add(inventoryItem);
                await _context.SaveChangesAsync();

                // Return created variant
                var createdVariantDto = new ProductVariantDTO
                {
                    Id = variant.Id,
                    SKU = variant.SKU,
                    Barcode = variant.Barcode,
                    Price = variant.Price,
                    Cost = variant.Cost,
                    MRP = variant.MRP,
                    StockQuantity = inventoryItem.StockQuantity, // Use the inventory item's stock quantity
                    IsActive = variant.IsActive,
                    ProductId = variant.ProductId,
                    ProductName = product.Name,
                    // Physical attributes
                    Size = variant.Size,
                    Weight = variant.Weight,
                    WeightUnit = variant.WeightUnit,
                    Length = variant.Length,
                    Breadth = variant.Breadth,
                    Height = variant.Height,
                    DimensionUnit = variant.DimensionUnit,
                    Volume = variant.Volume,
                    VolumeUnit = variant.VolumeUnit,
                    // Inventory items
                    InventoryItems = new List<InventoryItemDTO>
                    {
                        new InventoryItemDTO
                        {
                            Id = inventoryItem.Id,
                            LocationId = inventoryItem.LocationId,
                            LocationName = "Main Warehouse", // Default location name
                            LocationCode = "MAIN", // Default location code
                            StockQuantity = inventoryItem.StockQuantity,
                            ReorderLevel = inventoryItem.ReorderLevel,
                            LastRestockedAt = inventoryItem.LastRestockedAt
                        }
                    }
                };

                // Return the created variant directly since we don't have a GetProductVariant method
                return CreatedAtAction(nameof(GetProduct), new { id = variant.ProductId }, createdVariantDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating product variant");
                return StatusCode(500, "An error occurred while creating the product variant");
            }
        }

        // PUT: api/Product/variant/{id}
        [HttpPut("variant/{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> UpdateProductVariant(int id, UpdateProductVariantDTO variantDto)
        {
            try
            {
                var variant = await _context.ProductVariants
                    .Include(pv => pv.InventoryItems)
                    .Include(pv => pv.Product)
                    .FirstOrDefaultAsync(pv => pv.Id == id);

                if (variant == null)
                {
                    return NotFound();
                }

                // Check if user has access to the product's store
                if (!await CanAccessStoreAsync(variant.Product.StoreId))
                {
                    var currentUser = await _userManager.GetUserAsync(User);
                    _logger.LogWarning("User {UserId} attempted to update variant for product from unauthorized store {StoreId}",
                        currentUser?.Id, variant.Product.StoreId);
                    return Forbid();
                }

                // Check if SKU is unique if it's being changed
                if (!string.IsNullOrEmpty(variantDto.SKU) && variantDto.SKU != variant.SKU &&
                    await _context.ProductVariants.AnyAsync(pv => pv.SKU == variantDto.SKU))
                {
                    return BadRequest("SKU must be unique");
                }

                // Update variant properties
                if (!string.IsNullOrEmpty(variantDto.SKU))
                {
                    variant.SKU = variantDto.SKU;
                }

                if (!string.IsNullOrEmpty(variantDto.Barcode))
                {
                    variant.Barcode = variantDto.Barcode;
                }

                if (variantDto.Price.HasValue)
                {
                    variant.Price = variantDto.Price.Value;
                }

                if (variantDto.Cost.HasValue)
                {
                    variant.Cost = variantDto.Cost.Value;
                }

                if (variantDto.MRP.HasValue)
                {
                    variant.MRP = variantDto.MRP.Value;
                }

                if (variantDto.StockQuantity.HasValue)
                {
                    variant.StockQuantity = variantDto.StockQuantity.Value;

                    // Also update the inventory items
                    if (variant.InventoryItems != null && variant.InventoryItems.Any())
                    {
                        foreach (var item in variant.InventoryItems)
                        {
                            item.StockQuantity = variantDto.StockQuantity.Value;
                            item.UpdatedAt = DateTime.UtcNow;
                            _context.Entry(item).State = EntityState.Modified;
                        }
                    }
                }

                if (variantDto.ReorderLevel.HasValue)
                {
                    // Also update the inventory items
                    if (variant.InventoryItems != null && variant.InventoryItems.Any())
                    {
                        foreach (var item in variant.InventoryItems)
                        {
                            item.ReorderLevel = variantDto.ReorderLevel.Value;
                            item.UpdatedAt = DateTime.UtcNow;
                            _context.Entry(item).State = EntityState.Modified;
                        }
                    }
                }

                if (variantDto.IsActive.HasValue)
                {
                    variant.IsActive = variantDto.IsActive.Value;
                }

                // Update physical attributes
                if (variantDto.Size != null)
                {
                    variant.Size = variantDto.Size;
                }

                if (variantDto.Weight.HasValue)
                {
                    variant.Weight = variantDto.Weight.Value;
                }

                if (variantDto.WeightUnit != null)
                {
                    variant.WeightUnit = variantDto.WeightUnit;
                }

                if (variantDto.Length.HasValue)
                {
                    variant.Length = variantDto.Length.Value;
                }

                if (variantDto.Breadth.HasValue)
                {
                    variant.Breadth = variantDto.Breadth.Value;
                }

                if (variantDto.Height.HasValue)
                {
                    variant.Height = variantDto.Height.Value;
                }

                if (variantDto.DimensionUnit != null)
                {
                    variant.DimensionUnit = variantDto.DimensionUnit;
                }

                if (variantDto.Volume.HasValue)
                {
                    variant.Volume = variantDto.Volume.Value;
                }

                if (variantDto.VolumeUnit != null)
                {
                    variant.VolumeUnit = variantDto.VolumeUnit;
                }

                variant.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating product variant with ID {Id}", id);
                return StatusCode(500, "An error occurred while updating the product variant");
            }
        }

        // DELETE: api/Product/variant/{id}
        [HttpDelete("variant/{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteProductVariant(int id)
        {
            try
            {
                var variant = await _context.ProductVariants
                    .Include(pv => pv.Product)
                    .FirstOrDefaultAsync(pv => pv.Id == id);

                if (variant == null)
                {
                    return NotFound();
                }

                // Check if user has access to the product's store
                if (!await CanAccessStoreAsync(variant.Product.StoreId))
                {
                    var currentUser = await _userManager.GetUserAsync(User);
                    _logger.LogWarning("User {UserId} attempted to delete variant for product from unauthorized store {StoreId}",
                        currentUser?.Id, variant.Product.StoreId);
                    return Forbid();
                }

                // Remove variant
                _context.ProductVariants.Remove(variant);
                await _context.SaveChangesAsync();

                // Check if product still has variants
                var productHasVariants = await _context.ProductVariants
                    .AnyAsync(pv => pv.ProductId == variant.ProductId);

                if (!productHasVariants)
                {
                    // Update product to indicate it no longer has variants
                    var product = await _context.Products.FindAsync(variant.ProductId);
                    if (product != null)
                    {
                        product.HasVariants = false;
                        product.UpdatedAt = DateTime.UtcNow;
                        await _context.SaveChangesAsync();
                    }
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting product variant with ID {Id}", id);
                return StatusCode(500, "An error occurred while deleting the product variant");
            }
        }

        // POST: api/Product/{id}/images
        [HttpPost("{id}/images")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<IEnumerable<ProductImageDTO>>> AddProductImages(int id, [FromBody] List<ProductImageDTO> images)
        {
            try
            {
                var product = await _context.Products
                    .Include(p => p.Images)
                    .FirstOrDefaultAsync(p => p.Id == id);

                if (product == null)
                {
                    return NotFound("Product not found");
                }

                // Check if user has access to the product's store
                if (!await CanAccessStoreAsync(product.StoreId))
                {
                    var currentUser = await _userManager.GetUserAsync(User);
                    _logger.LogWarning("User {UserId} attempted to add images to product from unauthorized store {StoreId}",
                        currentUser?.Id, product.StoreId);
                    return Forbid();
                }

                if (images == null || !images.Any())
                {
                    return BadRequest("No images provided");
                }

                // Add new images
                var productImages = images.Select((image, index) => new ProductImage
                {
                    ProductId = product.Id,
                    ImageUrl = image.Url,
                    IsMain = image.IsMain,
                    DisplayOrder = image.DisplayOrder != 0 ? image.DisplayOrder : (product.Images.Any() ? product.Images.Max(i => i.DisplayOrder) + index + 1 : index)
                }).ToList();

                // If setting a new main image, unset the current main image
                if (productImages.Any(pi => pi.IsMain))
                {
                    foreach (var existingImage in product.Images.Where(i => i.IsMain))
                    {
                        existingImage.IsMain = false;
                        _context.Entry(existingImage).State = EntityState.Modified;
                    }
                }

                _context.ProductImages.AddRange(productImages);
                await _context.SaveChangesAsync();

                return Ok(productImages.Select(i => new ProductImageDTO
                {
                    Id = i.Id,
                    Url = i.ImageUrl,
                    IsMain = i.IsMain,
                    DisplayOrder = i.DisplayOrder
                }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding product images");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error adding product images");
            }
        }

        // DELETE: api/Product/{productId}/images/{imageId}
        [HttpDelete("{productId}/images/{imageId}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteProductImage(int productId, int imageId)
        {
            try
            {
                var product = await _context.Products
                    .Include(p => p.Images)
                    .FirstOrDefaultAsync(p => p.Id == productId);

                if (product == null)
                {
                    return NotFound("Product not found");
                }

                // Check if user has access to the product's store
                if (!await CanAccessStoreAsync(product.StoreId))
                {
                    var currentUser = await _userManager.GetUserAsync(User);
                    _logger.LogWarning("User {UserId} attempted to delete image from product in unauthorized store {StoreId}",
                        currentUser?.Id, product.StoreId);
                    return Forbid();
                }

                var image = product.Images.FirstOrDefault(i => i.Id == imageId);
                if (image == null)
                {
                    return NotFound("Image not found");
                }

                // If deleting the main image, set another image as main
                if (image.IsMain && product.Images.Count > 1)
                {
                    var newMainImage = product.Images.FirstOrDefault(i => i.Id != imageId);
                    if (newMainImage != null)
                    {
                        newMainImage.IsMain = true;
                        _context.Entry(newMainImage).State = EntityState.Modified;
                    }
                }

                _context.ProductImages.Remove(image);
                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting product image");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error deleting product image");
            }
        }

        // PUT: api/Product/{productId}/images/{imageId}/main
        [HttpPut("{productId}/images/{imageId}/main")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ProductImageDTO>> SetMainProductImage(int productId, int imageId)
        {
            try
            {
                var product = await _context.Products
                    .Include(p => p.Images)
                    .FirstOrDefaultAsync(p => p.Id == productId);

                if (product == null)
                {
                    return NotFound("Product not found");
                }

                // Check if user has access to the product's store
                if (!await CanAccessStoreAsync(product.StoreId))
                {
                    var currentUser = await _userManager.GetUserAsync(User);
                    _logger.LogWarning("User {UserId} attempted to set main image for product in unauthorized store {StoreId}",
                        currentUser?.Id, product.StoreId);
                    return Forbid();
                }

                var newMainImage = product.Images.FirstOrDefault(i => i.Id == imageId);
                if (newMainImage == null)
                {
                    return NotFound("Image not found");
                }

                // Unset current main image
                foreach (var image in product.Images.Where(i => i.IsMain))
                {
                    image.IsMain = false;
                    _context.Entry(image).State = EntityState.Modified;
                }

                // Set new main image
                newMainImage.IsMain = true;
                _context.Entry(newMainImage).State = EntityState.Modified;

                await _context.SaveChangesAsync();

                return Ok(new ProductImageDTO
                {
                    Id = newMainImage.Id,
                    Url = newMainImage.ImageUrl,
                    IsMain = newMainImage.IsMain,
                    DisplayOrder = newMainImage.DisplayOrder
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting main product image");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error setting main product image");
            }
        }

       }
}
