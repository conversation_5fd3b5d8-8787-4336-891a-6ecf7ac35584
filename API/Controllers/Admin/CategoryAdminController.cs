using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using MyShop.API.DTOs;
using MyShop.API.Services;
using System.IO;
using System.Collections.Generic;

namespace MyShop.API.Controllers.Admin
{
    [Route("api/admin/category")]
    [ApiController]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public class CategoryController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly IWebHostEnvironment _environment;
        private readonly ILogger<CategoryController> _logger;
        private readonly ICurrentUserService _currentUserService;

        public CategoryController(
            ApplicationDbContext context,
            IWebHostEnvironment environment,
            ILogger<CategoryController> logger,
            ICurrentUserService currentUserService)
        {
            _context = context;
            _environment = environment;
            _logger = logger;
            _currentUserService = currentUserService;
        }

        // GET: api/admin/category
        [HttpGet]
        public async Task<ActionResult<IEnumerable<CategoryDTO>>> GetCategories(
            [FromQuery] int? storeId = null,
            [FromQuery] bool? isActive = null,
            [FromQuery] bool? parentOnly = false)
        {
            try
            {
                var currentUser = await _currentUserService.GetCurrentUserAsync();
                if (currentUser == null)
                    return Unauthorized();

                IQueryable<Category> query = _context.Categories
                    .Include(c => c.Products)
                    .Include(c => c.Children)
                    .AsQueryable();

                // Filter by store if specified or if user is not super admin
                if (storeId.HasValue)
                {
                    query = query.Where(c => c.StoreId == storeId.Value);
                }
                else if (!currentUser.IsSuperAdmin)
                {
                    // Regular admin can only see categories from stores they have access to
                    var accessibleStoreIds = await _context.StoreAdmins
                        .Where(sa => sa.UserId == currentUser.Id)
                        .Select(sa => sa.StoreId)
                        .ToListAsync();

                    query = query.Where(c => accessibleStoreIds.Contains(c.StoreId));
                }

                // Apply additional filters
                if (isActive.HasValue)
                {
                    query = query.Where(c => c.IsActive == isActive.Value);
                }

                if (parentOnly == true)
                {
                    query = query.Where(c => c.ParentId == null);
                }

                var categories = await query
                    .OrderBy(c => c.StoreId)
                    .ThenBy(c => c.DisplayOrder)
                    .ToListAsync();

                var categoryDtos = categories.Select(c => new CategoryDTO
                {
                    Id = c.Id,
                    Name = c.Name,
                    Description = c.Description,
                    ImageUrl = c.ImageUrl,
                    IsActive = c.IsActive,
                    DisplayOrder = c.DisplayOrder,
                    ProductCount = c.Products.Count,
                    ParentId = c.ParentId,
                    IsSale = c.IsSale,
                    StoreId = c.StoreId
                }).ToList();

                return Ok(categoryDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting categories");
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // GET: api/admin/category/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<CategoryDTO>> GetCategory(int id)
        {
            try
            {
                var currentUser = await _currentUserService.GetCurrentUserAsync();
                if (currentUser == null)
                    return Unauthorized();

                var category = await _context.Categories
                    .Include(c => c.Products)
                    .Include(c => c.Children)
                    .FirstOrDefaultAsync(c => c.Id == id);

                if (category == null)
                    return NotFound();

                // Check if user has access to this store
                if (!currentUser.IsSuperAdmin)
                {
                    var hasAccess = await _context.StoreAdmins
                        .AnyAsync(sa => sa.UserId == currentUser.Id && sa.StoreId == category.StoreId);

                    if (!hasAccess)
                        return Forbid();
                }

                var categoryDto = new CategoryDTO
                {
                    Id = category.Id,
                    Name = category.Name,
                    Description = category.Description,
                    ImageUrl = category.ImageUrl,
                    IsActive = category.IsActive,
                    DisplayOrder = category.DisplayOrder,
                    ProductCount = category.Products.Count,
                    ParentId = category.ParentId,
                    IsSale = category.IsSale,
                    StoreId = category.StoreId,
                    Children = category.Children.Select(child => new CategoryDTO
                    {
                        Id = child.Id,
                        Name = child.Name,
                        Description = child.Description,
                        ImageUrl = child.ImageUrl,
                        IsActive = child.IsActive,
                        DisplayOrder = child.DisplayOrder,
                        ProductCount = child.Products.Count,
                        ParentId = child.ParentId,
                        IsSale = child.IsSale,
                        StoreId = child.StoreId
                    }).ToList()
                };

                return Ok(categoryDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting category {CategoryId}", id);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // POST: api/admin/category
        [HttpPost]
        public async Task<ActionResult<CategoryDTO>> CreateCategory(CreateCategoryDTO categoryDto)
        {
            try
            {
                if (!ModelState.IsValid)
                    return BadRequest(ModelState);

                var currentUser = await _currentUserService.GetCurrentUserAsync();
                if (currentUser == null)
                    return Unauthorized();

                // Validate store access
                if (!await HasStoreAccessAsync(categoryDto.StoreId))
                    return Forbid();

                // Validate parent category if specified
                if (categoryDto.ParentId.HasValue)
                {
                    var parentExists = await _context.Categories.AnyAsync(c =>
                        c.Id == categoryDto.ParentId.Value &&
                        c.StoreId == categoryDto.StoreId);

                    if (!parentExists)
                        return BadRequest("Parent category does not exist");
                }

                var category = new Category
                {
                    Name = categoryDto.Name,
                    Description = categoryDto.Description,
                    ImageUrl = categoryDto.ImageUrl ?? string.Empty,
                    IsActive = categoryDto.IsActive,
                    DisplayOrder = categoryDto.DisplayOrder,
                    ParentId = categoryDto.ParentId,
                    IsSale = categoryDto.IsSale,
                    StoreId = categoryDto.StoreId
                };

                _context.Categories.Add(category);
                await _context.SaveChangesAsync();

                var createdCategoryDto = new CategoryDTO
                {
                    Id = category.Id,
                    Name = category.Name,
                    Description = category.Description,
                    ImageUrl = category.ImageUrl,
                    IsActive = category.IsActive,
                    DisplayOrder = category.DisplayOrder,
                    ProductCount = 0,
                    ParentId = category.ParentId,
                    IsSale = category.IsSale,
                    StoreId = category.StoreId
                };

                return CreatedAtAction(nameof(GetCategory), new { id = category.Id }, createdCategoryDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating category");
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // PUT: api/admin/category/{id}
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateCategory(int id, UpdateCategoryDTO categoryDto)
        {
            try
            {
                if (!ModelState.IsValid)
                    return BadRequest(ModelState);

                var category = await _context.Categories.FindAsync(id);
                if (category == null)
                    return NotFound();

                // Validate store access
                if (!await HasStoreAccessAsync(category.StoreId))
                    return Forbid();

                // Validate parent category if specified
                if (categoryDto.ParentId.HasValue)
                {
                    // Prevent circular reference
                    if (categoryDto.ParentId.Value == id)
                        return BadRequest("Category cannot be its own parent");

                    var parentExists = await _context.Categories.AnyAsync(c =>
                        c.Id == categoryDto.ParentId.Value &&
                        c.StoreId == category.StoreId);

                    if (!parentExists)
                        return BadRequest("Parent category does not exist");

                    // Check for circular references
                    var childrenIds = await GetAllChildrenIds(id, category.StoreId);
                    if (childrenIds.Contains(categoryDto.ParentId.Value))
                        return BadRequest("Cannot set a child category as parent (circular reference)");
                }

                category.Name = categoryDto.Name;
                category.Description = categoryDto.Description;
                if (!string.IsNullOrEmpty(categoryDto.ImageUrl))
                {
                    category.ImageUrl = categoryDto.ImageUrl;
                }
                category.IsActive = categoryDto.IsActive;
                category.DisplayOrder = categoryDto.DisplayOrder;
                category.ParentId = categoryDto.ParentId;
                category.IsSale = categoryDto.IsSale;
                category.UpdatedAt = DateTime.UtcNow;

                _context.Entry(category).State = EntityState.Modified;

                try
                {
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!CategoryExists(id))
                        return NotFound();
                    else
                        throw;
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating category {CategoryId}", id);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // DELETE: api/admin/category/{id}
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteCategory(int id)
        {
            try
            {
                var category = await _context.Categories
                    .Include(c => c.Children)
                    .FirstOrDefaultAsync(c => c.Id == id);

                if (category == null)
                    return NotFound();

                // Validate store access
                if (!await HasStoreAccessAsync(category.StoreId))
                    return Forbid();

                // Check if category has products
                var hasProducts = await _context.Products.AnyAsync(p => p.CategoryId == id);
                if (hasProducts)
                    return BadRequest("Cannot delete category with associated products");

                // Check if the category has child categories
                if (category.Children.Any())
                    return BadRequest("Cannot delete category with child categories. Please delete or reassign child categories first.");

                // Delete the image file if it exists
                if (!string.IsNullOrEmpty(category.ImageUrl))
                {
                    try
                    {
                        // Extract filename from URL
                        var uri = new Uri(category.ImageUrl);
                        var filename = Path.GetFileName(uri.LocalPath);
                        var imagePath = Path.Combine(_environment.WebRootPath, "uploads", "images", filename);

                        if (System.IO.File.Exists(imagePath))
                        {
                            System.IO.File.Delete(imagePath);
                            _logger.LogInformation("Deleted image file for category {CategoryId}: {ImagePath}", id, imagePath);
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log the error but continue with category deletion
                        _logger.LogError(ex, "Error deleting image file for category {CategoryId}", id);
                    }
                }

                _context.Categories.Remove(category);
                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting category {CategoryId}", id);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // Helper method to get all children IDs recursively
        private async Task<List<int>> GetAllChildrenIds(int categoryId, int storeId)
        {
            var result = new List<int>();
            var directChildren = await _context.Categories
                .Where(c => c.ParentId == categoryId && c.StoreId == storeId)
                .Select(c => c.Id)
                .ToListAsync();

            result.AddRange(directChildren);

            foreach (var childId in directChildren)
            {
                var grandChildren = await GetAllChildrenIds(childId, storeId);
                result.AddRange(grandChildren);
            }

            return result;
        }

        // POST: api/admin/category/bulk-delete
        [HttpPost("bulk-delete")]
        public async Task<IActionResult> BulkDeleteCategories([FromBody] BulkCategoryIdsDTO dto)
        {
            try
            {
                if (dto.CategoryIds == null || dto.CategoryIds.Count == 0)
                    return BadRequest("No category IDs provided");

                var currentUser = await _currentUserService.GetCurrentUserAsync();
                if (currentUser == null)
                    return Unauthorized();

                // Get categories to delete
                var categoriesToDelete = await _context.Categories
                    .Where(c => dto.CategoryIds.Contains(c.Id))
                    .ToListAsync();

                if (categoriesToDelete.Count == 0)
                    return NotFound("None of the specified categories were found");

                // Check store access for each category
                foreach (var category in categoriesToDelete)
                {
                    if (!await HasStoreAccessAsync(category.StoreId))
                        return Forbid();
                }

                // Check if any category has children
                var categoryIdsWithChildren = await _context.Categories
                    .Where(c => dto.CategoryIds.Contains(c.Id) && _context.Categories.Any(child => child.ParentId == c.Id))
                    .Select(c => c.Id)
                    .ToListAsync();

                if (categoryIdsWithChildren.Any())
                {
                    return BadRequest($"Categories with IDs {string.Join(", ", categoryIdsWithChildren)} have child categories. Please delete or reassign child categories first.");
                }

                // Check if any category has products
                var categoryIdsWithProducts = await _context.Categories
                    .Where(c => dto.CategoryIds.Contains(c.Id) && _context.Products.Any(p => p.CategoryId == c.Id))
                    .Select(c => c.Id)
                    .ToListAsync();

                if (categoryIdsWithProducts.Any())
                {
                    return BadRequest($"Categories with IDs {string.Join(", ", categoryIdsWithProducts)} have associated products. Please reassign or delete the products first.");
                }

                // Delete image files
                foreach (var category in categoriesToDelete)
                {
                    if (!string.IsNullOrEmpty(category.ImageUrl))
                    {
                        try
                        {
                            // Extract filename from URL
                            var uri = new Uri(category.ImageUrl);
                            var filename = Path.GetFileName(uri.LocalPath);
                            var imagePath = Path.Combine(_environment.WebRootPath, "uploads", "images", filename);

                            if (System.IO.File.Exists(imagePath))
                            {
                                System.IO.File.Delete(imagePath);
                                _logger.LogInformation("Deleted image file for category {CategoryId}: {ImagePath}", category.Id, imagePath);
                            }
                        }
                        catch (Exception ex)
                        {
                            // Log the error but continue with category deletion
                            _logger.LogError(ex, "Error deleting image file for category {CategoryId}", category.Id);
                        }
                    }
                }

                // Delete categories
                _context.Categories.RemoveRange(categoriesToDelete);
                await _context.SaveChangesAsync();

                return Ok(new { message = $"Successfully deleted {categoriesToDelete.Count} categories" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error bulk deleting categories");
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // PUT: api/admin/category/bulk-status
        [HttpPut("bulk-status")]
        public async Task<IActionResult> BulkUpdateCategoryStatus([FromBody] BulkUpdateCategoryStatusDTO dto)
        {
            try
            {
                if (dto.CategoryIds == null || dto.CategoryIds.Count == 0)
                    return BadRequest("No category IDs provided");

                var currentUser = await _currentUserService.GetCurrentUserAsync();
                if (currentUser == null)
                    return Unauthorized();

                // Get categories to update
                var categoriesToUpdate = await _context.Categories
                    .Where(c => dto.CategoryIds.Contains(c.Id))
                    .ToListAsync();

                if (categoriesToUpdate.Count == 0)
                    return NotFound("None of the specified categories were found");

                // Check store access for each category
                foreach (var category in categoriesToUpdate)
                {
                    if (!await HasStoreAccessAsync(category.StoreId))
                        return Forbid();
                }

                // Update status
                foreach (var category in categoriesToUpdate)
                {
                    category.IsActive = dto.IsActive;
                    category.UpdatedAt = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();

                return Ok(new { message = $"Successfully updated status for {categoriesToUpdate.Count} categories" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error bulk updating category status");
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        private bool CategoryExists(int id)
        {
            return _context.Categories.Any(e => e.Id == id);
        }

        private async Task<bool> HasStoreAccessAsync(int storeId)
        {
            var currentUser = await _currentUserService.GetCurrentUserAsync();
            if (currentUser == null)
                return false;

            if (currentUser.IsSuperAdmin)
                return true;

            return await _context.StoreAdmins
                .AnyAsync(sa => sa.UserId == currentUser.Id && sa.StoreId == storeId);
        }
    }
}
