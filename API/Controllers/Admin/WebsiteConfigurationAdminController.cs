using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MyShop.API.Attributes;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using MyShop.API.DTOs;
using MyShop.API.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MyShop.API.Controllers.Admin
{
    [Route("api/admin/website-configuration")]
    [ApiController]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public class WebsiteConfigurationAdminController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILogger<WebsiteConfigurationAdminController> _logger;
        private readonly ICurrentUserService _currentUserService;

        public WebsiteConfigurationAdminController(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            ILogger<WebsiteConfigurationAdminController> logger,
            ICurrentUserService currentUserService)
        {
            _context = context;
            _userManager = userManager;
            _logger = logger;
            _currentUserService = currentUserService;
        }

        // GET: api/admin/website-configuration
        [HttpGet]
        [Permission("WebsiteConfiguration", "View")]
        public async Task<ActionResult<IEnumerable<WebsiteConfigurationDTO>>> GetAllWebsiteConfigurations()
        {
            try
            {
                var currentUser = await _currentUserService.GetCurrentUserAsync();
                if (currentUser == null)
                    return Unauthorized();

                // Check if user is super admin
                var isSuperAdmin = await _userManager.IsInRoleAsync(currentUser, "SuperAdmin");

                IQueryable<WebsiteConfiguration> query = _context.WebsiteConfigurations
                    .Include(wc => wc.Store);

                // If not super admin, only return configurations for stores the user has access to
                if (!isSuperAdmin)
                {
                    var accessibleStoreIds = await _context.StoreAdmins
                        .Where(sa => sa.UserId == currentUser.Id)
                        .Select(sa => sa.StoreId)
                        .ToListAsync();

                    query = query.Where(wc => accessibleStoreIds.Contains(wc.StoreId));
                }

                var configurations = await query.ToListAsync();

                return Ok(configurations.Select(MapToDTO));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all website configurations");
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // GET: api/admin/website-configuration/{storeId}
        [HttpGet("{storeId}")]
        [Permission("WebsiteConfiguration", "View")]
        public async Task<ActionResult<WebsiteConfigurationDTO>> GetWebsiteConfigurationByStore(int storeId)
        {
            try
            {
                if (!await CanAccessStoreAsync(storeId))
                    return Forbid();

                var config = await _context.WebsiteConfigurations
                    .Include(wc => wc.Store)
                    .FirstOrDefaultAsync(wc => wc.StoreId == storeId);

                if (config == null)
                {
                    // Create default configuration if none exists
                    var store = await _context.Stores.FindAsync(storeId);
                    if (store == null)
                        return NotFound("Store not found");

                    config = new WebsiteConfiguration
                    {
                        StoreId = storeId,
                        WebsiteTitle = store.Name,
                        LogoUrl = store.LogoUrl,
                        // Set other default values
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };

                    _context.WebsiteConfigurations.Add(config);
                    await _context.SaveChangesAsync();
                }

                return Ok(MapToDTO(config));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting website configuration for store {StoreId}", storeId);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // PUT: api/admin/website-configuration/{storeId}
        [HttpPut("{storeId}")]
        [Permission("WebsiteConfiguration", "Edit")]
        public async Task<IActionResult> UpdateWebsiteConfiguration(int storeId, UpdateWebsiteConfigurationDTO configDto)
        {
            try
            {
                if (!await CanAccessStoreAsync(storeId))
                    return Forbid();

                var config = await _context.WebsiteConfigurations
                    .FirstOrDefaultAsync(wc => wc.StoreId == storeId);

                if (config == null)
                {
                    // Create new configuration if it doesn't exist
                    var store = await _context.Stores.FindAsync(storeId);
                    if (store == null)
                        return NotFound("Store not found");

                    config = new WebsiteConfiguration
                    {
                        StoreId = storeId,
                        CreatedAt = DateTime.UtcNow
                    };
                    _context.WebsiteConfigurations.Add(config);
                }

                // Update properties
                config.WebsiteTitle = configDto.WebsiteTitle;
                config.LogoUrl = configDto.LogoUrl;
                config.MetaDescription = configDto.MetaDescription;
                config.MetaKeywords = configDto.MetaKeywords;
                config.AnnouncementText = configDto.AnnouncementText;
                config.ShowAnnouncement = configDto.ShowAnnouncement;
                config.InstagramUrl = configDto.InstagramUrl;
                config.FacebookUrl = configDto.FacebookUrl;
                config.TwitterUrl = configDto.TwitterUrl;
                config.YoutubeUrl = configDto.YoutubeUrl;
                config.WhatsappNumber = configDto.WhatsappNumber;
                config.Email = configDto.Email;
                config.Phone = configDto.Phone;
                config.Address = configDto.Address;
                config.ShowBannerSection = configDto.ShowBannerSection;
                config.ShowCategorySection = configDto.ShowCategorySection;
                config.ShowNewArrivalsSection = configDto.ShowNewArrivalsSection;
                config.ShowCollectionSection = configDto.ShowCollectionSection;
                config.ShowBestSellingSection = configDto.ShowBestSellingSection;
                config.BannerTitle = configDto.BannerTitle;
                config.BannerSubtitle = configDto.BannerSubtitle;
                config.BannerButtonText = configDto.BannerButtonText;
                config.BannerButtonLink = configDto.BannerButtonLink;
                config.CategorySectionTitle = configDto.CategorySectionTitle;
                config.NewArrivalsSectionTitle = configDto.NewArrivalsSectionTitle;
                config.CollectionSectionTitle = configDto.CollectionSectionTitle;
                config.BestSellingSectionTitle = configDto.BestSellingSectionTitle;
                config.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating website configuration for store {StoreId}", storeId);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // PUT: api/admin/website-configuration/{storeId}/sections
        [HttpPut("{storeId}/sections")]
        [Permission("WebsiteConfiguration", "Edit")]
        public async Task<IActionResult> UpdateSectionVisibility(int storeId, [FromBody] Dictionary<string, bool> sectionVisibility)
        {
            try
            {
                if (!await CanAccessStoreAsync(storeId))
                    return Forbid();

                var config = await _context.WebsiteConfigurations
                    .FirstOrDefaultAsync(wc => wc.StoreId == storeId);

                if (config == null)
                {
                    // Create new configuration if it doesn't exist
                    var store = await _context.Stores.FindAsync(storeId);
                    if (store == null)
                        return NotFound("Store not found");

                    config = new WebsiteConfiguration
                    {
                        StoreId = storeId,
                        CreatedAt = DateTime.UtcNow
                    };
                    _context.WebsiteConfigurations.Add(config);
                }

                // Update section visibility
                if (sectionVisibility.TryGetValue("showBannerSection", out bool showBanner))
                    config.ShowBannerSection = showBanner;

                if (sectionVisibility.TryGetValue("showCategorySection", out bool showCategory))
                    config.ShowCategorySection = showCategory;

                if (sectionVisibility.TryGetValue("showNewArrivalsSection", out bool showNewArrivals))
                    config.ShowNewArrivalsSection = showNewArrivals;

                if (sectionVisibility.TryGetValue("showCollectionSection", out bool showCollection))
                    config.ShowCollectionSection = showCollection;

                if (sectionVisibility.TryGetValue("showBestSellingSection", out bool showBestSelling))
                    config.ShowBestSellingSection = showBestSelling;

                config.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating section visibility for store {StoreId}", storeId);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        // Helper methods
        private async Task<bool> CanAccessStoreAsync(int storeId)
        {
            var currentUser = await _currentUserService.GetCurrentUserAsync();
            if (currentUser == null)
                return false;

            // Super admins can access all stores
            if (await _userManager.IsInRoleAsync(currentUser, "SuperAdmin"))
                return true;

            // Check if user is an admin of the store
            return await _context.StoreAdmins
                .AnyAsync(sa => sa.UserId == currentUser.Id && sa.StoreId == storeId);
        }

        private WebsiteConfigurationDTO MapToDTO(WebsiteConfiguration config)
        {
            return new WebsiteConfigurationDTO
            {
                Id = config.Id,
                StoreId = config.StoreId,
                StoreName = config.Store?.Name,
                WebsiteTitle = config.WebsiteTitle,
                LogoUrl = config.LogoUrl,
                MetaDescription = config.MetaDescription,
                MetaKeywords = config.MetaKeywords,
                AnnouncementText = config.AnnouncementText,
                ShowAnnouncement = config.ShowAnnouncement,
                Phone = config.Phone,
                Email = config.Email,
                Address = config.Address,
                FacebookUrl = config.FacebookUrl,
                InstagramUrl = config.InstagramUrl,
                TwitterUrl = config.TwitterUrl,
                WhatsappNumber = config.WhatsappNumber,
                YoutubeUrl = config.YoutubeUrl,
                ShowBannerSection = config.ShowBannerSection,
                ShowCategorySection = config.ShowCategorySection,
                ShowNewArrivalsSection = config.ShowNewArrivalsSection,
                ShowCollectionSection = config.ShowCollectionSection,
                ShowBestSellingSection = config.ShowBestSellingSection,
                BannerTitle = config.BannerTitle,
                BannerSubtitle = config.BannerSubtitle,
                BannerButtonText = config.BannerButtonText,
                BannerButtonLink = config.BannerButtonLink,
                CategorySectionTitle = config.CategorySectionTitle,
                NewArrivalsSectionTitle = config.NewArrivalsSectionTitle,
                CollectionSectionTitle = config.CollectionSectionTitle,
                BestSellingSectionTitle = config.BestSellingSectionTitle,
                CreatedAt = config.CreatedAt,
                UpdatedAt = config.UpdatedAt
            };
        }
    }
}
