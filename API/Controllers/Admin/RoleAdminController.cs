using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MyShop.API.Controllers.Admin
{
    [Route("api/admin/role")]
    [ApiController]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public class RoleController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly RoleManager<IdentityRole> _roleManager;
        private readonly UserManager<ApplicationUser> _userManager;

        public RoleController(
            ApplicationDbContext context,
            RoleManager<IdentityRole> roleManager,
            UserManager<ApplicationUser> userManager)
        {
            _context = context;
            _roleManager = roleManager;
            _userManager = userManager;
        }

        // GET: api/admin/role
        [HttpGet]
        public async Task<ActionResult<IEnumerable<object>>> GetRoles()
        {
            var roles = await _roleManager.Roles
                .Select(r => new
                {
                    Id = r.Id,
                    Name = r.Name,
                    NormalizedName = r.NormalizedName,
                    Description = "",
                    StoreId = (int?)null,
                    StoreName = (string)null,
                    IsActive = true
                })
                .ToListAsync();

            return roles;
        }

        // GET: api/admin/role/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<object>> GetRole(string id)
        {
            var role = await _roleManager.FindByIdAsync(id);

            if (role == null)
            {
                return NotFound();
            }

            return new
            {
                Id = role.Id,
                Name = role.Name,
                NormalizedName = role.NormalizedName,
                Description = "",
                StoreId = (int?)null,
                StoreName = (string)null,
                IsActive = true
            };
        }

        // POST: api/admin/role
        [HttpPost]
        public async Task<ActionResult<object>> CreateRole(RoleCreateDto roleDto)
        {
            // Check if role already exists
            var existingRole = await _roleManager.FindByNameAsync(roleDto.Name);
            if (existingRole != null)
            {
                return BadRequest("A role with this name already exists");
            }

            // Create role
            var role = new IdentityRole
            {
                Name = roleDto.Name
            };

            var result = await _roleManager.CreateAsync(role);
            if (!result.Succeeded)
            {
                return BadRequest(string.Join(", ", result.Errors.Select(e => e.Description)));
            }

            return CreatedAtAction(nameof(GetRole), new { id = role.Id }, new
            {
                Id = role.Id,
                Name = role.Name,
                NormalizedName = role.NormalizedName,
                Description = roleDto.Description,
                StoreId = (int?)null,
                StoreName = (string)null,
                IsActive = true
            });
        }

        // PUT: api/admin/role/{id}
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateRole(string id, RoleUpdateDto roleDto)
        {
            if (id != roleDto.Id)
            {
                return BadRequest("ID mismatch");
            }

            var role = await _roleManager.FindByIdAsync(id);
            if (role == null)
            {
                return NotFound();
            }

            // Don't allow changing SuperAdmin role
            if (role.Name == "SuperAdmin")
            {
                return BadRequest("Cannot modify the SuperAdmin role");
            }

            // Check if another role with the same name exists
            var existingRole = await _roleManager.FindByNameAsync(roleDto.Name);
            if (existingRole != null && existingRole.Id != id)
            {
                return BadRequest("A role with this name already exists");
            }

            // Update role
            role.Name = roleDto.Name;

            var result = await _roleManager.UpdateAsync(role);
            if (!result.Succeeded)
            {
                return BadRequest(string.Join(", ", result.Errors.Select(e => e.Description)));
            }

            return NoContent();
        }

        // DELETE: api/admin/role/{id}
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteRole(string id)
        {
            var role = await _roleManager.FindByIdAsync(id);
            if (role == null)
            {
                return NotFound();
            }

            // Don't allow deleting SuperAdmin role
            if (role.Name == "SuperAdmin")
            {
                return BadRequest("Cannot delete the SuperAdmin role");
            }

            // Check if role is in use
            var usersInRole = await _userManager.GetUsersInRoleAsync(role.Name);
            if (usersInRole.Any())
            {
                return BadRequest("Cannot delete a role that is assigned to users");
            }

            var result = await _roleManager.DeleteAsync(role);
            if (!result.Succeeded)
            {
                return BadRequest(string.Join(", ", result.Errors.Select(e => e.Description)));
            }

            return NoContent();
        }

        // GET: api/admin/role/store/{storeId}
        [HttpGet("store/{storeId}")]
        public async Task<ActionResult<IEnumerable<object>>> GetRolesByStore(int storeId)
        {
            // Since we're using ASP.NET Identity roles, we don't have store-specific roles
            // Return all roles for now
            var roles = await _roleManager.Roles
                .Select(r => new
                {
                    Id = r.Id,
                    Name = r.Name,
                    NormalizedName = r.NormalizedName,
                    Description = "",
                    StoreId = (int?)null,
                    StoreName = (string)null,
                    IsActive = true
                })
                .ToListAsync();

            return roles;
        }

        private async Task<bool> RoleExists(string id)
        {
            return await _roleManager.FindByIdAsync(id) != null;
        }
    }

    public class RoleCreateDto
    {
        public string Name { get; set; }
        public string Description { get; set; }
    }

    public class RoleUpdateDto
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
    }
}
