using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using MyShop.API.DTOs;
using System.Linq.Expressions;

namespace MyShop.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class CouponsController : StoreAwareController
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<CouponsController> _logger;

        public CouponsController(ApplicationDbContext context, ILogger<CouponsController> logger)
        {
            _context = context;
            _logger = logger;
        }

        private static decimal CalculatePotentialSavings(decimal cartTotal, CouponType couponType, decimal couponValue, decimal? maximumDiscountAmount)
        {
            // Calculate base discount amount based on coupon type
            decimal potentialSavings = couponType switch
            {
                CouponType.Percentage => cartTotal * (couponValue / 100),
                CouponType.FixedAmount => couponValue,
                CouponType.FreeShipping => 0, // Shipping savings would be calculated differently
                _ => 0
            };

            // Apply maximum discount limit if set
            if (maximumDiscountAmount.HasValue)
                potentialSavings = Math.Min(potentialSavings, maximumDiscountAmount.Value);

            // Ensure discount doesn't exceed cart total
            return Math.Min(potentialSavings, cartTotal);
        }

        [HttpGet("cart/available")]
        public async Task<ActionResult<IEnumerable<CouponDTO>>> GetAvailableCouponsForCart(
            [FromQuery] decimal cartTotal,
            [FromQuery] List<int>? productIds = null,
            [FromQuery] List<int>? categoryIds = null,
            [FromQuery] List<int>? collectionIds = null)
        {
            try
            {
                var storeId = GetCurrentStoreId();

                // Get active coupons that should be displayed on cart page
                var coupons = await _context.Coupons
                    .Include(c => c.Category)
                    .Include(c => c.Collection)
                    .Include(c => c.Product)
                    .Where(c =>
                        c.StoreId == storeId &&
                        c.IsActive &&
                        c.DisplayOnCartPage &&
                        c.StartDate <= DateTime.UtcNow &&
                        (!c.EndDate.HasValue || c.EndDate >= DateTime.UtcNow) &&
                        (c.UsageLimit == 0 || c.UsageCount < c.UsageLimit))
                    .ToListAsync();

                // Filter coupons based on cart contents and applicability
                var availableCoupons = coupons.Where(c =>
                    // First check minimum purchase amount for all coupons
                    (!c.MinimumPurchaseAmount.HasValue || cartTotal >= c.MinimumPurchaseAmount.Value) &&
                    (c.Applicability == CouponApplicability.All ||

                    // Then check specific applicability conditions
                    c.Applicability switch
                    {
                        // Category coupons require matching category IDs
                        CouponApplicability.Category => categoryIds != null &&
                                                      c.CategoryId.HasValue &&
                                                      categoryIds.Contains(c.CategoryId.Value),

                        // Collection coupons require matching collection IDs
                        CouponApplicability.Collection => collectionIds != null &&
                                                        c.CollectionId.HasValue &&
                                                        collectionIds.Contains(c.CollectionId.Value),

                        // Product coupons require matching product IDs
                        CouponApplicability.Product => productIds != null &&
                                                     c.ProductId.HasValue &&
                                                     productIds.Contains(c.ProductId.Value),

                        // Reject any other coupon types
                        _ => false
                    })
                );

                // Map coupons to DTOs and calculate potential savings in one step
                var couponDtos = availableCoupons
                    .Select(c => {
                        // Calculate potential savings
                        decimal savings = CalculatePotentialSavings(
                            cartTotal,
                            c.Type,
                            c.Value,
                            c.MaximumDiscountAmount);

                        // Create and return the DTO with savings included
                        return new CouponDTO
                        {
                            Id = c.Id,
                            Code = c.Code,
                            Description = c.Description,
                            Type = c.Type.ToString(),
                            Value = c.Value,
                            Applicability = c.Applicability.ToString(),
                            CategoryId = c.CategoryId,
                            CategoryName = c.Category?.Name,
                            CollectionId = c.CollectionId,
                            CollectionName = c.Collection?.Name,
                            ProductId = c.ProductId,
                            ProductName = c.Product?.Name,
                            MinimumPurchaseAmount = c.MinimumPurchaseAmount,
                            MaximumDiscountAmount = c.MaximumDiscountAmount,
                            MinimumQuantity = c.MinimumQuantity,
                            UsageLimit = c.UsageLimit,
                            UsageCount = c.UsageCount,
                            DisplayOnCartPage = c.DisplayOnCartPage,
                            StartDate = c.StartDate,
                            EndDate = c.EndDate,
                            PotentialSavings = savings,
                            IsActive = c.IsActive,
                            IsOneTimeUse = c.IsOneTimeUse,
                            StoreId = c.StoreId
                        };
                    })
                    .OrderByDescending(dto => dto.PotentialSavings)
                    .ToList();

                return Ok(new
                {
                    Coupons = couponDtos,
                    AvailableOffers = couponDtos.Count,
                    MaxSavings = couponDtos.Count > 0 ? couponDtos.Max(c => c.PotentialSavings) : 0m
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting available coupons for cart");
                return StatusCode(500, "An error occurred while retrieving available coupons");
            }
        }

        [HttpPost("validate")]
        public async Task<ActionResult> ValidateCoupon(
            [FromBody] CouponValidationRequest request)
        {
            try
            {
                var storeId = GetCurrentStoreId();

                if (string.IsNullOrEmpty(request.CouponCode))
                {
                    return BadRequest(new { isValid = false, message = "Coupon code is required" });
                }

                // Find the coupon by code (case insensitive)
                var coupons = await _context.Coupons
                    .Include(c => c.Category)
                    .Include(c => c.Collection)
                    .Include(c => c.Product)
                    .Where(c => c.StoreId == storeId)
                    .ToListAsync();

                var coupon = coupons.FirstOrDefault(c =>
                    string.Equals(c.Code, request.CouponCode, StringComparison.OrdinalIgnoreCase));

                if (coupon == null)
                {
                    return Ok(new { isValid = false, message = "Invalid coupon code" });
                }

                // Check if coupon is active
                if (!coupon.IsActive)
                {
                    return Ok(new { isValid = false, message = "This coupon is no longer active" });
                }

                // Check coupon date validity
                if (coupon.StartDate > DateTime.UtcNow)
                {
                    return Ok(new { isValid = false, message = "This coupon is not yet active" });
                }

                if (coupon.EndDate.HasValue && coupon.EndDate < DateTime.UtcNow)
                {
                    return Ok(new { isValid = false, message = "This coupon has expired" });
                }

                // Check usage limit
                if (coupon.UsageLimit > 0 && coupon.UsageCount >= coupon.UsageLimit)
                {
                    return Ok(new { isValid = false, message = "This coupon has reached its usage limit" });
                }

                // Check minimum purchase amount
                if (coupon.MinimumPurchaseAmount.HasValue && request.CartTotal < coupon.MinimumPurchaseAmount.Value)
                {
                    return Ok(new {
                        isValid = false,
                        message = $"Minimum purchase amount of ₹{coupon.MinimumPurchaseAmount.Value} required"
                    });
                }

                // Check minimum quantity
                if (coupon.MinimumQuantity.HasValue && request.TotalItems < coupon.MinimumQuantity.Value)
                {
                    return Ok(new {
                        isValid = false,
                        message = $"Minimum {coupon.MinimumQuantity.Value} items required"
                    });
                }

                // Check category restrictions
                if (coupon.Applicability == CouponApplicability.Category && coupon.CategoryId.HasValue)
                {
                    if (request.CategoryIds == null || !request.CategoryIds.Contains(coupon.CategoryId.Value))
                    {
                        return Ok(new {
                            isValid = false,
                            message = "This coupon is only applicable for specific categories"
                        });
                    }
                }

                // Check collection restrictions
                if (coupon.Applicability == CouponApplicability.Collection && coupon.CollectionId.HasValue)
                {
                    if (request.CollectionIds == null || !request.CollectionIds.Contains(coupon.CollectionId.Value))
                    {
                        return Ok(new {
                            isValid = false,
                            message = "This coupon is only applicable for specific collections"
                        });
                    }
                }

                // Check product restrictions
                if (coupon.Applicability == CouponApplicability.Product && coupon.ProductId.HasValue)
                {
                    if (request.ProductIds == null || !request.ProductIds.Contains(coupon.ProductId.Value))
                    {
                        return Ok(new {
                            isValid = false,
                            message = "This coupon is only applicable for specific products"
                        });
                    }
                }

                // Calculate potential savings
                decimal savings = CalculatePotentialSavings(
                    request.CartTotal,
                    coupon.Type,
                    coupon.Value,
                    coupon.MaximumDiscountAmount);

                // Map to DTO
                var couponDto = new CouponDTO
                {
                    Id = coupon.Id,
                    Code = coupon.Code,
                    Description = coupon.Description,
                    Type = coupon.Type.ToString(),
                    Value = coupon.Value,
                    Applicability = coupon.Applicability.ToString(),
                    CategoryId = coupon.CategoryId,
                    CategoryName = coupon.Category?.Name,
                    CollectionId = coupon.CollectionId,
                    CollectionName = coupon.Collection?.Name,
                    ProductId = coupon.ProductId,
                    ProductName = coupon.Product?.Name,
                    MinimumPurchaseAmount = coupon.MinimumPurchaseAmount,
                    MaximumDiscountAmount = coupon.MaximumDiscountAmount,
                    MinimumQuantity = coupon.MinimumQuantity,
                    DisplayOnCartPage = coupon.DisplayOnCartPage,
                    PotentialSavings = savings,
                    IsActive = coupon.IsActive,
                    StoreId = coupon.StoreId
                };

                return Ok(new {
                    isValid = true,
                    message = "Coupon is valid",
                    coupon = couponDto,
                    discount = savings
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating coupon");
                return StatusCode(500, new { isValid = false, message = "An error occurred while validating the coupon" });
            }
        }
    }

    public class CouponValidationRequest
    {
        public required string CouponCode { get; set; }
        public decimal CartTotal { get; set; }
        public int TotalItems { get; set; }
        public List<int>? ProductIds { get; set; }
        public List<int>? CategoryIds { get; set; }
        public List<int>? CollectionIds { get; set; }
    }
}
