using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MyShop.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class DebugController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILogger<DebugController> _logger;

        public DebugController(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            ILogger<DebugController> logger)
        {
            _context = context;
            _userManager = userManager;
            _logger = logger;
        }

        [HttpGet("users")]
        public async Task<IActionResult> GetAllUsers()
        {
            var users = await _context.Users.ToListAsync();

            var result = new List<object>();
            foreach (var user in users)
            {
                result.Add(new
                {
                    Id = user.Id,
                    UserName = user.UserName,
                    NormalizedUserName = user.NormalizedUserName,
                    Email = user.Email,
                    NormalizedEmail = user.NormalizedEmail,
                    EmailConfirmed = user.EmailConfirmed,
                    PasswordHash = user.PasswordHash != null ? "[REDACTED]" : null,
                    SecurityStamp = user.SecurityStamp,
                    ConcurrencyStamp = user.ConcurrencyStamp,
                    PhoneNumber = user.PhoneNumber,
                    PhoneNumberConfirmed = user.PhoneNumberConfirmed,
                    TwoFactorEnabled = user.TwoFactorEnabled,
                    LockoutEnd = user.LockoutEnd,
                    LockoutEnabled = user.LockoutEnabled,
                    AccessFailedCount = user.AccessFailedCount
                });
            }

            return Ok(result);
        }

        [HttpGet("roles")]
        public async Task<IActionResult> GetAllRoles()
        {
            var roles = await _context.Roles.ToListAsync();
            return Ok(roles);
        }

        [HttpGet("user-roles")]
        public async Task<IActionResult> GetUserRoles()
        {
            var userRoles = await _context.UserRoles.ToListAsync();
            return Ok(userRoles);
        }

        [HttpGet("find-user")]
        public async Task<IActionResult> FindUser([FromQuery] string email)
        {
            _logger.LogInformation("Attempting to find user with email: {Email}", email);

            // Method 1: Using UserManager.FindByEmailAsync
            var user1 = await _userManager.FindByEmailAsync(email);

            // Method 2: Using direct LINQ query with case-insensitive comparison
            var user2 = await _context.Users
                .OfType<ApplicationUser>()
                .FirstOrDefaultAsync(u => u.NormalizedEmail == email.ToUpper());

            // Method 3: Using direct LINQ query with case-sensitive comparison
            var user3 = await _context.Users
                .OfType<ApplicationUser>()
                .FirstOrDefaultAsync(u => u.Email == email);

            // Method 4: Using direct LINQ query with EF.Functions.Like
            var user4 = await _context.Users
                .OfType<ApplicationUser>()
                .FirstOrDefaultAsync(u => EF.Functions.Like(u.Email, email));

            return Ok(new {
                searchEmail = email,
                method1_UserManager = user1 != null ? new { id = user1.Id, email = user1.Email, normalizedEmail = user1.NormalizedEmail } : null,
                method2_NormalizedEmail = user2 != null ? new { id = user2.Id, email = user2.Email, normalizedEmail = user2.NormalizedEmail } : null,
                method3_ExactMatch = user3 != null ? new { id = user3.Id, email = user3.Email, normalizedEmail = user3.NormalizedEmail } : null,
                method4_LikeOperator = user4 != null ? new { id = user4.Id, email = user4.Email, normalizedEmail = user4.NormalizedEmail } : null
            });
        }

        [HttpPost("reset-admin")]
        public async Task<IActionResult> ResetAdmin()
        {
            try
            {
                // Find admin user by username
                var adminUser = await _context.Users
                    .Where(u => u.NormalizedUserName == "<EMAIL>")
                    .FirstOrDefaultAsync();

                if (adminUser != null)
                {
                    _logger.LogInformation("Found admin user with ID: {UserId}", adminUser.Id);

                    // Delete the user
                    _context.Users.Remove(adminUser);
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("Deleted admin user");
                }
                else
                {
                    _logger.LogInformation("Admin user not found");
                }

                // Create new admin user
                var newAdminUser = new ApplicationUser
                {
                    UserName = "<EMAIL>",
                    NormalizedUserName = "<EMAIL>",
                    Email = "<EMAIL>",
                    NormalizedEmail = "<EMAIL>",
                    EmailConfirmed = true,
                    SecurityStamp = Guid.NewGuid().ToString(),
                    ConcurrencyStamp = Guid.NewGuid().ToString(),
                    PhoneNumberConfirmed = false,
                    TwoFactorEnabled = false,
                    LockoutEnabled = false,
                    AccessFailedCount = 0,
                    FirstName = "Admin",
                    LastName = "User",
                    UserType = UserType.Admin,
                    Address = "Admin Address",
                    City = "Admin City",
                    State = "AS",
                    ZipCode = "12345",
                    Country = "USA",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                // Set password hash
                var passwordHasher = new PasswordHasher<ApplicationUser>();
                newAdminUser.PasswordHash = passwordHasher.HashPassword(newAdminUser, "Admin123!");

                // Add user to database
                _context.Users.Add(newAdminUser);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Created new admin user with ID: {UserId}", newAdminUser.Id);

                // Find Admin role
                var adminRole = await _context.Roles
                    .Where(r => r.NormalizedName == "ADMIN")
                    .FirstOrDefaultAsync();

                if (adminRole == null)
                {
                    // Create Admin role
                    adminRole = new IdentityRole
                    {
                        Name = "Admin",
                        NormalizedName = "ADMIN",
                        ConcurrencyStamp = Guid.NewGuid().ToString()
                    };

                    _context.Roles.Add(adminRole);
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("Created Admin role with ID: {RoleId}", adminRole.Id);
                }

                // Add user to Admin role
                var userRole = new IdentityUserRole<string>
                {
                    UserId = newAdminUser.Id,
                    RoleId = adminRole.Id
                };

                _context.UserRoles.Add(userRole);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Added admin user to Admin role");

                return Ok(new { message = "Admin user reset successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resetting admin user");
                return StatusCode(500, new { message = "Error resetting admin user", error = ex.Message });
            }
        }
    }
}
