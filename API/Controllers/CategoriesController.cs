using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using MyShop.API.DTOs;
using MyShop.API.Services;
using System.IO;

namespace MyShop.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CategoriesController : StoreAwareController
    {
        private readonly ApplicationDbContext _context;
        private readonly IWebHostEnvironment _environment;
        private readonly ILogger<CategoriesController> _logger;

        public CategoriesController(ApplicationDbContext context, IWebHostEnvironment environment, ILogger<CategoriesController> logger)
        {
            _context = context;
            _environment = environment;
            _logger = logger;
        }

        // GET: api/Categories
        [HttpGet]
        public async Task<ActionResult<IEnumerable<CategoryDTO>>> GetCategories(
            [FromQuery] bool? isActive = true,
            [FromQuery] bool? parentOnly = false)
        {
            var storeId = GetCurrentStoreId();

            var query = _context.Categories
                .Include(c => c.Products)
                .Include(c => c.Children)
                .Where(c => c.StoreId == storeId)
                .AsQueryable();

            if (isActive.HasValue)
            {
                query = query.Where(c => c.IsActive == isActive.Value);
            }

            // If parentOnly is true, only return parent categories (those with no parent)
            if (parentOnly == true)
            {
                query = query.Where(c => c.ParentId == null);
            }

            var categories = await query
                .OrderBy(c => c.DisplayOrder)
                .ToListAsync();

            var categoryDtos = new List<CategoryDTO>();

            // If we're getting all categories, build the hierarchy
            if (parentOnly != true)
            {
                // Convert all categories to DTOs
                var allCategoryDtos = categories.Select(c => new CategoryDTO
                {
                    Id = c.Id,
                    Name = c.Name,
                    Description = c.Description,
                    ImageUrl = c.ImageUrl,
                    IsActive = c.IsActive,
                    DisplayOrder = c.DisplayOrder,
                    ProductCount = c.Products.Count,
                    ParentId = c.ParentId,
                    IsSale = c.IsSale
                }).ToList();

                return Ok(allCategoryDtos);
            }
            else
            {
                // Build parent-child hierarchy
                foreach (var category in categories)
                {
                    var categoryDto = new CategoryDTO
                    {
                        Id = category.Id,
                        Name = category.Name,
                        Description = category.Description,
                        ImageUrl = category.ImageUrl,
                        IsActive = category.IsActive,
                        DisplayOrder = category.DisplayOrder,
                        ProductCount = category.Products.Count,
                        ParentId = category.ParentId,
                        IsSale = category.IsSale,
                        Children = category.Children.Select(child => new CategoryDTO
                        {
                            Id = child.Id,
                            Name = child.Name,
                            Description = child.Description,
                            ImageUrl = child.ImageUrl,
                            IsActive = child.IsActive,
                            DisplayOrder = child.DisplayOrder,
                            ProductCount = child.Products.Count,
                            ParentId = child.ParentId,
                            IsSale = child.IsSale
                        }).ToList()
                    };

                    categoryDtos.Add(categoryDto);
                }

                return Ok(categoryDtos);
            }
        }

        // GET: api/Categories/5
        [HttpGet("{id}")]
        public async Task<ActionResult<CategoryDTO>> GetCategory(int id)
        {
            var storeId = GetCurrentStoreId();

            var category = await _context.Categories
                .Include(c => c.Products)
                .Include(c => c.Children)
                .FirstOrDefaultAsync(c => c.Id == id && c.StoreId == storeId);

            if (category == null)
            {
                return NotFound();
            }

            var categoryDto = new CategoryDTO
            {
                Id = category.Id,
                Name = category.Name,
                Description = category.Description,
                ImageUrl = category.ImageUrl,
                IsActive = category.IsActive,
                DisplayOrder = category.DisplayOrder,
                ProductCount = category.Products.Count,
                ParentId = category.ParentId,
                IsSale = category.IsSale,
                Children = category.Children.Select(child => new CategoryDTO
                {
                    Id = child.Id,
                    Name = child.Name,
                    Description = child.Description,
                    ImageUrl = child.ImageUrl,
                    IsActive = child.IsActive,
                    DisplayOrder = child.DisplayOrder,
                    ProductCount = child.Products.Count,
                    ParentId = child.ParentId,
                    IsSale = child.IsSale
                }).ToList()
            };

            return Ok(categoryDto);
        }

        private bool CategoryExists(int id)
        {
            var storeId = GetCurrentStoreId();
            return _context.Categories.Any(e => e.Id == id && e.StoreId == storeId);
        }
    }
}
