using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using MyShop.API.DTOs;
using System.Security.Claims;

namespace MyShop.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class OrdersController : StoreAwareController
    {
        private readonly ApplicationDbContext _context;

        public OrdersController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: api/Orders/my-orders
        [HttpGet("my-orders")]
        [Authorize]
        public async Task<ActionResult<IEnumerable<OrderDTO>>> GetMyOrders(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            var storeId = GetCurrentStoreId();
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized();
            }

            var query = _context.Orders
                .Include(o => o.OrderItems)
                .ThenInclude(oi => oi.Product)
                .Where(o => o.UserId == userId && o.StoreId == storeId)
                .AsQueryable();

            // Apply pagination
            var totalCount = await query.CountAsync();
            var totalPages = (int)Math.Ceiling(totalCount / (double)pageSize);

            var orders = await query
                .OrderByDescending(o => o.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            var orderDtos = orders.Select(o => new OrderDTO
            {
                Id = o.Id,
                OrderNumber = o.OrderNumber,
                TotalAmount = o.TotalAmount,
                Status = o.Status.ToString(),
                UserId = o.UserId,
                IsGuestOrder = o.IsGuestOrder,
                CustomerEmail = o.CustomerEmail,
                CustomerPhone = o.CustomerPhone,
                ShippingAddress = o.ShippingAddress,
                ShippingCity = o.ShippingCity,
                ShippingState = o.ShippingState,
                ShippingZipCode = o.ShippingZipCode,
                ShippingCountry = o.ShippingCountry,
                TrackingNumber = o.TrackingNumber,
                ShippedDate = o.ShippedDate,
                DeliveredDate = o.DeliveredDate,
                CreatedAt = o.CreatedAt,
                StoreId = o.StoreId,
                OrderItems = o.OrderItems.Select(oi => new OrderItemDTO
                {
                    Id = oi.Id,
                    ProductId = oi.ProductId,
                    ProductName = oi.ProductName,
                    UnitPrice = oi.UnitPrice,
                    Quantity = oi.Quantity,
                    TotalPrice = oi.TotalPrice,
                    ProductImageUrl = oi.Product.ImageUrl
                }).ToList()
            }).ToList();

            Response.Headers.Append("X-Total-Count", totalCount.ToString());
            Response.Headers.Append("X-Total-Pages", totalPages.ToString());

            return Ok(orderDtos);
        }

        // GET: api/Orders/5
        [HttpGet("{id}")]
        public async Task<ActionResult<OrderDTO>> GetOrder(int id)
        {
            var storeId = GetCurrentStoreId();

            var order = await _context.Orders
                .Include(o => o.OrderItems)
                .ThenInclude(oi => oi.Product)
                .FirstOrDefaultAsync(o => o.Id == id && o.StoreId == storeId);

            if (order == null)
            {
                return NotFound();
            }

            // Check if user is authorized to view this order
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

            if (string.IsNullOrEmpty(userId) || order.UserId != userId)
            {
                return Forbid();
            }

            var orderDto = new OrderDTO
            {
                Id = order.Id,
                OrderNumber = order.OrderNumber,
                SubTotal = order.SubTotal,
                ShippingCost = order.ShippingCost,
                TaxAmount = order.TaxAmount,
                DiscountAmount = order.DiscountAmount,
                CouponCode = order.CouponCode,
                TotalAmount = order.TotalAmount,
                Status = order.Status.ToString(),
                PaymentMethod = order.PaymentMethod,
                UserId = order.UserId,
                IsGuestOrder = order.IsGuestOrder,
                CustomerEmail = order.CustomerEmail,
                CustomerPhone = order.CustomerPhone,
                ShippingAddress = order.ShippingAddress,
                ShippingCity = order.ShippingCity,
                ShippingState = order.ShippingState,
                ShippingZipCode = order.ShippingZipCode,
                ShippingCountry = order.ShippingCountry,
                StoreId = order.StoreId,
                OrderItems = order.OrderItems.Select(oi => new OrderItemDTO
                {
                    Id = oi.Id,
                    ProductId = oi.ProductId,
                    ProductName = oi.ProductName,
                    UnitPrice = oi.UnitPrice,
                    Quantity = oi.Quantity,
                    OriginalPrice = oi.OriginalPrice,
                    DiscountAmount = oi.DiscountAmount,
                    TotalPrice = oi.TotalPrice,
                    ProductImageUrl = oi.Product.ImageUrl
                }).ToList()
            };

            return Ok(orderDto);
        }

        // POST: api/Orders
        [HttpPost]
        public async Task<ActionResult<OrderDTO>> CreateOrder(CreateOrderDTO orderDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var storeId = GetCurrentStoreId();

            // Check if user is authenticated
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var isAuthenticated = !string.IsNullOrEmpty(userId);

            // Validate order items
            if (orderDto.OrderItems == null || !orderDto.OrderItems.Any())
            {
                return BadRequest("Order must contain at least one item");
            }

            // Get products for order items
            var productIds = orderDto.OrderItems.Select(oi => oi.ProductId).ToList();
            var products = await _context.Products
                .Include(p => p.Variants)
                    .ThenInclude(v => v.InventoryItems)
                .Where(p => productIds.Contains(p.Id) && p.IsActive && p.StoreId == storeId)
                .ToListAsync();

            if (products.Count != productIds.Count)
            {
                return BadRequest("One or more products are invalid or inactive");
            }

            // Check inventory
            foreach (var item in orderDto.OrderItems)
            {
                var product = products.First(p => p.Id == item.ProductId);
                var totalStock = product.Variants.Sum(v => v.InventoryItems.Sum(ii => ii.StockQuantity));
                if (totalStock < item.Quantity)
                {
                    return BadRequest($"Insufficient inventory for product: {product.Name}");
                }
            }

            // Check if coupon is valid
            decimal discountAmount = 0;
            Coupon? coupon = null;
            if (!string.IsNullOrEmpty(orderDto.CouponCode))
            {
                coupon = await _context.Coupons.FirstOrDefaultAsync(c =>
                    c.Code == orderDto.CouponCode.ToUpper() &&
                    c.StoreId == storeId);

                if (coupon != null && coupon.IsActive)
                {
                    // Calculate cart total for coupon validation
                    decimal totalCartValue = 0;
                    foreach (var item in orderDto.OrderItems)
                    {
                        var product = products.First(p => p.Id == item.ProductId);
                        var price = item.price > 0 ? item.price : product.Variants.FirstOrDefault()?.Price ?? 0;
                        totalCartValue += price * item.Quantity;
                    }

                    // Calculate discount based on coupon type
                    switch (coupon.Type)
                    {
                        case CouponType.Percentage:
                            discountAmount = totalCartValue * (coupon.Value / 100);
                            break;
                        case CouponType.FixedAmount:
                            discountAmount = coupon.Value;
                            break;
                        case CouponType.FreeShipping:
                            // Shipping cost will be handled separately
                            break;
                    }

                    // Apply maximum discount limit if set
                    if (coupon.MaximumDiscountAmount.HasValue && discountAmount > coupon.MaximumDiscountAmount.Value)
                    {
                        discountAmount = coupon.MaximumDiscountAmount.Value;
                    }

                    // Ensure discount doesn't exceed cart total
                    if (discountAmount > totalCartValue)
                    {
                        discountAmount = totalCartValue;
                    }

                    // Update coupon usage count
                    coupon.UsageCount++;
                    _context.Entry(coupon).State = EntityState.Modified;
                }
            }

            // Add validation for required customer information for guest orders
            if (!isAuthenticated)
            {
                if (string.IsNullOrEmpty(orderDto.CustomerEmail) ||
                    string.IsNullOrEmpty(orderDto.CustomerPhone) ||
                    string.IsNullOrEmpty(orderDto.ShippingAddress))
                {
                    return BadRequest("Customer information is required for guest orders");
                }
            }

            // Create new order
            var order = new Order
            {
                UserId = userId,
                IsGuestOrder = !isAuthenticated,
                OrderNumber = GenerateOrderNumber(),
                Status = OrderStatus.Pending,
                CustomerEmail = orderDto.CustomerEmail,
                CustomerPhone = orderDto.CustomerPhone,
                ShippingAddress = orderDto.ShippingAddress,
                ShippingCity = orderDto.ShippingCity,
                ShippingState = orderDto.ShippingState,
                ShippingZipCode = orderDto.ShippingZipCode,
                ShippingCountry = orderDto.ShippingCountry,
                StoreId = storeId,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            // Calculate order totals
            decimal subTotal = 0;
            var orderItems = new List<OrderItem>();

            foreach (var item in orderDto.OrderItems)
            {
                var product = products.First(p => p.Id == item.ProductId);
                var price = product.Variants.FirstOrDefault()?.Price ?? 0;
                var totalPrice = price * item.Quantity;
                subTotal += totalPrice;

                var orderItem = new OrderItem
                {
                    ProductId = item.ProductId,
                    ProductName = product.Name,
                    UnitPrice = price,
                    Quantity = item.Quantity,
                    TotalPrice = totalPrice,
                    Order = order
                };

                orderItems.Add(orderItem);
            }

            order.SubTotal = subTotal;
            order.TotalAmount = subTotal + (orderDto.ShippingCost ?? 0) + (orderDto.TaxAmount ?? 0) - (orderDto.DiscountAmount ?? 0);
            order.ShippingCost = orderDto.ShippingCost ?? 0;
            order.TaxAmount = orderDto.TaxAmount ?? 0;
            order.DiscountAmount = orderDto.DiscountAmount ?? 0;
            order.CouponCode = orderDto.CouponCode;
            order.OrderItems = orderItems;

            _context.Orders.Add(order);

            // Update inventory
            foreach (var item in orderItems)
            {
                var product = products.First(p => p.Id == item.ProductId);
                foreach (var variant in product.Variants)
                {
                    var inventoryToDeduct = item.Quantity;
                    foreach (var inventory in variant.InventoryItems.OrderBy(ii => ii.StockQuantity))
                    {
                        if (inventoryToDeduct <= 0) break;

                        var deduction = Math.Min(inventory.StockQuantity, inventoryToDeduct);
                        inventory.StockQuantity -= deduction;
                        inventoryToDeduct -= deduction;
                    }
                }
            }

            try
            {
                await _context.SaveChangesAsync();
                return CreatedAtAction(nameof(GetOrder), new { id = order.Id },
                    new OrderDTO
                    {
                        Id = order.Id,
                        OrderNumber = order.OrderNumber,
                        StoreId = order.StoreId
                    });
            }
            catch (DbUpdateException ex)
            {
                return StatusCode(500, "An error occurred while creating the order");
            }
        }

        private bool OrderExists(int id)
        {
            var storeId = GetCurrentStoreId();
            return _context.Orders.Any(e => e.Id == id && e.StoreId == storeId);
        }

        private string GenerateOrderNumber()
        {
            return $"ORD-{DateTime.UtcNow:yyyyMMdd}-{Guid.NewGuid().ToString().Substring(0, 8)}";
        }
    }
}
