using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using MyShop.API.DTOs;

namespace MyShop.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ProductGroupController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<ProductGroupController> _logger;

        public ProductGroupController(ApplicationDbContext context, ILogger<ProductGroupController> logger)
        {
            _context = context;
            _logger = logger;
        }

        // GET: api/ProductGroup
        [HttpGet]
        public async Task<ActionResult<IEnumerable<ProductGroupDTO>>> GetProductGroups()
        {
            try
            {
                var groups = await _context.ProductGroups
                    .Include(pg => pg.Products)
                    .ToListAsync();

                var groupDtos = groups.Select(g => new ProductGroupDTO
                {
                    Id = g.Id,
                    Name = g.Name,
                    Description = g.Description,
                    Products = g.Products.Select(p => new ProductDTO
                    {
                        Id = p.Id,
                        Name = p.Name,
                        ImageUrl = p.ImageUrl,
                        IsActive = p.IsActive
                    }).ToList()
                }).ToList();

                return Ok(groupDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting product groups");
                return StatusCode(500, "An error occurred while retrieving product groups");
            }
        }

        // GET: api/ProductGroup/5
        [HttpGet("{id}")]
        public async Task<ActionResult<ProductGroupDTO>> GetProductGroup(int id)
        {
            try
            {
                var group = await _context.ProductGroups
                    .Include(pg => pg.Products)
                        .ThenInclude(p => p.Category)
                    .Include(pg => pg.Products)
                        .ThenInclude(p => p.Images)
                    .Include(pg => pg.Products)
                        .ThenInclude(p => p.Collections)
                    .FirstOrDefaultAsync(pg => pg.Id == id);

                if (group == null)
                {
                    return NotFound();
                }

                var groupDto = new ProductGroupDTO
                {
                    Id = group.Id,
                    Name = group.Name,
                    Description = group.Description,
                    Products = group.Products.Select(p => new ProductDTO
                    {
                        Id = p.Id,
                        Name = p.Name,
                        Description = p.Description,
                        ImageUrl = p.ImageUrl,
                        Barcode = p.Barcode,
                        IsActive = p.IsActive,
                        IsFeatured = p.IsFeatured,
                        HasVariants = p.HasVariants,
                        CategoryId = p.CategoryId,
                        CategoryName = p.Category.Name,
                        ProductGroupId = p.ProductGroupId,
                        ProductGroupName = group.Name,
                        CollectionIds = p.Collections.Select(c => c.Id).ToList(),
                        CollectionNames = p.Collections.Select(c => c.Name).ToList(),
                        Images = p.Images.Select(i => new ProductImageDTO
                        {
                            Id = i.Id,
                            Url = i.ImageUrl,
                            IsMain = i.IsMain,
                            DisplayOrder = i.DisplayOrder
                        }).ToList(),
                        MainImage = p.Images.FirstOrDefault(i => i.IsMain)?.ImageUrl ?? p.ImageUrl
                    }).ToList()
                };

                return Ok(groupDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting product group with ID {Id}", id);
                return StatusCode(500, "An error occurred while retrieving the product group");
            }
        }

        // POST: api/ProductGroup
        [HttpPost]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ProductGroupDTO>> CreateProductGroup(CreateProductGroupDTO groupDto)
        {
            try
            {
                var group = new ProductGroup
                {
                    Name = groupDto.Name,
                    Description = groupDto.Description,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                // Add products to group
                if (groupDto.ProductIds != null && groupDto.ProductIds.Any())
                {
                    var products = await _context.Products
                        .Where(p => groupDto.ProductIds.Contains(p.Id))
                        .ToListAsync();

                    foreach (var product in products)
                    {
                        group.Products.Add(product);
                    }
                }

                _context.ProductGroups.Add(group);
                await _context.SaveChangesAsync();

                // Return created group
                var createdGroupDto = new ProductGroupDTO
                {
                    Id = group.Id,
                    Name = group.Name,
                    Description = group.Description,
                    Products = group.Products.Select(p => new ProductDTO
                    {
                        Id = p.Id,
                        Name = p.Name,
                        ImageUrl = p.ImageUrl,
                        IsActive = p.IsActive
                    }).ToList()
                };

                return CreatedAtAction(nameof(GetProductGroup), new { id = group.Id }, createdGroupDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating product group");
                return StatusCode(500, "An error occurred while creating the product group");
            }
        }

        // PUT: api/ProductGroup/5
        [HttpPut("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> UpdateProductGroup(int id, UpdateProductGroupDTO groupDto)
        {
            try
            {
                var group = await _context.ProductGroups
                    .Include(pg => pg.Products)
                    .FirstOrDefaultAsync(pg => pg.Id == id);

                if (group == null)
                {
                    return NotFound();
                }

                // Update group properties
                if (!string.IsNullOrEmpty(groupDto.Name))
                {
                    group.Name = groupDto.Name;
                }

                if (!string.IsNullOrEmpty(groupDto.Description))
                {
                    group.Description = groupDto.Description;
                }

                group.UpdatedAt = DateTime.UtcNow;

                // Update products if specified
                if (groupDto.ProductIds != null)
                {
                    // Clear existing products
                    group.Products.Clear();

                    // Add new products
                    var products = await _context.Products
                        .Where(p => groupDto.ProductIds.Contains(p.Id))
                        .ToListAsync();

                    foreach (var product in products)
                    {
                        group.Products.Add(product);
                    }
                }

                await _context.SaveChangesAsync();
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating product group with ID {Id}", id);
                return StatusCode(500, "An error occurred while updating the product group");
            }
        }

        // DELETE: api/ProductGroup/5
        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteProductGroup(int id)
        {
            try
            {
                var group = await _context.ProductGroups
                    .Include(pg => pg.Products)
                    .FirstOrDefaultAsync(pg => pg.Id == id);

                if (group == null)
                {
                    return NotFound();
                }

                // Remove group association from products
                foreach (var product in group.Products)
                {
                    product.ProductGroupId = null;
                }

                // Remove group
                _context.ProductGroups.Remove(group);
                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting product group with ID {Id}", id);
                return StatusCode(500, "An error occurred while deleting the product group");
            }
        }
    }
}
