using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyShop.API.Data;
using MyShop.API.DTOs;

namespace MyShop.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class StoreController : StoreAwareController
    {
        private readonly ApplicationDbContext _context;
        
        public StoreController(ApplicationDbContext context)
        {
            _context = context;
        }
        
        [HttpGet("current")]
        public async Task<ActionResult<StoreDTO>> GetCurrentStore()
        {
            if (CurrentStore == null)
                return NotFound("Store not found");
                
            var store = await _context.Stores
                .Include(s => s.ThemeConfiguration)
                .FirstOrDefaultAsync(s => s.Id == CurrentStore.Id);
                
            if (store == null)
                return NotFound();
                
            return Ok(MapToDTO(store));
        }
        
        private StoreDTO MapToDTO(Data.Entities.Store store)
        {
            return new StoreDTO
            {
                Id = store.Id,
                Name = store.Name,
                Description = store.Description,
                LogoUrl = store.LogoUrl,
                FaviconUrl = store.FaviconUrl,
                Email = store.Email,
                Phone = store.Phone,
                Address = store.Address,
                FacebookUrl = store.FacebookUrl,
                InstagramUrl = store.InstagramUrl,
                TwitterUrl = store.TwitterUrl,
                WhatsappNumber = store.WhatsappNumber,
                ThemeConfiguration = store.ThemeConfiguration != null ? 
                    new ThemeConfigurationDTO
                    {
                        Id = store.ThemeConfiguration.Id,
                        StoreId = store.ThemeConfiguration.StoreId,
                        Name = store.ThemeConfiguration.Name,
                        Description = store.ThemeConfiguration.Description,
                        IsActive = store.ThemeConfiguration.IsActive,
                        PrimaryColor = store.ThemeConfiguration.PrimaryColor,
                        SecondaryColor = store.ThemeConfiguration.SecondaryColor,
                        AccentColor = store.ThemeConfiguration.AccentColor,
                        TextPrimaryColor = store.ThemeConfiguration.TextPrimaryColor,
                        TextSecondaryColor = store.ThemeConfiguration.TextSecondaryColor,
                        TextLightColor = store.ThemeConfiguration.TextLightColor,
                        BackgroundPrimaryColor = store.ThemeConfiguration.BackgroundPrimaryColor,
                        BackgroundSecondaryColor = store.ThemeConfiguration.BackgroundSecondaryColor,
                        BackgroundAccentColor = store.ThemeConfiguration.BackgroundAccentColor,
                        ButtonPrimaryColor = store.ThemeConfiguration.ButtonPrimaryColor,
                        ButtonSecondaryColor = store.ThemeConfiguration.ButtonSecondaryColor,
                        ButtonTextColor = store.ThemeConfiguration.ButtonTextColor,
                        ButtonBorderRadius = store.ThemeConfiguration.ButtonBorderRadius,
                        CardBackgroundColor = store.ThemeConfiguration.CardBackgroundColor,
                        CardBorderColor = store.ThemeConfiguration.CardBorderColor,
                        CardBorderRadius = store.ThemeConfiguration.CardBorderRadius,
                        CardShadow = store.ThemeConfiguration.CardShadow,
                        HeadingFontFamily = store.ThemeConfiguration.HeadingFontFamily,
                        BodyFontFamily = store.ThemeConfiguration.BodyFontFamily,
                        FontBaseSize = store.ThemeConfiguration.FontBaseSize,
                        SpacingUnit = store.ThemeConfiguration.SpacingUnit,
                        ContainerMaxWidth = store.ThemeConfiguration.ContainerMaxWidth,
                        ContainerPadding = store.ThemeConfiguration.ContainerPadding,
                        HeaderBackgroundColor = store.ThemeConfiguration.HeaderBackgroundColor,
                        HeaderTextColor = store.ThemeConfiguration.HeaderTextColor,
                        HeaderHeight = store.ThemeConfiguration.HeaderHeight,
                        FooterBackgroundColor = store.ThemeConfiguration.FooterBackgroundColor,
                        FooterTextColor = store.ThemeConfiguration.FooterTextColor,
                        NavLinkColor = store.ThemeConfiguration.NavLinkColor,
                        NavLinkActiveColor = store.ThemeConfiguration.NavLinkActiveColor,
                        NavLinkHoverColor = store.ThemeConfiguration.NavLinkHoverColor,
                        InputBackgroundColor = store.ThemeConfiguration.InputBackgroundColor,
                        InputBorderColor = store.ThemeConfiguration.InputBorderColor,
                        InputBorderRadius = store.ThemeConfiguration.InputBorderRadius,
                        InputFocusBorderColor = store.ThemeConfiguration.InputFocusBorderColor,
                        CustomCSS = store.ThemeConfiguration.CustomCSS
                    } : null
            };
        }
    }
}
