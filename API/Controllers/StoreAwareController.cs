using System;
using System.Linq;
using Microsoft.AspNetCore.Mvc;
using MyShop.API.Data.Entities;

namespace MyShop.API.Controllers
{
    public abstract class StoreAwareController : ControllerBase
    {
        protected Store CurrentStore => HttpContext.Items["CurrentStore"] as Store;

        protected int GetCurrentStoreId()
        {
            if (CurrentStore == null)
            {
                // Default to store ID 1 if no store is found
                return 1;
            }

            return CurrentStore.Id;
        }

        protected IQueryable<T> FilterByStore<T>(IQueryable<T> query) where T : class, IStoreEntity
        {
            return query.Where(e => e.StoreId == GetCurrentStoreId());
        }
    }
}
