using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using MyShop.API.DTOs;
using MyShop.API.Services;

namespace MyShop.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AuthController : ControllerBase
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly RoleManager<IdentityRole> _roleManager;
        private readonly JwtTokenService _jwtTokenService;
        private readonly ApplicationDbContext _context;
        private readonly ILogger<AuthController> _logger;

        public AuthController(
            UserManager<ApplicationUser> userManager,
            SignInManager<ApplicationUser> signInManager,
            RoleManager<IdentityRole> roleManager,
            JwtTokenService jwtTokenService,
            ApplicationDbContext context,
            ILogger<AuthController> logger)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _roleManager = roleManager;
            _jwtTokenService = jwtTokenService;
            _context = context;
            _logger = logger;
        }

        [HttpPost("register")]
        public async Task<IActionResult> Register([FromBody] RegisterDTO model)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var user = new ApplicationUser
            {
                UserName = model.Email,
                Email = model.Email,
                FirstName = model.FirstName,
                LastName = model.LastName,
                Address = model.Address ?? string.Empty,
                City = model.City ?? string.Empty,
                State = model.State ?? string.Empty,
                ZipCode = model.ZipCode ?? string.Empty,
                Country = model.Country ?? string.Empty,
                PhoneNumber = model.PhoneNumber
            };

            var result = await _userManager.CreateAsync(user, model.Password);

            if (!result.Succeeded)
            {
                return BadRequest(new AuthResponseDTO
                {
                    Success = false,
                    ErrorMessage = string.Join(", ", result.Errors.Select(e => e.Description))
                });
            }

            // Add user to Customer role by default
            await _userManager.AddToRoleAsync(user, "Customer");

            // Generate JWT token
            var (token, expiration) = await _jwtTokenService.GenerateJwtToken(user);
            var roles = await _userManager.GetRolesAsync(user);

            return Ok(new AuthResponseDTO
            {
                Success = true,
                Token = token,
                UserId = user.Id,
                Email = user.Email,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Roles = roles.ToList(),
                Expiration = expiration
            });
        }

        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] LoginDTO model)
        {
            _logger.LogInformation("Login attempt for email: {Email}", model.Email);

            if (!ModelState.IsValid)
            {
                _logger.LogWarning("Invalid model state for login attempt: {Errors}",
                    string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage)));
                return BadRequest(ModelState);
            }

            // Try to find user by email first
            var user = await _userManager.FindByEmailAsync(model.Email);

            // If not found by email, try by username
            if (user == null)
            {
                _logger.LogInformation("User not found by email, trying username: {Username}", model.Email);
                user = await _userManager.FindByNameAsync(model.Email);
            }

            if (user == null)
            {
                _logger.LogWarning("Login failed: User not found with email/username {Email}", model.Email);
                return Unauthorized(new AuthResponseDTO
                {
                    Success = false,
                    ErrorMessage = "Invalid email or password"
                });
            }

            _logger.LogInformation("User found: {UserId}, Email: {Email}, UserName: {UserName}, Roles: {Roles}",
                user.Id, user.Email, user.UserName, string.Join(", ", await _userManager.GetRolesAsync(user)));

            var result = await _signInManager.CheckPasswordSignInAsync(user, model.Password, false);
            if (!result.Succeeded)
            {
                _logger.LogWarning("Login failed: Invalid password for user {Email}", model.Email);

                // Log password hash for debugging
                _logger.LogInformation("Password hash for user {Email}: {PasswordHash}", model.Email, user.PasswordHash ?? "null");

                return Unauthorized(new AuthResponseDTO
                {
                    Success = false,
                    ErrorMessage = "Invalid email or password"
                });
            }

            _logger.LogInformation("Password check succeeded for user {Email}", model.Email);

            // Check if store key is provided in the header
            string storeKey = GetStoreKeyFromRequest();
            Store store = null;

            if (!string.IsNullOrEmpty(storeKey))
            {
                // Try to find the store by key
                store = await _context.Stores.FirstOrDefaultAsync(s => s.StoreKey == storeKey && s.IsActive);

                if (store == null)
                {
                    _logger.LogWarning("Login failed: Store with key {StoreKey} not found or inactive", storeKey);
                    return BadRequest(new AuthResponseDTO
                    {
                        Success = false,
                        ErrorMessage = "Store not found or inactive"
                    });
                }

                _logger.LogInformation("Store found: {StoreId}, {StoreName}", store.Id, store.Name);
            }

            // Generate JWT token
            var (token, expiration) = await _jwtTokenService.GenerateJwtToken(user);
            var roles = await _userManager.GetRolesAsync(user);

            _logger.LogInformation("JWT token generated for user {Email}, Roles: {Roles}, Expiration: {Expiration}",
                user.Email, string.Join(", ", roles), expiration);

            return Ok(new AuthResponseDTO
            {
                Success = true,
                Token = token,
                UserId = user.Id,
                Email = user.Email ?? string.Empty,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Roles = roles.ToList(),
                Expiration = expiration,
                StoreId = store?.Id
            });
        }

        [HttpPost("admin/login")]
        public async Task<IActionResult> AdminLogin([FromBody] LoginDTO model)
        {
            _logger.LogInformation("Admin login attempt for email: {Email}", model.Email);

            if (!ModelState.IsValid)
            {
                _logger.LogWarning("Invalid model state for admin login attempt: {Errors}",
                    string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage)));
                return BadRequest(ModelState);
            }

            // Try to find user by email first
            var user = await _userManager.FindByEmailAsync(model.Email);

            // If not found by email, try by username
            if (user == null)
            {
                _logger.LogInformation("User not found by email, trying username: {Username}", model.Email);
                user = await _userManager.FindByNameAsync(model.Email);
            }

            if (user == null)
            {
                _logger.LogWarning("Admin login failed: User not found with email/username {Email}", model.Email);
                return Unauthorized(new AuthResponseDTO
                {
                    Success = false,
                    ErrorMessage = "Invalid email or password"
                });
            }

            // Check if user is an admin or super admin
            var roles = await _userManager.GetRolesAsync(user);
            bool isAdmin = roles.Contains("Admin") || roles.Contains("SuperAdmin") || user.UserType == UserType.Admin || user.UserType == UserType.SuperAdmin;

            if (!isAdmin)
            {
                _logger.LogWarning("Admin login failed: User {Email} is not an admin", model.Email);
                return Unauthorized(new AuthResponseDTO
                {
                    Success = false,
                    ErrorMessage = "You do not have admin privileges"
                });
            }

            _logger.LogInformation("Admin user found: {UserId}, Email: {Email}, UserName: {UserName}, Roles: {Roles}",
                user.Id, user.Email, user.UserName, string.Join(", ", roles));

            var result = await _signInManager.CheckPasswordSignInAsync(user, model.Password, false);
            if (!result.Succeeded)
            {
                _logger.LogWarning("Admin login failed: Invalid password for user {Email}", model.Email);
                return Unauthorized(new AuthResponseDTO
                {
                    Success = false,
                    ErrorMessage = "Invalid email or password"
                });
            }

            _logger.LogInformation("Password check succeeded for admin user {Email}", model.Email);

            // Get accessible stores for the admin
            var accessibleStores = await GetAccessibleStoresAsync(user);

            // Don't set a default store - let the frontend handle store selection
            StoreDTO defaultStore = null;

            // Generate JWT token
            var (token, expiration) = await _jwtTokenService.GenerateJwtToken(user);

            _logger.LogInformation("JWT token generated for admin user {Email}, Roles: {Roles}, Expiration: {Expiration}",
                user.Email, string.Join(", ", roles), expiration);

            return Ok(new AdminAuthResponseDTO
            {
                Success = true,
                Token = token,
                UserId = user.Id,
                Email = user.Email ?? string.Empty,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Roles = roles.ToList(),
                Expiration = expiration,
                AccessibleStores = accessibleStores,
                DefaultStore = defaultStore
            });
        }

        [HttpGet("check-admin")]
        public async Task<IActionResult> CheckAdminUser()
        {
            _logger.LogInformation("Checking if admin user exists");

            var adminEmail = "<EMAIL>";
            var adminUser = await _userManager.FindByEmailAsync(adminEmail);

            if (adminUser == null)
            {
                _logger.LogWarning("Admin user not found");
                return NotFound(new { message = "Admin user not found" });
            }

            var roles = await _userManager.GetRolesAsync(adminUser);
            var isAdmin = roles.Contains("Admin");

            _logger.LogInformation("Admin user found. Has Admin role: {IsAdmin}", isAdmin);

            return Ok(new
            {
                exists = true,
                email = adminUser.Email,
                isAdmin = isAdmin,
                roles = roles
            });
        }

        [HttpPost("create-admin")]
        public async Task<IActionResult> CreateAdminUser()
        {
            _logger.LogInformation("Creating admin user");

            // Check if admin user already exists
            var adminEmail = "<EMAIL>";
            var existingUser = await _userManager.FindByEmailAsync(adminEmail);

            if (existingUser != null)
            {
                _logger.LogInformation("Admin user already exists");

                // Ensure user has Admin role
                if (!await _userManager.IsInRoleAsync(existingUser, "Admin"))
                {
                    await _userManager.AddToRoleAsync(existingUser, "Admin");
                    _logger.LogInformation("Added Admin role to existing user");
                }

                return Ok(new { message = "Admin user already exists", userId = existingUser.Id });
            }

            // Create admin user
            var adminUser = new ApplicationUser
            {
                UserName = adminEmail,
                Email = adminEmail,
                FirstName = "Admin",
                LastName = "User",
                EmailConfirmed = true,
                UserType = UserType.Admin,
                Address = "Admin Address",
                City = "Admin City",
                State = "AS",
                ZipCode = "12345",
                Country = "USA"
            };

            // Ensure Admin role exists
            if (!await _roleManager.RoleExistsAsync("Admin"))
            {
                await _roleManager.CreateAsync(new IdentityRole("Admin"));
                _logger.LogInformation("Created Admin role");
            }

            // Create user with password
            var result = await _userManager.CreateAsync(adminUser, "Admin123!");

            if (!result.Succeeded)
            {
                var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                _logger.LogError("Failed to create admin user: {Errors}", errors);

                // Check if the error is due to duplicate username
                if (errors.Contains("is already taken"))
                {
                    // Try to find the user by username
                    var userByUsername = await _userManager.FindByNameAsync(adminEmail);
                    if (userByUsername != null)
                    {
                        _logger.LogInformation("Found user by username: {Username}", adminEmail);

                        // Reset password
                        var token = await _userManager.GeneratePasswordResetTokenAsync(userByUsername);
                        var resetResult = await _userManager.ResetPasswordAsync(userByUsername, token, "Admin123!");

                        if (resetResult.Succeeded)
                        {
                            _logger.LogInformation("Reset password for user: {Username}", adminEmail);

                            // Ensure user has Admin role
                            if (!await _userManager.IsInRoleAsync(userByUsername, "Admin"))
                            {
                                await _userManager.AddToRoleAsync(userByUsername, "Admin");
                                _logger.LogInformation("Added Admin role to existing user");
                            }

                            return Ok(new { message = "Admin user password reset successfully", userId = userByUsername.Id });
                        }
                        else
                        {
                            var resetErrors = string.Join(", ", resetResult.Errors.Select(e => e.Description));
                            _logger.LogError("Failed to reset password: {Errors}", resetErrors);
                        }
                    }
                }

                return BadRequest(new { message = "Failed to create admin user", errors });
            }

            // Add Admin role to user
            await _userManager.AddToRoleAsync(adminUser, "Admin");
            _logger.LogInformation("Added Admin role to new admin user");

            return Ok(new { message = "Admin user created successfully", userId = adminUser.Id });
        }

        private string GetStoreKeyFromRequest()
        {
            // Try to get store key from header
            if (Request.Headers.TryGetValue("X-Store-Key", out var headerValues))
            {
                return headerValues.FirstOrDefault();
            }

            // Try to get store key from query string
            if (Request.Query.TryGetValue("storeKey", out var queryValues))
            {
                return queryValues.FirstOrDefault();
            }

            return null;
        }

        [HttpGet("admin/me")]
        [Authorize(Roles = "Admin,SuperAdmin")]
        public async Task<IActionResult> GetCurrentAdminUser()
        {
            _logger.LogInformation("Getting current admin user");

            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                _logger.LogWarning("No authenticated user found");
                return Unauthorized(new { message = "Not authenticated" });
            }

            // Check if user is an admin or super admin
            var roles = await _userManager.GetRolesAsync(user);
            bool isAdmin = roles.Contains("Admin") || roles.Contains("SuperAdmin") || user.UserType == UserType.Admin || user.UserType == UserType.SuperAdmin;

            if (!isAdmin)
            {
                _logger.LogWarning("User {Email} is not an admin", user.Email);
                return Forbid();
            }

            // Get accessible stores for the admin
            var accessibleStores = await GetAccessibleStoresAsync(user);

            // Don't set a default store - let the frontend handle store selection
            StoreDTO defaultStore = null;

            _logger.LogInformation("Returning admin user info for {Email}, Roles: {Roles}",
                user.Email, string.Join(", ", roles));

            return Ok(new AdminAuthResponseDTO
            {
                Success = true,
                UserId = user.Id,
                Email = user.Email ?? string.Empty,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Roles = roles.ToList(),
                AccessibleStores = accessibleStores,
                DefaultStore = defaultStore
            });
        }

        [HttpGet("admin/accessible-stores")]
        [Authorize(Roles = "Admin,SuperAdmin")]
        private async Task<List<StoreDTO>> GetAccessibleStoresAsync(ApplicationUser user)
        {
            List<StoreDTO> stores;

            // Super admin can access all stores
            if (user.IsSuperAdmin || await _userManager.IsInRoleAsync(user, "SuperAdmin"))
            {
                stores = await _context.Stores
                    .Select(s => new StoreDTO
                    {
                        Id = s.Id,
                        Name = s.Name,
                        StoreKey = s.StoreKey,
                        IsActive = s.IsActive
                    })
                    .ToListAsync();

                // For SuperAdmin, set the default store (ID: 1) as the first in the list
                var defaultStore = stores.FirstOrDefault(s => s.Id == 1);
                if (defaultStore != null)
                {
                    // Remove it from its current position
                    stores.Remove(defaultStore);
                    // Add it to the beginning of the list
                    stores.Insert(0, defaultStore);
                }

                return stores;
            }

            // Store admin can only access their assigned stores
            stores = await _context.StoreAdmins
                .Where(sa => sa.UserId == user.Id)
                .Join(_context.Stores,
                    sa => sa.StoreId,
                    s => s.Id,
                    (sa, s) => new StoreDTO
                    {
                        Id = s.Id,
                        Name = s.Name,
                        StoreKey = s.StoreKey,
                        IsActive = s.IsActive
                    })
                .ToListAsync();

            return stores;
        }
    }
}
