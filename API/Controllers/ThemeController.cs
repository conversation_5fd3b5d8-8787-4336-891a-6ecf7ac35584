using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MyShop.API.Data;
using MyShop.API.Data.Entities;
using MyShop.API.DTOs;

namespace MyShop.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ThemeController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<ThemeController> _logger;

        public ThemeController(ApplicationDbContext context, ILogger<ThemeController> logger)
        {
            _context = context;
            _logger = logger;
        }

        [HttpGet("{storeKey}")]
        public async Task<ActionResult<ThemeConfigurationDTO>> GetThemeByStoreKey(string storeKey)
        {
            try
            {
                if (string.IsNullOrEmpty(storeKey))
                {
                    return BadRequest("Store key is required");
                }

                var store = await _context.Stores
                    .Include(s => s.ThemeConfiguration)
                    .FirstOrDefaultAsync(s => s.StoreKey == storeKey && s.IsActive);

                if (store == null)
                {
                    return NotFound($"No active store found with key: {storeKey}");
                }

                if (store.ThemeConfiguration == null)
                {
                    return NotFound($"No theme configuration found for store with key: {storeKey}");
                }

                var themeConfig = new ThemeConfigurationDTO
                {
                    Id = store.ThemeConfiguration.Id,
                    PrimaryColor = store.ThemeConfiguration.PrimaryColor,
                    SecondaryColor = store.ThemeConfiguration.SecondaryColor,
                    AccentColor = store.ThemeConfiguration.AccentColor,
                    TextPrimaryColor = store.ThemeConfiguration.TextPrimaryColor,
                    TextSecondaryColor = store.ThemeConfiguration.TextSecondaryColor,
                    TextLightColor = store.ThemeConfiguration.TextLightColor,
                    BackgroundPrimaryColor = store.ThemeConfiguration.BackgroundPrimaryColor,
                    BackgroundSecondaryColor = store.ThemeConfiguration.BackgroundSecondaryColor,
                    BackgroundAccentColor = store.ThemeConfiguration.BackgroundAccentColor,
                    HeaderBackgroundColor = store.ThemeConfiguration.HeaderBackgroundColor,
                    HeaderTextColor = store.ThemeConfiguration.HeaderTextColor,
                    FooterBackgroundColor = store.ThemeConfiguration.FooterBackgroundColor,
                    FooterTextColor = store.ThemeConfiguration.FooterTextColor,
                    HeadingFontFamily = store.ThemeConfiguration.HeadingFontFamily,
                    BodyFontFamily = store.ThemeConfiguration.BodyFontFamily,
                    ButtonPrimaryColor = store.ThemeConfiguration.ButtonPrimaryColor,
                    ButtonSecondaryColor = store.ThemeConfiguration.ButtonSecondaryColor,
                    ButtonTextColor = store.ThemeConfiguration.ButtonTextColor,
                    ButtonBorderRadius = store.ThemeConfiguration.ButtonBorderRadius,
                    LinkColor = store.ThemeConfiguration.LinkColor, // Using the new LinkColor property
                    StoreId = store.Id,
                    StoreName = store.Name,
                    LogoUrl = store.LogoUrl,
                    FaviconUrl = store.FaviconUrl
                };

                return Ok(themeConfig);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting theme configuration for store key {StoreKey}", storeKey);
                return StatusCode(500, "An error occurred while retrieving the theme configuration");
            }
        }
    }
}
