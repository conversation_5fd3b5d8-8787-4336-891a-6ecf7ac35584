using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyShop.API.Services;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace MyShop.API.Attributes
{
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = true)]
    public class PermissionAttribute : Attribute, IAsyncAuthorizationFilter
    {
        private readonly string _module;
        private readonly string _action;
        private readonly bool _requireStoreId;

        public PermissionAttribute(string module, string action, bool requireStoreId = false)
        {
            _module = module;
            _action = action;
            _requireStoreId = requireStoreId;
        }

        public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            var permissionService = context.HttpContext.RequestServices.GetService(typeof(PermissionService)) as PermissionService;

            if (permissionService == null)
            {
                context.Result = new StatusCodeResult(500);
                return;
            }

            // Try to get storeId from route data
            int? storeId = null;
            if (context.RouteData.Values.ContainsKey("storeId") &&
                int.TryParse(context.RouteData.Values["storeId"].ToString(), out int parsedStoreId))
            {
                storeId = parsedStoreId;
            }
            // If not in route, try to get from query string
            else if (context.HttpContext.Request.Query.ContainsKey("storeId") &&
                    int.TryParse(context.HttpContext.Request.Query["storeId"].FirstOrDefault(), out parsedStoreId))
            {
                storeId = parsedStoreId;
            }
            // If not in query, try to get from request body for POST/PUT requests
            else if (context.HttpContext.Request.Method == "POST" || context.HttpContext.Request.Method == "PUT")
            {
                // We can't read the request body here as it would interfere with model binding
                // The controller will need to check permissions after model binding
            }

            // If storeId is required but not found, return forbidden
            if (_requireStoreId && !storeId.HasValue)
            {
                context.Result = new BadRequestObjectResult("Store ID is required");
                return;
            }

            var hasPermission = await permissionService.HasPermissionAsync(_module, _action, storeId);

            if (!hasPermission)
            {
                context.Result = new ForbidResult();
            }
        }
    }
}
