{"ConnectionStrings": {"DefaultConnection": "Server=localhost,1433;Database=ECOMDB;User Id=sa;Password=dockerStrongPwd123;TrustServerCertificate=True;MultipleActiveResultSets=true;"}, "Jwt": {"Key": "ThisIsMySecretKeyForMyShopApplicationJwtTokenAuthentication", "Issuer": "MyShop.API", "Audience": "MyShop.Client", "ExpireDays": 7}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Email": {"SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "fromEmail": "<EMAIL>", "Password": "bytcacomktkrkpdl", "SenderEmail": "<EMAIL>", "SenderName": "Jaipur Cotton House", "UseDevelopmentMode": false}, "Store": {"OrderNumberPrefix": "JCH", "OrderNumberLength": 8}}