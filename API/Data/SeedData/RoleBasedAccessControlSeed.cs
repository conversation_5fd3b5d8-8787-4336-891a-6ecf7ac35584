using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using MyShop.API.Data.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MyShop.API.Data
{
    public static class RoleBasedAccessControlSeed
    {
        public static async Task SeedRolesAndPermissions(
            ApplicationDbContext context,
            RoleManager<IdentityRole> roleManager)
        {
            // Create default ASP.NET Identity roles if they don't exist
            var roles = new List<string> {
                "SuperAdmin",
                "Admin",
                "StoreAdmin",
                "InventoryManager",
                "OrderManager",
                "FinanceManager",
                "MarketingManager",
                "CustomerSupport",
                "Staff"
            };

            foreach (var roleName in roles)
            {
                var roleExists = await roleManager.RoleExistsAsync(roleName);
                if (!roleExists)
                {
                    await roleManager.CreateAsync(new IdentityRole(roleName));
                }
            }
        }
    }
}
