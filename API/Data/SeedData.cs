using Microsoft.EntityFrameworkCore;
using MyShop.API.Data.Entities;

namespace MyShop.API.Data
{
    public static class SeedData
    {
        public static async Task SeedLocationsAsync(ApplicationDbContext context)
        {
            // Check if any locations exist
            if (!await context.Locations.AnyAsync())
            {
                // Create a default main location
                var mainLocation = new Location
                {
                    Code = "MAIN",
                    Name = "Main Warehouse",
                    Address = "Main Address",
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                context.Locations.Add(mainLocation);
                await context.SaveChangesAsync();

                // Create default inventory items for all product variants
                var variants = await context.ProductVariants
                    .ToListAsync();

                foreach (var variant in variants)
                {
                    // Check if this variant already has inventory items
                    var hasInventoryItems = await context.InventoryItems
                        .AnyAsync(ii => ii.ProductVariantId == variant.Id);

                    if (!hasInventoryItems)
                    {
                        var inventoryItem = new InventoryItem
                        {
                            LocationId = mainLocation.Id,
                            ProductVariantId = variant.Id,
                            StockQuantity = 0, // Default to 0 stock
                            ReorderLevel = 10, // Default reorder level
                            LastRestockedAt = DateTime.UtcNow,
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow
                        };

                        context.InventoryItems.Add(inventoryItem);
                    }
                }

                await context.SaveChangesAsync();
            }
        }
    }
}
