using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using MyShop.API.Data.Entities;

namespace MyShop.API.Data
{
    public class DbSeeder
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly RoleManager<IdentityRole> _roleManager;
        private readonly ILogger<DbSeeder> _logger;

        public DbSeeder(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            RoleManager<IdentityRole> roleManager,
            ILogger<DbSeeder> logger)
        {
            _context = context;
            _userManager = userManager;
            _roleManager = roleManager;
            _logger = logger;
        }

        public async Task SeedAsync()
        {
            try
            {
                // Check if database is already seeded
                if (await IsDatabaseSeededAsync())
                {
                    _logger.LogInformation("Database is already seeded. Skipping seeding process.");
                    return;
                }

                // Seed roles
                await SeedRolesAsync();

                // Seed users
                await SeedUsersAsync();

                // Seed default store
                await SeedDefaultStoreAsync();

                // Seed categories
                await SeedCategoriesAsync();

                // Seed collections
                await SeedCollectionsAsync();

                // Seed products
                await SeedProductsAsync();

                // Seed navigation menus
                await SeedNavigationMenusAsync();

                // Seed orders
                await SeedOrdersAsync();

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while seeding the database.");
                throw;
            }
        }

        private async Task SeedRolesAsync()
        {
            if (!await _roleManager.RoleExistsAsync("Admin"))
            {
                await _roleManager.CreateAsync(new IdentityRole("Admin"));
            }

            if (!await _roleManager.RoleExistsAsync("Customer"))
            {
                await _roleManager.CreateAsync(new IdentityRole("Customer"));
            }
        }

        private async Task SeedUsersAsync()
        {
            // Admin user
            if (await _userManager.FindByEmailAsync("<EMAIL>") == null)
            {
                var adminUser = new ApplicationUser
                {
                    UserName = "<EMAIL>",
                    Email = "<EMAIL>",
                    FirstName = "Admin",
                    LastName = "User",
                    EmailConfirmed = true,
                    Address = "123 Admin St",
                    City = "Admin City",
                    State = "AS",
                    ZipCode = "12345",
                    Country = "USA",
                    PhoneNumber = "************"
                };

                var result = await _userManager.CreateAsync(adminUser, "Admin123!");
                if (result.Succeeded)
                {
                    await _userManager.AddToRoleAsync(adminUser, "Admin");
                }
            }

            // Customer user
            if (await _userManager.FindByEmailAsync("<EMAIL>") == null)
            {
                var customerUser = new ApplicationUser
                {
                    UserName = "<EMAIL>",
                    Email = "<EMAIL>",
                    FirstName = "Sample",
                    LastName = "Customer",
                    EmailConfirmed = true,
                    Address = "456 Customer Ave",
                    City = "Customer City",
                    State = "CS",
                    ZipCode = "67890",
                    Country = "USA",
                    PhoneNumber = "************"
                };

                var result = await _userManager.CreateAsync(customerUser, "Customer123!");
                if (result.Succeeded)
                {
                    await _userManager.AddToRoleAsync(customerUser, "Customer");
                }
            }
        }

        private async Task SeedCategoriesAsync()
        {
            if (!_context.Categories.Any())
            {
                // Create parent categories first
                var parentCategories = new List<Category>
                {
                    new Category
                    {
                        Name = "Women's Ethnic",
                        Description = "Traditional and designer ethnic wear for women",
                        ImageUrl = "https://images.unsplash.com/photo-1610189020382-668a904cb5dd?q=80&w=1000",
                        IsActive = true,
                        DisplayOrder = 1,
                        ParentId = null,
                        IsSale = false
                    },
                    new Category
                    {
                        Name = "Men's Wear",
                        Description = "Traditional and modern wear for men",
                        ImageUrl = "https://images.unsplash.com/photo-1583391733956-3750e0ff4e8b?q=80&w=1000",
                        IsActive = true,
                        DisplayOrder = 2,
                        ParentId = null,
                        IsSale = false
                    },
                    new Category
                    {
                        Name = "Kids Wear",
                        Description = "Cute and comfortable clothing for kids",
                        ImageUrl = "https://images.unsplash.com/photo-1595032551995-8bfa9dc8d006?q=80&w=1000",
                        IsActive = true,
                        DisplayOrder = 3,
                        ParentId = null,
                        IsSale = false
                    },
                    new Category
                    {
                        Name = "Sale Items",
                        Description = "Special discounted items across all categories",
                        ImageUrl = "https://images.unsplash.com/photo-1610189020382-668a904cb5dd?q=80&w=1000",
                        IsActive = true,
                        DisplayOrder = 4,
                        ParentId = null,
                        IsSale = true
                    }
                };

                await _context.Categories.AddRangeAsync(parentCategories);
                await _context.SaveChangesAsync();

                // Now add child categories
                var womensEthnic = await _context.Categories.FirstOrDefaultAsync(c => c.Name == "Women's Ethnic");
                var mensWear = await _context.Categories.FirstOrDefaultAsync(c => c.Name == "Men's Wear");
                var kidsWear = await _context.Categories.FirstOrDefaultAsync(c => c.Name == "Kids Wear");
                var saleItems = await _context.Categories.FirstOrDefaultAsync(c => c.Name == "Sale Items");

                if (womensEthnic != null)
                {
                    var womenSubcategories = new List<Category>
                    {
                        new Category
                        {
                            Name = "Sarees",
                            Description = "Traditional Indian sarees in various fabrics and designs",
                            ImageUrl = "https://images.unsplash.com/photo-1610189020382-668a904cb5dd?q=80&w=1000",
                            IsActive = true,
                            DisplayOrder = 1,
                            ParentId = womensEthnic.Id,
                            IsSale = false
                        },
                        new Category
                        {
                            Name = "Salwar Kameez",
                            Description = "Elegant salwar kameez sets for all occasions",
                            ImageUrl = "https://images.unsplash.com/photo-1583391733956-3750e0ff4e8b?q=80&w=1000",
                            IsActive = true,
                            DisplayOrder = 2,
                            ParentId = womensEthnic.Id,
                            IsSale = false
                        },
                        new Category
                        {
                            Name = "Lehengas",
                            Description = "Beautiful lehengas for weddings and special occasions",
                            ImageUrl = "https://images.unsplash.com/photo-1595032551995-8bfa9dc8d006?q=80&w=1000",
                            IsActive = true,
                            DisplayOrder = 3,
                            ParentId = womensEthnic.Id,
                            IsSale = false
                        },
                        new Category
                        {
                            Name = "Kurtis",
                            Description = "Stylish kurtis for everyday wear",
                            ImageUrl = "https://images.unsplash.com/photo-1610189020382-668a904cb5dd?q=80&w=1000",
                            IsActive = true,
                            DisplayOrder = 4,
                            ParentId = womensEthnic.Id,
                            IsSale = false
                        },
                        new Category
                        {
                            Name = "Ethnic Gowns",
                            Description = "Fusion ethnic gowns for modern women",
                            ImageUrl = "https://images.unsplash.com/photo-1583391733956-3750e0ff4e8b?q=80&w=1000",
                            IsActive = true,
                            DisplayOrder = 5,
                            ParentId = womensEthnic.Id,
                            IsSale = false
                        }
                    };

                    await _context.Categories.AddRangeAsync(womenSubcategories);
                }

                if (mensWear != null)
                {
                    var menSubcategories = new List<Category>
                    {
                        new Category
                        {
                            Name = "Kurtas",
                            Description = "Traditional and designer kurtas for men",
                            ImageUrl = "https://images.unsplash.com/photo-1610189020382-668a904cb5dd?q=80&w=1000",
                            IsActive = true,
                            DisplayOrder = 1,
                            ParentId = mensWear.Id,
                            IsSale = false
                        },
                        new Category
                        {
                            Name = "Sherwanis",
                            Description = "Elegant sherwanis for special occasions",
                            ImageUrl = "https://images.unsplash.com/photo-1583391733956-3750e0ff4e8b?q=80&w=1000",
                            IsActive = true,
                            DisplayOrder = 2,
                            ParentId = mensWear.Id,
                            IsSale = false
                        },
                        new Category
                        {
                            Name = "Ethnic Jackets",
                            Description = "Stylish ethnic jackets for men",
                            ImageUrl = "https://images.unsplash.com/photo-1595032551995-8bfa9dc8d006?q=80&w=1000",
                            IsActive = true,
                            DisplayOrder = 3,
                            ParentId = mensWear.Id,
                            IsSale = false
                        }
                    };

                    await _context.Categories.AddRangeAsync(menSubcategories);
                }

                if (kidsWear != null)
                {
                    var kidsSubcategories = new List<Category>
                    {
                        new Category
                        {
                            Name = "Girls Ethnic",
                            Description = "Traditional wear for girls",
                            ImageUrl = "https://images.unsplash.com/photo-1610189020382-668a904cb5dd?q=80&w=1000",
                            IsActive = true,
                            DisplayOrder = 1,
                            ParentId = kidsWear.Id,
                            IsSale = false
                        },
                        new Category
                        {
                            Name = "Boys Ethnic",
                            Description = "Traditional wear for boys",
                            ImageUrl = "https://images.unsplash.com/photo-1583391733956-3750e0ff4e8b?q=80&w=1000",
                            IsActive = true,
                            DisplayOrder = 2,
                            ParentId = kidsWear.Id,
                            IsSale = false
                        }
                    };

                    await _context.Categories.AddRangeAsync(kidsSubcategories);
                }

                if (saleItems != null)
                {
                    var saleSubcategories = new List<Category>
                    {
                        new Category
                        {
                            Name = "Women's Sale",
                            Description = "Discounted women's ethnic wear",
                            ImageUrl = "https://images.unsplash.com/photo-1610189020382-668a904cb5dd?q=80&w=1000",
                            IsActive = true,
                            DisplayOrder = 1,
                            ParentId = saleItems.Id,
                            IsSale = true
                        },
                        new Category
                        {
                            Name = "Men's Sale",
                            Description = "Discounted men's ethnic wear",
                            ImageUrl = "https://images.unsplash.com/photo-1583391733956-3750e0ff4e8b?q=80&w=1000",
                            IsActive = true,
                            DisplayOrder = 2,
                            ParentId = saleItems.Id,
                            IsSale = true
                        },
                        new Category
                        {
                            Name = "Kids' Sale",
                            Description = "Discounted kids' ethnic wear",
                            ImageUrl = "https://images.unsplash.com/photo-1595032551995-8bfa9dc8d006?q=80&w=1000",
                            IsActive = true,
                            DisplayOrder = 3,
                            ParentId = saleItems.Id,
                            IsSale = true
                        }
                    };

                    await _context.Categories.AddRangeAsync(saleSubcategories);
                }

                await _context.SaveChangesAsync();
            }
        }

        private async Task SeedCollectionsAsync()
        {
            if (!_context.Collections.Any())
            {
                var collections = new List<Collection>
                {
                    new Collection
                    {
                        Name = "Wedding Collection",
                        Description = "Exquisite ethnic wear for wedding ceremonies",
                        ImageUrl = "https://images.unsplash.com/photo-1610189020382-668a904cb5dd?q=80&w=1000",
                        IsActive = true,
                        DisplayOrder = 1
                    },
                    new Collection
                    {
                        Name = "Festival Collection",
                        Description = "Vibrant ethnic wear for festive occasions",
                        ImageUrl = "https://images.unsplash.com/photo-1583391733956-3750e0ff4e8b?q=80&w=1000",
                        IsActive = true,
                        DisplayOrder = 2
                    },
                    new Collection
                    {
                        Name = "Summer Collection",
                        Description = "Light and comfortable ethnic wear for summer",
                        ImageUrl = "https://images.unsplash.com/photo-1595032551995-8bfa9dc8d006?q=80&w=1000",
                        IsActive = true,
                        DisplayOrder = 3
                    },
                    new Collection
                    {
                        Name = "Designer Collection",
                        Description = "Premium designer ethnic wear",
                        ImageUrl = "https://images.unsplash.com/photo-1610189020382-668a904cb5dd?q=80&w=1000",
                        IsActive = true,
                        DisplayOrder = 4
                    }
                };

                await _context.Collections.AddRangeAsync(collections);
                await _context.SaveChangesAsync();
            }
        }

        private async Task SeedProductsAsync()
        {
            if (!_context.Products.Any())
            {
                var categories = await _context.Categories.ToListAsync();
                var collections = await _context.Collections.ToListAsync();

                if (categories.Count() == 0 || collections.Count() == 0)
                {
                    return;
                }

                var products = new List<Product>();

                // Sarees
                var sareeCategory = categories.First(c => c.Name == "Sarees");
                products.Add(new Product
                {
                    Name = "Banarasi Silk Saree",
                    Description = "Handwoven Banarasi silk saree with intricate gold zari work. Perfect for weddings and special occasions.",
                   ImageUrl = "https://images.unsplash.com/photo-1610189020382-668a904cb5dd?q=80&w=1000",
                    IsActive = true,
                    IsFeatured = true,
                    CategoryId = sareeCategory.Id,
                    Collections = new List<Collection> { collections[0], collections[3] } // Wedding, Designer
                });

                products.Add(new Product
                {
                    Name = "Cotton Handloom Saree",
                    Description = "Comfortable cotton handloom saree with traditional motifs. Ideal for daily wear and casual occasions.",

                    ImageUrl = "https://images.unsplash.com/photo-1583391733956-3750e0ff4e8b?q=80&w=1000",
                    IsActive = true,
                    IsFeatured = false,
                    CategoryId = sareeCategory.Id,
                    Collections = new List<Collection> { collections[2] } // Summer
                });

                // Salwar Kameez
                var salwarCategory = categories.First(c => c.Name == "Salwar Kameez");
                products.Add(new Product
                {
                    Name = "Embroidered Anarkali Suit",
                    Description = "Elegant Anarkali suit with intricate embroidery. Perfect for festive occasions and celebrations.",

                    ImageUrl = "https://images.unsplash.com/photo-1595032551995-8bfa9dc8d006?q=80&w=1000",
                    IsActive = true,
                    IsFeatured = true,
                    CategoryId = salwarCategory.Id,
                    Collections = new List<Collection> { collections[1], collections[3] } // Festival, Designer
                });

                // Lehengas
                var lehengaCategory = categories.First(c => c.Name == "Lehengas");
                products.Add(new Product
                {
                    Name = "Bridal Lehenga Choli",
                    Description = "Stunning bridal lehenga with heavy embroidery and mirror work. Perfect for wedding ceremonies.",
                    ImageUrl = "https://images.unsplash.com/photo-1610189020382-668a904cb5dd?q=80&w=1000",
                    IsActive = true,
                    IsFeatured = true,
                    CategoryId = lehengaCategory.Id,
                    Collections = new List<Collection> { collections[0], collections[3] } // Wedding, Designer
                });

                // Kurtis
                var kurtiCategory = categories.First(c => c.Name == "Kurtis");
                products.Add(new Product
                {
                    Name = "Printed Cotton Kurti",
                    Description = "Comfortable cotton kurti with trendy prints. Perfect for casual and office wear.",
                    ImageUrl = "https://images.unsplash.com/photo-1583391733956-3750e0ff4e8b?q=80&w=1000",
                    IsActive = true,
                    IsFeatured = false,
                    CategoryId = kurtiCategory.Id,
                    Collections = new List<Collection> { collections[2] } // Summer
                });

                // Ethnic Gowns
                var gownCategory = categories.First(c => c.Name == "Ethnic Gowns");
                products.Add(new Product
                {
                    Name = "Designer Floor Length Gown",
                    Description = "Elegant floor-length gown with contemporary design. Perfect for receptions and parties.",
                    ImageUrl = "https://images.unsplash.com/photo-1595032551995-8bfa9dc8d006?q=80&w=1000",
                    IsActive = true,
                    IsFeatured = true,
                    CategoryId = gownCategory.Id,
                    Collections = new List<Collection> { collections[1], collections[3] } // Festival, Designer
                });

                await _context.Products.AddRangeAsync(products);
                await _context.SaveChangesAsync();

                // Create default variants for each product
                var variants = new List<ProductVariant>();
                foreach (var product in products)
                {
                    var variant = new ProductVariant
                    {
                        ProductId = product.Id,
                        SKU = $"SKU-{Guid.NewGuid().ToString()[..8]}",
                        Price = Random.Shared.Next(500, 2000),
                        Cost = Random.Shared.Next(300, 1500),
                        MRP = Random.Shared.Next(600, 2500),
                        IsActive = true,
                        Weight = 0.5m,
                        WeightUnit = "kg"
                    };
                    variants.Add(variant);
                }

                await _context.ProductVariants.AddRangeAsync(variants);
                await _context.SaveChangesAsync();

                // Add inventory for each variant
                // Get or create default location
                var defaultLocation = await _context.Locations.FirstOrDefaultAsync();
                if (defaultLocation == null)
                {
                    // Create a default location if none exists
                    defaultLocation = new Location
                    {
                        Code = "MAIN",
                        Name = "Main Warehouse",
                        Address = "Main Address",
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };
                    _context.Locations.Add(defaultLocation);
                    await _context.SaveChangesAsync();
                }

                var inventoryItems = new List<InventoryItem>();
                foreach (var variant in variants)
                {
                    inventoryItems.Add(new InventoryItem
                    {
                        LocationId = defaultLocation.Id,
                        ProductVariantId = variant.Id,
                        StockQuantity = Random.Shared.Next(10, 100),
                        ReorderLevel = 10,
                        LastRestockedAt = DateTime.UtcNow,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    });
                }

                await _context.InventoryItems.AddRangeAsync(inventoryItems);
                await _context.SaveChangesAsync();
            }
        }

        private async Task SeedNavigationMenusAsync()
        {
            if (!_context.NavigationMenus.Any())
            {
                // Create single-level menus
                var menus = new List<NavigationMenu>
                {
                    new NavigationMenu
                    {
                        Name = "Home",
                        Url = "/",
                        DisplayOrder = 1,
                        IsActive = true,
                        Icon = "home"
                    },
                    new NavigationMenu
                    {
                        Name = "Categories",
                        Url = "/categories",
                        DisplayOrder = 2,
                        IsActive = true,
                        Icon = "category"
                    },
                    new NavigationMenu
                    {
                        Name = "Collections",
                        Url = "/collections",
                        DisplayOrder = 3,
                        IsActive = true,
                        Icon = "collections"
                    },
                    new NavigationMenu
                    {
                        Name = "Sale",
                        Url = "/sale",
                        DisplayOrder = 4,
                        IsActive = true,
                        Icon = "offer"
                    },
                    new NavigationMenu
                    {
                        Name = "Contact Us",
                        Url = "/contact",
                        DisplayOrder = 5,
                        IsActive = true,
                        Icon = "contact_mail"
                    },
                    new NavigationMenu
                    {
                        Name = "FAQ",
                        Url = "/faq",
                        DisplayOrder = 6,
                        IsActive = true,
                        Icon = "help"
                    }
                };

                await _context.NavigationMenus.AddRangeAsync(menus);
                await _context.SaveChangesAsync();
            }
        }

        private async Task SeedOrdersAsync()
        {
            if (!_context.Orders.Any())
            {
                var products = await _context.Products.ToListAsync();
                var customerUser = await _userManager.FindByEmailAsync("<EMAIL>");

                if (products.Count() == 0 || customerUser == null)
                {
                    return;
                }

                // Create 5 orders for the customer
                var customerOrders = new List<Order>();
                for (int i = 0; i < 5; i++)
                {
                    var order = new Order
                    {
                        OrderNumber = $"ORD-{DateTime.UtcNow:yyyyMMdd}-{Guid.NewGuid().ToString().Substring(0, 8).ToUpper()}",
                        UserId = customerUser.Id,
                        IsGuestOrder = false,
                        CustomerEmail = customerUser.Email,
                        CustomerPhone = customerUser.PhoneNumber ?? "************",
                        ShippingAddress = customerUser.Address,
                        ShippingCity = customerUser.City,
                        ShippingState = customerUser.State,
                        ShippingZipCode = customerUser.ZipCode,
                        ShippingCountry = customerUser.Country,
                        Status = (OrderStatus)(i % 4), // Different statuses
                        CreatedAt = DateTime.UtcNow.AddDays(-i * 2), // Different dates
                        OrderItems = new List<OrderItem>()
                    };

                    // Add 1-3 random products to each order
                    var numProducts = Random.Shared.Next(1, 4);
                    var selectedProducts = products.OrderBy(x => Guid.NewGuid()).Take(numProducts).ToList();

                    foreach (var product in selectedProducts)
                    {
                        var quantity = Random.Shared.Next(1, 3);
                        var orderItem = new OrderItem
                        {
                            ProductId = product.Id,
                            ProductName = product.Name,
                            UnitPrice = product.Variants.FirstOrDefault()?.Price ?? 0  ,
                            Quantity = quantity,
                            TotalPrice = product.Variants.FirstOrDefault()?.Price ?? 0  * quantity
                        };

                        order.OrderItems.Add(orderItem);
                    }

                    order.TotalAmount = order.OrderItems.Sum(item => item.TotalPrice);
                    customerOrders.Add(order);
                }

                // Create 5 guest orders
                var guestOrders = new List<Order>();
                for (int i = 0; i < 5; i++)
                {
                    var order = new Order
                    {
                        OrderNumber = $"ORD-{DateTime.UtcNow:yyyyMMdd}-{Guid.NewGuid().ToString().Substring(0, 8).ToUpper()}",
                        IsGuestOrder = true,
                        CustomerEmail = $"guest{i+1}@example.com",
                        CustomerPhone = $"555-{Random.Shared.Next(100, 999)}-{Random.Shared.Next(1000, 9999)}",
                        ShippingAddress = $"{Random.Shared.Next(100, 999)} Guest St",
                        ShippingCity = "Guest City",
                        ShippingState = "GS",
                        ShippingZipCode = $"{Random.Shared.Next(10000, 99999)}",
                        ShippingCountry = "USA",
                        Status = (OrderStatus)(i % 4), // Different statuses
                        CreatedAt = DateTime.UtcNow.AddDays(-i * 3), // Different dates
                        OrderItems = new List<OrderItem>()
                    };

                    // Add 1-2 random products to each order
                    var numProducts = Random.Shared.Next(1, 3);
                    var selectedProducts = products.OrderBy(x => Guid.NewGuid()).Take(numProducts).ToList();

                    foreach (var product in selectedProducts)
                    {
                        var quantity = Random.Shared.Next(1, 3);
                        var orderItem = new OrderItem
                        {
                            ProductId = product.Id,
                            ProductName = product.Name,
                            UnitPrice = product.Variants.FirstOrDefault()?.Price ?? 0 ,
                            Quantity = quantity,
                            TotalPrice = product.Variants.FirstOrDefault()?.Price ?? 0 * quantity
                        };

                        order.OrderItems.Add(orderItem);
                    }

                    order.TotalAmount = order.OrderItems.Sum(item => item.TotalPrice);
                    guestOrders.Add(order);
                }

                await _context.Orders.AddRangeAsync(customerOrders);
                await _context.Orders.AddRangeAsync(guestOrders);
                await _context.SaveChangesAsync();
            }
        }

        private async Task SeedDefaultStoreAsync()
        {
            if (!await _context.Stores.AnyAsync())
            {
                // Create default store
                var defaultStore = new Store
                {
                    Name = "Default Store",
                    Description = "Default store for the application",
                    IsActive = true,
                    PrimaryDomain = "localhost",
                    LogoUrl = "/images/logo.png",
                    FaviconUrl = "/images/favicon.ico",
                    Email = "<EMAIL>",
                    Phone = "1234567890",
                    Address = "123 Main St, City, Country",
                    FacebookUrl = "https://facebook.com",
                    InstagramUrl = "https://instagram.com",
                    TwitterUrl = "https://twitter.com",
                    WhatsappNumber = "1234567890",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.Stores.Add(defaultStore);
                await _context.SaveChangesAsync();

                // Create theme configuration for the store
                var themeConfig = new ThemeConfiguration
                {
                    StoreId = defaultStore.Id,
                    PrimaryColor = "#4CAF50",
                    SecondaryColor = "#2196F3",
                    AccentColor = "#FF9800",
                    TextPrimaryColor = "#212121",
                    TextSecondaryColor = "#757575",
                    TextLightColor = "#FFFFFF",
                    BackgroundPrimaryColor = "#FFFFFF",
                    BackgroundSecondaryColor = "#F5F5F5",
                    BackgroundAccentColor = "#E0E0E0",
                    HeaderBackgroundColor = "#FFFFFF",
                    HeaderTextColor = "#212121",
                    FooterBackgroundColor = "#F5F5F5",
                    FooterTextColor = "#757575",
                    ButtonPrimaryColor = "#4CAF50",
                    ButtonSecondaryColor = "#2196F3",
                    ButtonTextColor = "#FFFFFF",
                    NavLinkColor = "#212121",
                    NavLinkActiveColor = "#4CAF50",
                    NavLinkHoverColor = "#2196F3",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.ThemeConfigurations.Add(themeConfig);

                // Create website configuration for the store
                var websiteConfig = new WebsiteConfiguration
                {
                    StoreId = defaultStore.Id,
                    WebsiteTitle = defaultStore.Name,
                    LogoUrl = defaultStore.LogoUrl,
                    MetaDescription = defaultStore.Description,
                    MetaKeywords = "",
                    AnnouncementText = "Free shipping on all orders above ₹999",
                    ShowAnnouncement = true,
                    Email = defaultStore.Email,
                    Phone = defaultStore.Phone,
                    Address = defaultStore.Address,
                    FacebookUrl = defaultStore.FacebookUrl,
                    InstagramUrl = defaultStore.InstagramUrl,
                    TwitterUrl = defaultStore.TwitterUrl,
                    WhatsappNumber = defaultStore.WhatsappNumber,
                    YoutubeUrl = "",
                    ShowBannerSection = true,
                    ShowCategorySection = true,
                    ShowNewArrivalsSection = true,
                    ShowCollectionSection = true,
                    ShowBestSellingSection = true,
                    BannerTitle = "Welcome to " + defaultStore.Name,
                    BannerSubtitle = "Discover our exclusive range of traditional ethnic wear",
                    BannerButtonText = "Shop Now",
                    BannerButtonLink = "/products",
                    CategorySectionTitle = "Shop By Category",
                    NewArrivalsSectionTitle = "New Arrivals",
                    CollectionSectionTitle = "Shop By Collection",
                    BestSellingSectionTitle = "Best Selling",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.WebsiteConfigurations.Add(websiteConfig);
                await _context.SaveChangesAsync();

                // Make admin user a super admin
                var adminUser = await _userManager.FindByEmailAsync("<EMAIL>");
                if (adminUser != null)
                {
                    adminUser.IsSuperAdmin = true;
                    await _userManager.UpdateAsync(adminUser);

                    // Create store admin for default store
                    var storeAdmin = new StoreAdmin
                    {
                        UserId = adminUser.Id,
                        StoreId = defaultStore.Id,
                        CanManageProducts = true,
                        CanManageOrders = true,
                        CanManageCustomers = true,
                        CanManageSettings = true,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };

                    _context.StoreAdmins.Add(storeAdmin);
                    await _context.SaveChangesAsync();
                }
            }
        }

        private async Task<bool> IsDatabaseSeededAsync()
        {
            // Check if there are any users, categories, products, navigation menus, etc.
            // If any of these exist, we consider the database seeded
            return await _context.Users.AnyAsync() &&
                   await _context.Categories.AnyAsync() &&
                   await _context.Collections.AnyAsync() &&
                   await _context.NavigationMenus.AnyAsync();
        }
    }
}
