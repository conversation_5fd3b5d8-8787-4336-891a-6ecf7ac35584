using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using MyShop.API.Data.Entities;

namespace MyShop.API.Data
{
    public class ApplicationDbContext : IdentityDbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        public DbSet<Product> Products { get; set; }
        public DbSet<ProductImage> ProductImages { get; set; }
        public DbSet<ProductVariant> ProductVariants { get; set; }
        public DbSet<ProductAttribute> ProductAttributes { get; set; }
        public DbSet<ProductGroup> ProductGroups { get; set; }
        public DbSet<ProductType> ProductTypes { get; set; }
        public DbSet<Category> Categories { get; set; }
        public DbSet<Collection> Collections { get; set; }
        public DbSet<Order> Orders { get; set; }
        public DbSet<OrderItem> OrderItems { get; set; }

        public DbSet<InventoryItem> InventoryItems { get; set; }
        public DbSet<Coupon> Coupons { get; set; }
        public DbSet<CouponUsage> CouponUsages { get; set; }
        public DbSet<Location> Locations { get; set; }
        public DbSet<OtpVerification> OtpVerifications { get; set; }
        public DbSet<NavigationMenu> NavigationMenus { get; set; }
        public DbSet<WebsiteConfiguration> WebsiteConfigurations { get; set; }
        public DbSet<WebsiteBanner> WebsiteBanners { get; set; }
        public DbSet<Store> Stores { get; set; }
        public DbSet<StoreDomain> StoreDomains { get; set; }
        public DbSet<StoreAdmin> StoreAdmins { get; set; }
        public DbSet<ThemeConfiguration> ThemeConfigurations { get; set; }
        public DbSet<UserModulePermission> UserModulePermissions { get; set; }

        // Role-based access control is now handled by ASP.NET Identity


        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure the discriminator for ApplicationUser
            modelBuilder.Entity<ApplicationUser>(entity =>
            {
                entity.ToTable("AspNetUsers");
                entity.HasDiscriminator<string>("Discriminator")
                      .HasValue<ApplicationUser>("ApplicationUser");
            });

            modelBuilder.Entity<Order>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.OrderNumber).IsRequired();
                entity.Property(e => e.Status).HasConversion<string>();
                entity.Property(e => e.SubTotal).HasColumnType("decimal(18,2)");
                entity.Property(e => e.ShippingCost).HasColumnType("decimal(18,2)");
                entity.Property(e => e.TaxAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.DiscountAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.TotalAmount).HasColumnType("decimal(18,2)");

                entity.HasMany(e => e.OrderItems)
                      .WithOne(e => e.Order)
                      .HasForeignKey(e => e.OrderId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<OrderItem>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.UnitPrice).HasColumnType("decimal(18,2)");
                entity.Property(e => e.TotalPrice).HasColumnType("decimal(18,2)");
                entity.Property(e => e.OriginalPrice).HasColumnType("decimal(18,2)");
                entity.Property(e => e.DiscountAmount).HasColumnType("decimal(18,2)");
            });

            // Configure relationships
            modelBuilder.Entity<Product>()
                .HasOne(p => p.Category)
                .WithMany(c => c.Products)
                .HasForeignKey(p => p.CategoryId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Product>()
                .HasOne(p => p.ProductType)
                .WithMany(pt => pt.Products)
                .HasForeignKey(p => p.ProductTypeId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Product>()
                .HasMany(p => p.Collections)
                .WithMany(c => c.Products)
                .UsingEntity(j => j.ToTable("ProductCollections"));

            modelBuilder.Entity<OrderItem>()
                .HasOne(oi => oi.Order)
                .WithMany(o => o.OrderItems)
                .HasForeignKey(oi => oi.OrderId);

            modelBuilder.Entity<OrderItem>()
                .HasOne(oi => oi.Product)
                .WithMany()
                .HasForeignKey(oi => oi.ProductId);

            // Inventory relationship (multiple locations)
            modelBuilder.Entity<InventoryItem>()
                .HasOne(ii => ii.ProductVariant)
                .WithMany(pv => pv.InventoryItems)
                .HasForeignKey(ii => ii.ProductVariantId);

            modelBuilder.Entity<InventoryItem>()
                .HasOne(ii => ii.Location)
                .WithMany(l => l.InventoryItems)
                .HasForeignKey(ii => ii.LocationId);

            // Removed Inventory table in favor of InventoryItems

            modelBuilder.Entity<ProductImage>()
                .HasOne(pi => pi.Product)
                .WithMany(p => p.Images)
                .HasForeignKey(pi => pi.ProductId);

            modelBuilder.Entity<ProductImage>()
                .HasIndex(pi => new { pi.ProductId, pi.IsMain })
                .HasFilter("[IsMain] = 1");

            // SKU removed from Product entity as it should only exist at variant level

            // Configure ProductVariant relationships
            modelBuilder.Entity<ProductVariant>()
                .HasOne(pv => pv.Product)
                .WithMany(p => p.Variants)
                .HasForeignKey(pv => pv.ProductId);

            modelBuilder.Entity<ProductVariant>()
                .HasIndex(pv => pv.SKU)
                .IsUnique();

            // Configure Coupon relationships
            modelBuilder.Entity<Coupon>()
                .HasOne(c => c.Category)
                .WithMany()
                .HasForeignKey(c => c.CategoryId)
                .IsRequired(false)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Coupon>()
                .HasOne(c => c.Collection)
                .WithMany()
                .HasForeignKey(c => c.CollectionId)
                .IsRequired(false)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Coupon>()
                .HasOne(c => c.Product)
                .WithMany()
                .HasForeignKey(c => c.ProductId)
                .IsRequired(false)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<CouponUsage>()
                .HasOne(cu => cu.Coupon)
                .WithMany(c => c.CouponUsages)
                .HasForeignKey(cu => cu.CouponId);

            modelBuilder.Entity<CouponUsage>()
                .HasOne(cu => cu.User)
                .WithMany()
                .HasForeignKey(cu => cu.UserId)
                .IsRequired(false);

            modelBuilder.Entity<CouponUsage>()
                .HasOne(cu => cu.Order)
                .WithMany()
                .HasForeignKey(cu => cu.OrderId)
                .IsRequired(false);

            // Configure ProductAttribute relationships
            modelBuilder.Entity<ProductAttribute>()
                .HasOne(pa => pa.Product)
                .WithMany(p => p.Attributes)
                .HasForeignKey(pa => pa.ProductId);

            // Configure ProductGroup relationships
            modelBuilder.Entity<Product>()
                .HasOne(p => p.ProductGroup)
                .WithMany(pg => pg.Products)
                .HasForeignKey(p => p.ProductGroupId)
                .IsRequired(false);

            // Configure decimal properties for variants
            modelBuilder.Entity<ProductVariant>(entity =>
            {
                entity.Property(e => e.Price).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Cost).HasColumnType("decimal(18,2)");
            });

            // Configure Store entity
            modelBuilder.Entity<Store>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.HasMany(s => s.AdditionalDomains)
                      .WithOne(d => d.Store)
                      .HasForeignKey(d => d.StoreId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(s => s.ThemeConfiguration)
                      .WithOne(t => t.Store)
                      .HasForeignKey<ThemeConfiguration>(t => t.StoreId)
                      .OnDelete(DeleteBehavior.NoAction);
            });



            // Configure StoreAdmin entity
            modelBuilder.Entity<StoreAdmin>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.HasOne(sa => sa.User)
                      .WithMany(u => u.StoreAdmins)
                      .HasForeignKey(sa => sa.UserId);

                entity.HasOne(sa => sa.Store)
                      .WithMany()
                      .HasForeignKey(sa => sa.StoreId);
            });

            // Configure ThemeConfiguration entity
            modelBuilder.Entity<ThemeConfiguration>(entity =>
            {
                entity.HasKey(e => e.Id);
            });

            // Configure WebsiteConfiguration entity
            modelBuilder.Entity<WebsiteConfiguration>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.HasOne(wc => wc.Store)
                      .WithOne()
                      .HasForeignKey<WebsiteConfiguration>(wc => wc.StoreId)
                      .OnDelete(DeleteBehavior.NoAction);
            });

            // Configure WebsiteBanner entity
            modelBuilder.Entity<WebsiteBanner>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.HasOne(e => e.Store)
                      .WithMany()
                      .HasForeignKey(e => e.StoreId)
                      .OnDelete(DeleteBehavior.NoAction);
            });

            // Configure UserModulePermission entity
            modelBuilder.Entity<UserModulePermission>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.HasOne(ump => ump.User)
                      .WithMany()
                      .HasForeignKey(ump => ump.UserId);

                entity.HasOne(ump => ump.Store)
                      .WithMany()
                      .HasForeignKey(ump => ump.StoreId)
                      .IsRequired(false)
                      .OnDelete(DeleteBehavior.NoAction);

                // Create a unique index on UserId, Module, and StoreId
                entity.HasIndex(ump => new { ump.UserId, ump.Module, ump.StoreId })
                      .IsUnique();
            });

            // Role-based access control is now handled by ASP.NET Identity
        }
    }
}