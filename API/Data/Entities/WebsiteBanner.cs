using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MyShop.API.Data.Entities
{
    public class WebsiteBanner : IStoreEntity
    {
        [Key]
        public int Id { get; set; }

        public string ImageUrl { get; set; } = "";
        public string Title { get; set; } = "";
        public string Subtitle { get; set; } = "";
        public string ButtonText { get; set; } = "Shop Now";
        public string ButtonLink { get; set; } = "/products";
        public int DisplayOrder { get; set; } = 0;
        public bool IsActive { get; set; } = true;

        // Timestamps
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Store relationship
        public int StoreId { get; set; }
        public virtual Store Store { get; set; } = null!;
    }
}
