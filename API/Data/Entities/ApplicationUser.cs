using Microsoft.AspNetCore.Identity;
using System;

namespace MyShop.API.Data.Entities
{
    public enum UserType
    {
        SuperAdmin,
        Admin,
        Staff,
        Customer
    }

    public class ApplicationUser : IdentityUser
    {
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string City { get; set; } = string.Empty;
        public string State { get; set; } = string.Empty;
        public string ZipCode { get; set; } = string.Empty;
        public string Country { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // User type property
        public UserType UserType { get; set; } = UserType.Customer;

        // Multi-store support
        public bool IsSuperAdmin { get; set; } = false;
        public virtual ICollection<StoreAdmin> StoreAdmins { get; set; } = new List<StoreAdmin>();

        // Additional properties for tracking customer behavior
        public DateTime? LastLoginDate { get; set; }
        public string? LastLoginIp { get; set; }

        // Navigation properties
        public virtual ICollection<Order> Orders { get; set; } = new List<Order>();
    }
}
