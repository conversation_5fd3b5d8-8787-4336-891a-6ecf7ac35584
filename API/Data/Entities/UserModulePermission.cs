using System;
using System.ComponentModel.DataAnnotations;

namespace MyShop.API.Data.Entities
{
    public class UserModulePermission
    {
        [Key]
        public int Id { get; set; }
        
        public string UserId { get; set; }
        public virtual ApplicationUser User { get; set; }
        
        public string Module { get; set; }
        
        public bool CanView { get; set; } = false;
        public bool CanCreate { get; set; } = false;
        public bool CanEdit { get; set; } = false;
        public bool CanDelete { get; set; } = false;
        
        public int? StoreId { get; set; }
        public virtual Store Store { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}
