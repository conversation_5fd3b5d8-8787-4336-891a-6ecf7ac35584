namespace MyShop.API.Data.Entities
{
    public class ProductAttribute
    {
        public int Id { get; set; }
        public string Key { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
        public bool IsVariantAttribute { get; set; } = false; // Indicates if this attribute is used to create variants

        // Foreign keys
        public int ProductId { get; set; }

        // Navigation properties
        public virtual Product Product { get; set; } = null!;
    }
}
