using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MyShop.API.Data.Entities
{
    public class WebsiteConfiguration
    {
        [Key]
        public int Id { get; set; }

        // Store relationship
        public int StoreId { get; set; }
        public virtual Store Store { get; set; } = null!;

        // General Settings
        public string WebsiteTitle { get; set; } = "Jaipur Cotton House";
        public string LogoUrl { get; set; } = "";
        public string MetaDescription { get; set; } = "";
        public string MetaKeywords { get; set; } = "";

        // Announcement
        public string AnnouncementText { get; set; } = "Free shipping on all orders above ₹999";
        public bool ShowAnnouncement { get; set; } = true;

        // Contact Information
        public string Phone { get; set; } = "";
        public string Email { get; set; } = "";
        public string Address { get; set; } = "";

        // Social Media
        public string FacebookUrl { get; set; } = "";
        public string InstagramUrl { get; set; } = "";
        public string TwitterUrl { get; set; } = "";
        public string WhatsappNumber { get; set; } = "";
        public string YoutubeUrl { get; set; } = "";

        // Home Page Sections Visibility
        public bool ShowBannerSection { get; set; } = true;
        public bool ShowCategorySection { get; set; } = true;
        public bool ShowNewArrivalsSection { get; set; } = true;
        public bool ShowCollectionSection { get; set; } = true;
        public bool ShowBestSellingSection { get; set; } = true;

        // Banner Configuration
        public string BannerTitle { get; set; } = "Welcome to Jaipur Cotton House";
        public string BannerSubtitle { get; set; } = "Discover our exclusive range of traditional ethnic wear";
        public string BannerButtonText { get; set; } = "Shop Now";
        public string BannerButtonLink { get; set; } = "/products";

        // Section Titles
        public string CategorySectionTitle { get; set; } = "Shop By Category";
        public string NewArrivalsSectionTitle { get; set; } = "New Arrivals";
        public string CollectionSectionTitle { get; set; } = "Shop By Collection";
        public string BestSellingSectionTitle { get; set; } = "Best Selling";

        // Timestamps
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}
