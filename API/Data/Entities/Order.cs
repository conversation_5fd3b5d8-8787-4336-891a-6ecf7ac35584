namespace MyShop.API.Data.Entities
{
    public enum OrderStatus
    {
        Pending,
        Processing,
        ReadyToShip,
        Shipped,
        OutForDelivery,
        Delivered,
        Cancelled,
        Returned,
        RefundInitiated,
        Refunded
    }

    public enum PaymentStatus
    {
        Pending,
        Paid,
        Failed,
        Refunded,
        PartiallyRefunded
    }

    public enum ShippingMethod
    {
        Standard,
        Express,
        NextDay,
        InternationalStandard,
        InternationalExpress,
        LocalPickup
    }

    public class Order : IStoreEntity
    {
        public int Id { get; set; }
        public string OrderNumber { get; set; } = string.Empty;
        public decimal SubTotal { get; set; }
        public decimal ShippingCost { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal DiscountAmount { get; set; }
        public string? CouponCode { get; set; }
        public int? CouponId { get; set; }
        public decimal TotalAmount { get; set; }
        public OrderStatus Status { get; set; } = OrderStatus.Pending;
        public PaymentStatus PaymentStatus { get; set; } = PaymentStatus.Pending;
        public string PaymentMethod { get; set; } = string.Empty;
        public string? PaymentTransactionId { get; set; }
        public ShippingMethod ShippingMethod { get; set; } = ShippingMethod.Standard;
        public string? UserId { get; set; }
        public bool IsGuestOrder { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        public string CustomerEmail { get; set; } = string.Empty;
        public string CustomerPhone { get; set; } = string.Empty;
        public string ShippingAddress { get; set; } = string.Empty;
        public string ShippingCity { get; set; } = string.Empty;
        public string ShippingState { get; set; } = string.Empty;
        public string ShippingZipCode { get; set; } = string.Empty;
        public string ShippingCountry { get; set; } = string.Empty;
        public string? BillingAddress { get; set; }
        public string? BillingCity { get; set; }
        public string? BillingState { get; set; }
        public string? BillingZipCode { get; set; }
        public string? BillingCountry { get; set; }
        public string? CourierName { get; set; }
        public string? TrackingNumber { get; set; }
        public string? TrackingUrl { get; set; }
        public string? Notes { get; set; }
        public string? AdminNotes { get; set; }
        public string? CancellationReason { get; set; }
        public string? ReturnReason { get; set; }
        public DateTime? ProcessedDate { get; set; }
        public DateTime? ReadyToShipDate { get; set; }
        public DateTime? ShippedDate { get; set; }
        public DateTime? OutForDeliveryDate { get; set; }
        public DateTime? DeliveredDate { get; set; }
        public DateTime? CancelledDate { get; set; }
        public DateTime? ReturnedDate { get; set; }
        public DateTime? RefundInitiatedDate { get; set; }
        public DateTime? RefundedDate { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Store relationship
        public int StoreId { get; set; }
        public virtual Store Store { get; set; } = null!;

        // Navigation properties
        public virtual ApplicationUser? User { get; set; }
        public virtual ICollection<OrderItem> OrderItems { get; set; } = new List<OrderItem>();
        public virtual Coupon? Coupon { get; set; }
    }
}
