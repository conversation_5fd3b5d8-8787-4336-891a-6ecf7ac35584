namespace MyShop.API.Data.Entities
{
    public class InventoryItem
    {
        public int Id { get; set; }
        public int StockQuantity { get; set; }
        public int ReorderLevel { get; set; } = 10;

        // Foreign keys
        public int LocationId { get; set; }
        public int? ProductId { get; set; }
        public int? ProductVariantId { get; set; }

        // Navigation properties
        public virtual Location Location { get; set; } = null!;
        public virtual Product? Product { get; set; }
        public virtual ProductVariant? ProductVariant { get; set; }

        public DateTime LastRestockedAt { get; set; } = DateTime.UtcNow;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}
