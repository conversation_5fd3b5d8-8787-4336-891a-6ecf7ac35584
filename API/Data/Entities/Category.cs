namespace MyShop.API.Data.Entities
{
    public class Category : IStoreEntity
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string ImageUrl { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
        public int DisplayOrder { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Parent-Child relationship
        public int? ParentId { get; set; }
        public virtual Category? Parent { get; set; }
        public virtual ICollection<Category> Children { get; set; } = new List<Category>();

        // Store relationship
        public int StoreId { get; set; }
        public virtual Store Store { get; set; } = null!;

        // Navigation properties
        public virtual ICollection<Product> Products { get; set; } = new List<Product>();

        // Additional properties
        public bool IsSale { get; set; } = false;
    }
}
