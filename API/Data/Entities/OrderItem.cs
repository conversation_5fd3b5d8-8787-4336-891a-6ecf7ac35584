namespace MyShop.API.Data.Entities
{
    public class OrderItem
    {
        public int Id { get; set; }
        public int OrderId { get; set; }
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public decimal UnitPrice { get; set; }
        public int Quantity { get; set; }
        public decimal TotalPrice { get; set; }
        
        /// <summary>
        /// Original price before any discounts
        /// </summary>
        public decimal OriginalPrice { get; set; }
        
        /// <summary>
        /// Discount amount applied to this item
        /// </summary>
        public decimal DiscountAmount { get; set; }
        
        // Navigation properties
        public virtual Order Order { get; set; } = null!;
        public virtual Product Product { get; set; } = null!;
    }
}
