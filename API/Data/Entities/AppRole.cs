using System;
using System.Collections.Generic;

namespace MyShop.API.Data.Entities
{
    public class AppRole
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public int? StoreId { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation properties
        public Store Store { get; set; }
        public ICollection<AppRolePermission> RolePermissions { get; set; } = new List<AppRolePermission>();
        public ICollection<AppUserRole> UserRoles { get; set; } = new List<AppUserRole>();
    }
}
