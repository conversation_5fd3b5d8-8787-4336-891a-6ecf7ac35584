using System;
using System.Collections.Generic;
using MyShop.API.DTOs;

namespace MyShop.API.Data.Entities
{
    public enum CouponType
    {
        Percentage,
        FixedAmount,
        FreeShipping
    }


    public class Coupon : IStoreEntity
    {
        public int Id { get; set; }
        public string Code { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public CouponType Type { get; set; } = CouponType.Percentage;
        public decimal Value { get; set; } // Percentage or fixed amount
        public CouponApplicability Applicability { get; set; } = CouponApplicability.All;
        public int? CategoryId { get; set; }
        public int? CollectionId { get; set; }
        public int? ProductId { get; set; }
        public decimal? MinimumPurchaseAmount { get; set; }
        public int? MinimumQuantity { get; set; }
        public decimal? MaximumDiscountAmount { get; set; }
        public bool IsActive { get; set; } = true;
        public bool IsOneTimeUse { get; set; } = false;
        public bool DisplayOnCartPage { get; set; } = false;
        public int UsageLimit { get; set; } = 0; // 0 means unlimited
        public int UsageCount { get; set; } = 0;
        public DateTime StartDate { get; set; } = DateTime.UtcNow;
        public DateTime? EndDate { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Store relationship
        public int StoreId { get; set; }
        public virtual Store Store { get; set; } = null!;

        // Navigation properties
        public virtual Category? Category { get; set; }
        public virtual Collection? Collection { get; set; }
        public virtual Product? Product { get; set; }
        public virtual ICollection<CouponUsage> CouponUsages { get; set; } = new List<CouponUsage>();
    }
}
