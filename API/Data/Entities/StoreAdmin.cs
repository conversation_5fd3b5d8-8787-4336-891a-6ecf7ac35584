using System;
using System.ComponentModel.DataAnnotations;

namespace MyShop.API.Data.Entities
{
    public class StoreAdmin
    {
        [Key]
        public int Id { get; set; }
        public string UserId { get; set; }
        public virtual ApplicationUser User { get; set; }
        
        public int StoreId { get; set; }
        public virtual Store Store { get; set; }
        
        public bool CanManageProducts { get; set; } = true;
        public bool CanManageOrders { get; set; } = true;
        public bool CanManageCustomers { get; set; } = true;
        public bool CanManageSettings { get; set; } = false;
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}
