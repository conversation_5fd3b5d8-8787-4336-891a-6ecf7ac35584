using System;
using System.Collections.Generic;

namespace MyShop.API.Data.Entities
{
    public class ProductType : IStoreEntity
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Store relationship
        public int StoreId { get; set; }
        public virtual Store Store { get; set; } = null!;

        // Navigation properties
        public virtual ICollection<Product> Products { get; set; } = new List<Product>();
    }
}
