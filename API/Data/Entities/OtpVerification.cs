using System;

namespace MyShop.API.Data.Entities
{
    public class OtpVerification
    {
        public int Id { get; set; }
        public string? Email { get; set; } // Nullable as requested
        public string PhoneNumber { get; set; } = string.Empty;
        public string Otp { get; set; } = string.Empty;
        public DateTime ExpiresAt { get; set; }
        public bool IsVerified { get; set; } = false;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }
}
