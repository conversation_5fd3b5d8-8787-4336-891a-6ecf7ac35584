namespace MyShop.API.Data.Entities
{
    public class Inventory
    {
        public int Id { get; set; }
        public string SKU { get; set; } = string.Empty; // Map with SKU instead of ProductVariantId
        public int? ProductVariantId { get; set; } // Optional, can be null for SKU-only inventory
        public int StockQuantity { get; set; }
        public int ReorderLevel { get; set; }
        public DateTime LastRestockedAt { get; set; } = DateTime.UtcNow;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation property
        public virtual ProductVariant? ProductVariant { get; set; } // Can be null for SKU-only inventory
    }
}
