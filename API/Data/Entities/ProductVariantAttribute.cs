namespace MyShop.API.Data.Entities
{
    public class ProductVariantAttribute
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
        
        // Foreign keys
        public int ProductVariantId { get; set; }
        
        // Navigation properties
        public virtual ProductVariant ProductVariant { get; set; } = null!;
    }
}
