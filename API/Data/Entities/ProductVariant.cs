using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace MyShop.API.Data.Entities
{
    public class ProductVariant
    {
        public int Id { get; set; }
        public string SKU { get; set; } = string.Empty;
        public string Barcode { get; set; } = string.Empty; // Kept for backward compatibility

        [Column(TypeName = "decimal(18,2)")]
        public decimal Price { get; set; } // Kept for backward compatibility

        [Column(TypeName = "decimal(18,2)")]
        public decimal Cost { get; set; } // Kept for backward compatibility

        [Column(TypeName = "decimal(18,2)")]
        public decimal MRP { get; set; } // Maximum Retail Price
        public bool IsActive { get; set; } = true;
        public string Size { get; set; } = string.Empty;

        // Physical attributes
        [Column(TypeName = "decimal(10,3)")]
        public decimal Weight { get; set; }
        public string WeightUnit { get; set; } = "kg"; // Kept for backward compatibility

        [Column(TypeName = "decimal(10,2)")]
        public decimal Length { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        public decimal Breadth { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        public decimal Height { get; set; }

        public string DimensionUnit { get; set; } = "cm"; // Kept for backward compatibility

        [Column(TypeName = "decimal(15,3)")]
        public decimal Volume { get; set; }

        public string VolumeUnit { get; set; } = "cm3"; // Kept for backward compatibility

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        public int StockQuantity { get; set; } = 0;

        // Foreign keys
        public int ProductId { get; set; }

        // Navigation properties
        public virtual Product Product { get; set; } = null!;

        // Inventory relationship (multiple locations)
        public virtual ICollection<InventoryItem> InventoryItems { get; set; } = new List<InventoryItem>();
    }
}
