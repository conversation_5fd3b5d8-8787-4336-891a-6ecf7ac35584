using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace MyShop.API.Data.Entities
{
    public class Product : IStoreEntity
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string ImageUrl { get; set; } = string.Empty; // Main product image
        public string Barcode { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
        public bool IsFeatured { get; set; } = false;

        // Core fields as specified
        public string StyleCode { get; set; } = string.Empty;
        public int ReturnExchangeCondition { get; set; } = 0;
        public string Color { get; set; } = string.Empty;
        public string HSNCode { get; set; } = string.Empty;
        public string GSTType { get; set; } = string.Empty;


        public string CustomAttributes { get; set; } = string.Empty; // Stored as JSON
        public bool HasVariants { get; set; } = false; // Indicates if this product has variants
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Foreign keys
        public int CategoryId { get; set; }
        public int? ProductGroupId { get; set; } // Optional group for related products
        public int? ProductTypeId { get; set; } // Optional product type

        // Store relationship
        public int StoreId { get; set; }
        public virtual Store Store { get; set; } = null!;

        // Navigation properties
        public virtual Category Category { get; set; } = null!;
        public virtual ProductGroup? ProductGroup { get; set; }
        public virtual ProductType? ProductType { get; set; }
        public virtual ICollection<Collection> Collections { get; set; } = new List<Collection>();
        public virtual ICollection<ProductImage> Images { get; set; } = new List<ProductImage>();
        public virtual ICollection<ProductAttribute> Attributes { get; set; } = new List<ProductAttribute>();
        public virtual ICollection<ProductVariant> Variants { get; set; } = new List<ProductVariant>();
    }
}
