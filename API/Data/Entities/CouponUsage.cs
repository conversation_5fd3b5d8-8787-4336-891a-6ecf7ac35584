using System;

namespace MyShop.API.Data.Entities
{
    public class CouponUsage
    {
        public int Id { get; set; }
        public int CouponId { get; set; }
        public string? UserId { get; set; }
        public int? OrderId { get; set; }
        public decimal DiscountAmount { get; set; }
        public DateTime UsedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual Coupon Coupon { get; set; } = null!;
        public virtual ApplicationUser? User { get; set; }
        public virtual Order? Order { get; set; }
    }
}
