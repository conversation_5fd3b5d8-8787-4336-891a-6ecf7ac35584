using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace MyShop.API.Data.Entities
{
    public class Store
    {
        [Key]
        public int Id { get; set; }

        // Store Information
        public string Name { get; set; } = "Default Store";
        public string Description { get; set; } = "";
        public bool IsActive { get; set; } = true;

        // Store Identification
        [StringLength(10)]
        public string StoreKey { get; set; } = "";

        // Domain Configuration
        public string PrimaryDomain { get; set; } = "localhost";

        // Store Branding
        public string LogoUrl { get; set; } = "";
        public string FaviconUrl { get; set; } = "";

        // Store Contact Information
        public string Email { get; set; } = "";
        public string Phone { get; set; } = "";
        public string Address { get; set; } = "";

        // Social Media
        public string FacebookUrl { get; set; } = "";
        public string InstagramUrl { get; set; } = "";
        public string TwitterUrl { get; set; } = "";
        public string WhatsappNumber { get; set; } = "";

        // Relationships
        public virtual ICollection<StoreDomain> AdditionalDomains { get; set; }
        public virtual ThemeConfiguration ThemeConfiguration { get; set; }

        // Timestamps
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}
