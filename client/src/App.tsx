import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { StyledEngineProvider } from '@mui/material';
import CustomThemeProvider from './components/theme/ThemeProvider';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import './styles/customerLayout.css';

// Layouts
import CustomerLayout from './layouts/CustomerLayout';

// Customer Pages
import Home from './pages/Home';
import CollectionPage from './pages/CollectionPage';
import ProductDetail from './pages/ProductDetail';
import Cart from './pages/Cart';
import Checkout from './pages/Checkout';

import Register from './pages/Register';
import OtpLogin from './pages/OtpLogin';
import Wishlist from './pages/Wishlist';

// Types
import { RootState } from './store';

// Theme is now managed by CustomThemeProvider

// Protected route component
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { isAuthenticated } = useSelector((state: RootState) => state.auth);

  if (!isAuthenticated) {
    return <Navigate to="/otp-login" replace />;
  }

  return children;
};



function App() {
  return (
    <StyledEngineProvider injectFirst>
      <CustomThemeProvider>
        <ToastContainer position="top-right" autoClose={3000} hideProgressBar={false} newestOnTop closeOnClick rtl={false} pauseOnFocusLoss draggable pauseOnHover />
        <Router>
        <Routes>
          {/* Customer Routes */}
          <Route path="/" element={<CustomerLayout />}>
            <Route index element={<Home />} />
            <Route path="products" element={<CollectionPage />} />
            <Route path="product/:id" element={<ProductDetail />} />
            <Route path="category/:categoryId" element={<CollectionPage />} />
            <Route path="collection/:collectionId" element={<CollectionPage />} />
            <Route path="cart" element={<Cart />} />
            <Route path="checkout" element={<Checkout />} />
            <Route path="login" element={<Navigate to="/otp-login" replace />} />
            <Route path="otp-login" element={<OtpLogin />} />
            <Route path="register" element={<Register />} />
            <Route path="wishlist" element={
              <ProtectedRoute>
                <Wishlist />
              </ProtectedRoute>
            } />
            <Route path="profile" element={
              <ProtectedRoute>
                <div>Profile Page (To be implemented)</div>
              </ProtectedRoute>
            } />
            <Route path="orders" element={
              <ProtectedRoute>
                <div>Orders Page (To be implemented)</div>
              </ProtectedRoute>
            } />
          </Route>

          {/* Admin routes removed - now handled by separate admin application */}

          {/* 404 Route */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Router>
      </CustomThemeProvider>
    </StyledEngineProvider>
  );
}

export default App;
