import { configureStore } from '@reduxjs/toolkit';
import authReducer from './slices/authSlice';
import cartReducer from './slices/cartSlice';
import productReducer from './slices/productSlice';
import productVariantReducer from './slices/productVariantSlice';
import productTypeReducer from './slices/productTypeSlice';
import categoryReducer from './slices/categorySlice';
import collectionReducer from './slices/collectionSlice';
import orderReducer from './slices/orderSlice';
import navigationReducer from './slices/navigationSlice';
import wishlistReducer from './slices/wishlistSlice';
import userPreferencesReducer from './slices/userPreferencesSlice';
import websiteConfigReducer from './slices/websiteConfigSlice';
import themeReducer from './slices/themeSlice';

export const store = configureStore({
  reducer: {
    auth: authReducer,
    cart: cartReducer,
    products: productReducer,
    productVariants: productVariantReducer,
    productTypes: productTypeReducer,
    categories: categoryReducer,
    collections: collectionReducer,
    orders: orderReducer,
    navigation: navigationReducer,
    wishlist: wishlistReducer,
    userPreferences: userPreferencesReducer,
    websiteConfig: websiteConfigReducer,
    theme: themeReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
