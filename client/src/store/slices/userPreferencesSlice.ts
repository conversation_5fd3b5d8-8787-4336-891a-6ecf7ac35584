import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import config from '../../config';

export interface UserPreferences {
  productsPerRow: number;
  showSizeSelector: boolean;
}

interface UserPreferencesState {
  preferences: UserPreferences;
}

// Always use default preferences
const loadPreferences = (): UserPreferences => {
  // Return defaults from config
  return {
    productsPerRow: config.productDisplay.defaultProductsPerRow,
    showSizeSelector: config.productDisplay.defaultShowSizeSelector,
  };
};

// No need to save preferences
const savePreferences = (preferences: UserPreferences) => {
  // Do nothing - don't persist preferences
};

const initialState: UserPreferencesState = {
  preferences: loadPreferences(),
};

const userPreferencesSlice = createSlice({
  name: 'userPreferences',
  initialState,
  reducers: {
    setProductsPerRow: (state, action: PayloadAction<number>) => {
      state.preferences.productsPerRow = action.payload;
    },
    setShowSizeSelector: (state, action: PayloadAction<boolean>) => {
      state.preferences.showSizeSelector = action.payload;
    },
    resetPreferences: (state) => {
      state.preferences = {
        productsPerRow: config.productDisplay.defaultProductsPerRow,
        showSizeSelector: config.productDisplay.defaultShowSizeSelector,
      };
    },
  },
});

export const {
  setProductsPerRow,
  setShowSizeSelector,
  resetPreferences
} = userPreferencesSlice.actions;

export default userPreferencesSlice.reducer;
