import { createSlice, PayloadAction } from '@reduxjs/toolkit';

}
export interface CartItem {
  productId: number;
  name: string;
  price: number;
  quantity: number;
  imageUrl: string;
  variantId?: number;
  sku?: string;
  size?: string;
  originalPrice?: number;
  discountAmount?: number;
}

interface CartState {
  items: CartItem[];
  totalItems: number;
  totalAmount: number;
  discountAmount: number;
  couponCode: string;
}

// Load cart from localStorage
const loadCart = (): CartState => {
  const cartJson = localStorage.getItem('cart');
  if (cartJson) {
    const cart = JSON.parse(cartJson) as CartState;
    return cart;
  }
  return {
    items: [],
    totalItems: 0,
    totalAmount: 0,
    discountAmount: 0,
    couponCode: ''
  };
};

// Save cart to localStorage
const saveCart = (cart: CartState) => {
  localStorage.setItem('cart', JSON.stringify(cart));
};

const initialState: CartState = loadCart();

const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    addToCart: (state, action: PayloadAction<CartItem>) => {
      const { productId, name, price, quantity, imageUrl, variantId, sku, size } = action.payload;

      // If variant is specified, check if that specific variant is in cart
      const existingItemIndex = variantId
        ? state.items.findIndex(item => item.productId === productId && item.variantId === variantId)
        : state.items.findIndex(item => item.productId === productId && !item.variantId);

      if (existingItemIndex >= 0) {
        // Update existing item
        state.items[existingItemIndex].quantity += quantity;
      } else {
        // Add new item
        state.items.push({
          productId,
          name,
          price,
          quantity,
          imageUrl,
          variantId,
          sku,
          size
        });
      }

      state.totalItems = state.items.reduce((total, item) => total + item.quantity, 0);
      state.totalAmount = state.items.reduce((total, item) => total + (item.price * item.quantity), 0);

      saveCart(state);
    },
    removeFromCart: (state, action: PayloadAction<{productId: number, variantId?: number}>) => {
      const { productId, variantId } = action.payload;

      if (variantId) {
        // Remove specific variant
        state.items = state.items.filter(item => !(item.productId === productId && item.variantId === variantId));
      } else {
        // Remove all variants of this product
        state.items = state.items.filter(item => item.productId !== productId);
      }

      state.totalItems = state.items.reduce((total, item) => total + item.quantity, 0);
      state.totalAmount = state.items.reduce((total, item) => total + (item.price * item.quantity), 0);

      saveCart(state);
    },
    updateQuantity: (state, action: PayloadAction<{ productId: number; quantity: number; variantId?: number }>) => {
      const { productId, quantity, variantId } = action.payload;

      const itemIndex = variantId
        ? state.items.findIndex(item => item.productId === productId && item.variantId === variantId)
        : state.items.findIndex(item => item.productId === productId && !item.variantId);

      if (itemIndex >= 0) {
        state.items[itemIndex].quantity = quantity;
      }

      state.totalItems = state.items.reduce((total, item) => total + item.quantity, 0);
      state.totalAmount = state.items.reduce((total, item) => total + (item.price * item.quantity), 0);

      saveCart(state);
    },
    clearCart: (state) => {
      state.items = [];
      state.totalItems = 0;
      state.totalAmount = 0;
      state.discountAmount = 0;
      state.couponCode = '';

      saveCart(state);
    },
    }
  }
});

export const { addToCart, removeFromCart, updateQuantity, clearCart, applyCoupon } = cartSlice.actions;
export default cartSlice.reducer;
