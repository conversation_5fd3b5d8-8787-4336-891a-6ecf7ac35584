import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import api from '../../services/api';

export interface WebsiteConfiguration {
  id: number;
  websiteTitle: string;
  logoUrl: string;
  metaDescription: string;
  metaKeywords: string;
  announcementText: string;
  showAnnouncement: boolean;
  instagramUrl: string;
  facebookUrl: string;
  twitterUrl: string;
  youtubeUrl: string;
  whatsappNumber: string;
  email: string;
  phone: string;
  address: string;
  showBannerSection: boolean;
  showCategorySection: boolean;
  showNewArrivalsSection: boolean;
  showCollectionSection: boolean;
  showBestSellingSection: boolean;
  bannerTitle: string;
  bannerSubtitle: string;
  bannerButtonText: string;
  bannerButtonLink: string;
  categorySectionTitle: string;
  newArrivalsSectionTitle: string;
  collectionSectionTitle: string;
  bestSellingSectionTitle: string;
}

export interface WebsiteBanner {
  id: number;
  imageUrl: string;
  title: string;
  subtitle: string;
  buttonText: string;
  buttonLink: string;
  displayOrder: number;
  isActive: boolean;
}

interface WebsiteConfigState {
  configuration: WebsiteConfiguration | null;
  banners: WebsiteBanner[];
  loading: boolean;
  error: string | null;
}

const initialState: WebsiteConfigState = {
  configuration: null,
  banners: [],
  loading: false,
  error: null
};

// Fetch website configuration
export const fetchWebsiteConfiguration = createAsyncThunk(
  'websiteConfig/fetchConfiguration',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/WebsiteConfiguration');
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || 'Failed to fetch website configuration');
    }
  }
);

// Update website configuration
export const updateWebsiteConfiguration = createAsyncThunk(
  'websiteConfig/updateConfiguration',
  async (configData: Partial<WebsiteConfiguration>, { rejectWithValue }) => {
    try {
      await api.put('/WebsiteConfiguration', configData);
      return configData;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || 'Failed to update website configuration');
    }
  }
);

// Update section visibility
export const updateSectionVisibility = createAsyncThunk(
  'websiteConfig/updateSectionVisibility',
  async (sectionVisibility: Record<string, boolean>, { rejectWithValue }) => {
    try {
      await api.put('/WebsiteConfiguration/sections', sectionVisibility);
      return sectionVisibility;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || 'Failed to update section visibility');
    }
  }
);

// Fetch website banners
export const fetchWebsiteBanners = createAsyncThunk(
  'websiteConfig/fetchBanners',
  async (isActive: boolean = true, { rejectWithValue }) => {
    try {
      const response = await api.get('/WebsiteBanners', {
        params: { isActive }
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || 'Failed to fetch website banners');
    }
  }
);

// Create website banner
export const createWebsiteBanner = createAsyncThunk(
  'websiteConfig/createBanner',
  async (bannerData: Omit<WebsiteBanner, 'id'>, { rejectWithValue }) => {
    try {
      const response = await api.post('/WebsiteBanners', bannerData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || 'Failed to create website banner');
    }
  }
);

// Update website banner
export const updateWebsiteBanner = createAsyncThunk(
  'websiteConfig/updateBanner',
  async ({ id, bannerData }: { id: number; bannerData: Omit<WebsiteBanner, 'id'> }, { rejectWithValue }) => {
    try {
      await api.put(`/WebsiteBanners/${id}`, bannerData);
      return { id, ...bannerData };
    } catch (error: any) {
      return rejectWithValue(error.response?.data || 'Failed to update website banner');
    }
  }
);

// Delete website banner
export const deleteWebsiteBanner = createAsyncThunk(
  'websiteConfig/deleteBanner',
  async (id: number, { rejectWithValue }) => {
    try {
      await api.delete(`/WebsiteBanners/${id}`);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || 'Failed to delete website banner');
    }
  }
);

// Reorder website banners
export const reorderWebsiteBanners = createAsyncThunk(
  'websiteConfig/reorderBanners',
  async (bannerOrders: Record<number, number>, { rejectWithValue }) => {
    try {
      await api.put('/WebsiteBanners/reorder', bannerOrders);
      return bannerOrders;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || 'Failed to reorder website banners');
    }
  }
);

const websiteConfigSlice = createSlice({
  name: 'websiteConfig',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    // Fetch website configuration
    builder.addCase(fetchWebsiteConfiguration.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(fetchWebsiteConfiguration.fulfilled, (state, action: PayloadAction<WebsiteConfiguration>) => {
      state.loading = false;
      state.configuration = action.payload;
    });
    builder.addCase(fetchWebsiteConfiguration.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });

    // Update website configuration
    builder.addCase(updateWebsiteConfiguration.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(updateWebsiteConfiguration.fulfilled, (state, action: PayloadAction<Partial<WebsiteConfiguration>>) => {
      state.loading = false;
      if (state.configuration) {
        state.configuration = { ...state.configuration, ...action.payload };
      }
    });
    builder.addCase(updateWebsiteConfiguration.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });

    // Update section visibility
    builder.addCase(updateSectionVisibility.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(updateSectionVisibility.fulfilled, (state, action: PayloadAction<Record<string, boolean>>) => {
      state.loading = false;
      if (state.configuration) {
        state.configuration = { ...state.configuration, ...action.payload };
      }
    });
    builder.addCase(updateSectionVisibility.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });

    // Fetch website banners
    builder.addCase(fetchWebsiteBanners.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(fetchWebsiteBanners.fulfilled, (state, action: PayloadAction<WebsiteBanner[]>) => {
      state.loading = false;
      state.banners = action.payload;
    });
    builder.addCase(fetchWebsiteBanners.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });

    // Create website banner
    builder.addCase(createWebsiteBanner.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(createWebsiteBanner.fulfilled, (state, action: PayloadAction<WebsiteBanner>) => {
      state.loading = false;
      state.banners.push(action.payload);
    });
    builder.addCase(createWebsiteBanner.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });

    // Update website banner
    builder.addCase(updateWebsiteBanner.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(updateWebsiteBanner.fulfilled, (state, action: PayloadAction<WebsiteBanner>) => {
      state.loading = false;
      const index = state.banners.findIndex(banner => banner.id === action.payload.id);
      if (index !== -1) {
        state.banners[index] = action.payload;
      }
    });
    builder.addCase(updateWebsiteBanner.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });

    // Delete website banner
    builder.addCase(deleteWebsiteBanner.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(deleteWebsiteBanner.fulfilled, (state, action: PayloadAction<number>) => {
      state.loading = false;
      state.banners = state.banners.filter(banner => banner.id !== action.payload);
    });
    builder.addCase(deleteWebsiteBanner.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });

    // Reorder website banners
    builder.addCase(reorderWebsiteBanners.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(reorderWebsiteBanners.fulfilled, (state, action: PayloadAction<Record<number, number>>) => {
      state.loading = false;
      // Update display order of banners
      state.banners = state.banners.map(banner => {
        if (action.payload[banner.id] !== undefined) {
          return { ...banner, displayOrder: action.payload[banner.id] };
        }
        return banner;
      }).sort((a, b) => a.displayOrder - b.displayOrder);
    });
    builder.addCase(reorderWebsiteBanners.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });
  }
});

export default websiteConfigSlice.reducer;
