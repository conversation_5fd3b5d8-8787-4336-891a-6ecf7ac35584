import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import api from '../../services/api';
import { RootState } from '..';

export interface OrderItem {
  id: number;
  productId: number;
  productName: string;
  unitPrice: number;
  quantity: number;
  totalPrice: number;
  productImageUrl?: string;
}

export interface Order {
  id: number;
  orderNumber: string;
  totalAmount: number;
  status: string;
  userId?: string;
  isGuestOrder: boolean;
  customerEmail: string;
  customerPhone: string;
  shippingAddress: string;
  shippingCity: string;
  shippingState: string;
  shippingZipCode: string;
  shippingCountry: string;
  trackingNumber?: string;
  shippedDate?: string;
  deliveredDate?: string;
  createdAt: string;
  orderItems: OrderItem[];
}

export interface CreateOrderItem {
  productId: number;
  quantity: number;
}

export interface CreateOrder {
  userId?: string;
  isGuestOrder: boolean;
  customerEmail: string;
  customerPhone: string;
  shippingAddress: string;
  shippingCity: string;
  shippingState: string;
  shippingZipCode: string;
  shippingCountry: string;
  orderItems: CreateOrderItem[];
}

interface OrdersResponse {
  data: Order[];
  headers: {
    'x-total-count': string;
    'x-total-pages': string;
  };
}

interface OrderState {
  orders: Order[];
  order: Order | null;
  loading: boolean;
  error: string | null;
  success: boolean;
  totalCount: number;
  totalPages: number;
  currentPage: number;
}

export const createOrder = createAsyncThunk<Order, CreateOrder, { rejectValue: string; state: RootState }>(
  'orders/createOrder',
  async (orderData, { rejectWithValue, getState }) => {
    try {
      const response = await api.post<Order>(`/orders`, orderData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to create order');
    }
  }
);

export const fetchMyOrders = createAsyncThunk<OrdersResponse, { page?: number; pageSize?: number }, { rejectValue: string; state: RootState }>(
  'orders/fetchMyOrders',
  async (params, { rejectWithValue, getState }) => {
    try {
      const { auth } = getState();
      if (!auth.isAuthenticated) {
        return rejectWithValue('User not authenticated');
      }

      const { page = 1, pageSize = 10 } = params;

      const response = await api.get<Order[]>(`/orders/my-orders?page=${page}&pageSize=${pageSize}`);

      return {
        data: response.data,
        headers: {
          'x-total-count': response.headers['x-total-count'],
          'x-total-pages': response.headers['x-total-pages']
        }
      };
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to fetch orders');
    }
  }
);

export const fetchOrderById = createAsyncThunk<Order, number, { rejectValue: string; state: RootState }>(
  'orders/fetchOrderById',
  async (id, { rejectWithValue, getState }) => {
    try {
      const response = await api.get<Order>(`/orders/${id}`);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to fetch order');
    }
  }
);

const initialState: OrderState = {
  orders: [],
  order: null,
  loading: false,
  error: null,
  success: false,
  totalCount: 0,
  totalPages: 0,
  currentPage: 1
};

const orderSlice = createSlice({
  name: 'orders',
  initialState,
  reducers: {
    clearOrderError: (state) => {
      state.error = null;
    },
    clearOrderSuccess: (state) => {
      state.success = false;
    },
    setCurrentPage: (state, action: PayloadAction<number>) => {
      state.currentPage = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(createOrder.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(createOrder.fulfilled, (state, action: PayloadAction<Order>) => {
        state.loading = false;
        state.order = action.payload;
        state.success = true;
      })
      .addCase(createOrder.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to create order';
        state.success = false;
      })
      .addCase(fetchMyOrders.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchMyOrders.fulfilled, (state, action) => {
        state.loading = false;
        state.orders = action.payload.data;
        state.totalCount = parseInt(action.payload.headers['x-total-count'] || '0');
        state.totalPages = parseInt(action.payload.headers['x-total-pages'] || '0');
      })
      .addCase(fetchMyOrders.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to fetch orders';
      })
      .addCase(fetchOrderById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchOrderById.fulfilled, (state, action: PayloadAction<Order>) => {
        state.loading = false;
        state.order = action.payload;
      })
      .addCase(fetchOrderById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to fetch order';
      });
  }
});

export const { clearOrderError, clearOrderSuccess, setCurrentPage } = orderSlice.actions;
export default orderSlice.reducer;
