import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import api from '../../services/api';

export interface ProductImage {
  id?: number;
  imageUrl?: string;
  url?: string; // API returns url instead of imageUrl
  isMain: boolean;
  displayOrder?: number;
}

export interface ProductAttribute {
  id?: number;
  productId: number;
  key: string;
  value: string;
  isVariantAttribute?: boolean;
}

export interface ProductVariant {
  id: number;
  sku: string;
  barcode?: string;
  price: number;
  cost?: number;
  mrp?: number;
  stockQuantity: number;
  reorderLevel?: number;
  isActive: boolean;
  productId: number;
  productName: string;
  size?: string;
  weight?: number;
  weightUnit?: string;
  length?: number;
  breadth?: number;
  height?: number;
  dimensionUnit?: string;
  volume?: number;
  volumeUnit?: string;
  variantAttributes?: Record<string, string>;
  inventory?: {
    id: number;
    sku: string;
    stockQuantity: number;
    reorderLevel: number;
    lastRestockedAt?: string;
  };
}

export interface Product {
  id: number;
  name: string;
  description: string;
  imageUrl: string;
  barcode?: string;
  isActive: boolean;
  isFeatured: boolean;
  hasVariants: boolean;
  categoryId: number;
  categoryName: string;
  productGroupId?: number;
  productGroupName?: string;
  productTypeId?: number;
  productTypeName?: string;
  stockQuantity: number;
  reorderLevel?: number;
  collectionIds: number[];
  collectionNames: string[];
  images?: ProductImage[];
  mainImage?: string;
  styleCode?: string;
  returnExchangeCondition?: number;
  color?: string;
  HSNCode?: string;  // Note the uppercase HSNCode from API
  GSTType?: string;  // Note the uppercase GSTType from API
  customAttributes?: Record<string, string>;
  variants?: ProductVariant[];
  attributes?: ProductAttribute[];
}

interface ProductsState {
  products: Product[];
  product: Product | null;
  loading: boolean;
  error: string | null;
  totalCount: number;
  totalPages: number;
  currentPage: number;
  importResult: {
    success: boolean;
    message: string;
    importedProducts: any[];
    errors: string[];
  } | null;
}

interface ProductsResponse {
  data: Product[];
  headers: {
    'x-total-count': string;
    'x-total-pages': string;
  };
}

export const fetchProducts = createAsyncThunk<
  ProductsResponse,
  {
    page?: number;
    pageSize?: number;
    search?: string;
    categoryId?: number;
    collectionId?: number;
    isFeatured?: boolean;
    isActive?: boolean;
  },
  { rejectValue: string }
>(
  'products/fetchProducts',
  async (params, { rejectWithValue }) => {
    try {
      const { page = 1, pageSize = 10, search, categoryId, collectionId, isFeatured, isActive = true } = params;

      let url = `/product?page=${page}&pageSize=${pageSize}`;

      if (search) url += `&search=${encodeURIComponent(search)}`;
      if (categoryId) url += `&categoryId=${categoryId}`;
      if (collectionId) url += `&collectionId=${collectionId}`;
      if (isFeatured !== undefined) url += `&isFeatured=${isFeatured}`;
      if (isActive !== undefined) url += `&isActive=${isActive}`;

      const response = await api.get<Product[]>(url);

      return {
        data: response.data,
        headers: {
          'x-total-count': response.headers['x-total-count'],
          'x-total-pages': response.headers['x-total-pages']
        }
      };
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to fetch products');
    }
  }
);

export const fetchProductById = createAsyncThunk<Product, number, { rejectValue: string }>(
  'products/fetchProductById',
  async (id, { rejectWithValue }) => {
    try {
      const response = await api.get<Product>(`/product/${id}`);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to fetch product');
    }
  }
);

export interface CreateProductDto {
  name: string;
  description: string;
  barcode?: string;
  isActive: boolean;
  isFeatured: boolean;
  hasVariants?: boolean;
  categoryId: number;
  collectionIds?: number[];
  imageUrl?: string;
  images?: { url: string; isMain: boolean; displayOrder?: number }[];
  // Product specific fields
  styleCode?: string;
  returnExchangeCondition?: number;
  color?: string;
  HSNCode?: string;  // Note the uppercase HSNCode for API
  GSTType?: string;  // Note the uppercase GSTType for API
  productGroupId?: number;
  productTypeId?: number;
  productTypeName?: string;
  customAttributes?: Record<string, string>;
  // Variant related fields
  variants?: {
    sku: string;
    barcode?: string;
    price: number;
    cost?: number;
    mrp?: number;
    stockQuantity: number;
    reorderLevel?: number;
    isActive: boolean;
    size?: string;
    weight?: number;
    weightUnit?: string;
    length?: number;
    breadth?: number;
    height?: number;
    dimensionUnit?: string;
    variantAttributes?: Record<string, string>;
  }[];
}

export const createProduct = createAsyncThunk<Product, CreateProductDto, { rejectValue: string }>(
  'products/createProduct',
  async (productData, { rejectWithValue }) => {
    try {
      const response = await api.post<Product>('/product', productData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to create product');
    }
  }
);

export interface UpdateProductDto extends Partial<CreateProductDto> {
  id: number;
}

export const updateProduct = createAsyncThunk<Product, UpdateProductDto, { rejectValue: string }>(
  'products/updateProduct',
  async ({ id, ...productData }, { rejectWithValue }) => {
    try {
      const response = await api.put<Product>(`/product/${id}`, productData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to update product');
    }
  }
);

export const deleteProduct = createAsyncThunk<number, number, { rejectValue: string }>(
  'products/deleteProduct',
  async (id, { rejectWithValue }) => {
    try {
      await api.delete(`/product/${id}`);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to delete product');
    }
  }
);

export const uploadProductImages = createAsyncThunk<
  ProductImage[],
  { productId: number; images: { imageUrl: string; isMain: boolean; displayOrder?: number }[] },
  { rejectValue: string }
>(
  'products/uploadProductImages',
  async ({ productId, images }, { rejectWithValue }) => {
    try {
      // Convert imageUrl to url for API compatibility
      const apiImages = images.map(img => ({
        url: img.imageUrl,
        isMain: img.isMain,
        displayOrder: img.displayOrder
      }));
      const response = await api.post<ProductImage[]>(`/product/${productId}/images`, apiImages);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to upload product images');
    }
  }
);

export const setMainProductImage = createAsyncThunk<
  ProductImage,
  { productId: number; imageId: number },
  { rejectValue: string }
>(
  'products/setMainProductImage',
  async ({ productId, imageId }, { rejectWithValue }) => {
    try {
      const response = await api.put<ProductImage>(`/product/${productId}/images/${imageId}/main`);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to set main product image');
    }
  }
);

export const deleteProductImage = createAsyncThunk<
  void,
  { productId: number; imageId: number },
  { rejectValue: string }
>(
  'products/deleteProductImage',
  async ({ productId, imageId }, { rejectWithValue }) => {
    try {
      await api.delete(`/product/${productId}/images/${imageId}`);
      return;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to delete product image');
    }
  }
);

export const importProducts = createAsyncThunk<
  { success: boolean; message: string; importedProducts: any[]; errors: string[] },
  FormData,
  { rejectValue: string }
>(
  'products/importProducts',
  async (formData, { rejectWithValue }) => {
    try {
      const response = await api.post('/ProductImportExport/import', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to import products');
    }
  }
);

export const exportProductsTemplate = createAsyncThunk<void, void, { rejectValue: string }>(
  'products/exportProductsTemplate',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/ProductImportExport/export-template', { responseType: 'blob' });
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'product_import_template.xlsx');
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to export products template');
    }
  }
);

export const exportProducts = createAsyncThunk<void, void, { rejectValue: string }>(
  'products/exportProducts',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/ProductImportExport/export', { responseType: 'blob' });
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `products_export_${new Date().toISOString().split('T')[0]}.xlsx`);
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to export products');
    }
  }
);

const initialState: ProductsState = {
  products: [],
  product: null,
  loading: false,
  error: null,
  totalCount: 0,
  totalPages: 0,
  currentPage: 1,
  importResult: null
};

const productSlice = createSlice({
  name: 'products',
  initialState,
  reducers: {
    clearProductError: (state) => {
      state.error = null;
    },
    setCurrentPage: (state, action: PayloadAction<number>) => {
      state.currentPage = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchProducts.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProducts.fulfilled, (state, action) => {
        state.loading = false;
        state.products = action.payload.data;
        state.totalCount = parseInt(action.payload.headers['x-total-count'] || '0');
        state.totalPages = parseInt(action.payload.headers['x-total-pages'] || '0');
      })
      .addCase(fetchProducts.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to fetch products';
      })
      .addCase(fetchProductById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProductById.fulfilled, (state, action) => {
        state.loading = false;
        state.product = action.payload;
      })
      .addCase(fetchProductById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to fetch product';
      })
      .addCase(createProduct.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createProduct.fulfilled, (state, action) => {
        state.loading = false;
        state.products = [...state.products, action.payload];
      })
      .addCase(createProduct.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to create product';
      })
      .addCase(updateProduct.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateProduct.fulfilled, (state, action) => {
        state.loading = false;
        state.products = state.products.map(product =>
          product.id === action.payload.id ? action.payload : product
        );
        if (state.product && state.product.id === action.payload.id) {
          state.product = action.payload;
        }
      })
      .addCase(updateProduct.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to update product';
      })
      .addCase(deleteProduct.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteProduct.fulfilled, (state, action) => {
        state.loading = false;
        state.products = state.products.filter(product => product.id !== action.payload);
        if (state.product && state.product.id === action.payload) {
          state.product = null;
        }
      })
      .addCase(deleteProduct.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to delete product';
      })
      .addCase(uploadProductImages.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(uploadProductImages.fulfilled, (state, action) => {
        state.loading = false;
        if (state.product) {
          state.product.images = [...(state.product.images || []), ...action.payload];
        }
      })
      .addCase(uploadProductImages.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to upload product images';
      })
      .addCase(setMainProductImage.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(setMainProductImage.fulfilled, (state, action) => {
        state.loading = false;
        if (state.product && state.product.images) {
          state.product.images = state.product.images.map(image => ({
            ...image,
            isMain: image.id === action.payload.id
          }));
        }
      })
      .addCase(setMainProductImage.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to set main product image';
      })
      .addCase(deleteProductImage.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteProductImage.fulfilled, (state, action) => {
        state.loading = false;
        if (state.product && state.product.images) {
          state.product.images = state.product.images.filter(
            image => image.id !== (action.meta.arg as { productId: number; imageId: number }).imageId
          );
        }
      })
      .addCase(deleteProductImage.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to delete product image';
      })
      .addCase(importProducts.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.importResult = null;
      })
      .addCase(importProducts.fulfilled, (state, action) => {
        state.loading = false;
        state.importResult = action.payload;
      })
      .addCase(importProducts.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to import products';
      });
  }
});

export const { clearProductError, setCurrentPage } = productSlice.actions;
export default productSlice.reducer;
