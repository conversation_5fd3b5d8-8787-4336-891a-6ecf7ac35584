import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import api from '../../services/api';
import { ProductVariant, ProductImage } from './productSlice';

export interface CreateProductVariantDto {
  sku?: string;
  barcode?: string;
  price: number;
  cost: number;
  mrp: number;
  stockQuantity: number;
  reorderLevel?: number;
  isActive?: boolean;
  productId: number;
  // Physical attributes
  size?: string;
  weight?: number;
  weightUnit?: string;
  length?: number;
  breadth?: number;
  height?: number;
  dimensionUnit?: string;
  volume?: number;
  volumeUnit?: string;
  variantAttributes: Record<string, string>;
  images?: { url: string; isMain: boolean; displayOrder?: number }[];
  locationId?: number; // Added for inventory location
}

export interface UpdateProductVariantDto {
  sku?: string;
  barcode?: string;
  price?: number;
  cost?: number;
  mrp?: number;
  stockQuantity?: number;
  reorderLevel?: number;
  isActive?: boolean;
  // Physical attributes
  size?: string;
  weight?: number;
  weightUnit?: string;
  length?: number;
  breadth?: number;
  height?: number;
  dimensionUnit?: string;
  volume?: number;
  volumeUnit?: string;
  variantAttributes?: Record<string, string>;
  images?: { url: string; isMain: boolean; displayOrder?: number }[];
  locationId?: number; // Added for inventory location
}

interface ProductVariantsState {
  variants: ProductVariant[];
  variant: ProductVariant | null;
  loading: boolean;
  error: string | null;
}

const initialState: ProductVariantsState = {
  variants: [],
  variant: null,
  loading: false,
  error: null
};

// Fetch variants for a product
export const fetchProductVariants = createAsyncThunk<ProductVariant[], number>(
  'productVariants/fetchProductVariants',
  async (productId, { rejectWithValue }) => {
    try {
      const response = await api.get<ProductVariant[]>(`/ProductVariant?productId=${productId}`);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to fetch product variants');
    }
  }
);

// Fetch a single variant
export const fetchProductVariant = createAsyncThunk<ProductVariant, number>(
  'productVariants/fetchProductVariant',
  async (variantId, { rejectWithValue }) => {
    try {
      const response = await api.get<ProductVariant>(`/ProductVariant/${variantId}`);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to fetch product variant');
    }
  }
);

// Create a new variant
export const createProductVariant = createAsyncThunk<ProductVariant, CreateProductVariantDto>(
  'productVariants/createProductVariant',
  async (variantData, { rejectWithValue }) => {
    try {
      const response = await api.post<ProductVariant>('/ProductVariant', variantData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to create product variant');
    }
  }
);

// Update a variant
export const updateProductVariant = createAsyncThunk<void, { id: number; variantData: UpdateProductVariantDto }>(
  'productVariants/updateProductVariant',
  async ({ id, variantData }, { rejectWithValue }) => {
    try {
      await api.put(`/ProductVariant/${id}`, variantData);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to update product variant');
    }
  }
);

// Delete a variant
export const deleteProductVariant = createAsyncThunk<void, number>(
  'productVariants/deleteProductVariant',
  async (variantId, { rejectWithValue }) => {
    try {
      await api.delete(`/ProductVariant/${variantId}`);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to delete product variant');
    }
  }
);

const productVariantSlice = createSlice({
  name: 'productVariants',
  initialState,
  reducers: {
    clearVariant: (state) => {
      state.variant = null;
    },
    clearVariants: (state) => {
      state.variants = [];
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch variants
      .addCase(fetchProductVariants.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProductVariants.fulfilled, (state, action) => {
        state.loading = false;
        state.variants = action.payload;
      })
      .addCase(fetchProductVariants.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Fetch single variant
      .addCase(fetchProductVariant.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProductVariant.fulfilled, (state, action) => {
        state.loading = false;
        state.variant = action.payload;
      })
      .addCase(fetchProductVariant.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Create variant
      .addCase(createProductVariant.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createProductVariant.fulfilled, (state, action) => {
        state.loading = false;
        state.variants.push(action.payload);
      })
      .addCase(createProductVariant.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Update variant
      .addCase(updateProductVariant.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateProductVariant.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(updateProductVariant.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Delete variant
      .addCase(deleteProductVariant.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteProductVariant.fulfilled, (state, action) => {
        state.loading = false;
        state.variants = state.variants.filter(variant => variant.id !== action.meta.arg);
      })
      .addCase(deleteProductVariant.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  }
});

export const { clearVariant, clearVariants } = productVariantSlice.actions;

export default productVariantSlice.reducer;
