import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import api from '../../services/api';

export interface ThemeConfiguration {
  id: number;
  name: string;
  description: string;
  isActive: boolean;
  
  // Primary Colors
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  
  // Text Colors
  textPrimaryColor: string;
  textSecondaryColor: string;
  textLightColor: string;
  
  // Background Colors
  backgroundPrimaryColor: string;
  backgroundSecondaryColor: string;
  backgroundAccentColor: string;
  
  // Button Styles
  buttonPrimaryColor: string;
  buttonSecondaryColor: string;
  buttonTextColor: string;
  buttonBorderRadius: string;
  
  // Card Styles
  cardBackgroundColor: string;
  cardBorderColor: string;
  cardBorderRadius: string;
  cardShadow: string;
  
  // Typography
  headingFontFamily: string;
  bodyFontFamily: string;
  fontBaseSize: string;
  
  // Spacing
  spacingUnit: string;
  containerMaxWidth: string;
  containerPadding: string;
  
  // Header Styles
  headerBackgroundColor: string;
  headerTextColor: string;
  headerHeight: string;
  
  // Footer Styles
  footerBackgroundColor: string;
  footerTextColor: string;
  
  // Navigation Styles
  navLinkColor: string;
  navLinkActiveColor: string;
  navLinkHoverColor: string;
  
  // Form Styles
  inputBackgroundColor: string;
  inputBorderColor: string;
  inputBorderRadius: string;
  inputFocusBorderColor: string;
  
  // Custom CSS
  customCSS: string;
}

interface ThemeState {
  activeTheme: ThemeConfiguration | null;
  themes: ThemeConfiguration[];
  loading: boolean;
  error: string | null;
}

const initialState: ThemeState = {
  activeTheme: null,
  themes: [],
  loading: false,
  error: null
};

// Fetch active theme
export const fetchActiveTheme = createAsyncThunk(
  'theme/fetchActiveTheme',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/ThemeConfiguration/active');
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || 'Failed to fetch active theme');
    }
  }
);

// Fetch all themes
export const fetchAllThemes = createAsyncThunk(
  'theme/fetchAllThemes',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/ThemeConfiguration');
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || 'Failed to fetch themes');
    }
  }
);

// Create a new theme
export const createTheme = createAsyncThunk(
  'theme/createTheme',
  async (themeData: Partial<ThemeConfiguration>, { rejectWithValue }) => {
    try {
      const response = await api.post('/ThemeConfiguration', themeData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || 'Failed to create theme');
    }
  }
);

// Update a theme
export const updateTheme = createAsyncThunk(
  'theme/updateTheme',
  async ({ id, themeData }: { id: number, themeData: Partial<ThemeConfiguration> }, { rejectWithValue }) => {
    try {
      await api.put(`/ThemeConfiguration/${id}`, themeData);
      return { id, ...themeData };
    } catch (error: any) {
      return rejectWithValue(error.response?.data || 'Failed to update theme');
    }
  }
);

// Activate a theme
export const activateTheme = createAsyncThunk(
  'theme/activateTheme',
  async (id: number, { rejectWithValue }) => {
    try {
      await api.put(`/ThemeConfiguration/${id}/activate`);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || 'Failed to activate theme');
    }
  }
);

// Delete a theme
export const deleteTheme = createAsyncThunk(
  'theme/deleteTheme',
  async (id: number, { rejectWithValue }) => {
    try {
      await api.delete(`/ThemeConfiguration/${id}`);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || 'Failed to delete theme');
    }
  }
);

const themeSlice = createSlice({
  name: 'theme',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    // Fetch active theme
    builder.addCase(fetchActiveTheme.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(fetchActiveTheme.fulfilled, (state, action: PayloadAction<ThemeConfiguration>) => {
      state.loading = false;
      state.activeTheme = action.payload;
    });
    builder.addCase(fetchActiveTheme.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });

    // Fetch all themes
    builder.addCase(fetchAllThemes.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(fetchAllThemes.fulfilled, (state, action: PayloadAction<ThemeConfiguration[]>) => {
      state.loading = false;
      state.themes = action.payload;
    });
    builder.addCase(fetchAllThemes.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });

    // Create a theme
    builder.addCase(createTheme.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(createTheme.fulfilled, (state, action: PayloadAction<ThemeConfiguration>) => {
      state.loading = false;
      state.themes.push(action.payload);
      if (action.payload.isActive) {
        state.activeTheme = action.payload;
        // Update other themes to be inactive
        state.themes = state.themes.map(theme => 
          theme.id !== action.payload.id ? { ...theme, isActive: false } : theme
        );
      }
    });
    builder.addCase(createTheme.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });

    // Update a theme
    builder.addCase(updateTheme.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(updateTheme.fulfilled, (state, action: PayloadAction<Partial<ThemeConfiguration> & { id: number }>) => {
      state.loading = false;
      const { id } = action.payload;
      
      // Update the theme in the themes array
      state.themes = state.themes.map(theme => 
        theme.id === id ? { ...theme, ...action.payload } : theme
      );
      
      // If the updated theme is active, update the activeTheme
      if (state.activeTheme && state.activeTheme.id === id) {
        state.activeTheme = { ...state.activeTheme, ...action.payload };
      }
      
      // If the theme is being activated, update other themes to be inactive
      if (action.payload.isActive) {
        state.themes = state.themes.map(theme => 
          theme.id !== id ? { ...theme, isActive: false } : theme
        );
        
        // Set the updated theme as active
        const updatedTheme = state.themes.find(theme => theme.id === id);
        if (updatedTheme) {
          state.activeTheme = updatedTheme;
        }
      }
    });
    builder.addCase(updateTheme.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });

    // Activate a theme
    builder.addCase(activateTheme.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(activateTheme.fulfilled, (state, action: PayloadAction<number>) => {
      state.loading = false;
      const themeId = action.payload;
      
      // Update all themes to be inactive except the activated one
      state.themes = state.themes.map(theme => ({
        ...theme,
        isActive: theme.id === themeId
      }));
      
      // Set the activated theme as active
      const activatedTheme = state.themes.find(theme => theme.id === themeId);
      if (activatedTheme) {
        state.activeTheme = activatedTheme;
      }
    });
    builder.addCase(activateTheme.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });

    // Delete a theme
    builder.addCase(deleteTheme.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(deleteTheme.fulfilled, (state, action: PayloadAction<number>) => {
      state.loading = false;
      const themeId = action.payload;
      
      // Remove the deleted theme from the themes array
      state.themes = state.themes.filter(theme => theme.id !== themeId);
      
      // If the deleted theme was active, set activeTheme to null
      if (state.activeTheme && state.activeTheme.id === themeId) {
        state.activeTheme = null;
      }
    });
    builder.addCase(deleteTheme.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });
  }
});

export default themeSlice.reducer;
