import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import api from '../../services/api';

export interface NavigationMenu {
  id: number;
  name: string;
  url: string;
  parentId?: number;
  displayOrder: number;
  isActive: boolean;
  icon?: string;
  children: NavigationMenu[];
}

interface NavigationState {
  menus: NavigationMenu[];
  loading: boolean;
  error: string | null;
}

interface FetchNavigationMenusParams {
  isActive?: boolean;
  isAdmin?: boolean;
}

export const fetchNavigationMenus = createAsyncThunk<NavigationMenu[], FetchNavigationMenusParams | undefined, { rejectValue: string }>(
  'navigation/fetchNavigationMenus',
  async (params = { isActive: true }, { rejectWithValue }) => {
    try {
      const { isActive = true, isAdmin = false } = params;
      const url = isAdmin
        ? `/NavigationMenus/all`
        : `/NavigationMenus?isActive=${isActive}`;

      const response = await api.get<NavigationMenu[]>(url);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to fetch navigation menus');
    }
  }
);

export const createNavigationMenu = createAsyncThunk<NavigationMenu, any, { rejectValue: string }>(
  'navigation/createNavigationMenu',
  async (menuData, { rejectWithValue }) => {
    try {
      const response = await api.post<NavigationMenu>(`/NavigationMenus`, menuData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to create navigation menu');
    }
  }
);

export const updateNavigationMenu = createAsyncThunk<NavigationMenu, { id: number; menu: any }, { rejectValue: string }>(
  'navigation/updateNavigationMenu',
  async ({ id, menu }, { rejectWithValue }) => {
    try {
      const response = await api.put<NavigationMenu>(`/NavigationMenus/${id}`, menu);
      return { ...menu, id }; // API returns 204 No Content, so we construct the response
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to update navigation menu');
    }
  }
);

export const deleteNavigationMenu = createAsyncThunk<number, number, { rejectValue: string }>(
  'navigation/deleteNavigationMenu',
  async (id, { rejectWithValue }) => {
    try {
      await api.delete(`/NavigationMenus/${id}`);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to delete navigation menu');
    }
  }
);

const initialState: NavigationState = {
  menus: [],
  loading: false,
  error: null
};

const navigationSlice = createSlice({
  name: 'navigation',
  initialState,
  reducers: {
    clearNavigationError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch navigation menus
      .addCase(fetchNavigationMenus.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchNavigationMenus.fulfilled, (state, action: PayloadAction<NavigationMenu[]>) => {
        state.loading = false;
        state.menus = action.payload;
      })
      .addCase(fetchNavigationMenus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to fetch navigation menus';
      })

      // Create navigation menu
      .addCase(createNavigationMenu.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createNavigationMenu.fulfilled, (state, action: PayloadAction<NavigationMenu>) => {
        state.loading = false;
        state.menus.push(action.payload);
      })
      .addCase(createNavigationMenu.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to create navigation menu';
      })

      // Update navigation menu
      .addCase(updateNavigationMenu.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateNavigationMenu.fulfilled, (state, action: PayloadAction<NavigationMenu>) => {
        state.loading = false;
        const index = state.menus.findIndex(menu => menu.id === action.payload.id);
        if (index !== -1) {
          state.menus[index] = action.payload;
        }
      })
      .addCase(updateNavigationMenu.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to update navigation menu';
      })

      // Delete navigation menu
      .addCase(deleteNavigationMenu.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteNavigationMenu.fulfilled, (state, action: PayloadAction<number>) => {
        state.loading = false;
        state.menus = state.menus.filter(menu => menu.id !== action.payload);
      })
      .addCase(deleteNavigationMenu.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to delete navigation menu';
      });
  }
});

export const { clearNavigationError } = navigationSlice.actions;
export default navigationSlice.reducer;
