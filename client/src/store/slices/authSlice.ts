import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import axios from 'axios';
import config from '../../config';
import api from '../../services/api';

// Use the API URL from the configuration file
const API_URL = config.apiUrl;

interface User {
  userId: string;
  email: string;
  firstName: string;
  lastName: string;
  roles: string[];
  token: string;
  expiration: string;
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
  otpRequested: boolean;
}

interface LoginCredentials {
  email: string;
  password: string;
}

interface RegisterCredentials {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
  phoneNumber?: string;
}

interface RequestOtpCredentials {
  email?: string;
  phoneNumber?: string;
}

interface VerifyOtpCredentials {
  email?: string;
  phoneNumber?: string;
  otp: string;
}

interface CustomerAuthResponse {
  success: boolean;
  token?: string;
  userId?: string;
  email?: string;
  phoneNumber?: string;
  firstName?: string;
  lastName?: string;
  isNewUser?: boolean;
  expiration?: string;
  errorMessage?: string;
}

interface AuthResponse {
  success: boolean;
  token: string;
  userId: string;
  email: string;
  firstName: string;
  lastName: string;
  roles: string[];
  expiration: string;
  errorMessage?: string;
}

// Load user from localStorage
const loadUser = (): User | null => {
  const userJson = localStorage.getItem('user');
  if (userJson) {
    const user = JSON.parse(userJson) as User;
    // Check if token is expired
    if (new Date(user.expiration) > new Date()) {
      return user;
    } else {
      localStorage.removeItem('user');
    }
  }
  return null;
};

export const login = createAsyncThunk<User, LoginCredentials, { rejectValue: string }>(
  'auth/login',
  async (credentials, { rejectWithValue }) => {
    try {
      const response = await axios.post<AuthResponse>(`${API_URL}/auth/login`, credentials);

      if (!response.data.success) {
        return rejectWithValue(response.data.errorMessage || 'Login failed');
      }

      const user: User = {
        userId: response.data.userId,
        email: response.data.email,
        firstName: response.data.firstName,
        lastName: response.data.lastName,
        roles: response.data.roles,
        token: response.data.token,
        expiration: response.data.expiration
      };

      localStorage.setItem('user', JSON.stringify(user));
      return user;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.errorMessage || error.message || 'Login failed');
    }
  }
);

export const register = createAsyncThunk<User, RegisterCredentials, { rejectValue: string }>(
  'auth/register',
  async (credentials, { rejectWithValue }) => {
    try {
      const response = await axios.post<AuthResponse>(`${API_URL}/auth/register`, credentials);

      if (!response.data.success) {
        return rejectWithValue(response.data.errorMessage || 'Registration failed');
      }

      const user: User = {
        userId: response.data.userId,
        email: response.data.email,
        firstName: response.data.firstName,
        lastName: response.data.lastName,
        roles: response.data.roles,
        token: response.data.token,
        expiration: response.data.expiration
      };

      localStorage.setItem('user', JSON.stringify(user));
      return user;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.errorMessage || error.message || 'Registration failed');
    }
  }
);

export const requestOtp = createAsyncThunk<void, RequestOtpCredentials, { rejectValue: string }>(
  'auth/requestOtp',
  async (credentials, { rejectWithValue }) => {
    try {
      console.log('Requesting OTP with credentials:', credentials);
      const response = await api.post<CustomerAuthResponse>(`/CustomerAuth/request-otp`, credentials);
      console.log('OTP request response:', response.data);

      if (!response.data.success) {
        return rejectWithValue(response.data.errorMessage || 'Failed to send OTP');
      }

      return;
    } catch (error: any) {
      console.error('Error requesting OTP:', error);
      return rejectWithValue(error.response?.data?.errorMessage || error.message || 'Failed to send OTP');
    }
  }
);

export const verifyOtp = createAsyncThunk<User, VerifyOtpCredentials, { rejectValue: string }>(
  'auth/verifyOtp',
  async (credentials, { rejectWithValue }) => {
    try {
      console.log('Verifying OTP with credentials:', credentials);
      const response = await api.post<CustomerAuthResponse>(`/CustomerAuth/verify-otp`, credentials);
      console.log('OTP verification response:', response.data);

      if (!response.data.success) {
        return rejectWithValue(response.data.errorMessage || 'OTP verification failed');
      }

      const user: User = {
        userId: response.data.userId!,
        email: response.data.email || '',
        firstName: response.data.firstName || '',
        lastName: response.data.lastName || '',
        roles: ['Customer'],
        token: response.data.token!,
        expiration: response.data.expiration!.toString()
      };

      localStorage.setItem('user', JSON.stringify(user));
      return user;
    } catch (error: any) {
      console.error('Error verifying OTP:', error);
      return rejectWithValue(error.response?.data?.errorMessage || error.message || 'OTP verification failed');
    }
  }
);

const initialState: AuthState = {
  user: loadUser(),
  isAuthenticated: !!loadUser(),
  loading: false,
  error: null,
  otpRequested: false
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    logout: (state) => {
      localStorage.removeItem('user');
      state.user = null;
      state.isAuthenticated = false;
      state.error = null;
    },
    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(login.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(login.fulfilled, (state, action: PayloadAction<User>) => {
        state.loading = false;
        state.user = action.payload;
        state.isAuthenticated = true;
      })
      .addCase(login.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Login failed';
      })
      .addCase(register.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(register.fulfilled, (state, action: PayloadAction<User>) => {
        state.loading = false;
        state.user = action.payload;
        state.isAuthenticated = true;
      })
      .addCase(register.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Registration failed';
      })
      .addCase(requestOtp.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.otpRequested = false;
      })
      .addCase(requestOtp.fulfilled, (state) => {
        state.loading = false;
        state.otpRequested = true;
      })
      .addCase(requestOtp.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to send OTP';
        state.otpRequested = false;
      })
      .addCase(verifyOtp.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(verifyOtp.fulfilled, (state, action: PayloadAction<User>) => {
        state.loading = false;
        state.user = action.payload;
        state.isAuthenticated = true;
        state.otpRequested = false;
      })
      .addCase(verifyOtp.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'OTP verification failed';
      });
  }
});

export const { logout, clearError } = authSlice.actions;
export default authSlice.reducer;
