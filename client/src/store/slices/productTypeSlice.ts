import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { ProductType, getProductTypes, createProductType, updateProductType, deleteProductType } from '../../services/productTypeService';

interface ProductTypeState {
  productTypes: ProductType[];
  loading: boolean;
  error: string | null;
}

const initialState: ProductTypeState = {
  productTypes: [],
  loading: false,
  error: null
};

export const fetchProductTypes = createAsyncThunk(
  'productTypes/fetchProductTypes',
  async (_, { rejectWithValue }) => {
    try {
      return await getProductTypes();
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch product types');
    }
  }
);

export const addProductType = createAsyncThunk(
  'productTypes/addProductType',
  async (productType: Omit<ProductType, 'id'>, { rejectWithValue }) => {
    try {
      return await createProductType(productType);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to add product type');
    }
  }
);

export const editProductType = createAsyncThunk(
  'productTypes/editProductType',
  async (productType: ProductType, { rejectWithValue }) => {
    try {
      await updateProductType(productType);
      return productType;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update product type');
    }
  }
);

export const removeProductType = createAsyncThunk(
  'productTypes/removeProductType',
  async (id: number, { rejectWithValue }) => {
    try {
      await deleteProductType(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete product type');
    }
  }
);

const productTypeSlice = createSlice({
  name: 'productTypes',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Fetch product types
      .addCase(fetchProductTypes.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProductTypes.fulfilled, (state, action: PayloadAction<ProductType[]>) => {
        state.loading = false;
        state.productTypes = action.payload;
      })
      .addCase(fetchProductTypes.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Add product type
      .addCase(addProductType.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(addProductType.fulfilled, (state, action: PayloadAction<ProductType>) => {
        state.loading = false;
        state.productTypes.push(action.payload);
      })
      .addCase(addProductType.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Edit product type
      .addCase(editProductType.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(editProductType.fulfilled, (state, action: PayloadAction<ProductType>) => {
        state.loading = false;
        const index = state.productTypes.findIndex(pt => pt.id === action.payload.id);
        if (index !== -1) {
          state.productTypes[index] = action.payload;
        }
      })
      .addCase(editProductType.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Remove product type
      .addCase(removeProductType.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(removeProductType.fulfilled, (state, action: PayloadAction<number>) => {
        state.loading = false;
        state.productTypes = state.productTypes.filter(pt => pt.id !== action.payload);
      })
      .addCase(removeProductType.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  }
});

export default productTypeSlice.reducer;
