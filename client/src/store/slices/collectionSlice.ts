import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import api from '../../services/api';

export interface Collection {
  id: number;
  name: string;
  description: string;
  imageUrl: string;
  isActive: boolean;
  displayOrder: number;
  productCount: number;
}

interface CollectionState {
  collections: Collection[];
  collection: Collection | null;
  loading: boolean;
  error: string | null;
}

export const fetchCollections = createAsyncThunk<Collection[], boolean | undefined, { rejectValue: string }>(
  'collections/fetchCollections',
  async (isActive = true, { rejectWithValue }) => {
    try {
      const response = await api.get<Collection[]>(`/collections?isActive=${isActive}`);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to fetch collections');
    }
  }
);

export const fetchCollectionById = createAsyncThunk<Collection, number, { rejectValue: string }>(
  'collections/fetchCollectionById',
  async (id, { rejectWithValue }) => {
    try {
      const response = await api.get<Collection>(`/collections/${id}`);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to fetch collection');
    }
  }
);

export const createCollection = createAsyncThunk<Collection, any, { rejectValue: string }>(
  'collections/createCollection',
  async (collectionData, { rejectWithValue }) => {
    try {
      const response = await api.post<Collection>(`/collections`, collectionData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to create collection');
    }
  }
);

export const updateCollection = createAsyncThunk<Collection, { id: number; collection: any }, { rejectValue: string }>(
  'collections/updateCollection',
  async ({ id, collection }, { rejectWithValue }) => {
    try {
      await api.put<Collection>(`/collections/${id}`, collection);
      return { ...collection, id }; // API returns 204 No Content, so we construct the response
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to update collection');
    }
  }
);

export const deleteCollection = createAsyncThunk<number, number, { rejectValue: string }>(
  'collections/deleteCollection',
  async (id, { rejectWithValue }) => {
    try {
      await api.delete(`/collections/${id}`);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to delete collection');
    }
  }
);

const initialState: CollectionState = {
  collections: [],
  collection: null,
  loading: false,
  error: null
};

const collectionSlice = createSlice({
  name: 'collections',
  initialState,
  reducers: {
    clearCollectionError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch collections
      .addCase(fetchCollections.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCollections.fulfilled, (state, action: PayloadAction<Collection[]>) => {
        state.loading = false;
        state.collections = action.payload;
      })
      .addCase(fetchCollections.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to fetch collections';
      })

      // Fetch collection by ID
      .addCase(fetchCollectionById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCollectionById.fulfilled, (state, action: PayloadAction<Collection>) => {
        state.loading = false;
        state.collection = action.payload;
      })
      .addCase(fetchCollectionById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to fetch collection';
      })

      // Create collection
      .addCase(createCollection.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createCollection.fulfilled, (state, action: PayloadAction<Collection>) => {
        state.loading = false;
        state.collections.push(action.payload);
      })
      .addCase(createCollection.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to create collection';
      })

      // Update collection
      .addCase(updateCollection.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateCollection.fulfilled, (state, action: PayloadAction<Collection>) => {
        state.loading = false;
        const index = state.collections.findIndex(collection => collection.id === action.payload.id);
        if (index !== -1) {
          state.collections[index] = action.payload;
        }
      })
      .addCase(updateCollection.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to update collection';
      })

      // Delete collection
      .addCase(deleteCollection.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteCollection.fulfilled, (state, action: PayloadAction<number>) => {
        state.loading = false;
        state.collections = state.collections.filter(collection => collection.id !== action.payload);
      })
      .addCase(deleteCollection.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to delete collection';
      });
  }
});

export const { clearCollectionError } = collectionSlice.actions;
export default collectionSlice.reducer;
