import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface WishlistItem {
  productId: number;
  name: string;
  price: number;
  imageUrl: string;
}

interface WishlistState {
  items: WishlistItem[];
}

// Get initial wishlist from localStorage
const loadWishlist = (): WishlistItem[] => {
  try {
    const wishlistItems = localStorage.getItem('wishlist');
    return wishlistItems ? JSON.parse(wishlistItems) : [];
  } catch (error) {
    console.error('Failed to load wishlist from localStorage:', error);
    return [];
  }
};

const initialState: WishlistState = {
  items: loadWishlist(),
};

const wishlistSlice = createSlice({
  name: 'wishlist',
  initialState,
  reducers: {
    addToWishlist: (state, action: PayloadAction<WishlistItem>) => {
      // Check if item already exists in wishlist
      const existingItem = state.items.find(item => item.productId === action.payload.productId);
      
      if (!existingItem) {
        state.items.push(action.payload);
        // Save to localStorage
        localStorage.setItem('wishlist', JSON.stringify(state.items));
      }
    },
    removeFromWishlist: (state, action: PayloadAction<number>) => {
      state.items = state.items.filter(item => item.productId !== action.payload);
      // Save to localStorage
      localStorage.setItem('wishlist', JSON.stringify(state.items));
    },
    clearWishlist: (state) => {
      state.items = [];
      // Save to localStorage
      localStorage.setItem('wishlist', JSON.stringify(state.items));
    },
  },
});

export const { addToWishlist, removeFromWishlist, clearWishlist } = wishlistSlice.actions;

export default wishlistSlice.reducer;
