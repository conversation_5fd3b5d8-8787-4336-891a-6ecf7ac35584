import { configureStore } from '@reduxjs/toolkit';
import cartReducer from './slices/cartSlice';
import authReducer from './slices/authSlice';
import navigationReducer from './slices/navigationSlice';
import categoryReducer from './slices/categorySlice';
import collectionReducer from './slices/collectionSlice';
import wishlistReducer from './slices/wishlistSlice';
import productReducer from './slices/productSlice';
import orderReducer from './slices/orderSlice';

export const store = configureStore({
  reducer: {
    cart: cartReducer,
    auth: authReducer,
    navigation: navigationReducer,
    categories: categoryReducer,
    collections: collectionReducer,
    wishlist: wishlistReducer,
    products: productReducer,
    orders: orderReducer
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;