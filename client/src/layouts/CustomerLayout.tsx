import React, { useEffect, useState } from 'react';
import { Outlet, Link, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { AppBar, Toolbar, Typography, Button, Badge, Container, Box, IconButton, Menu, MenuItem, Drawer, List, ListItem, ListItemText, ListItemIcon, Divider, Collapse, Chip, Grid } from '@mui/material';
import { Helmet } from 'react-helmet';
import { ShoppingCart, Person, Menu as MenuIcon, Home, Category, Collections, Search, ExitToApp, Login, ExpandLess, ExpandMore, LocalOffer, Favorite } from '@mui/icons-material';
import { getValidImageUrl, createImageErrorHandler } from '../utils/imageUtils';
import { RootState } from '../store';
import { logout } from '../store/slices/authSlice';
import { fetchNavigationMenus } from '../store/slices/navigationSlice';
import { fetchCategories } from '../store/slices/categorySlice';
import { fetchCollections } from '../store/slices/collectionSlice';
import { fetchWebsiteConfiguration } from '../store/slices/websiteConfigSlice';
import '../styles/customerLayout.css';

// Import components
import CategoryHoverMenu from '../components/navigation/CategoryHoverMenu';
import CollectionHoverMenu from '../components/navigation/CollectionHoverMenu';
import NavigationHoverMenu from '../components/navigation/NavigationHoverMenu';
import Announcement from '../components/common/Announcement';

const CustomerLayout: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const auth = useSelector((state: RootState) => state.auth);
  const isAuthenticated = auth?.isAuthenticated || false;
  const user = auth?.user || null;
  const cart = useSelector((state: RootState) => state.cart);
  const totalItems = cart?.totalItems || 0;

  const navigation = useSelector((state: RootState) => state.navigation);
  const menus = navigation?.menus || [];

  const wishlist = useSelector((state: RootState) => state.wishlist);
  const wishlistItems = wishlist?.items || [];

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [mobileOpen, setMobileOpen] = useState(false);
  const [openCategories, setOpenCategories] = useState<{[key: number]: boolean}>({});

  // State for hover menus
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [activeMenu, setActiveMenu] = useState<string | null>(null);

  // Function to toggle nested category menu
  const handleCategoryClick = (categoryId: number) => {
    setOpenCategories(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };

  // Get website configuration
  const { configuration } = useSelector((state: RootState) => state.websiteConfig);

  // Debug configuration
  useEffect(() => {
    console.log('Website configuration:', configuration);

    // Direct API call to check the response
    fetch('/api/WebsiteConfiguration')
      .then(response => response.json())
      .then(data => {
        console.log('Direct API call response:', data);
      })
      .catch(error => {
        console.error('Error fetching website configuration:', error);
      });
  }, [configuration]);

  useEffect(() => {
    // @ts-ignore
    dispatch(fetchNavigationMenus());
    // @ts-ignore
    dispatch(fetchCategories());
    // @ts-ignore
    dispatch(fetchCollections());
    // @ts-ignore
    dispatch(fetchWebsiteConfiguration());
  }, [dispatch]);

  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleProfileMenuClose = () => {
    setAnchorEl(null);
  };

  // Hover menu handlers
  const handleMenuEnter = (event: React.MouseEvent<HTMLElement>, menuType: string) => {
    setMenuAnchorEl(event.currentTarget);
    setActiveMenu(menuType);
  };

  const handleMenuLeave = () => {
    // Use a timeout to prevent the menu from closing immediately when moving from button to menu
    setTimeout(() => {
      if (!document.querySelector(':hover > .MuiPopper-root')) {
        setMenuAnchorEl(null);
        setActiveMenu(null);
      }
    }, 100);
  };

  // Handle mouse leave for the entire menu (button + dropdown)
  const handleMenuMouseLeave = () => {
    setMenuAnchorEl(null);
    setActiveMenu(null);
  };

  const handleHoverMenuClose = () => {
    setMenuAnchorEl(null);
    setActiveMenu(null);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleLogout = () => {
    // @ts-ignore
    dispatch(logout());
    navigate('/');
    handleMenuClose();
  };

  const drawer = (
    <div>
      <Box sx={{ p: 2, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        {configuration?.logoUrl && configuration.logoUrl.trim() !== '' ? (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              width: '100%',
              p: 1.5,
              borderRadius: 1,
              bgcolor: 'rgba(0, 0, 0, 0.03)',
              boxShadow: '0 1px 3px rgba(0,0,0,0.05)',
              border: '1px solid rgba(0,0,0,0.08)'
            }}
          >
            <Box
              component="img"
              src={getValidImageUrl(configuration.logoUrl)}
              alt={configuration?.websiteTitle || 'MyShop'}
              sx={{
                height: '60px',
                maxWidth: '90%',
                objectFit: 'contain',
                filter: 'drop-shadow(0px 1px 2px rgba(0,0,0,0.1))'
              }}
              onError={createImageErrorHandler()}
            />
          </Box>
        ) : (
          <Typography variant="h6" component="div">
            {configuration?.websiteTitle || 'MyShop'}
          </Typography>
        )}
      </Box>
      <Divider />
      <List>
        <ListItem component={Link} to="/" onClick={handleDrawerToggle}>
          <ListItemIcon>
            <Home />
          </ListItemIcon>
          <ListItemText primary="Home" />
        </ListItem>
        {/* Dynamic Navigation Menus */}
        {menus.map((menu) => {
          // Skip child menus, they'll be rendered under their parents
          if (menu.parentId) return null;

          // Check if this menu has children
          const hasChildren = menu.children && menu.children.length > 0;

          // Determine the icon to use
          let MenuIcon = Home; // Default icon
          if (menu.icon === 'category') MenuIcon = Category;
          else if (menu.icon === 'collections') MenuIcon = Collections;
          else if (menu.icon === 'offer') MenuIcon = LocalOffer;

          return (
            <React.Fragment key={menu.id}>
              <ListItem
                component={hasChildren ? 'div' : Link}
                to={hasChildren ? undefined : menu.url}
                onClick={hasChildren ?
                  () => handleCategoryClick(menu.id) :
                  handleDrawerToggle}
              >
                <ListItemIcon>
                  <MenuIcon />
                </ListItemIcon>
                <ListItemText primary={menu.name} />
                {menu.icon === 'offer' && (
                  <Chip
                    size="small"
                    label="SALE"
                    color="error"
                    sx={{ ml: 1 }}
                  />
                )}
                {hasChildren && (
                  openCategories[menu.id] ? <ExpandLess /> : <ExpandMore />
                )}
              </ListItem>

              {/* Render children if any */}
              {hasChildren && (
                <Collapse in={openCategories[menu.id]} timeout="auto" unmountOnExit>
                  <List component="div" disablePadding>
                    {menu.children.map((childMenu) => (
                      <ListItem
                        key={childMenu.id}
                        sx={{ pl: 4 }}
                        component={Link}
                        to={childMenu.url}
                        onClick={handleDrawerToggle}
                      >
                        {childMenu.icon === 'offer' && (
                          <ListItemIcon>
                            <LocalOffer />
                          </ListItemIcon>
                        )}
                        <ListItemText primary={childMenu.name} />
                        {childMenu.icon === 'offer' && (
                          <Chip
                            size="small"
                            label="SALE"
                            color="error"
                            sx={{ ml: 1 }}
                          />
                        )}
                      </ListItem>
                    ))}
                  </List>
                </Collapse>
              )}
            </React.Fragment>
          );
        })}
      </List>
      <Divider />
      <List>
        {!isAuthenticated ? (
          <>
            <ListItem component={Link} to="/otp-login" onClick={handleDrawerToggle}>
              <ListItemIcon>
                <Login />
              </ListItemIcon>
              <ListItemText primary="Login" />
            </ListItem>
            <ListItem component={Link} to="/register" onClick={handleDrawerToggle}>
              <ListItemIcon>
                <Person />
              </ListItemIcon>
              <ListItemText primary="Register" />
            </ListItem>
          </>
        ) : (
          <>
            <ListItem component={Link} to="/profile" onClick={handleDrawerToggle}>
              <ListItemIcon>
                <Person />
              </ListItemIcon>
              <ListItemText primary="Profile" />
            </ListItem>
            <ListItem component={Link} to="/wishlist" onClick={handleDrawerToggle}>
              <ListItemIcon>
                <Favorite />
              </ListItemIcon>
              <ListItemText primary="My Wishlist" />
              {wishlistItems.length > 0 && (
                <Chip size="small" color="error" label={wishlistItems.length} />
              )}
            </ListItem>
            <ListItem component={Link} to="/orders" onClick={handleDrawerToggle}>
              <ListItemIcon>
                <Person />
              </ListItemIcon>
              <ListItemText primary="My Orders" />
            </ListItem>
            <ListItem onClick={() => { handleLogout(); handleDrawerToggle(); }}>
              <ListItemIcon>
                <ExitToApp />
              </ListItemIcon>
              <ListItemText primary="Logout" />
            </ListItem>
          </>
        )}
      </List>
    </div>
  );

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh', width: '100%' }}>
      {/* SEO Meta Tags */}
      <Helmet>
        <title>{configuration?.websiteTitle || 'Jaipur Cotton House'}</title>
        <meta name="description" content={configuration?.metaDescription || 'Discover our exclusive range of traditional ethnic wear'} />
        <meta name="keywords" content={configuration?.metaKeywords || 'ethnic wear, traditional clothing, cotton'} />
      </Helmet>

      {/* Announcement Bar */}
      <Announcement
        text={configuration?.announcementText || 'Free shipping on all orders above ₹999'}
        show={configuration?.showAnnouncement || false}
      />

      <AppBar position="sticky">
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { sm: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          <Box component={Link} to="/" sx={{ flexGrow: 1, textDecoration: 'none', color: 'white', display: 'flex', alignItems: 'center' }}>
            {configuration?.logoUrl && configuration.logoUrl.trim() !== '' ? (
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mr: 2,
                  px: 1.5,
                  py: 0.5,
                  borderRadius: 1,
                  bgcolor: 'rgba(255, 255, 255, 0.15)',
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    bgcolor: 'rgba(255, 255, 255, 0.2)',
                  }
                }}
              >
                <Box
                  component="img"
                  src={getValidImageUrl(configuration.logoUrl)}
                  alt={configuration?.websiteTitle || 'MyShop'}
                  sx={{
                    height: '40px',
                    maxWidth: '180px',
                    objectFit: 'contain',
                    filter: 'drop-shadow(0px 2px 3px rgba(0,0,0,0.2))'
                  }}
                  onError={createImageErrorHandler()}
                />
              </Box>
            ) : (
              <Typography variant="h6" sx={{ flexGrow: 1 }}>
                {configuration?.websiteTitle || 'MyShop'}
              </Typography>
            )}
          </Box>
          <Box sx={{ display: { xs: 'none', sm: 'flex' } }}>
            {/* Dynamic Navigation Menus */}
            {menus.filter(menu => !menu.parentId).map(menu => (
              <Button
                key={menu.id}
                color="inherit"
                aria-haspopup="true"
                onMouseEnter={(e) => handleMenuEnter(e, menu.name.toLowerCase())}
                onMouseLeave={handleMenuLeave}
                component={Link}
                to={menu.url}
                sx={{ display: 'flex', alignItems: 'center' }}
              >
                {menu.name}
                {menu.icon === 'offer' && (
                  <Chip
                    size="small"
                    label="SALE"
                    color="error"
                    sx={{ ml: 1, height: 20 }}
                  />
                )}
              </Button>
            ))}
          </Box>
          <Box sx={{ display: 'flex' }}>
            <IconButton color="inherit" component={Link} to="/search">
              <Search />
            </IconButton>
            {isAuthenticated && (
              <IconButton color="inherit" component={Link} to="/wishlist">
                <Badge badgeContent={wishlistItems.length} color="error">
                  <Favorite />
                </Badge>
              </IconButton>
            )}
            <IconButton color="inherit" component={Link} to="/cart">
              <Badge badgeContent={totalItems} color="error">
                <ShoppingCart />
              </Badge>
            </IconButton>
            {isAuthenticated ? (
              <>
                <IconButton
                  edge="end"
                  aria-label="account of current user"
                  aria-controls="menu-appbar"
                  aria-haspopup="true"
                  onClick={handleProfileMenuOpen}
                  color="inherit"
                >
                  <Person />
                </IconButton>
                <Menu
                  id="menu-appbar"
                  anchorEl={anchorEl}
                  anchorOrigin={{
                    vertical: 'top',
                    horizontal: 'right',
                  }}
                  keepMounted
                  transformOrigin={{
                    vertical: 'top',
                    horizontal: 'right',
                  }}
                  open={Boolean(anchorEl)}
                  onClose={handleProfileMenuClose}
                >
                  <MenuItem onClick={handleProfileMenuClose} component={Link} to="/profile">Profile</MenuItem>
                  <MenuItem onClick={handleProfileMenuClose} component={Link} to="/orders">My Orders</MenuItem>
                  {user?.roles.includes('Admin') && (
                    <MenuItem onClick={handleProfileMenuClose} component={Link} to="/admin">Admin Panel</MenuItem>
                  )}
                  <MenuItem onClick={handleLogout}>Logout</MenuItem>
                </Menu>
              </>
            ) : (
              <Button color="inherit" component={Link} to="/otp-login">
                Login
              </Button>
            )}
          </Box>
        </Toolbar>
      </AppBar>
      <Drawer
        variant="temporary"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true, // Better open performance on mobile.
        }}
        sx={{
          display: { xs: 'block', sm: 'none' },
          '& .MuiDrawer-paper': { boxSizing: 'border-box', width: 240 },
        }}
      >
        {drawer}
      </Drawer>
      <Box
        sx={{
          mt: 4,
          mb: 4,
          flexGrow: 1,
          overflow: 'auto',
          width: '100%',
        }}
      >
        <Outlet />
      </Box>

      {/* Hover Menus */}
      {menus.filter(menu => !menu.parentId).map(menu => {
        // Use specialized hover menus for Categories and Collections
        if (menu.name.toLowerCase() === 'categories') {
          return (
            <Box key={menu.id} onMouseLeave={handleMenuMouseLeave}>
              <CategoryHoverMenu
                anchorEl={activeMenu === menu.name.toLowerCase() ? menuAnchorEl : null}
                open={activeMenu === menu.name.toLowerCase()}
                onClose={handleHoverMenuClose}
              />
            </Box>
          );
        } else if (menu.name.toLowerCase() === 'collections') {
          return (
            <Box key={menu.id} onMouseLeave={handleMenuMouseLeave}>
              <CollectionHoverMenu
                anchorEl={activeMenu === menu.name.toLowerCase() ? menuAnchorEl : null}
                open={activeMenu === menu.name.toLowerCase()}
                onClose={handleHoverMenuClose}
              />
            </Box>
          );
        } else {
          // Use generic hover menu for other navigation items
          return (
            <Box key={menu.id} onMouseLeave={handleMenuMouseLeave}>
              <NavigationHoverMenu
                anchorEl={activeMenu === menu.name.toLowerCase() ? menuAnchorEl : null}
                open={activeMenu === menu.name.toLowerCase()}
                onClose={handleHoverMenuClose}
                menuName={menu.name}
              />
            </Box>
          );
        }
      })}
      <Box component="footer" sx={{ bgcolor: 'background.paper', pt: 6, pb: 4, width: '100%', borderTop: '1px solid rgba(0,0,0,0.08)' }}>
        <Container className="content-container">
          <Grid container spacing={4}>
            {/* Logo and Store Info */}
            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: { xs: 'center', md: 'flex-start' }, mb: 3 }}>
                {configuration?.logoUrl && configuration.logoUrl.trim() !== '' ? (
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'center',
                      p: 2,
                      borderRadius: 2,
                      bgcolor: 'rgba(0, 0, 0, 0.02)',
                      boxShadow: '0 1px 5px rgba(0,0,0,0.03)',
                      border: '1px solid rgba(0,0,0,0.05)',
                      transition: 'all 0.3s ease',
                      mb: 2,
                      '&:hover': {
                        boxShadow: '0 3px 10px rgba(0,0,0,0.08)',
                        transform: 'translateY(-2px)'
                      }
                    }}
                  >
                    <Box
                      component="img"
                      src={getValidImageUrl(configuration.logoUrl)}
                      alt={configuration?.websiteTitle || 'Jaipur Cotton House'}
                      sx={{
                        height: '70px',
                        maxWidth: '220px',
                        objectFit: 'contain',
                        filter: 'drop-shadow(0px 2px 4px rgba(0,0,0,0.1))'
                      }}
                      onError={createImageErrorHandler()}
                    />
                  </Box>
                ) : (
                  <Typography variant="h6" gutterBottom sx={{ mb: 2 }}>
                    {configuration?.websiteTitle || 'Jaipur Cotton House'}
                  </Typography>
                )}

                {/* Store Description */}
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2, textAlign: { xs: 'center', md: 'left' } }}>
                  {configuration?.metaDescription || 'Discover our exclusive range of traditional ethnic wear'}
                </Typography>

                {/* Contact Information */}
                {configuration?.phone && (
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1, textAlign: { xs: 'center', md: 'left' } }}>
                    <strong>Phone:</strong> {configuration.phone}
                  </Typography>
                )}
                {configuration?.email && (
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1, textAlign: { xs: 'center', md: 'left' } }}>
                    <strong>Email:</strong> {configuration.email}
                  </Typography>
                )}
                {configuration?.address && (
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1, textAlign: { xs: 'center', md: 'left' } }}>
                    <strong>Address:</strong> {configuration.address}
                  </Typography>
                )}
              </Box>
            </Grid>

            {/* Quick Links */}
            <Grid item xs={12} sm={6} md={4}>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600, textAlign: { xs: 'center', md: 'left' } }}>
                Quick Links
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: { xs: 'center', md: 'flex-start' } }}>
                <Link to="/" style={{ textDecoration: 'none', color: 'inherit', marginBottom: '8px' }}>
                  <Typography variant="body2" color="text.secondary" sx={{ '&:hover': { color: 'primary.main' } }}>
                    Home
                  </Typography>
                </Link>
                <Link to="/products" style={{ textDecoration: 'none', color: 'inherit', marginBottom: '8px' }}>
                  <Typography variant="body2" color="text.secondary" sx={{ '&:hover': { color: 'primary.main' } }}>
                    All Products
                  </Typography>
                </Link>
                <Link to="/cart" style={{ textDecoration: 'none', color: 'inherit', marginBottom: '8px' }}>
                  <Typography variant="body2" color="text.secondary" sx={{ '&:hover': { color: 'primary.main' } }}>
                    Shopping Cart
                  </Typography>
                </Link>
                <Link to="/wishlist" style={{ textDecoration: 'none', color: 'inherit', marginBottom: '8px' }}>
                  <Typography variant="body2" color="text.secondary" sx={{ '&:hover': { color: 'primary.main' } }}>
                    Wishlist
                  </Typography>
                </Link>
                <Link to="/otp-login" style={{ textDecoration: 'none', color: 'inherit', marginBottom: '8px' }}>
                  <Typography variant="body2" color="text.secondary" sx={{ '&:hover': { color: 'primary.main' } }}>
                    Login / Register
                  </Typography>
                </Link>
              </Box>
            </Grid>

            {/* Social Media */}
            <Grid item xs={12} sm={6} md={4}>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600, textAlign: { xs: 'center', md: 'left' } }}>
                Connect With Us
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: { xs: 'center', md: 'flex-start' }, gap: 2, mb: 3 }}>
                {configuration?.facebookUrl && (
                  <IconButton component="a" href={configuration.facebookUrl} target="_blank" rel="noopener noreferrer" size="small" sx={{ color: '#3b5998' }}>
                    <Box component="img" src="/icons/facebook.svg" alt="Facebook" sx={{ width: 24, height: 24 }} />
                  </IconButton>
                )}
                {configuration?.instagramUrl && (
                  <IconButton component="a" href={configuration.instagramUrl} target="_blank" rel="noopener noreferrer" size="small" sx={{ color: '#e1306c' }}>
                    <Box component="img" src="/icons/instagram.svg" alt="Instagram" sx={{ width: 24, height: 24 }} />
                  </IconButton>
                )}
                {configuration?.twitterUrl && (
                  <IconButton component="a" href={configuration.twitterUrl} target="_blank" rel="noopener noreferrer" size="small" sx={{ color: '#1da1f2' }}>
                    <Box component="img" src="/icons/twitter.svg" alt="Twitter" sx={{ width: 24, height: 24 }} />
                  </IconButton>
                )}
                {configuration?.whatsappNumber && (
                  <IconButton component="a" href={`https://wa.me/${configuration.whatsappNumber}`} target="_blank" rel="noopener noreferrer" size="small" sx={{ color: '#25d366' }}>
                    <Box component="img" src="/icons/whatsapp.svg" alt="WhatsApp" sx={{ width: 24, height: 24 }} />
                  </IconButton>
                )}
              </Box>

              <Typography variant="body2" color="text.secondary" sx={{ mb: 2, textAlign: { xs: 'center', md: 'left' } }}>
                Subscribe to our newsletter for updates on new arrivals and special offers.
              </Typography>
            </Grid>
          </Grid>

          <Divider sx={{ my: 3 }} />

          <Box sx={{ display: 'flex', justifyContent: 'space-between', flexDirection: { xs: 'column', sm: 'row' }, alignItems: 'center' }}>
            <Typography variant="body2" color="text.secondary" sx={{ textAlign: { xs: 'center', sm: 'left' } }}>
              © {new Date().getFullYear()} {configuration?.websiteTitle || 'Jaipur Cotton House'}. All rights reserved.
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, mt: { xs: 2, sm: 0 } }}>
              <Link to="/privacy-policy" style={{ textDecoration: 'none' }}>
                <Typography variant="body2" color="text.secondary" sx={{ '&:hover': { color: 'primary.main' } }}>
                  Privacy Policy
                </Typography>
              </Link>
              <Link to="/terms-of-service" style={{ textDecoration: 'none' }}>
                <Typography variant="body2" color="text.secondary" sx={{ '&:hover': { color: 'primary.main' } }}>
                  Terms of Service
                </Typography>
              </Link>
            </Box>
          </Box>
        </Container>
      </Box>
    </Box>
  );
};

export default CustomerLayout;
