import React from 'react';
import { Link } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import {
  Card,
  Typography,
  Box,
  Button,
  Chip,
} from '@mui/material';
import { addToCart } from '../../store/slices/cartSlice';
import { getValidImageUrl, createImageErrorHandler } from '../../utils/imageUtils';

interface ProductCardProps {
  product: {
    id: string;
    name: string;
    price: number;
    mrp?: number;
    imageUrl?: string;
    mainImage?: string;
    sku: string;
    stockQuantity: number;
    variants?: Array<{
      id: string;
      size: string;
      price: number;
      mrp?: number;
      stockQuantity: number;
      isActive: boolean;
    }>;
  };
  onQuickAdd: (product: any) => void;
}

const ProductCard: React.FC<ProductCardProps> = ({ product, onQuickAdd }) => {
  const dispatch = useDispatch();
  console.log(product);

  // Get best price (lowest active variant price or product price)
  const getBestPrice = (product: any) => {
    if (!product) return 0;

    if (product.variants && product.variants.length > 0) {
      const activeVariantPrices = product.variants
        .filter((v: any) => v.isActive && v.stockQuantity > 0)
        .map((v: any) => v.price);
      return activeVariantPrices.length > 0
        ? Math.min(...activeVariantPrices)
        : product.price;
    }
    return product.price || 0;
  };

  // Get MRP (Maximum Retail Price)
  const getMRP = (product: any) => {
    if (!product) return 0;
    if (product.mrp) return product.mrp;
    if (product.variants && product.variants.length > 0) {
      const variantMRPs = product.variants
        .filter((v: any) => v.isActive && v.stockQuantity > 0)
        .map((v: any) => v.mrp || v.price);
      return variantMRPs.length > 0 ? Math.max(...variantMRPs) : (product.mrp || product.price || 0);
    }
    return product.mrp || product.price || 0;
  };

  // Calculate discount percentage
  const getDiscountPercentage = (product: any) => {
    const mrp = getMRP(product);
    const bestPrice = getBestPrice(product);
    if (!mrp || !bestPrice || mrp <= bestPrice) return 0;
    return Math.round(((mrp - bestPrice) / mrp) * 100);
  };

  const discountPercentage = getDiscountPercentage(product);
  const mrp = getMRP(product);

  // No need to trim title as we're using WebkitLineClamp for truncation

  return (
    <Card
      sx={{
        width: '100%',
        height: 'auto',
        display: 'flex',
        flexDirection: 'column',
        borderRadius: 2,
        overflow: 'hidden',
        position: 'relative',
        mx: 0,
        px: 0,
        boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
        transition: 'all 0.3s ease',
        '&:hover': {
          transform: 'translateY(-5px)',
          boxShadow: '0 12px 20px rgba(0,0,0,0.1)',
          '& .product-image': {
            transform: 'scale(1.05)'
          },
          '& .add-to-cart-button': {
            transform: 'translateY(0)',
            opacity: 1
          }
        }
      }}
    >
      {/* Product Image */}
      <Box sx={{
        position: 'relative',
        height: { xs: '200px', sm: '240px', md: '280px' },
        width: '100%',
        overflow: 'hidden',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f9f9f9'
      }}>
        <Link
          to={`/product/${product.id}`}
          style={{
            display: 'flex',
            width: '100%',
            height: '100%',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <Box
            component="img"
            className="product-image"
            src={product.mainImage || product.imageUrl ? getValidImageUrl(product.mainImage || product.imageUrl) : `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='300' height='300' viewBox='0 0 300 300'%3E%3Crect width='300' height='300' fill='%23f0f0f0'/%3E%3Ctext x='150' y='150' font-family='Arial' font-size='18' text-anchor='middle' fill='%23999'%3ENo Image%3C/text%3E%3C/svg%3E`}
            alt={product.name}
            onError={createImageErrorHandler()}
            sx={{
              maxWidth: '100%',
              maxHeight: '100%',
              objectFit: 'contain',
              display: 'block',
              transition: 'transform 0.5s ease',
              padding: '10px'
            }}
          />
        </Link>

        {/* Discount Badge */}
        {discountPercentage > 0 && (
          <Box
            sx={{
              position: 'absolute',
              top: 10,
              right: 10,
              backgroundColor: '#ff3f6c',
              color: 'white',
              fontSize: '0.7rem',
              fontWeight: 'bold',
              padding: '2px 8px',
              borderRadius: '2px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
              zIndex: 1
            }}
          >
            {discountPercentage}% OFF
          </Box>
        )}
      </Box>

      {/* Product Info */}
      <Box
        sx={{
          p: { xs: 1.5, sm: 2 },
          display: 'flex',
          flexDirection: 'column',
          backgroundColor: 'white',
          position: 'relative',
          minHeight: '120px',
          borderTop: '1px solid #f0f0f0'
        }}
      >
        <Box sx={{ flex: '1 1 auto', mb: 2 }}>
          <Typography
            variant="subtitle1"
            component="h2"
            sx={{
              fontWeight: 500,
              mb: 1,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              lineHeight: 1.3,
              height: '2.6em',
              fontSize: { xs: '0.85rem', sm: '0.9rem', md: '0.95rem' },
              color: 'text.primary',
              width: '100%'
            }}
          >
            {product.name}
          </Typography>

          {/* Price Section */}
          <Box sx={{
            display: 'flex',
            alignItems: 'baseline',
            gap: 1,
            mt: 1,
            flexWrap: 'wrap',
            width: '100%'
          }}>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 600,
                fontSize: { xs: '1rem', sm: '1.1rem', md: '1.2rem' },
                color: 'text.primary'
              }}
            >
              ₹{getBestPrice(product).toFixed(0)}
            </Typography>
            {discountPercentage > 0 && mrp > 0 && (
              <Typography
                variant="body2"
                sx={{
                  textDecoration: 'line-through',
                  color: 'text.secondary',
                  fontSize: { xs: '0.8rem', sm: '0.9rem', md: '1rem' }
                }}
              >
                ₹{mrp.toFixed(0)}
              </Typography>
            )}
          </Box>
        </Box>

        {/* Add to Cart Button */}
        <Button
          className="add-to-cart-button"
          variant="contained"
          onClick={() => onQuickAdd ? onQuickAdd(product) : dispatch(addToCart({
            productId: parseInt(product.id),
            name: product.name,
            price: getBestPrice(product),
            quantity: 1,
            imageUrl: product.mainImage || product.imageUrl || '',
            sku: product.sku,
            mrpPrice: getMRP(product)
          }))}
          disabled={product.stockQuantity <= 0}
          sx={{
            mt: 'auto',
            width: '100%',
            height: '36px',
            backgroundColor: '#ff3f6c',
            color: 'white',
            borderRadius: '2px',
            transition: 'all 0.3s ease',
            '&:hover': {
              backgroundColor: '#ff527b',
              boxShadow: '0 2px 5px rgba(255, 63, 108, 0.3)'
            },
            '&:disabled': {
              backgroundColor: '#cccccc',
              color: '#666666'
            },
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 1,
            fontSize: { xs: '0.75rem', sm: '0.8rem', md: '0.85rem' },
            fontWeight: 600,
            textTransform: 'none',
            letterSpacing: '0.5px'
          }}
        >
          {product.stockQuantity <= 0 ? 'Out of Stock' : 'Add to Cart'}
        </Button>
      </Box>
    </Card>
  );
};

export default ProductCard;
