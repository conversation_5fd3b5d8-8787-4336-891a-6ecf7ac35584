import React from 'react';
import { Box, Typography, useTheme, useMediaQuery } from '@mui/material';
import { Campaign } from '@mui/icons-material';

interface AnnouncementProps {
  text: string;
  show: boolean;
}

const Announcement: React.FC<AnnouncementProps> = ({ text, show }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  if (!show || !text) return null;

  return (
    <Box
      sx={{
        width: '100%',
        backgroundColor: '#000',
        color: '#fff',
        py: { xs: 0.5, sm: 0.75 },
        overflow: 'hidden',
        position: 'relative',
        zIndex: 1100,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: 1,
          width: '100%',
          overflow: 'hidden',
          whiteSpace: 'nowrap',
        }}
      >
        <Campaign fontSize="small" sx={{ color: '#fff', animation: 'pulse 2s infinite' }} />
        <Box
          sx={{
            overflow: 'hidden',
            width: '100%',
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <Typography
            variant="body2"
            component="div"
            sx={{
              fontWeight: 500,
              fontSize: { xs: '0.75rem', sm: '0.875rem' },
              whiteSpace: 'nowrap',
              animation: 'marquee 20s linear infinite',
              '@keyframes marquee': {
                '0%': { transform: 'translateX(100%)' },
                '100%': { transform: 'translateX(-100%)' }
              },
              '@keyframes pulse': {
                '0%': { opacity: 0.6 },
                '50%': { opacity: 1 },
                '100%': { opacity: 0.6 }
              }
            }}
          >
            {text}
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

export default Announcement;
