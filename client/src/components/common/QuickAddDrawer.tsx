import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  Drawer,
  Box,
  Typography,
  IconButton,
  Divider,
  <PERSON><PERSON>,
  Chip,
  TextField
} from '@mui/material';
import {
  Close as CloseIcon,
  Add as AddIcon,
  Remove as RemoveIcon
} from '@mui/icons-material';
import { addToCart } from '../../store/slices/cartSlice';

interface ProductVariant {
  id: number;
  size: string;
  price: number;
  mrp?: number;
  stockQuantity: number;
  isActive: boolean;
  sku?: string;
}

interface Product {
  id: number;
  name: string;
  price: number;
  mrp?: number;
  imageUrl?: string;
  mainImage?: string;
  stockQuantity: number;
  variants?: ProductVariant[];
  categoryId?: number;
  categoryName?: string;
  collectionIds?: number[];
  collectionNames?: string[];
}

interface QuickAddDrawerProps {
  open: boolean;
  onClose: () => void;
  product: Product | null;
  onSuccess?: (message: string) => void;
  isMobile?: boolean;
}

const QuickAddDrawer: React.FC<QuickAddDrawerProps> = ({
  open,
  onClose,
  product,
  onSuccess,
  isMobile = false
}) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null);
  const [quantity, setQuantity] = useState(1);
  const [variantError, setVariantError] = useState<string>('');

  const handleVariantSelect = (variant: ProductVariant) => {
    setSelectedVariant(variant);
    setVariantError('');
  };

  const handleAddToCart = () => {
    if (!product) return;

    if (product.variants && product.variants.length > 0 && !selectedVariant) {
      setVariantError('Please select a size');
      return;
    }

    // Calculate MRP price
    const mrpPrice = selectedVariant?.mrp || product.mrp ||
      (selectedVariant ? selectedVariant.price * 1.2 : product.price * 1.2);

    const cartItem = {
      productId: product.id,
      name: product.name,
      price: selectedVariant ? selectedVariant.price : product.price,
      quantity: quantity,
      imageUrl: product.mainImage || product.imageUrl || '',
      variantId: selectedVariant ? selectedVariant.id : undefined,
      sku: selectedVariant ? selectedVariant.sku : '',
      size: selectedVariant ? selectedVariant.size : '',
      mrpPrice: mrpPrice,
      categoryId: product.categoryId,
      categoryName: product.categoryName,
      collectionIds: product.collectionIds,
      collectionNames: product.collectionNames
    };

    // @ts-ignore
    dispatch(addToCart(cartItem));

    if (onSuccess) {
      onSuccess(`${product.name} added to cart`);
    }

    onClose();
  };

  const handleViewCart = () => {
    onClose();
    navigate('/cart');
  };

  const handleCheckout = () => {
    onClose();
    navigate('/checkout');
  };

  if (!product) return null;

  return (
    <Drawer
      anchor={isMobile ? 'bottom' : 'right'}
      open={open}
      onClose={onClose}
      sx={{
        '& .MuiDrawer-paper': {
          width: { xs: '100%', sm: 400 },
          maxHeight: { xs: '80vh', sm: '100%' },
          borderTopLeftRadius: { xs: 16, sm: 0 },
          borderTopRightRadius: { xs: 16, sm: 0 },
          p: { xs: 2, sm: 3 }
        }
      }}
    >
      {/* Drawer Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6" sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }}>
          {product.name}
        </Typography>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </Box>

      <Divider sx={{ mb: 2 }} />

      {/* Product Image */}
      <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
        <Box
          component="img"
          src={product.mainImage || product.imageUrl || 'https://via.placeholder.com/300'}
          alt={product.name}
          sx={{
            width: '100%',
            maxWidth: 200,
            height: 'auto',
            objectFit: 'contain',
            borderRadius: 1
          }}
        />
      </Box>

      {/* Variant Selection */}
      {product.variants && product.variants.length > 0 && (
        <Box sx={{ mb: 3 }}>
          <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 0.5 }}>
            Size: <span style={{ color: variantError ? 'red' : 'inherit' }}>{variantError ? `(${variantError})` : ''}</span>
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {product.variants.map((variant) => (
              <Chip
                key={variant.id}
                label={variant.size}
                onClick={() => handleVariantSelect(variant)}
                color={selectedVariant?.id === variant.id ? 'primary' : 'default'}
                variant={selectedVariant?.id === variant.id ? 'filled' : 'outlined'}
                disabled={variant.stockQuantity <= 0 || !variant.isActive}
                sx={{
                  borderRadius: '4px',
                  fontWeight: selectedVariant?.id === variant.id ? 'bold' : 'normal',
                  cursor: 'pointer',
                  border: variantError && !selectedVariant ? '1px solid red' : undefined
                }}
              />
            ))}
          </Box>
        </Box>
      )}

      {/* Quantity */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 0.5 }}>
          Quantity:
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <IconButton
            size="small"
            onClick={() => setQuantity(prev => Math.max(1, prev - 1))}
            sx={{ border: '1px solid #ddd' }}
          >
            <RemoveIcon fontSize="small" />
          </IconButton>

          <TextField
            value={quantity}
            onChange={(e) => {
              const val = parseInt(e.target.value);
              if (!isNaN(val) && val > 0) {
                setQuantity(val);
              }
            }}
            inputProps={{ min: 1 }}
            sx={{ width: '60px', '& input': { textAlign: 'center' } }}
            size="small"
          />

          <IconButton
            size="small"
            onClick={() => setQuantity(prev => prev + 1)}
            sx={{ border: '1px solid #ddd' }}
          >
            <AddIcon fontSize="small" />
          </IconButton>
        </Box>
      </Box>

      <Divider sx={{ mb: 2 }} />

      {/* Add to Cart Button */}
      <Button
        variant="contained"
        fullWidth
        onClick={handleAddToCart}
        disabled={product.variants && product.variants.length > 0 && !selectedVariant}
        sx={{
          backgroundColor: '#ff3f6c',
          '&:hover': { backgroundColor: '#ff527b' },
          py: 1.5,
          mb: 2
        }}
      >
        ADD TO CART
      </Button>

      {/* Action Buttons */}
      <Box sx={{ display: 'flex', gap: 2 }}>
        <Button
          variant="outlined"
          fullWidth
          onClick={handleViewCart}
          sx={{
            flex: 1,
            borderColor: '#ff3f6c',
            color: '#ff3f6c',
            '&:hover': { borderColor: '#ff527b', backgroundColor: 'rgba(255, 63, 108, 0.04)' }
          }}
        >
          VIEW CART
        </Button>
        <Button
          variant="contained"
          fullWidth
          onClick={handleCheckout}
          sx={{
            backgroundColor: '#000',
            '&:hover': { backgroundColor: '#333' },
            flex: 1
          }}
        >
          CHECKOUT
        </Button>
      </Box>
    </Drawer>
  );
};

export default QuickAddDrawer;
