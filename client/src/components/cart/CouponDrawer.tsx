import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  Box,
  Typography,
  IconButton,
  Button,
  List,
  CircularProgress,
  Alert,
  Paper,
  Chip,
  TextField,
  Divider,
  InputAdornment
} from '@mui/material';
import { Close, LocalOffer, CheckCircleOutline, Redeem } from '@mui/icons-material';
import { useDispatch, useSelector } from 'react-redux';
import { applyCoupon } from '../../store/slices/cartSlice';
import { RootState } from '../../store';
import api from '../../services/api';

interface CouponDrawerProps {
  open: boolean;
  onClose: () => void;
  cartTotal: number;
  totalItems: number;
}

interface Coupon {
  id: number;
  code: string;
  description: string;
  type: string;
  value: number;
  applicability: string;
  minimumPurchaseAmount?: number;
  minimumQuantity?: number;
  maximumDiscountAmount?: number;
  isActive: boolean;
  displayOnCartPage: boolean;
  categoryIds?: number[];
  collectionIds?: number[];
  potentialSavings?: number;
}

const CouponDrawer: React.FC<CouponDrawerProps> = ({ open, onClose, cartTotal, totalItems }) => {
  const dispatch = useDispatch();
  const { couponCode, items } = useSelector((state: RootState) => state.cart);

  const [coupons, setCoupons] = useState<Coupon[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [manualCouponCode, setManualCouponCode] = useState<string>('');
  const [validatingCoupon, setValidatingCoupon] = useState(false);

  // Fetch coupons when drawer opens
  useEffect(() => {
    if (open) {
      fetchCoupons();
    }
  }, [open, cartTotal, items]);

  // Function to fetch available coupons
  const fetchCoupons = async () => {
    setLoading(true);
    try {
      // Build query parameters
      const params = new URLSearchParams();
      params.append('cartTotal', cartTotal.toString());

      // Add product IDs
      items.forEach(item => {
        params.append('productIds', item.productId.toString());
      });

      // Add category IDs
      items.forEach(item => {
        if (item.categoryId) {
          params.append('categoryIds', item.categoryId.toString());
        }
      });

      // Add collection IDs
      items.forEach(item => {
        if (item.collectionIds) {
          item.collectionIds.forEach(id => {
            params.append('collectionIds', id.toString());
          });
        }
      });

      // Make API call
      const response = await api.get(`/coupons/cart/available?${params.toString()}`);
      console.log('Coupons response:', response.data);

      // Get coupons from response - handle both array and object formats
      let couponsData = [];

      if (response.data.coupons && Array.isArray(response.data.coupons)) {
        couponsData = response.data.coupons;
      }

      console.log(`Found ${couponsData.length} coupons in API response`);

      // If we have no coupons but the API says there are offers available,
      // create a mock coupon for display purposes
      if (couponsData.length === 0 && response.data.availableOffers == 0) {
        console.log('Creating mock coupon because API reports offers but returned none');
        couponsData = [];
      }

      setCoupons(couponsData);
    } catch (err) {
      console.error('Error fetching coupons:', err);
      setCoupons([]);
    } finally {
      setLoading(false);
    }
  };

  // Function to validate if a coupon can be applied
  const validateCoupon = (coupon: Coupon) => {
    // Basic validation
    if (!coupon) {
      console.log('Invalid coupon: coupon is null or undefined');
      return { isValid: false, discount: 0 };
    }

    if (!cartTotal || cartTotal <= 0) {
      console.log('Invalid coupon: cart total is zero or negative');
      return { isValid: false, discount: 0 };
    }

    // Check if coupon is active
    if (coupon.isActive === false) {
      console.log(`Coupon ${coupon.code} is inactive`);
      return { isValid: false, discount: 0 };
    }

    // Check minimum purchase amount
    if (coupon.minimumPurchaseAmount && cartTotal < coupon.minimumPurchaseAmount) {
      console.log(`Coupon ${coupon.code} requires minimum purchase of ${coupon.minimumPurchaseAmount}, but cart total is ${cartTotal}`);
      return { isValid: false, discount: 0 };
    }

    // Check minimum quantity
    if (coupon.minimumQuantity && totalItems < coupon.minimumQuantity) {
      console.log(`Coupon ${coupon.code} requires minimum quantity of ${coupon.minimumQuantity}, but cart has ${totalItems} items`);
      return { isValid: false, discount: 0 };
    }

    // Check for category restrictions
    if (coupon.categoryIds && Array.isArray(coupon.categoryIds) && coupon.categoryIds.length > 0) {
      const hasItemInCategory = items.some(item =>
        item.categoryId && coupon.categoryIds?.includes(item.categoryId)
      );

      if (!hasItemInCategory) {
        console.log(`Coupon ${coupon.code} requires items from specific categories, but none found in cart`);
        return { isValid: false, discount: 0 };
      }
    }

    // Check for collection restrictions
    if (coupon.collectionIds && Array.isArray(coupon.collectionIds) && coupon.collectionIds.length > 0) {
      const hasItemInCollection = items.some(item =>
        item.collectionIds && item.collectionIds.some(id => coupon.collectionIds?.includes(id))
      );

      if (!hasItemInCollection) {
        console.log(`Coupon ${coupon.code} requires items from specific collections, but none found in cart`);
        return { isValid: false, discount: 0 };
      }
    }

    // Use potential savings from API if available
    if (coupon.potentialSavings !== undefined && coupon.potentialSavings > 0) {
      console.log(`Coupon ${coupon.code} is valid with API-provided savings of ${coupon.potentialSavings}`);
      return { isValid: true, discount: coupon.potentialSavings };
    }

    // Calculate discount if not provided by API
    let discount = 0;
    if (coupon.type === 'Percentage') {
      discount = (cartTotal * coupon.value) / 100;
      // Apply maximum discount cap if exists
      if (coupon.maximumDiscountAmount) {
        discount = Math.min(discount, coupon.maximumDiscountAmount);
      }
    } else if (coupon.type === 'FixedAmount') {
      discount = coupon.value;
    }

    console.log(`Coupon ${coupon.code} is valid with calculated savings of ${discount}`);
    return { isValid: true, discount };
  };

  // Function to get error message for invalid coupons
  const getValidationErrorMessage = (coupon: Coupon) => {
    if (!cartTotal) return 'Cart is empty';
    if (coupon.minimumPurchaseAmount && cartTotal < coupon.minimumPurchaseAmount) {
      return `Minimum purchase amount of ₹${coupon.minimumPurchaseAmount} required`;
    }
    if (coupon.minimumQuantity && totalItems < coupon.minimumQuantity) {
      return `Minimum ${coupon.minimumQuantity} items required`;
    }

    // Check for category restrictions
    if (coupon.categoryIds && coupon.categoryIds.length > 0) {
      const hasItemInCategory = items.some(item =>
        item.categoryId && coupon.categoryIds?.includes(item.categoryId)
      );

      if (!hasItemInCategory) {
        return 'This coupon is only applicable for specific categories';
      }
    }

    // Check for collection restrictions
    if (coupon.collectionIds && coupon.collectionIds.length > 0) {
      const hasItemInCollection = items.some(item =>
        item.collectionIds && item.collectionIds.some(id => coupon.collectionIds?.includes(id))
      );

      if (!hasItemInCollection) {
        return 'This coupon is only applicable for specific collections';
      }
    }

    return 'Coupon cannot be applied';
  };

  // Function to apply a coupon
  const handleApplyCoupon = (code: string) => {
    setError(null);
    setSuccess(null);

    const coupon = coupons.find(c => c.code === code);
    if (!coupon) {
      setError('Invalid coupon code');
      return;
    }

    const validationResult = validateCoupon(coupon);
    if (!validationResult.isValid) {
      setError(getValidationErrorMessage(coupon));
      return;
    }

    dispatch(applyCoupon({
      code: coupon.code,
      discountAmount: validationResult.discount
    }));

    setSuccess(`Coupon ${code} applied successfully!`);
    setTimeout(() => {
      onClose();
    }, 1500);
  };

  // Function to validate a manual coupon code
  const handleValidateManualCoupon = async () => {
    if (!manualCouponCode.trim()) {
      setError('Please enter a coupon code');
      return;
    }

    setError(null);
    setSuccess(null);
    setValidatingCoupon(true);

    try {
      // Build request body with cart information
      const requestBody = {
        couponCode: manualCouponCode,
        cartTotal: cartTotal,
        totalItems: totalItems,
        productIds: items.map(item => item.productId),
        categoryIds: items
          .filter(item => item.categoryId)
          .map(item => item.categoryId),
        collectionIds: items
          .flatMap(item => item.collectionIds || [])
      };

      // Call the validate endpoint
      const response = await api.post('/coupons/validate', requestBody);
      console.log('Coupon validation response:', response.data);

      if (response.data.isValid) {
        // Apply the coupon
        dispatch(applyCoupon({
          code: response.data.coupon.code,
          discountAmount: response.data.discount
        }));

        setSuccess(`Coupon ${response.data.coupon.code} applied successfully!`);
        setManualCouponCode('');

        // Add the coupon to the list if it's not already there
        if (!coupons.some(c => c.code === response.data.coupon.code)) {
          setCoupons([...coupons, response.data.coupon]);
        }

        setTimeout(() => {
          onClose();
        }, 1500);
      } else {
        setError(response.data.message || 'This coupon cannot be applied');
      }
    } catch (err) {
      console.error('Error validating coupon:', err);
      setError('An error occurred while validating the coupon');
    } finally {
      setValidatingCoupon(false);
    }
  };

  // Find the best coupon (highest discount)
  const findBestCoupon = () => {
    if (!coupons.length) return null;

    let bestCoupon = null;
    let maxDiscount = 0;

    coupons.forEach(coupon => {
      const validationResult = validateCoupon(coupon);
      if (validationResult.isValid && validationResult.discount > maxDiscount) {
        maxDiscount = validationResult.discount;
        bestCoupon = coupon.code;
      }
    });

    return bestCoupon;
  };

  const bestCouponCode = findBestCoupon();

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      sx={{ '& .MuiDrawer-paper': { width: { xs: '100%', sm: 400 } } }}
    >
      <Box sx={{ p: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">Available Offers</Typography>
          <IconButton onClick={onClose} size="small">
            <Close />
          </IconButton>
        </Box>

        {/* Manual Coupon Input */}
        <Paper
          elevation={0}
          sx={{
            p: 2,
            mb: 3,
            border: '1px solid #e0e0e0',
            borderRadius: 2,
            bgcolor: '#f9f9f9'
          }}
        >
          <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600, color: '#333' }}>
            Have a coupon code?
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <TextField
              size="small"
              placeholder="Enter coupon code"
              fullWidth
              value={manualCouponCode}
              onChange={(e) => setManualCouponCode(e.target.value.toUpperCase())}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Redeem fontSize="small" color="action" />
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  bgcolor: 'white'
                }
              }}
            />
            <Button
              variant="contained"
              color="primary"
              disabled={validatingCoupon || !manualCouponCode.trim()}
              onClick={handleValidateManualCoupon}
              sx={{
                bgcolor: '#ff3f6c',
                '&:hover': { bgcolor: '#ff527b' },
                whiteSpace: 'nowrap'
              }}
            >
              {validatingCoupon ? 'Validating...' : 'Apply'}
            </Button>
          </Box>
        </Paper>

        <Divider sx={{ mb: 2 }} />

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {success}
          </Alert>
        )}

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: '#333' }}>
              {coupons.length} {coupons.length === 1 ? 'Offer' : 'Offers'} Available for You
            </Typography>

            {coupons.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <Typography variant="body1" color="text.secondary">
                  No coupons available for your cart
                </Typography>
              </Box>
            ) : (
              <List>
                {coupons.map((coupon) => {
                  const validationResult = validateCoupon(coupon);
                  const isApplied = couponCode === coupon.code;
                  const isBest = bestCouponCode === coupon.code;

                  return (
                    <Paper
                      key={coupon.id}
                      elevation={0}
                      sx={{
                        mb: 2,
                        p: 2,
                        border: '1px solid #e0e0e0',
                        borderLeft: isBest ? '4px solid #4caf50' : validationResult.isValid ? '1px solid #e0e0e0' : '1px solid #ffcdd2',
                        opacity: validationResult.isValid ? 1 : 0.8,
                        position: 'relative',
                        overflow: 'hidden',
                        borderRadius: 2,
                        transition: 'all 0.2s ease',
                        '&:hover': {
                          boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
                          borderColor: validationResult.isValid ? '#4caf50' : '#ffcdd2'
                        }
                      }}
                    >
                      {!validationResult.isValid && (
                        <Box sx={{
                          position: 'absolute',
                          top: 0,
                          right: 0,
                          bgcolor: '#ffebee',
                          color: '#d32f2f',
                          px: 1.5,
                          py: 0.5,
                          fontSize: '10px',
                          fontWeight: 'bold',
                          borderBottomLeftRadius: 8,
                          boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                          transform: 'translateY(-1px)',
                          letterSpacing: '0.5px'
                        }}>
                          NOT ELIGIBLE
                        </Box>
                      )}
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                        <Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                            <LocalOffer
                              color={validationResult.isValid ? "success" : "error"}
                              fontSize="small"
                              sx={{ mr: 1 }}
                            />
                            <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                              {coupon.code}
                            </Typography>
                            {isBest && (
                              <Chip
                                label="Best Offer"
                                size="small"
                                color="success"
                                sx={{
                                  ml: 1,
                                  fontWeight: 'bold',
                                  fontSize: '10px',
                                  height: '20px',
                                  '& .MuiChip-label': { px: 1 }
                                }}
                              />
                            )}
                          </Box>

                          <Typography variant="body2" sx={{ mb: 1 }}>
                            {coupon.description}
                          </Typography>

                          {coupon.minimumPurchaseAmount && (
                            <Typography variant="caption" color="text.secondary" display="block">
                              Min. purchase: ₹{coupon.minimumPurchaseAmount}
                            </Typography>
                          )}

                          {coupon.minimumQuantity && (
                            <Typography variant="caption" color="text.secondary" display="block">
                              Min. quantity: {coupon.minimumQuantity} items
                            </Typography>
                          )}

                          {validationResult.isValid ? (
                            <Typography variant="body2" color="success.main" sx={{ mt: 0.5, fontWeight: 'bold' }}>
                              Save ₹{validationResult.discount.toFixed(0)} on this order
                            </Typography>
                          ) : (
                            <Typography variant="body2" color="error" sx={{ mt: 0.5 }}>
                              {getValidationErrorMessage(coupon)}
                            </Typography>
                          )}
                        </Box>

                        <Box>
                          {isApplied ? (
                            <Chip
                              label="Applied"
                              color="success"
                              size="small"
                              icon={<CheckCircleOutline fontSize="small" />}
                              sx={{
                                fontWeight: 'bold',
                                bgcolor: '#e8f5e9',
                                border: '1px solid #4caf50',
                                '& .MuiChip-icon': { color: '#4caf50' }
                              }}
                            />
                          ) : (
                            <Button
                              variant="outlined"
                              size="small"
                              color={validationResult.isValid ? "success" : "error"}
                              disabled={!validationResult.isValid}
                              onClick={() => handleApplyCoupon(coupon.code)}
                              sx={{
                                fontWeight: 'bold',
                                borderWidth: '1.5px',
                                '&:hover': {
                                  borderWidth: '1.5px',
                                  bgcolor: validationResult.isValid ? 'rgba(76, 175, 80, 0.08)' : 'rgba(211, 47, 47, 0.08)'
                                }
                              }}
                            >
                              {validationResult.isValid ? "Apply" : "Ineligible"}
                            </Button>
                          )}
                        </Box>
                      </Box>
                    </Paper>
                  );
                })}
              </List>
            )}
          </>
        )}
      </Box>
    </Drawer>
  );
};

export default CouponDrawer;