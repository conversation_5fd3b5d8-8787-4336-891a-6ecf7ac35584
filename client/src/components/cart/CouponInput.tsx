import React, { useState } from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Collapse,
  IconButton,
  Paper
} from '@mui/material';
import { Close as CloseIcon } from '@mui/icons-material';
import api from '../../services/api';

interface CouponInputProps {
  onApplyCoupon: (code: string, discountAmount: number) => void;
  cartTotal: number;
  totalItems: number;
}

interface CouponValidationResult {
  isValid: boolean;
  message: string;
  discountAmount: number;
  coupon?: {
    code: string;
    description: string;
    type: string;
    value: number;
  };
}

const CouponInput: React.FC<CouponInputProps> = ({ onApplyCoupon, cartTotal, totalItems }) => {
  const [couponCode, setCouponCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [appliedCoupon, setAppliedCoupon] = useState<CouponValidationResult | null>(null);

  const handleApplyCoupon = async () => {
    if (!couponCode.trim()) {
      setError('Please enter a coupon code');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      try {
        const response = await api.post<CouponValidationResult>('/coupons/validate', {
          code: couponCode,
          cartTotal,
          totalItems
        });

        if (response.data.isValid) {
          setSuccess(response.data.message);
          setAppliedCoupon(response.data);
          onApplyCoupon(couponCode, response.data.discountAmount);

          // Also call the apply endpoint to record usage
          await api.post('/coupons/apply', {
            code: couponCode,
            cartTotal,
            totalItems
          });
        } else {
          setError(response.data.message);
        }
      } catch (apiErr) {
        console.warn('API not available, simulating coupon validation');

        // Simulate coupon validation with mock data
        const mockCoupons = [
          { code: 'WELCOME10', discount: 10, type: 'percentage', minAmount: 0 },
          { code: 'SAVE20', discount: 20, type: 'percentage', minAmount: 1000 },
          { code: 'FLAT100', discount: 100, type: 'fixed', minAmount: 500 }
        ];

        const coupon = mockCoupons.find(c => c.code === couponCode.toUpperCase());

        if (coupon) {
          if (cartTotal < coupon.minAmount) {
            setError(`Minimum purchase amount of ₹${coupon.minAmount} required`);
          } else {
            const discountAmount = coupon.type === 'percentage'
              ? (cartTotal * coupon.discount / 100)
              : coupon.discount;

            const mockResult: CouponValidationResult = {
              isValid: true,
              message: 'Coupon applied successfully',
              discountAmount: discountAmount,
              coupon: {
                code: coupon.code,
                description: `${coupon.type === 'percentage' ? coupon.discount + '% off' : '₹' + coupon.discount + ' off'} on your purchase`,
                type: coupon.type === 'percentage' ? 'Percentage' : 'FixedAmount',
                value: coupon.discount
              }
            };

            setSuccess(mockResult.message);
            setAppliedCoupon(mockResult);
            onApplyCoupon(couponCode, mockResult.discountAmount);
          }
        } else {
          setError('Invalid coupon code');
        }
      }
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to apply coupon');
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveCoupon = () => {
    setCouponCode('');
    setAppliedCoupon(null);
    setSuccess(null);
    setError(null);
    onApplyCoupon('', 0);
  };

  return (
    <Paper sx={{ p: 2, mb: 2 }}>
      <Typography variant="h6" gutterBottom>
        Apply Coupon
      </Typography>

      {appliedCoupon ? (
        <Box sx={{ mb: 2 }}>
          <Alert
            severity="success"
            action={
              <IconButton
                aria-label="close"
                color="inherit"
                size="small"
                onClick={handleRemoveCoupon}
              >
                <CloseIcon fontSize="inherit" />
              </IconButton>
            }
            sx={{ mb: 1 }}
          >
            Coupon <strong>{appliedCoupon.coupon?.code}</strong> applied! You saved ₹{appliedCoupon.discountAmount.toFixed(2)}
          </Alert>
          <Typography variant="body2" color="text.secondary">
            {appliedCoupon.coupon?.description}
          </Typography>
        </Box>
      ) : (
        <>
          <Box sx={{ display: 'flex', mb: 1 }}>
            <TextField
              size="small"
              placeholder="Enter coupon code"
              value={couponCode}
              onChange={(e) => setCouponCode(e.target.value.toUpperCase())}
              sx={{ mr: 1, flex: 1 }}
            />
            <Button
              variant="outlined"
              onClick={handleApplyCoupon}
              disabled={loading || !couponCode.trim()}
            >
              {loading ? <CircularProgress size={24} /> : 'Apply'}
            </Button>
          </Box>

          <Collapse in={!!error || !!success}>
            {error && <Alert severity="error">{error}</Alert>}
            {success && <Alert severity="success">{success}</Alert>}
          </Collapse>
        </>
      )}
    </Paper>
  );
};

export default CouponInput;
