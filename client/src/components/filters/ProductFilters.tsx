import React from 'react';
import {
  Box,
  Button,
  Checkbox,
  ListItemText,
  Menu,
  MenuItem,
  Chip,
} from '@mui/material';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';

interface FilterOption {
  value: string;
  label: string;
}

interface ProductFiltersProps {
  products: any[];
  selectedProductType: string[];
  setSelectedProductType: (types: string[]) => void;
  selectedSizes: string[];
  setSelectedSizes: (sizes: string[]) => void;
  selectedColors: string[];
  setSelectedColors: (colors: string[]) => void;
  sortBy: string;
  setSortBy: (sort: string) => void;
}

const ProductFilters: React.FC<ProductFiltersProps> = ({
  products,
  selectedProductType,
  setSelectedProductType,
  selectedSizes,
  setSelectedSizes,
  selectedColors,
  setSelectedColors,
  sortBy,
  setSortBy,
}) => {
  const [filterAnchorEl, setFilterAnchorEl] = React.useState<{
    productType: HTMLElement | null;
    size: HTMLElement | null;
    color: HTMLElement | null;
    sort: HTMLElement | null;
  }>({
    productType: null,
    size: null,
    color: null,
    sort: null,
  });

  // Extract unique values from products
  const productTypes = [...new Set(products.map(p => p.productTypeName))].filter(Boolean);
  const sizes = [...new Set(products.flatMap(p => p.variants?.map(v => v.size) || []))].filter(Boolean);
  const colors = [...new Set(products.map(p => p.color))].filter(Boolean);

  console.log('Available filters:', { productTypes, sizes, colors });

  const sortOptions: FilterOption[] = [
    { value: 'popularity', label: 'Popularity' },
    { value: 'newest', label: 'Newest First' },
    { value: 'priceAsc', label: 'Price: Low to High' },
    { value: 'priceDesc', label: 'Price: High to Low' }
  ];

  const handleFilterClick = (
    event: React.MouseEvent<HTMLElement>,
    filterType: 'productType' | 'size' | 'color' | 'sort'
  ) => {
    setFilterAnchorEl(prev => ({
      ...prev,
      [filterType]: event.currentTarget,
    }));
  };

  const handleFilterClose = (filterType: 'productType' | 'size' | 'color' | 'sort') => {
    setFilterAnchorEl(prev => ({
      ...prev,
      [filterType]: null,
    }));
  };

  return (
    <Box sx={{
      display: 'flex',
      gap: 1,
      mb: 3,
      flexWrap: 'wrap',
      alignItems: 'center'
    }}>
      {/* Product Type Filter */}
      <Button
        variant="outlined"
        onClick={(e) => handleFilterClick(e, 'productType')}
        endIcon={<ArrowDropDownIcon />}
        size="small"
      >
        Product Type
      </Button>
      <Menu
        anchorEl={filterAnchorEl.productType}
        open={Boolean(filterAnchorEl.productType)}
        onClose={() => handleFilterClose('productType')}
      >
        {productTypes.map((type) => (
          <MenuItem key={type}>
            <Checkbox
              size="small"
              checked={selectedProductType.includes(type)}
              onChange={(e) => {
                if (e.target.checked) {
                  setSelectedProductType([...selectedProductType, type]);
                } else {
                  setSelectedProductType(selectedProductType.filter(t => t !== type));
                }
              }}
            />
            <ListItemText primary={type} />
          </MenuItem>
        ))}
      </Menu>

      {/* Size Filter */}
      <Button
        variant="outlined"
        onClick={(e) => handleFilterClick(e, 'size')}
        endIcon={<ArrowDropDownIcon />}
        size="small"
      >
        Size
      </Button>
      <Menu
        anchorEl={filterAnchorEl.size}
        open={Boolean(filterAnchorEl.size)}
        onClose={() => handleFilterClose('size')}
      >
        {sizes.map((size) => (
          <MenuItem key={size}>
            <Checkbox
              size="small"
              checked={selectedSizes.includes(size)}
              onChange={(e) => {
                if (e.target.checked) {
                  setSelectedSizes([...selectedSizes, size]);
                } else {
                  setSelectedSizes(selectedSizes.filter(s => s !== size));
                }
              }}
            />
            <ListItemText primary={size} />
          </MenuItem>
        ))}
      </Menu>

      {/* Color Filter */}
      <Button
        variant="outlined"
        onClick={(e) => handleFilterClick(e, 'color')}
        endIcon={<ArrowDropDownIcon />}
        size="small"
      >
        Color
      </Button>
      <Menu
        anchorEl={filterAnchorEl.color}
        open={Boolean(filterAnchorEl.color)}
        onClose={() => handleFilterClose('color')}
      >
        {colors.map((color) => (
          <MenuItem key={color}>
            <Checkbox
              size="small"
              checked={selectedColors.includes(color)}
              onChange={(e) => {
                if (e.target.checked) {
                  setSelectedColors([...selectedColors, color]);
                } else {
                  setSelectedColors(selectedColors.filter(c => c !== color));
                }
              }}
            />
            <ListItemText primary={color} />
          </MenuItem>
        ))}
      </Menu>

      {/* Sort Filter */}
      <Button
        variant="outlined"
        onClick={(e) => handleFilterClick(e, 'sort')}
        endIcon={<ArrowDropDownIcon />}
        size="small"
        sx={{ marginLeft: 'auto' }}
      >
        Sort By: {sortOptions.find(option => option.value === sortBy)?.label}
      </Button>
      <Menu
        anchorEl={filterAnchorEl.sort}
        open={Boolean(filterAnchorEl.sort)}
        onClose={() => handleFilterClose('sort')}
      >
        {sortOptions.map((option) => (
          <MenuItem
            key={option.value}
            onClick={() => {
              setSortBy(option.value);
              handleFilterClose('sort');
            }}
            selected={sortBy === option.value}
          >
            {option.label}
          </MenuItem>
        ))}
      </Menu>

      {/* Active Filters Display */}
      <Box sx={{
        display: 'flex',
        gap: 1,
        flexWrap: 'wrap',
        width: '100%',
        mt: 1
      }}>
        {selectedProductType.map((type) => (
          <Chip
            key={type}
            label={type}
            onDelete={() => {
              setSelectedProductType(selectedProductType.filter(t => t !== type));
            }}
            size="small"
          />
        ))}
        {selectedSizes.map((size) => (
          <Chip
            key={size}
            label={`Size: ${size}`}
            onDelete={() => {
              setSelectedSizes(selectedSizes.filter(s => s !== size));
            }}
            size="small"
          />
        ))}
        {selectedColors.map((color) => (
          <Chip
            key={color}
            label={`Color: ${color}`}
            onDelete={() => {
              setSelectedColors(selectedColors.filter(c => c !== color));
            }}
            size="small"
          />
        ))}
      </Box>
    </Box>
  );
};

export default ProductFilters;