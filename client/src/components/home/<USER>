import React from 'react';
import { Link } from 'react-router-dom';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardMedia,
  CardContent,
  Button,
  useTheme,
  useMediaQuery,
  Skeleton,
  alpha,
  Paper
} from '@mui/material';
import { ArrowForward } from '@mui/icons-material';
import { Collection } from '../../store/slices/collectionSlice';
import '../../styles/customerLayout.css';

interface CollectionSectionProps {
  title: string;
  collections: Collection[];
  loading: boolean;
}

const CollectionSection: React.FC<CollectionSectionProps> = ({ title, collections, loading }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  if (loading) {
    return (
      <Box className="full-width-container" sx={{ width: '100%' }}>
        <Typography variant="h4" component="h2" gutterBottom>
          {title}
        </Typography>
        <Grid container spacing={3}>
          {[1, 2, 3, 4].map((item) => (
            <Grid item xs={12} sm={6} key={item}>
              <Card>
                <Skeleton variant="rectangular" height={300} />
                <CardContent>
                  <Skeleton variant="text" width="80%" />
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  }

  if (!collections || collections.length === 0) {
    return null;
  }

  return (
    <Box className="full-width-container" sx={{ width: '100%' }}>
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: { xs: 2, md: 3 },
        flexWrap: 'wrap',
        gap: 1
      }}>
        <Typography
          variant="h4"
          component="h2"
          sx={{
            fontSize: { xs: '1.5rem', sm: '1.75rem', md: '2rem' },
            fontWeight: 600,
            position: 'relative',
            '&::after': {
              content: '""',
              position: 'absolute',
              bottom: -8,
              left: 0,
              width: 60,
              height: 3,
              backgroundColor: theme.palette.primary.main,
              borderRadius: 1.5
            }
          }}
        >
          {title}
        </Typography>
        <Button
          component={Link}
          to="/collections"
          color="primary"
          variant="outlined"
          size={isMobile ? 'small' : 'medium'}
          endIcon={<ArrowForward />}
          sx={{
            borderRadius: '4px',
            fontWeight: 500,
            transition: 'all 0.3s ease',
            '&:hover': {
              backgroundColor: alpha(theme.palette.primary.main, 0.05),
              transform: 'translateY(-2px)'
            }
          }}
        >
          View All
        </Button>
      </Box>

      <Grid container spacing={{ xs: 2, sm: 3 }}>
        {collections.map((collection) => (
          <Grid item xs={12} sm={6} md={4} key={collection.id}>
            <Paper
              elevation={0}
              sx={{
                height: '100%',
                borderRadius: 2,
                overflow: 'hidden',
                position: 'relative',
                boxShadow: '0 4px 15px rgba(0,0,0,0.1)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-5px)',
                  boxShadow: '0 8px 25px rgba(0,0,0,0.15)',
                  '& .collection-image': {
                    transform: 'scale(1.05)'
                  },
                  '& .collection-overlay': {
                    background: 'linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 50%, rgba(0,0,0,0.1) 100%)'
                  }
                },
              }}
            >
              <Box
                component={Link}
                to={`/collection/${collection.id}`}
                sx={{
                  display: 'block',
                  textDecoration: 'none',
                  height: '100%',
                  position: 'relative',
                }}
              >
                <Box sx={{
                  position: 'relative',
                  height: { xs: 220, sm: 250, md: 280 },
                  overflow: 'hidden',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: '#f5f5f5'
                }}>
                  <CardMedia
                    component="img"
                    image={collection.imageUrl || 'https://via.placeholder.com/600x300?text=Collection'}
                    alt={collection.name}
                    className="collection-image"
                    sx={{
                      height: '100%',
                      width: '100%',
                      objectFit: 'cover',
                      transition: 'transform 0.7s ease'
                    }}
                  />
                  <Box
                    className="collection-overlay"
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      height: '100%',
                      background: 'linear-gradient(to top, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.3) 50%, rgba(0,0,0,0.1) 100%)',
                      transition: 'background 0.3s ease',
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'flex-end',
                      p: { xs: 2, sm: 3 }
                    }}
                  >
                    <Typography
                      variant="h5"
                      component="h3"
                      color="white"
                      sx={{
                        fontWeight: 'bold',
                        textShadow: '1px 1px 3px rgba(0,0,0,0.7)',
                        fontSize: { xs: '1.25rem', sm: '1.5rem', md: '1.75rem' },
                        mb: 1
                      }}
                    >
                      {collection.name}
                    </Typography>
                    {collection.description && (
                      <Typography
                        variant="body2"
                        color="white"
                        sx={{
                          textShadow: '1px 1px 2px rgba(0,0,0,0.7)',
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden',
                          opacity: 0.9,
                          fontSize: { xs: '0.75rem', sm: '0.875rem' },
                          lineHeight: 1.5
                        }}
                      >
                        {collection.description}
                      </Typography>
                    )}
                    <Button
                      variant="outlined"
                      size="small"
                      sx={{
                        mt: 2,
                        color: 'white',
                        borderColor: 'white',
                        alignSelf: 'flex-start',
                        '&:hover': {
                          borderColor: 'white',
                          backgroundColor: 'rgba(255,255,255,0.1)'
                        },
                        fontSize: { xs: '0.75rem', sm: '0.875rem' },
                        py: 0.5
                      }}
                    >
                      Explore
                    </Button>
                  </Box>
                </Box>
              </Box>
            </Paper>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default CollectionSection;
