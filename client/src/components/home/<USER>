import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Box, Button, Typography, Paper, useTheme, useMediaQuery, Container, alpha } from '@mui/material';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Pagination, Navigation, Autoplay, EffectFade } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';
import 'swiper/css/effect-fade';
import { WebsiteBanner } from '../../store/slices/websiteConfigSlice';
import { ArrowForward } from '@mui/icons-material';
import '../../styles/customerLayout.css';

interface BannerProps {
  banners: WebsiteBanner[];
}

const Banner: React.FC<BannerProps> = ({ banners }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));
  const [activeIndex, setActiveIndex] = useState(0);

  if (!banners || banners.length === 0) {
    return null;
  }

  // Custom pagination style
  const paginationStyle = {
    '.swiper-pagination-bullet': {
      width: '12px',
      height: '12px',
      opacity: 0.7,
      backgroundColor: 'white',
      transition: 'all 0.3s ease',
    },
    '.swiper-pagination-bullet-active': {
      opacity: 1,
      width: '30px',
      borderRadius: '6px',
      backgroundColor: theme.palette.primary.main,
    },
  };

  return (
    <Box
      className="banner-container"
      sx={{
        width: '100%',
        mb: { xs: 2, sm: 3, md: 4 },
        position: 'relative',
        overflow: 'hidden',
        margin: 0,
        padding: 0,
        '&::after': {
          content: '""',
          position: 'absolute',
          bottom: 0,
          left: 0,
          width: '100%',
          height: '100px',
          background: 'linear-gradient(to top, rgba(255,255,255,1) 0%, rgba(255,255,255,0) 100%)',
          zIndex: 1,
          pointerEvents: 'none',
        }
      }}
    >
      <Swiper
        modules={[Pagination, Navigation, Autoplay, EffectFade]}
        spaceBetween={0}
        slidesPerView={1}
        pagination={{
          clickable: true,
          dynamicBullets: true,
        }}
        navigation={!isMobile}
        autoplay={{
          delay: 5000,
          disableOnInteraction: false,
        }}
        loop={true}
        effect="fade"
        fadeEffect={{ crossFade: true }}
        onSlideChange={(swiper) => setActiveIndex(swiper.realIndex)}
        style={{
          width: '100%',
          height: isMobile ? '350px' : isTablet ? '450px' : '550px',
          maxHeight: '70vh',
        }}
        className="banner-swiper"
      >
        {banners.map((banner, index) => (
          <SwiperSlide key={banner.id}>
            <Box
              sx={{
                position: 'relative',
                height: '100%',
                width: '100%',
                overflow: 'hidden',
              }}
            >
              {/* Background Image with Parallax Effect */}
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  backgroundImage: `url(${banner.imageUrl})`,
                  backgroundSize: 'cover',
                  backgroundPosition: { xs: 'center', md: 'center 30%' },
                  transform: 'scale(1.05)',
                  transition: 'transform 1.5s ease-out',
                  ...(activeIndex === index && {
                    transform: 'scale(1)',
                  }),
                }}
              />

              {/* Overlay Gradient */}
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  background: 'linear-gradient(to right, rgba(0,0,0,0.6) 0%, rgba(0,0,0,0.3) 50%, rgba(0,0,0,0.1) 100%)',
                  opacity: 0,
                  animation: activeIndex === index ? 'fadeIn 0.5s forwards' : 'none',
                  '@keyframes fadeIn': {
                    '0%': { opacity: 0 },
                    '100%': { opacity: 1 },
                  },
                }}
              />

              {/* Content Container */}
              <Container maxWidth={false} className="full-width-container" sx={{ height: '100%', position: 'relative', zIndex: 2, px: { xs: 2, md: 4 } }}>
                <Box
                  sx={{
                    position: 'absolute',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    maxWidth: { xs: '90%', sm: '70%', md: '50%' },
                    textAlign: { xs: 'center', md: 'left' },
                    color: 'white',
                    p: { xs: 2, sm: 3, md: 4 },
                    left: { xs: '50%', md: 0 },
                    ml: { xs: '-45%', md: 0 },
                    opacity: 0,
                    animation: activeIndex === index ? 'slideIn 0.8s forwards' : 'none',
                    '@keyframes slideIn': {
                      '0%': { opacity: 0, transform: 'translate(0, -40%)' },
                      '100%': { opacity: 1, transform: 'translate(0, -50%)' },
                    },
                  }}
                >
                  {banner.title && (
                    <Typography
                      variant={isMobile ? 'h5' : isTablet ? 'h4' : 'h3'}
                      component="h1"
                      gutterBottom
                      sx={{
                        fontWeight: 'bold',
                        textShadow: '2px 2px 4px rgba(0,0,0,0.5)',
                        mb: { xs: 1, sm: 2 },
                        lineHeight: 1.2,
                        letterSpacing: '0.5px',
                      }}
                    >
                      {banner.title}
                    </Typography>
                  )}

                  {banner.subtitle && (
                    <Typography
                      variant={isMobile ? 'body2' : isTablet ? 'body1' : 'h6'}
                      sx={{
                        mb: { xs: 2, sm: 3 },
                        textShadow: '1px 1px 2px rgba(0,0,0,0.5)',
                        maxWidth: '90%',
                        mx: { xs: 'auto', md: 0 },
                        lineHeight: 1.5,
                      }}
                    >
                      {banner.subtitle}
                    </Typography>
                  )}

                  {banner.buttonText && (
                    <Button
                      component={Link}
                      to={banner.buttonLink || '/products'}
                      variant="contained"
                      endIcon={<ArrowForward />}
                      size={isMobile ? 'medium' : 'large'}
                      sx={{
                        mt: { xs: 1, sm: 2 },
                        fontWeight: 'bold',
                        px: { xs: 3, sm: 4 },
                        py: { xs: 1, sm: 1.5 },
                        borderRadius: '4px',
                        backgroundColor: theme.palette.primary.main,
                        color: 'white',
                        boxShadow: '0 4px 14px rgba(0, 0, 0, 0.25)',
                        position: 'relative',
                        overflow: 'hidden',
                        '&::before': {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          left: '-100%',
                          width: '100%',
                          height: '100%',
                          background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
                          transition: 'all 0.5s',
                        },
                        '&:hover': {
                          backgroundColor: alpha(theme.palette.primary.main, 0.9),
                          transform: 'translateY(-3px)',
                          boxShadow: '0 6px 20px rgba(0, 0, 0, 0.3)',
                          '&::before': {
                            left: '100%',
                          },
                        },
                        transition: 'all 0.3s ease',
                      }}
                    >
                      {banner.buttonText}
                    </Button>
                  )}
                </Box>
              </Container>
            </Box>
          </SwiperSlide>
        ))}
      </Swiper>

      {/* Custom Styles for Pagination */}
      <style jsx global>{`
        .banner-swiper .swiper-pagination-bullet {
          width: 12px;
          height: 12px;
          opacity: 0.7;
          background-color: white;
          transition: all 0.3s ease;
        }
        .banner-swiper .swiper-pagination-bullet-active {
          opacity: 1;
          width: 30px;
          border-radius: 6px;
          background-color: ${theme.palette.primary.main};
        }
        .banner-swiper .swiper-button-next,
        .banner-swiper .swiper-button-prev {
          color: white;
          background-color: rgba(0, 0, 0, 0.3);
          width: 40px;
          height: 40px;
          border-radius: 50%;
          transition: all 0.3s ease;
        }
        .banner-swiper .swiper-button-next:hover,
        .banner-swiper .swiper-button-prev:hover {
          background-color: rgba(0, 0, 0, 0.5);
        }
        .banner-swiper .swiper-button-next:after,
        .banner-swiper .swiper-button-prev:after {
          font-size: 18px;
        }
      `}</style>
    </Box>
  );
};

export default Banner;
