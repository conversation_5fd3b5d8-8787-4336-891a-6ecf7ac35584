import React from 'react';
import { Link } from 'react-router-dom';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardMedia,
  CardContent,
  Button,
  useTheme,
  useMediaQuery,
  Skeleton,
  alpha
} from '@mui/material';
import { ArrowForward } from '@mui/icons-material';
import { Category } from '../../store/slices/categorySlice';
import '../../styles/customerLayout.css';

interface CategorySectionProps {
  title: string;
  categories: Category[];
  loading: boolean;
}

const CategorySection: React.FC<CategorySectionProps> = ({ title, categories, loading }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  if (loading) {
    return (
      <Box sx={{ mb: 6 }}>
        <Typography variant="h4" component="h2" gutterBottom>
          {title}
        </Typography>
        <Grid container spacing={3}>
          {[1, 2, 3, 4].map((item) => (
            <Grid item xs={6} sm={4} md={3} key={item}>
              <Card>
                <Skeleton variant="rectangular" height={200} />
                <CardContent>
                  <Skeleton variant="text" width="80%" />
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  }

  if (!categories || categories.length === 0) {
    return null;
  }

  return (
    <Box className="full-width-container" sx={{ width: '100%' }}>
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: { xs: 2, md: 3 },
        flexWrap: 'wrap',
        gap: 1
      }}>
        <Typography
          variant="h4"
          component="h2"
          sx={{
            fontSize: { xs: '1.5rem', sm: '1.75rem', md: '2rem' },
            fontWeight: 600,
            position: 'relative',
            '&::after': {
              content: '""',
              position: 'absolute',
              bottom: -8,
              left: 0,
              width: 60,
              height: 3,
              backgroundColor: theme.palette.primary.main,
              borderRadius: 1.5
            }
          }}
        >
          {title}
        </Typography>
        <Button
          component={Link}
          to="/products"
          color="primary"
          variant="outlined"
          size={isMobile ? 'small' : 'medium'}
          endIcon={<ArrowForward />}
          sx={{
            borderRadius: '4px',
            fontWeight: 500,
            transition: 'all 0.3s ease',
            '&:hover': {
              backgroundColor: alpha(theme.palette.primary.main, 0.05),
              transform: 'translateY(-2px)'
            }
          }}
        >
          View All
        </Button>
      </Box>
      <Box sx={{
        width: '100%',
        overflowX: 'auto',
        pb: 2,
        '&::-webkit-scrollbar': {
          height: 6,
        },
        '&::-webkit-scrollbar-track': {
          backgroundColor: '#f1f1f1',
          borderRadius: 3,
        },
        '&::-webkit-scrollbar-thumb': {
          backgroundColor: '#c1c1c1',
          borderRadius: 3,
          '&:hover': {
            backgroundColor: '#a1a1a1',
          },
        },
      }}>
        <Box sx={{
          display: 'flex',
          gap: { xs: 2, sm: 3 },
          py: 1,
          width: 'max-content',
          minWidth: '100%',
        }}>
          {categories.map((category) => (
            <Box
              key={category.id}
              sx={{
                width: { xs: 140, sm: 160, md: 180 },
                flexShrink: 0,
              }}
            >
              <Box
                component={Link}
                to={`/category/${category.id}`}
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  textDecoration: 'none',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-5px)',
                    '& .category-image-container': {
                      boxShadow: '0 8px 20px rgba(0,0,0,0.15)',
                    },
                    '& .category-image': {
                      transform: 'scale(1.05)'
                    },
                    '& .category-name': {
                      color: theme.palette.primary.main,
                    }
                  },
                }}
              >
                <Box
                  className="category-image-container"
                  sx={{
                    width: { xs: 120, sm: 140, md: 160 },
                    height: { xs: 120, sm: 140, md: 160 },
                    borderRadius: '50%',
                    overflow: 'hidden',
                    boxShadow: '0 4px 10px rgba(0,0,0,0.1)',
                    transition: 'box-shadow 0.3s ease',
                    mb: 2,
                    position: 'relative',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: '#f9f9f9'
                  }}
                >
                  <Box
                    component="img"
                    src={category.imageUrl || 'https://via.placeholder.com/300x300?text=Category'}
                    alt={category.name}
                    className="category-image"
                    sx={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                      transition: 'transform 0.5s ease',
                    }}
                  />
                </Box>
                <Typography
                  className="category-name"
                  variant="subtitle1"
                  component="h3"
                  color="text.primary"
                  align="center"
                  sx={{
                    fontWeight: 600,
                    fontSize: { xs: '0.9rem', sm: '1rem', md: '1.1rem' },
                    lineHeight: 1.3,
                    transition: 'color 0.3s ease',
                    mt: 1,
                  }}
                >
                  {category.name}
                </Typography>
              </Box>
            </Box>
          ))}
        </Box>
      </Box>
    </Box>
  );
};

export default CategorySection;
