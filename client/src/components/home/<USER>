import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import {
  Box,
  Typography,
  Button,
  Card,
  CardContent,
  Skeleton,
  useTheme,
  useMediaQuery,
  alpha
} from '@mui/material';
import { ArrowForward } from '@mui/icons-material';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import { Product } from '../../store/slices/productSlice';
import { toast } from 'react-toastify';
import ProductCard from '../common/ProductCard';
import QuickAddDrawer from '../common/QuickAddDrawer';
import '../../styles/customerLayout.css';

interface ProductCarouselProps {
  title: string;
  products: Product[];
  loading: boolean;
  viewAllLink: string;
  wishlistItems?: any[];
}

const ProductCarousel: React.FC<ProductCarouselProps> = ({
  title,
  products,
  loading,
  viewAllLink,
  wishlistItems = []
}) => {
  const dispatch = useDispatch();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));
  const [quickAddDrawerOpen, setQuickAddDrawerOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

  const handleQuickAdd = (product: any) => {
    // Convert string IDs to numbers for the drawer component
    const convertedProduct = {
      ...product,
      id: parseInt(product.id),
      variants: product.variants?.map((v: any) => ({
        ...v,
        id: parseInt(v.id)
      }))
    };

    setSelectedProduct(convertedProduct);
    setQuickAddDrawerOpen(true);
  };

  const handleDrawerClose = () => {
    setQuickAddDrawerOpen(false);
  };

  const handleAddSuccess = (message: string) => {
    toast.success(message);
  };

  const getSlidesPerView = () => {
    if (isMobile) return 2.2;
    if (isTablet) return 3.2;
    return 5.2;
  };

  if (loading) {
    return (
      <Box className="full-width-container" sx={{ width: '100%' }}>
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: { xs: 2, md: 3 },
          flexWrap: 'wrap',
          gap: 1
        }}>
          <Typography
            variant="h4"
            component="h2"
            sx={{
              fontSize: { xs: '1.5rem', sm: '1.75rem', md: '2rem' },
              fontWeight: 600,
              position: 'relative',
              '&::after': {
                content: '""',
                position: 'absolute',
                bottom: -8,
                left: 0,
                width: 60,
                height: 3,
                backgroundColor: theme.palette.primary.main,
                borderRadius: 1.5
              }
            }}
          >
            {title}
          </Typography>
          <Button
            component={Link}
            to={viewAllLink}
            color="primary"
            variant="outlined"
            size={isMobile ? 'small' : 'medium'}
            endIcon={<ArrowForward />}
            sx={{
              borderRadius: '4px',
              fontWeight: 500,
              transition: 'all 0.3s ease',
              '&:hover': {
                backgroundColor: alpha(theme.palette.primary.main, 0.05),
                transform: 'translateY(-2px)'
              }
            }}
          >
            View All
          </Button>
        </Box>
        <Swiper
          modules={[Navigation, Pagination]}
          spaceBetween={16}
          slidesPerView={getSlidesPerView()}
          navigation
          pagination={{ clickable: true }}
        >
          {[1, 2, 3, 4, 5].map((item) => (
            <SwiperSlide key={item}>
              <Card
                sx={{
                  width: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  borderRadius: 2,
                  overflow: 'hidden',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
                }}
              >
                <Box sx={{
                  position: 'relative',
                  height: { xs: '200px', sm: '240px', md: '280px' },
                  width: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: '#f9f9f9'
                }}>
                  <Skeleton
                    variant="rectangular"
                    sx={{
                      width: '80%',
                      height: '80%',
                      borderRadius: '4px'
                    }}
                  />
                </Box>
                <CardContent sx={{ p: { xs: 1.5, sm: 2 } }}>
                  <Skeleton variant="text" width="90%" height={20} />
                  <Skeleton variant="text" width="60%" height={16} />
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 1, gap: 1 }}>
                    <Skeleton variant="text" width="30%" height={24} />
                    <Skeleton variant="text" width="20%" height={16} />
                  </Box>
                  <Skeleton variant="rectangular" width="100%" height={40} sx={{ mt: 2, borderRadius: 1 }} />
                </CardContent>
              </Card>
            </SwiperSlide>
          ))}
        </Swiper>
      </Box>
    );
  }

  if (!products || products.length === 0) {
    return null;
  }

  return (
    <Box className="full-width-container" sx={{ width: '100%' }}>
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: { xs: 2, md: 3 },
        flexWrap: 'wrap',
        gap: 1
      }}>
        <Typography
          variant="h4"
          component="h2"
          sx={{
            fontSize: { xs: '1.5rem', sm: '1.75rem', md: '2rem' },
            fontWeight: 600,
            position: 'relative',
            '&::after': {
              content: '""',
              position: 'absolute',
              bottom: -8,
              left: 0,
              width: 60,
              height: 3,
              backgroundColor: theme.palette.primary.main,
              borderRadius: 1.5
            }
          }}
        >
          {title}
        </Typography>
        <Button
          component={Link}
          to={viewAllLink}
          color="primary"
          variant="outlined"
          size={isMobile ? 'small' : 'medium'}
          endIcon={<ArrowForward />}
          sx={{
            borderRadius: '4px',
            fontWeight: 500,
            transition: 'all 0.3s ease',
            '&:hover': {
              backgroundColor: alpha(theme.palette.primary.main, 0.05),
              transform: 'translateY(-2px)'
            }
          }}
        >
          View All
        </Button>
      </Box>
      <Swiper
        modules={[Navigation, Pagination]}
        spaceBetween={16}
        slidesPerView={getSlidesPerView()}
        navigation
        pagination={{ clickable: true }}
      >
        {products.map((product) => {
          // Map the product to the format expected by ProductCard
          const mappedProduct = {
            id: product.id.toString(),
            name: product.name,
            price: product.variants && product.variants.length > 0 ? product.variants[0].price : 0,
            sku: product.variants && product.variants.length > 0 ? product.variants[0].sku || '' : '',
            stockQuantity: product.stockQuantity || 0,
            imageUrl: product.imageUrl || '',
            mainImage: product.mainImage || '',
            variants: product.variants?.map(v => ({
              id: v.id.toString(),
              size: v.size || '',
              price: v.price,
              mrp: v.mrp,
              stockQuantity: v.stockQuantity,
              isActive: v.isActive
            }))
          };

          return (
            <SwiperSlide key={product.id}>
              <ProductCard
                product={mappedProduct}
                onQuickAdd={handleQuickAdd}
              />
            </SwiperSlide>
          );
        })}
      </Swiper>

      {/* Quick Add Drawer */}
      <QuickAddDrawer
        open={quickAddDrawerOpen}
        onClose={handleDrawerClose}
        product={selectedProduct}
        onSuccess={handleAddSuccess}
        isMobile={isMobile}
      />
    </Box>
  );
};

export default ProductCarousel;
