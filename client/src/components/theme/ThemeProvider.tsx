import React, { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { ThemeProvider as MuiThemeProvider, createTheme, CssBaseline } from '@mui/material';
import { RootState } from '../../store';
import { fetchActiveTheme } from '../../store/slices/themeSlice';
import { AppDispatch } from '../../store';

interface CustomThemeProviderProps {
  children: React.ReactNode;
}

const CustomThemeProvider: React.FC<CustomThemeProviderProps> = ({ children }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { activeTheme } = useSelector((state: RootState) => state.theme);

  useEffect(() => {
    dispatch(fetchActiveTheme());
  }, [dispatch]);

  // Create a theme instance based on the active theme configuration
  const theme = useMemo(() => {
    if (!activeTheme) {
      // Default theme if no active theme is available
      return createTheme({
        palette: {
          primary: {
            main: '#ff3f6c',
          },
          secondary: {
            main: '#282c3f',
          },
          background: {
            default: '#ffffff',
            paper: '#ffffff',
          },
          text: {
            primary: '#282c3f',
            secondary: '#94969f',
          },
        },
        typography: {
          fontFamily: "'Roboto', sans-serif",
          h1: {
            fontFamily: "'Poppins', sans-serif",
          },
          h2: {
            fontFamily: "'Poppins', sans-serif",
          },
          h3: {
            fontFamily: "'Poppins', sans-serif",
          },
          h4: {
            fontFamily: "'Poppins', sans-serif",
          },
          h5: {
            fontFamily: "'Poppins', sans-serif",
          },
          h6: {
            fontFamily: "'Poppins', sans-serif",
          },
        },
        components: {
          MuiButton: {
            styleOverrides: {
              root: {
                borderRadius: '4px',
              },
            },
          },
          MuiCard: {
            styleOverrides: {
              root: {
                borderRadius: '8px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              },
            },
          },
          MuiCssBaseline: {
            styleOverrides: `
              html, body, #root {
                height: 100%;
                width: 100%;
                margin: 0;
                padding: 0;
                overflow: auto;
              }
            `,
          },
        },
      });
    }

    // Create a theme based on the active theme configuration
    return createTheme({
      palette: {
        primary: {
          main: activeTheme.primaryColor,
        },
        secondary: {
          main: activeTheme.secondaryColor,
        },
        success: {
          main: activeTheme.accentColor,
        },
        background: {
          default: activeTheme.backgroundPrimaryColor,
          paper: activeTheme.cardBackgroundColor,
        },
        text: {
          primary: activeTheme.textPrimaryColor,
          secondary: activeTheme.textSecondaryColor,
        },
      },
      typography: {
        fontFamily: activeTheme.bodyFontFamily,
        fontSize: parseInt(activeTheme.fontBaseSize) || 16,
        h1: {
          fontFamily: activeTheme.headingFontFamily,
        },
        h2: {
          fontFamily: activeTheme.headingFontFamily,
        },
        h3: {
          fontFamily: activeTheme.headingFontFamily,
        },
        h4: {
          fontFamily: activeTheme.headingFontFamily,
        },
        h5: {
          fontFamily: activeTheme.headingFontFamily,
        },
        h6: {
          fontFamily: activeTheme.headingFontFamily,
        },
      },
      components: {
        MuiButton: {
          styleOverrides: {
            root: {
              borderRadius: activeTheme.buttonBorderRadius,
            },
            contained: {
              backgroundColor: activeTheme.buttonPrimaryColor,
              color: activeTheme.buttonTextColor,
              '&:hover': {
                backgroundColor: activeTheme.buttonPrimaryColor,
                opacity: 0.9,
              },
            },
            outlined: {
              borderColor: activeTheme.buttonPrimaryColor,
              color: activeTheme.buttonPrimaryColor,
            },
          },
        },
        MuiCard: {
          styleOverrides: {
            root: {
              backgroundColor: activeTheme.cardBackgroundColor,
              borderRadius: activeTheme.cardBorderRadius,
              boxShadow: activeTheme.cardShadow,
              borderColor: activeTheme.cardBorderColor,
            },
          },
        },
        MuiAppBar: {
          styleOverrides: {
            root: {
              backgroundColor: activeTheme.headerBackgroundColor,
              color: activeTheme.headerTextColor,
            },
          },
        },
        MuiCssBaseline: {
          styleOverrides: `
            html, body, #root {
              height: 100%;
              width: 100%;
              margin: 0;
              padding: 0;
              overflow: auto;
            }
            
            a {
              color: ${activeTheme.navLinkColor};
              text-decoration: none;
            }
            
            a:hover {
              color: ${activeTheme.navLinkHoverColor};
            }
            
            .MuiContainer-root {
              max-width: ${activeTheme.containerMaxWidth} !important;
              padding-left: ${activeTheme.containerPadding} !important;
              padding-right: ${activeTheme.containerPadding} !important;
            }
            
            .footer {
              background-color: ${activeTheme.footerBackgroundColor};
              color: ${activeTheme.footerTextColor};
            }
            
            ${activeTheme.customCSS || ''}
          `,
        },
        MuiTextField: {
          styleOverrides: {
            root: {
              '& .MuiOutlinedInput-root': {
                borderRadius: activeTheme.inputBorderRadius,
                '& fieldset': {
                  borderColor: activeTheme.inputBorderColor,
                },
                '&:hover fieldset': {
                  borderColor: activeTheme.inputFocusBorderColor,
                },
                '&.Mui-focused fieldset': {
                  borderColor: activeTheme.inputFocusBorderColor,
                },
              },
            },
          },
        },
      },
    });
  }, [activeTheme]);

  // Generate CSS variables for the theme
  useEffect(() => {
    if (activeTheme) {
      const root = document.documentElement;
      
      // Set CSS variables for colors
      root.style.setProperty('--primary-color', activeTheme.primaryColor);
      root.style.setProperty('--secondary-color', activeTheme.secondaryColor);
      root.style.setProperty('--accent-color', activeTheme.accentColor);
      root.style.setProperty('--text-primary-color', activeTheme.textPrimaryColor);
      root.style.setProperty('--text-secondary-color', activeTheme.textSecondaryColor);
      root.style.setProperty('--text-light-color', activeTheme.textLightColor);
      root.style.setProperty('--background-primary-color', activeTheme.backgroundPrimaryColor);
      root.style.setProperty('--background-secondary-color', activeTheme.backgroundSecondaryColor);
      root.style.setProperty('--background-accent-color', activeTheme.backgroundAccentColor);
      
      // Set CSS variables for buttons
      root.style.setProperty('--button-primary-color', activeTheme.buttonPrimaryColor);
      root.style.setProperty('--button-secondary-color', activeTheme.buttonSecondaryColor);
      root.style.setProperty('--button-text-color', activeTheme.buttonTextColor);
      root.style.setProperty('--button-border-radius', activeTheme.buttonBorderRadius);
      
      // Set CSS variables for cards
      root.style.setProperty('--card-background-color', activeTheme.cardBackgroundColor);
      root.style.setProperty('--card-border-color', activeTheme.cardBorderColor);
      root.style.setProperty('--card-border-radius', activeTheme.cardBorderRadius);
      root.style.setProperty('--card-shadow', activeTheme.cardShadow);
      
      // Set CSS variables for typography
      root.style.setProperty('--heading-font-family', activeTheme.headingFontFamily);
      root.style.setProperty('--body-font-family', activeTheme.bodyFontFamily);
      root.style.setProperty('--font-base-size', activeTheme.fontBaseSize);
      
      // Set CSS variables for spacing
      root.style.setProperty('--spacing-unit', activeTheme.spacingUnit);
      root.style.setProperty('--container-max-width', activeTheme.containerMaxWidth);
      root.style.setProperty('--container-padding', activeTheme.containerPadding);
      
      // Set CSS variables for header
      root.style.setProperty('--header-background-color', activeTheme.headerBackgroundColor);
      root.style.setProperty('--header-text-color', activeTheme.headerTextColor);
      root.style.setProperty('--header-height', activeTheme.headerHeight);
      
      // Set CSS variables for footer
      root.style.setProperty('--footer-background-color', activeTheme.footerBackgroundColor);
      root.style.setProperty('--footer-text-color', activeTheme.footerTextColor);
      
      // Set CSS variables for navigation
      root.style.setProperty('--nav-link-color', activeTheme.navLinkColor);
      root.style.setProperty('--nav-link-active-color', activeTheme.navLinkActiveColor);
      root.style.setProperty('--nav-link-hover-color', activeTheme.navLinkHoverColor);
      
      // Set CSS variables for forms
      root.style.setProperty('--input-background-color', activeTheme.inputBackgroundColor);
      root.style.setProperty('--input-border-color', activeTheme.inputBorderColor);
      root.style.setProperty('--input-border-radius', activeTheme.inputBorderRadius);
      root.style.setProperty('--input-focus-border-color', activeTheme.inputFocusBorderColor);
    }
  }, [activeTheme]);

  return (
    <MuiThemeProvider theme={theme}>
      <CssBaseline />
      {children}
    </MuiThemeProvider>
  );
};

export default CustomThemeProvider;
