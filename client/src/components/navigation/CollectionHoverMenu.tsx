import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { AppDispatch, RootState } from '../../store';
import { fetchCollections } from '../../store/slices/collectionSlice';
import {
  Box,
  Typography,
  Popper,
  Paper,
  Fade,
  styled,
  alpha,
  Card,
  CardMedia,
  CardContent,
  CardActionArea
} from '@mui/material';
import { LocalOffer } from '@mui/icons-material';

// Styled components for premium look
const MenuContainer = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: theme.spacing(1.5),
  boxShadow: '0 10px 30px rgba(0, 0, 0, 0.15)',
  width: '100%',
  maxWidth: 1100,
  overflow: 'hidden',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  background: `linear-gradient(to bottom, ${alpha(theme.palette.grey[100], 0.98)}, ${theme.palette.background.paper})`,
}));

const CollectionCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  borderRadius: theme.spacing(1.5),
  overflow: 'hidden',
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  boxShadow: '0 5px 15px rgba(0,0,0,0.08)',
  border: `1px solid ${alpha(theme.palette.grey[300], 0.5)}`,
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: '0 15px 30px rgba(0, 0, 0, 0.15)',
  },
}));

const CollectionMedia = styled(CardMedia)(() => ({
  height: 180,
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'linear-gradient(to bottom, rgba(0,0,0,0), rgba(0,0,0,0.3))',
  }
}));

const SaleOverlay = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: theme.spacing(1),
  right: theme.spacing(1),
  backgroundColor: theme.palette.error.main,
  color: theme.palette.error.contrastText,
  padding: theme.spacing(0.5, 1),
  borderRadius: theme.spacing(0.5),
  fontWeight: 'bold',
  display: 'flex',
  alignItems: 'center',
  fontSize: '0.75rem',
  zIndex: 2,
  boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
  maxWidth: '40%',
  overflow: 'hidden',
}));

const CollectionTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  fontSize: '1.1rem',
  marginBottom: theme.spacing(0.5),
  color: theme.palette.text.primary,
  display: '-webkit-box',
  WebkitLineClamp: 2,
  WebkitBoxOrient: 'vertical',
  overflow: 'hidden',
  lineHeight: 1.2,
}));

const CollectionDescription = styled(Typography)(({ theme }) => ({
  color: alpha(theme.palette.text.primary, 0.7),
  fontSize: '0.85rem',
  display: '-webkit-box',
  WebkitLineClamp: 2,
  WebkitBoxOrient: 'vertical',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  lineHeight: 1.4,
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  marginBottom: theme.spacing(3),
  position: 'relative',
  paddingBottom: theme.spacing(1),
  fontSize: '1.5rem',
  color: theme.palette.primary.main,
  '&:after': {
    content: '""',
    position: 'absolute',
    bottom: 0,
    left: 0,
    width: 60,
    height: 3,
    backgroundColor: theme.palette.primary.main,
  },
}));

interface CollectionHoverMenuProps {
  anchorEl: HTMLElement | null;
  open: boolean;
  onClose: () => void;
}

const CollectionHoverMenu: React.FC<CollectionHoverMenuProps> = ({ anchorEl, open, onClose }) => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  // const theme = useTheme(); // Uncomment if needed for future styling
  const { collections } = useSelector((state: RootState) => state.collections);

  useEffect(() => {
    if (open && collections.length === 0) {
      dispatch(fetchCollections());
    }
  }, [open, collections.length, dispatch]);

  const handleCollectionClick = (collectionId: number) => {
    navigate(`/collection/${collectionId}`);
    onClose();
  };

  // Check if collection name includes "sale" to mark it as a sale collection
  const isSaleCollection = (name: string) => {
    return name.toLowerCase().includes('sale');
  };

  return (
    <Popper
      open={open}
      anchorEl={anchorEl}
      placement="bottom-start"
      transition
      style={{ zIndex: 1300 }}
    >
      {({ TransitionProps }) => (
        <Fade {...TransitionProps} timeout={350}>
          <MenuContainer>
            <SectionTitle variant="h6">Explore Our Collections</SectionTitle>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', mx: -1.5 }}>
              {collections.map((collection) => (
                <Box key={collection.id} sx={{ width: '25%', p: 1.5 }}>
                  <CollectionCard>
                    <CardActionArea onClick={() => handleCollectionClick(collection.id)}>
                      <CollectionMedia
                        image={collection.imageUrl || "https://images.unsplash.com/photo-1558769132-cb1aea458c5e?q=80&w=1000"}
                        title={collection.name}
                      >
                        {isSaleCollection(collection.name) && (
                          <SaleOverlay>
                            <LocalOffer fontSize="small" sx={{ mr: 0.5 }} />
                            SALE
                          </SaleOverlay>
                        )}
                      </CollectionMedia>
                      <CardContent sx={{ flexGrow: 1, p: 2 }}>
                        <CollectionTitle variant="subtitle1">
                          {collection.name}
                        </CollectionTitle>
                        <CollectionDescription variant="body2">
                          {collection.description || "Discover our exclusive collection featuring the latest trends and timeless classics."}
                        </CollectionDescription>
                      </CardContent>
                    </CardActionArea>
                  </CollectionCard>
                </Box>
              ))}
            </Box>

            {collections.length === 0 && (
              <Box sx={{ display: 'flex', flexWrap: 'wrap', mx: -1.5 }}>
                {[1, 2, 3, 4].map((index) => (
                  <Box key={index} sx={{ width: '25%', p: 1.5 }}>
                    <CollectionCard>
                      <CardActionArea>
                        <CollectionMedia
                          image={`https://images.unsplash.com/photo-155876913${index}-cb1aea458c5e?q=80&w=1000`}
                          title={`Sample Collection ${index}`}
                        />
                        <CardContent sx={{ flexGrow: 1, p: 2 }}>
                          <CollectionTitle variant="subtitle1">
                            {index === 1 ? "Summer Collection" :
                             index === 2 ? "Wedding Collection" :
                             index === 3 ? "Festival Collection" : "Ethnic Collection"}
                          </CollectionTitle>
                          <CollectionDescription variant="body2">
                            Discover our exclusive collection featuring the latest trends and timeless classics.
                          </CollectionDescription>
                        </CardContent>
                      </CardActionArea>
                    </CollectionCard>
                  </Box>
                ))}
              </Box>
            )}
          </MenuContainer>
        </Fade>
      )}
    </Popper>
  );
};

export default CollectionHoverMenu;
