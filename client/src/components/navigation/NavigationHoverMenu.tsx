import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { AppDispatch, RootState } from '../../store';
import { fetchNavigationMenus, NavigationMenu } from '../../store/slices/navigationSlice';
import {
  Box,
  Typography,
  Popper,
  Paper,
  Fade,
  List,
  ListItem,
  ListItemText,
  styled,
  alpha,
  Chip,
  Grid
} from '@mui/material';
import { LocalOffer } from '@mui/icons-material';

// Styled components for premium look
const MenuContainer = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: theme.spacing(1.5),
  boxShadow: '0 10px 30px rgba(0, 0, 0, 0.15)',
  width: '100%',
  maxWidth: 1100,
  overflow: 'hidden',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  background: `linear-gradient(to bottom, ${alpha(theme.palette.grey[100], 0.98)}, ${theme.palette.background.paper})`,
}));

const MenuColumn = styled(Box)(({ theme }) => ({
  padding: theme.spacing(0, 2),
  borderRight: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
  '&:last-child': {
    borderRight: 'none',
  }
}));

const MenuTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  marginBottom: theme.spacing(1.5),
  paddingBottom: theme.spacing(1),
  borderBottom: `2px solid ${theme.palette.primary.main}`,
  display: 'inline-block',
  color: theme.palette.primary.main,
}));

const MenuItem = styled(ListItem)(({ theme }) => ({
  borderRadius: theme.spacing(0.5),
  padding: theme.spacing(0.5, 1.5),
  margin: theme.spacing(0.3, 0),
  color: theme.palette.text.secondary,
  '&:hover': {
    backgroundColor: alpha(theme.palette.grey[200], 0.8),
    cursor: 'pointer',
    transform: 'translateX(5px)',
    color: theme.palette.text.primary,
  },
  transition: 'all 0.2s ease',
  fontSize: '0.9rem',
}));

const SaleTag = styled(Chip)(({ theme }) => ({
  backgroundColor: theme.palette.error.main,
  color: theme.palette.error.contrastText,
  fontWeight: 'bold',
  fontSize: '0.7rem',
  height: 20,
  marginLeft: theme.spacing(1),
}));

interface NavigationHoverMenuProps {
  anchorEl: HTMLElement | null;
  open: boolean;
  onClose: () => void;
  menuName: string;
}

const NavigationHoverMenu: React.FC<NavigationHoverMenuProps> = ({
  anchorEl,
  open,
  onClose,
  menuName
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { menus } = useSelector((state: RootState) => state.navigation);

  useEffect(() => {
    if (open && menus.length === 0) {
      dispatch(fetchNavigationMenus());
    }
  }, [open, menus.length, dispatch]);

  const handleMenuItemClick = (url: string) => {
    navigate(url);
    onClose();
  };

  // Find the parent menu that matches the menuName
  const parentMenu = menus.find(menu =>
    menu.name.toLowerCase() === menuName.toLowerCase() && !menu.parentId
  );

  // Get all child menus for this parent
  const childMenus = parentMenu
    ? menus.filter(menu => menu.parentId === parentMenu.id)
    : [];

  // Group child menus into columns (max 8 items per column)
  const groupMenus = (menus: NavigationMenu[], itemsPerColumn: number = 8) => {
    const result: NavigationMenu[][] = [];
    for (let i = 0; i < menus.length; i += itemsPerColumn) {
      result.push(menus.slice(i, i + itemsPerColumn));
    }
    return result;
  };

  const menuColumns = groupMenus(childMenus);

  // If no parent menu found or no children, don't render anything
  if (!parentMenu || childMenus.length === 0) {
    return null;
  }

  return (
    <Popper
      open={open}
      anchorEl={anchorEl}
      placement="bottom-start"
      transition
      style={{ zIndex: 1300 }}
    >
      {({ TransitionProps }) => (
        <Fade {...TransitionProps} timeout={350}>
          <MenuContainer>
            <Grid container spacing={2}>
              {menuColumns.map((column, columnIndex) => (
                <Grid item xs={Math.floor(12 / menuColumns.length)} key={columnIndex}>
                  <MenuColumn>
                    {columnIndex === 0 && (
                      <MenuTitle variant="subtitle1">
                        {parentMenu.name}
                      </MenuTitle>
                    )}
                    <List dense disablePadding>
                      {column.map((menu) => (
                        <MenuItem key={menu.id} onClick={() => handleMenuItemClick(menu.url)}>
                          <ListItemText
                            primary={
                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                {menu.name}
                                {menu.icon === 'offer' && (
                                  <SaleTag
                                    size="small"
                                    label="SALE"
                                    color="error"
                                    icon={<LocalOffer fontSize="small" />}
                                  />
                                )}
                              </Box>
                            }
                          />
                        </MenuItem>
                      ))}
                    </List>
                  </MenuColumn>
                </Grid>
              ))}
            </Grid>
          </MenuContainer>
        </Fade>
      )}
    </Popper>
  );
};

export default NavigationHoverMenu;
