import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { AppDispatch, RootState } from '../../store';
import { fetchCategories } from '../../store/slices/categorySlice';
import {
  Box,
  Typography,
  Popper,
  Paper,
  Fade,
  List,
  ListItem,
  ListItemText,
  styled,
  alpha,
  useTheme,
  CardMedia
} from '@mui/material';
import { LocalOffer } from '@mui/icons-material';

// Styled components for premium look
const MenuContainer = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: theme.spacing(1.5),
  boxShadow: '0 10px 30px rgba(0, 0, 0, 0.15)',
  width: '100%',
  maxWidth: 1100,
  overflow: 'hidden',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  background: `linear-gradient(to bottom, ${alpha(theme.palette.grey[100], 0.98)}, ${theme.palette.background.paper})`,
}));

const SubCategoryItem = styled(ListItem)(({ theme }) => ({
  borderRadius: theme.spacing(0.5),
  padding: theme.spacing(0.5, 1.5),
  margin: theme.spacing(0.3, 0),
  color: theme.palette.text.secondary,
  '&:hover': {
    backgroundColor: alpha(theme.palette.grey[200], 0.8),
    cursor: 'pointer',
    transform: 'translateX(5px)',
    color: theme.palette.text.primary,
  },
  transition: 'all 0.2s ease',
  fontSize: '0.9rem',
}));

const SaleTag = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.error.main,
  color: theme.palette.error.contrastText,
  padding: theme.spacing(0.2, 0.8),
  borderRadius: theme.spacing(0.5),
  fontSize: '0.7rem',
  fontWeight: 'bold',
  display: 'inline-flex',
  alignItems: 'center',
  marginLeft: theme.spacing(1),
  boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
}));

const CategoryImage = styled(CardMedia)(({ theme }) => ({
  height: 180,
  borderRadius: theme.spacing(1.5),
  marginBottom: theme.spacing(1.5),
  backgroundSize: 'cover',
  backgroundPosition: 'center',
  position: 'relative',
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  boxShadow: '0 5px 15px rgba(0,0,0,0.1)',
  '&:hover': {
    transform: 'translateY(-5px)',
    boxShadow: '0 8px 20px rgba(0,0,0,0.15)',
  },
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'linear-gradient(to bottom, rgba(0,0,0,0), rgba(0,0,0,0.7))',
    borderRadius: theme.spacing(1.5),
  }
}));

const CategoryImageTitle = styled(Typography)(({ theme }) => ({
  position: 'absolute',
  bottom: theme.spacing(1.5),
  left: theme.spacing(1.5),
  right: theme.spacing(1.5),
  color: '#fff',
  fontWeight: 700,
  zIndex: 1,
  textShadow: '0 2px 4px rgba(0,0,0,0.8)',
  fontSize: '1.1rem',
  letterSpacing: '0.5px',
  lineHeight: 1.2,
  display: '-webkit-box',
  WebkitLineClamp: 2,
  WebkitBoxOrient: 'vertical',
  overflow: 'hidden',
}));

const FeaturedImage = styled('img')(({ theme }) => ({
  width: '100%',
  height: 220,
  objectFit: 'cover',
  borderRadius: theme.spacing(1.5),
  marginTop: theme.spacing(2),
  boxShadow: '0 5px 15px rgba(0,0,0,0.1)',
  transition: 'transform 0.3s ease',
  '&:hover': {
    transform: 'scale(1.02)',
  },
}));



const GridContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexWrap: 'wrap',
  margin: theme.spacing(-1.5),
}));

const GridItem = styled(Box)(({ theme }) => ({
  padding: theme.spacing(1.5),
  width: '33.33%',
}));

interface CategoryHoverMenuProps {
  anchorEl: HTMLElement | null;
  open: boolean;
  onClose: () => void;
}

const CategoryHoverMenu: React.FC<CategoryHoverMenuProps> = ({ anchorEl, open, onClose }) => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const theme = useTheme();
  const { categories } = useSelector((state: RootState) => state.categories);

  // Get parent categories (those with no parent)
  const parentCategories = categories.filter(cat => cat.parentId === null);

  // Get sale categories
  const saleCategories = categories.filter(cat => cat.isSale);

  useEffect(() => {
    if (open && categories.length === 0) {
      dispatch(fetchCategories());
    }
  }, [open, categories.length, dispatch]);

  const handleCategoryClick = (categoryId: number) => {
    navigate(`/category/${categoryId}`);
    onClose();
  };

  const getChildCategories = (parentId: number) => {
    return categories.filter(cat => cat.parentId === parentId);
  };

  return (
    <Popper
      open={open}
      anchorEl={anchorEl}
      placement="bottom-start"
      transition
      style={{ zIndex: 1300 }}
    >
      {({ TransitionProps }) => (
        <Fade {...TransitionProps} timeout={350}>
          <MenuContainer>
            <Box sx={{ display: 'flex' }}>
              {/* Left side - Categories */}
              <Box sx={{ width: '66.67%', pr: 3 }}>
                <Typography variant="h6" sx={{
                  fontWeight: 700,
                  mb: 2,
                  pb: 1,
                  borderBottom: `2px solid ${theme.palette.primary.main}`,
                  display: 'inline-block'
                }}>
                  Shop by Category
                </Typography>

                <GridContainer>
                  {parentCategories.map((category) => (
                    <GridItem key={category.id}>
                      {/* Category Image with Title */}
                      <Box sx={{ position: 'relative', cursor: 'pointer' }} onClick={() => handleCategoryClick(category.id)}>
                        <CategoryImage
                          image={category.imageUrl || "https://images.unsplash.com/photo-1610189020382-668a904cb5dd?q=80&w=1000"}
                          title={category.name}
                        />
                        <CategoryImageTitle variant="subtitle1">
                          <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
                            <Box sx={{ mb: category.isSale ? 0.5 : 0 }}>
                              {category.name}
                            </Box>
                            {category.isSale && (
                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <SaleTag sx={{ flexShrink: 0 }}>
                                  <LocalOffer fontSize="small" sx={{ mr: 0.5 }} />
                                  SALE
                                </SaleTag>
                              </Box>
                            )}
                          </Box>
                        </CategoryImageTitle>
                      </Box>

                      {/* Subcategories List */}
                      <List dense disablePadding sx={{ mt: 1 }}>
                        {getChildCategories(category.id).map((child) => (
                          <SubCategoryItem key={child.id} onClick={() => handleCategoryClick(child.id)}>
                            <ListItemText
                              primary={
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  {child.name}
                                  {child.isSale && (
                                    <SaleTag>
                                      <LocalOffer fontSize="small" sx={{ mr: 0.5 }} />
                                      SALE
                                    </SaleTag>
                                  )}
                                </Box>
                              }
                            />
                          </SubCategoryItem>
                        ))}
                      </List>
                    </GridItem>
                  ))}
                </GridContainer>
              </Box>

              {/* Right side - Sale Items */}
              <Box sx={{ width: '33.33%', pl: 3, borderLeft: `1px solid ${alpha(theme.palette.primary.main, 0.1)}` }}>
                <Typography variant="h6" sx={{
                  fontWeight: 700,
                  mb: 2,
                  pb: 1,
                  borderBottom: `2px solid ${theme.palette.error.main}`,
                  display: 'inline-block',
                  color: theme.palette.error.main
                }}>
                  Sale & Offers
                </Typography>

                {/* Sale Category with Image */}
                <Box sx={{ position: 'relative', cursor: 'pointer', mb: 3 }} onClick={() => navigate('/sale')}>
                  <CategoryImage
                    image="https://images.unsplash.com/photo-1595032551995-8bfa9dc8d006?q=80&w=1000"
                    title="Sale Items"
                  />
                  <CategoryImageTitle variant="subtitle1">
                    <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
                      <Box sx={{ mb: 0.5 }}>
                        Special Offers
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <SaleTag sx={{ flexShrink: 0 }}>
                          <LocalOffer fontSize="small" sx={{ mr: 0.5 }} />
                          SALE
                        </SaleTag>
                      </Box>
                    </Box>
                  </CategoryImageTitle>
                </Box>

                {/* Sale Categories List */}
                <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1, color: theme.palette.text.secondary }}>
                  Trending Sale Categories
                </Typography>
                <List dense disablePadding sx={{ mb: 3 }}>
                  {saleCategories.slice(0, 5).map((category) => (
                    <SubCategoryItem key={category.id} onClick={() => handleCategoryClick(category.id)}>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            {category.name}
                            <SaleTag sx={{ ml: 1, transform: 'scale(0.8)' }}>
                              <LocalOffer fontSize="small" sx={{ mr: 0.5 }} />
                              SALE
                            </SaleTag>
                          </Box>
                        }
                      />
                    </SubCategoryItem>
                  ))}
                </List>

                {/* Featured Sale Image */}
                <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1, color: theme.palette.text.secondary }}>
                  Featured Collection
                </Typography>
                <FeaturedImage
                  src="https://images.unsplash.com/photo-1610189020382-668a904cb5dd?q=80&w=1000"
                  alt="Featured Sale"
                />
              </Box>
            </Box>
          </MenuContainer>
        </Fade>
      )}
    </Popper>
  );
};

export default CategoryHoverMenu;
