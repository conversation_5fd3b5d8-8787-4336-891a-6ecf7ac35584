import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  <PERSON>,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Paper
} from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import { requestOtp, verifyOtp } from '../../store/slices/authSlice';
import { RootState, AppDispatch } from '../../store';

interface OtpVerificationProps {
  onVerificationSuccess: () => void;
}

const OtpVerification: React.FC<OtpVerificationProps> = ({ onVerificationSuccess }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { loading, error, isAuthenticated } = useSelector((state: RootState) => state.auth);

  const [step, setStep] = useState<'request' | 'verify'>('request');
  const [email, setEmail] = useState('');
  const [otp, setOtp] = useState('');
  const [formErro<PERSON>, setFormErrors] = useState<Record<string, string>>({});
  const [timer, setTimer] = useState(0);

  useEffect(() => {
    if (isAuthenticated) {
      onVerificationSuccess();
    }
  }, [isAuthenticated, onVerificationSuccess]);

  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;
    
    if (timer > 0 && step === 'verify') {
      interval = setInterval(() => {
        setTimer((prevTimer) => prevTimer - 1);
      }, 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [timer, step]);

  const validateRequestForm = () => {
    const errors: Record<string, string> = {};

    if (!email) errors.email = 'Email is required';
    else if (!/\S+@\S+\.\S+/.test(email)) errors.email = 'Email is invalid';

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const validateVerifyForm = () => {
    const errors: Record<string, string> = {};

    if (!otp) errors.otp = 'OTP is required';
    else if (!/^[0-9]{6}$/.test(otp)) errors.otp = 'OTP must be 6 digits';

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleRequestOtp = async () => {
    if (validateRequestForm()) {
      try {
        const credentials = {
          email: email,
          phoneNumber: undefined
        };

        await dispatch(requestOtp(credentials)).unwrap();
        setStep('verify');
        setTimer(600); // 10 minutes in seconds
      } catch (error) {
        console.error('Error requesting OTP:', error);
      }
    }
  };

  const handleVerifyOtp = async () => {
    if (validateVerifyForm()) {
      try {
        const credentials = {
          email: email,
          phoneNumber: undefined,
          otp
        };

        await dispatch(verifyOtp(credentials)).unwrap();
        // onVerificationSuccess will be called via the useEffect when isAuthenticated changes
      } catch (error) {
        console.error('Error verifying OTP:', error);
      }
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    if (name === 'email') setEmail(value);
    else if (name === 'otp') setOtp(value);

    // Clear error when user types
    if (formErrors[name]) {
      setFormErrors({
        ...formErrors,
        [name]: ''
      });
    }
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  };

  return (
    <Paper elevation={0} sx={{ p: 3, mb: 3, border: '1px solid #e0e0e0' }}>
      <Typography variant="h6" gutterBottom>
        {step === 'request' ? 'Login with Email' : 'Verify OTP'}
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {step === 'request' ? (
        <>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Please enter your email to receive a verification code
          </Typography>

          <TextField
            name="email"
            label="Email"
            type="email"
            value={email}
            onChange={handleInputChange}
            fullWidth
            margin="normal"
            error={!!formErrors.email}
            helperText={formErrors.email}
            disabled={loading}
            size="small"
          />

          <Button
            type="button"
            fullWidth
            variant="contained"
            color="primary"
            onClick={handleRequestOtp}
            disabled={loading}
            sx={{ mt: 2 }}
          >
            {loading ? <CircularProgress size={24} /> : 'Send Verification Code'}
          </Button>
        </>
      ) : (
        <>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            We've sent a verification code to {email}
          </Typography>

          <TextField
            name="otp"
            label="Enter Verification Code"
            type="text"
            value={otp}
            onChange={handleInputChange}
            fullWidth
            margin="normal"
            error={!!formErrors.otp}
            helperText={formErrors.otp}
            disabled={loading}
            inputProps={{ maxLength: 6 }}
            size="small"
          />

          {timer > 0 && (
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Code expires in: {formatTime(timer)}
            </Typography>
          )}

          <Button
            type="button"
            fullWidth
            variant="contained"
            color="primary"
            onClick={handleVerifyOtp}
            disabled={loading}
            sx={{ mt: 2 }}
          >
            {loading ? <CircularProgress size={24} /> : 'Verify Code'}
          </Button>

          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
            <Button
              variant="text"
              color="primary"
              onClick={() => setStep('request')}
              disabled={loading}
              size="small"
            >
              Change Email
            </Button>

            <Button
              variant="text"
              color="primary"
              onClick={handleRequestOtp}
              disabled={loading || timer > 540} // Disable resend for first minute
              size="small"
            >
              Resend Code
            </Button>
          </Box>
        </>
      )}
    </Paper>
  );
};

export default OtpVerification;
