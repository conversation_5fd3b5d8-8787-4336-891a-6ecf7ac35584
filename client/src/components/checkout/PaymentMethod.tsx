import React from 'react';
import {
  Box,
  Typography,
  RadioGroup,
  FormControlLabel,
  Radio,
  FormHelperText
} from '@mui/material';
import { CreditCard, LocalAtm } from '@mui/icons-material';

interface PaymentMethodProps {
  paymentMethod: string;
  setPaymentMethod: (method: string) => void;
  errors: Record<string, string>;
}

const PaymentMethod: React.FC<PaymentMethodProps> = ({
  paymentMethod,
  setPaymentMethod,
  errors
}) => {
  return (
    <Box>
      <RadioGroup
        value={paymentMethod}
        onChange={(e) => setPaymentMethod(e.target.value)}
      >
        <Box sx={{ mb: 1 }}>
          <FormControlLabel
            value="COD"
            control={<Radio size="small" />}
            label={
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <LocalAtm sx={{ mr: 0.5, color: 'text.secondary', fontSize: '1.2rem' }} />
                <Typography variant="body2">Cash on Delivery</Typography>
              </Box>
            }
          />
        </Box>

        <Box>
          <FormControlLabel
            value="PREPAID"
            control={<Radio size="small" />}
            label={
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CreditCard sx={{ mr: 0.5, color: 'text.secondary', fontSize: '1.2rem' }} />
                <Typography variant="body2">Online Payment</Typography>
              </Box>
            }
          />
        </Box>
      </RadioGroup>

      {errors.paymentMethod && (
        <FormHelperText error>{errors.paymentMethod}</FormHelperText>
      )}
    </Box>
  );
};

export default PaymentMethod;
