import React, { ReactNode } from 'react';
import { Box } from '@mui/material';
import '../../styles/customerLayout.css';

interface CustomerLayoutProps {
  children: ReactNode;
  className?: string;
}

/**
 * CustomerLayout component provides a full-width layout for the customer portal
 * It removes left and right margins and applies consistent styling
 * All pages should use this layout for consistent container behavior
 */
const CustomerLayout: React.FC<CustomerLayoutProps> = ({ children, className = '' }) => {
  return (
    <Box
      className={`full-width-container ${className}`}
      component="div"
      sx={{
        width: '100%',
        maxWidth: '100%',
        margin: 0,
        padding: 0,
        boxSizing: 'border-box',
        overflow: 'hidden'
      }}
    >
      {children}
    </Box>
  );
};

export default CustomerLayout;
