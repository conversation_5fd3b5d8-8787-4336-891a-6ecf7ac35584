import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import {
  Typography,
  Container,
  Box,
  Paper,
  TextField,
  Button,
  Grid,
  Divider,
  Alert,
  CircularProgress,
  Tabs,
  Tab
} from '@mui/material';
import { requestOtp, verifyOtp, clearError } from '../store/slices/authSlice';
import { RootState, AppDispatch } from '../store';

const OtpLogin: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated, user, loading, error } = useSelector((state: RootState) => state.auth);

  const [step, setStep] = useState<'request' | 'verify'>('request');
  const [contactMethod, setContactMethod] = useState<'email' | 'phone'>('email');
  const [email, setEmail] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [otp, setOtp] = useState('');
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      const from = location.state?.from?.pathname || '/';
      navigate(from);
    }
  }, [isAuthenticated, navigate, location]);

  // Clear error when component unmounts
  useEffect(() => {
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);

  const validateRequestForm = () => {
    const errors: Record<string, string> = {};

    if (contactMethod === 'email') {
      if (!email) errors.email = 'Email is required';
      else if (!/\S+@\S+\.\S+/.test(email)) errors.email = 'Email is invalid';
    } else {
      if (!phoneNumber) errors.phoneNumber = 'Phone number is required';
      else if (!/^\+?[0-9]{10,15}$/.test(phoneNumber)) errors.phoneNumber = 'Phone number is invalid';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const validateVerifyForm = () => {
    const errors: Record<string, string> = {};

    if (!otp) errors.otp = 'OTP is required';
    else if (!/^[0-9]{6}$/.test(otp)) errors.otp = 'OTP must be 6 digits';

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleRequestOtp = async () => {
    if (validateRequestForm()) {
      try {
        console.log('Dispatching requestOtp action');
        const credentials = {
          email: contactMethod === 'email' ? email : undefined,
          phoneNumber: contactMethod === 'phone' ? phoneNumber : undefined
        };
        console.log('OTP credentials:', credentials);

        await dispatch(requestOtp(credentials)).unwrap();
        console.log('OTP requested successfully');

        setStep('verify');
      } catch (error) {
        console.error('Error requesting OTP:', error);
      }
    } else {
      console.log('Form validation failed');
    }
  };

  const handleVerifyOtp = async () => {
    if (validateVerifyForm()) {
      try {
        console.log('Dispatching verifyOtp action');
        const credentials = {
          email: contactMethod === 'email' ? email : undefined,
          phoneNumber: contactMethod === 'phone' ? phoneNumber : undefined,
          otp
        };
        console.log('Verify OTP credentials:', credentials);

        await dispatch(verifyOtp(credentials)).unwrap();
        console.log('OTP verified successfully');

        // Redirect will happen automatically due to the useEffect
      } catch (error) {
        console.error('Error verifying OTP:', error);
      }
    } else {
      console.log('Verify form validation failed');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    if (name === 'email') setEmail(value);
    else if (name === 'phoneNumber') setPhoneNumber(value);
    else if (name === 'otp') setOtp(value);

    // Clear error when user types
    if (formErrors[name]) {
      setFormErrors({
        ...formErrors,
        [name]: ''
      });
    }
  };

  const handleTabChange = (_event: React.SyntheticEvent, newValue: 'email' | 'phone') => {
    setContactMethod(newValue);
    setFormErrors({});
  };

  return (
    <Container maxWidth="sm" sx={{ mt: 8, mb: 4 }}>
      <Paper elevation={3} sx={{ p: 4, borderRadius: 2 }}>
        <Typography variant="h4" align="center" gutterBottom>
          {step === 'request' ? 'Login with OTP' : 'Verify OTP'}
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {step === 'request' ? (
          <>
            <Tabs
              value={contactMethod}
              onChange={handleTabChange}
              indicatorColor="primary"
              textColor="primary"
              variant="fullWidth"
              sx={{ mb: 3 }}
            >
              <Tab value="email" label="Email" />
              <Tab value="phone" label="Phone" />
            </Tabs>

            {contactMethod === 'email' ? (
              <TextField
                name="email"
                label="Email"
                type="email"
                value={email}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                error={!!formErrors.email}
                helperText={formErrors.email}
                disabled={loading}
              />
            ) : (
              <TextField
                name="phoneNumber"
                label="Phone Number"
                type="tel"
                value={phoneNumber}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                error={!!formErrors.phoneNumber}
                helperText={formErrors.phoneNumber || "Include country code (e.g., +1 for US)"}
                disabled={loading}
              />
            )}

            <Button
              type="button"
              fullWidth
              variant="contained"
              color="primary"
              size="large"
              onClick={handleRequestOtp}
              disabled={loading}
              sx={{ mt: 3, mb: 2 }}
            >
              {loading ? <CircularProgress size={24} /> : 'Send OTP'}
            </Button>
          </>
        ) : (
          <>
            <Typography variant="body1" gutterBottom>
              We've sent a verification code to {contactMethod === 'email' ? email : phoneNumber}
            </Typography>

            <TextField
              name="otp"
              label="Enter OTP"
              type="text"
              value={otp}
              onChange={handleInputChange}
              fullWidth
              margin="normal"
              error={!!formErrors.otp}
              helperText={formErrors.otp}
              disabled={loading}
              inputProps={{ maxLength: 6 }}
            />

            <Button
              type="button"
              fullWidth
              variant="contained"
              color="primary"
              size="large"
              onClick={handleVerifyOtp}
              disabled={loading}
              sx={{ mt: 3, mb: 2 }}
            >
              {loading ? <CircularProgress size={24} /> : 'Verify OTP'}
            </Button>

            <Box sx={{ textAlign: 'center', mt: 2 }}>
              <Button
                variant="text"
                color="primary"
                onClick={() => setStep('request')}
                disabled={loading}
              >
                Change {contactMethod === 'email' ? 'Email' : 'Phone Number'}
              </Button>

              <Button
                variant="text"
                color="primary"
                onClick={handleRequestOtp}
                disabled={loading}
                sx={{ ml: 2 }}
              >
                Resend OTP
              </Button>
            </Box>
          </>
        )}

        <Divider sx={{ my: 3 }} />

        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="body1">
            Want to login with password?{' '}
            <Link to="/login" style={{ color: '#ff3f6c', textDecoration: 'none', fontWeight: 'bold' }}>
              Login
            </Link>
          </Typography>
        </Box>

        <Box sx={{ textAlign: 'center', mt: 2 }}>
          <Typography variant="body2" color="text.secondary">
            Are you an admin?{' '}
            <Link to="/admin/login" style={{ color: '#ff3f6c', textDecoration: 'none' }}>
              Admin Login
            </Link>
          </Typography>
        </Box>
      </Paper>
    </Container>
  );
};

export default OtpLogin;
