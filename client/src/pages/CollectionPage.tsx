import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useParams } from 'react-router-dom';
import { RootState } from '../store';
import { fetchCategoryById } from '../store/slices/categorySlice';
import { fetchCollectionById } from '../store/slices/collectionSlice';
import {
  Box,
  Typography,
  Breadcrumbs,
  Snackbar,
  Container,
} from '@mui/material';
import CustomerLayout from '../components/layout/CustomerLayout';
import '../styles/customerLayout.css';
import ProductCard from '../components/common/ProductCard';
import ProductFilters from '../components/filters/ProductFilters';
import QuickAddDrawer from '../components/common/QuickAddDrawer';
import { fetchProducts, Product } from '../store/slices/productSlice';

const CollectionPage: React.FC = () => {
  const dispatch = useDispatch();
  const { categoryId, collectionId } = useParams();

  // State definitions with proper typing
  const [selectedProduct, setSelectedProduct] = useState<any>(null);
  const [quickAddDrawerOpen, setQuickAddDrawerOpen] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [isMobile, setIsMobile] = useState(window.innerWidth < 600);

  // Filter states
  const [selectedProductType, setSelectedProductType] = useState<string[]>([]);
  const [selectedSizes, setSelectedSizes] = useState<string[]>([]);
  const [selectedColors, setSelectedColors] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState('popularity');
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);

  // Get data from Redux store
  const { products } = useSelector((state: RootState) => state.products);
  const { category, loading: categoryLoading } = useSelector((state: RootState) => state.categories);
  const { collection, loading: collectionLoading } = useSelector((state: RootState) => state.collections);

  // Fetch products when component mounts or when categoryId/collectionId changes
  useEffect(() => {
    const params: any = {
      page: 1,
      pageSize: 20,
      isActive: true
    };

    if (categoryId) {
      params.categoryId = parseInt(categoryId);
    }

    if (collectionId) {
      params.collectionId = parseInt(collectionId);
    }

    // @ts-ignore - Ignore type error for now
    dispatch(fetchProducts(params));
  }, [dispatch, categoryId, collectionId]);

  // Fetch category or collection details
  useEffect(() => {
    if (categoryId) {
      // @ts-ignore
      dispatch(fetchCategoryById(parseInt(categoryId)));
    } else if (collectionId) {
      // @ts-ignore
      dispatch(fetchCollectionById(parseInt(collectionId)));
    }
  }, [dispatch, categoryId, collectionId]);

  // Handle window resize for mobile detection
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 600);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Update filteredProducts when products change or filters change
  useEffect(() => {
    if (!products) return;

    console.log('Filtering products with:', {
      selectedProductType,
      selectedSizes,
      selectedColors,
      sortBy
    });

    let filtered = [...products];

    // Apply product type filter
    if (selectedProductType.length > 0) {
      filtered = filtered.filter(product => {
        // Check if product type name matches any selected type
        return selectedProductType.includes(product.productTypeName || '');
      });
    }

    // Apply size filter
    if (selectedSizes.length > 0) {
      filtered = filtered.filter(product =>
        product.variants?.some(variant =>
          selectedSizes.includes(variant.size || '')));
    }

    // Apply color filter
    if (selectedColors.length > 0) {
      filtered = filtered.filter(product =>
        selectedColors.includes(product.color || ''));
    }

    // Apply sorting
    if (sortBy === 'priceAsc') {
      filtered.sort((a, b) => {
        const aPrice = a.variants && a.variants.length > 0 ? a.variants[0].price : 0;
        const bPrice = b.variants && b.variants.length > 0 ? b.variants[0].price : 0;
        return aPrice - bPrice;
      });
    } else if (sortBy === 'priceDesc') {
      filtered.sort((a, b) => {
        const aPrice = a.variants && a.variants.length > 0 ? a.variants[0].price : 0;
        const bPrice = b.variants && b.variants.length > 0 ? b.variants[0].price : 0;
        return bPrice - aPrice;
      });
    } else if (sortBy === 'newest') {
      // Sort by ID as a proxy for creation date (higher ID = newer)
      filtered.sort((a, b) => b.id - a.id);
    }

    console.log(`Filtered products: ${filtered.length} of ${products.length}`);
    setFilteredProducts(filtered);
  }, [products, selectedProductType, selectedSizes, selectedColors, sortBy]);


  const handleQuickAdd = (product: Product) => {
    // Convert the product to the format expected by QuickAddDrawer
    const convertedProduct = {
      ...product,
      id: product.id,
      price: product.variants && product.variants.length > 0 ? product.variants[0].price : 0,
      mrp: product.variants && product.variants.length > 0 ? product.variants[0].mrp : undefined,
      variants: product.variants?.map(v => ({
        ...v,
        id: v.id
      }))
    };

    setSelectedProduct(convertedProduct);
    setQuickAddDrawerOpen(true);
  };

  // Get page title
  const getTitle = () => {
    if (collectionId && collection) {
      return collection.name;
    } else if (categoryId && category) {
      return category.name;
    }
    return 'All Products';
  };

  return (
    <CustomerLayout>
      <Container className="content-container" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
            <Link to="/" style={{ textDecoration: 'none', color: 'inherit' }}>
              Home
            </Link>
            {(categoryLoading || collectionLoading) ? (
              <Typography color="text.secondary">Loading...</Typography>
            ) : (
              <Typography color="text.primary">{getTitle()}</Typography>
            )}
          </Breadcrumbs>

          <Typography variant="h4" component="h1" sx={{ fontWeight: 700, mb: 3 }}>
            {(categoryLoading || collectionLoading) ? 'Loading...' : getTitle()}
          </Typography>
        </Box>

      {/* Filters */}
      <Box sx={{ mb: 3 }}>
        <ProductFilters
          products={products || []}
          selectedProductType={selectedProductType}
          setSelectedProductType={setSelectedProductType}
          selectedSizes={selectedSizes}
          setSelectedSizes={setSelectedSizes}
          selectedColors={selectedColors}
          setSelectedColors={setSelectedColors}
          sortBy={sortBy}
          setSortBy={setSortBy}
        />
      </Box>

      {/* Products Grid */}
      <Box className="products-grid" sx={{
        gridTemplateColumns: {
          xs: 'repeat(2, 1fr)',
          sm: 'repeat(3, 1fr)',
          md: 'repeat(4, 1fr)',
          lg: 'repeat(5, 1fr)',
          xl: 'repeat(6, 1fr)'
        },
        gap: { xs: 1, sm: 2, md: 3 },
      }}>
        {filteredProducts && filteredProducts.length > 0 ? filteredProducts.map((product) => (
          <ProductCard
            key={product.id}
            product={{
              id: product.id.toString(),
              name: product.name,
              price: product.variants && product.variants.length > 0 ? product.variants[0].price : 0,
              sku: product.variants && product.variants.length > 0 ? product.variants[0].sku : '',
              stockQuantity: product.stockQuantity || 0,
              imageUrl: product.imageUrl,
              mainImage: product.mainImage,
              variants: product.variants?.map(v => ({
                id: v.id.toString(),
                size: v.size || '',
                price: v.price,
                mrp: v.mrp,
                stockQuantity: v.stockQuantity,
                isActive: v.isActive
              }))
            }}
            onQuickAdd={() => handleQuickAdd(product)}
          />
        )) : products && products.map((product) => (
          <ProductCard
            key={product.id}
            product={{
              id: product.id.toString(),
              name: product.name,
              price: product.variants && product.variants.length > 0 ? product.variants[0].price : 0,
              sku: product.variants && product.variants.length > 0 ? product.variants[0].sku : '',
              stockQuantity: product.stockQuantity || 0,
              imageUrl: product.imageUrl,
              mainImage: product.mainImage,
              variants: product.variants?.map(v => ({
                id: v.id.toString(),
                size: v.size || '',
                price: v.price,
                mrp: v.mrp,
                stockQuantity: v.stockQuantity,
                isActive: v.isActive
              }))
            }}
            onQuickAdd={() => handleQuickAdd(product)}
          />
        ))}
      </Box>

      {/* Quick Add Drawer */}
      <QuickAddDrawer
        open={quickAddDrawerOpen}
        onClose={() => setQuickAddDrawerOpen(false)}
        product={selectedProduct}
        onSuccess={(message) => {
          setSnackbarMessage(message);
          setSnackbarOpen(true);
        }}
        isMobile={isMobile}
      />

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={() => setSnackbarOpen(false)}
        message={snackbarMessage}
      />
      </Container>
    </CustomerLayout>
  );
};

export default CollectionPage;
