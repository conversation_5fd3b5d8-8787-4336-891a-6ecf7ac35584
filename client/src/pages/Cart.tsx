import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import {
  Typography,
  Container,
  Box,
  Paper,
  Grid as MuiGrid,
  Button,
  IconButton,
  Divider,
  Alert,
  Chip
} from '@mui/material';
import CustomerLayout from '../components/layout/CustomerLayout';
import '../styles/customerLayout.css';

import { Add, Remove, Close, LocalOffer, ConfirmationNumber } from '@mui/icons-material';
import { updateQuantity, removeFromCart, clearCart, applyCoupon } from '../store/slices/cartSlice';
import { RootState } from '../store';
import CouponDrawer from '../components/cart/CouponDrawer';
import api from '../services/api';

// Create a wrapper component for Grid with proper types
const Grid = (props: any) => <MuiGrid {...props} />;
const GridItem = (props: any) => <MuiGrid item {...props} />;

const Cart: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { items, totalItems, totalAmount, discountAmount, couponCode } = useSelector((state: RootState) => state.cart);
  const { isAuthenticated } = useSelector((state: RootState) => state.auth);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [maxSavings, setMaxSavings] = useState<number>(0);
  const [availableOffers, setAvailableOffers] = useState<number>(4);
  const [navigatingToCheckout, setNavigatingToCheckout] = useState(false);

  // Effect to fetch maximum possible savings when cart changes
  useEffect(() => {
    const fetchMaxSavings = async () => {
      if (totalItems === 0 || totalAmount === 0) {
        setMaxSavings(0);
        setAvailableOffers(0);
        return;
      }

      try {
        // Collect parameters for API call
        const productIds = items.map(item => item.productId);
        const categoryIds = items
          .filter(item => item.categoryId)
          .map(item => item.categoryId as number);
        const collectionIds = items
          .flatMap(item => item.collectionIds || []);

        // Build query parameters
        const params = new URLSearchParams();
        params.append('cartTotal', totalAmount.toString());

        // Add product IDs
        productIds.forEach(id => params.append('productIds', id.toString()));

        // Add category IDs
        categoryIds.forEach(id => params.append('categoryIds', id.toString()));

        // Add collection IDs
        collectionIds.forEach(id => params.append('collectionIds', id.toString()));

        console.log('Fetching coupons with params:', params.toString());

        // Make API call with parameters
        const response = await api.get(`/coupons/cart/available?${params.toString()}`);
        console.log('Available coupons response:', response.data);

        // Log the exact structure of the response to debug
        console.log('Response keys:', Object.keys(response.data));
        console.log('Response structure:', JSON.stringify(response.data, null, 2));

        // Extract data from response - API now returns camelCase property names
        const availableOffers = response.data.availableOffers || 0;
        const maxSavings = response.data.maxSavings || 0;

        console.log('API response values:', {
          availableOffers,
          maxSavings,
          responseData: response.data
        });

        // Use the values directly from the API response
        setMaxSavings(maxSavings);
        setAvailableOffers(availableOffers);

        console.log(`Set maxSavings to ${maxSavings} and availableOffers to ${availableOffers}`);
      } catch (err) {
        console.error('Error fetching max savings:', err);
        setMaxSavings(0);
        setAvailableOffers(0);
      }
    };

    if (totalItems > 0) {
      fetchMaxSavings();
    } else {
      setMaxSavings(0);
      setAvailableOffers(0);
    }
  }, [totalItems, totalAmount, items]);

  const handleUpdateQuantity = (productId: number, quantity: number, variantId?: number) => {
    if (quantity > 0) {
      // @ts-ignore
      dispatch(updateQuantity({ productId, quantity, variantId }));
      // Clear coupon when updating quantity
      if (couponCode) {
        // @ts-ignore
        dispatch(applyCoupon({ code: '', discountAmount: 0 }));
        setDrawerOpen(true); // Open drawer to reapply coupon
      }
    }
  };

  const handleRemoveItem = (productId: number, variantId?: number) => {
    // @ts-ignore
    dispatch(removeFromCart({ productId, variantId }));

    // Clear coupon when removing an item
    if (couponCode) {
      // @ts-ignore
      dispatch(applyCoupon({ code: '', discountAmount: 0 }));
      setDrawerOpen(true); // Open drawer to reapply coupon
    }
  };

  const handleClearCart = () => {
    if (window.confirm('Are you sure you want to clear your cart?')) {
      // @ts-ignore
      dispatch(clearCart());
    }
  };

  // Set a flag when navigating to checkout to prevent coupon reset
  const handleCheckout = () => {
    setNavigatingToCheckout(true);
    navigate('/checkout');
  };

  // Effect to reset coupon when navigating away from cart page (except to checkout)
  useEffect(() => {
    return () => {
      // Only reset coupon if not navigating to checkout
      if (!navigatingToCheckout && couponCode) {
        console.log('Navigating away from cart - resetting coupon');
        // @ts-ignore
        dispatch(applyCoupon({ code: '', discountAmount: 0 }));
      }
    };
  }, [dispatch, couponCode, navigatingToCheckout]);

  if (items.length === 0) {
    return (
      <CustomerLayout>
        <Container className="content-container section-spacing">
          <Typography variant="h5" gutterBottom>Your cart is empty</Typography>
          <Button
            variant="contained"
            component={Link}
            to="/products"
            sx={{
              mt: 2,
              backgroundColor: '#ff3f6c',
              '&:hover': { backgroundColor: '#ff527b' }
            }}
          >
            Continue Shopping
          </Button>
        </Container>
      </CustomerLayout>
    );
  }

  return (
    <CustomerLayout>
      <Container className="content-container section-spacing">
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 600, color: '#282c3f' }}>Shopping Cart</Typography>
      <Grid container spacing={4}>
        <GridItem xs={12} md={8}>
          {items.map((item) => (
            <Paper key={`${item.productId}-${item.variantId}`} sx={{ p: 2, mb: 2, border: '1px solid #e0e0e0', width: '100%', borderRadius: 2, transition: 'all 0.2s ease', '&:hover': { boxShadow: '0 2px 8px rgba(0,0,0,0.08)' }, position: 'relative' }}>
              <IconButton
                size="small"
                onClick={() => handleRemoveItem(item.productId, item.variantId)}
                sx={{
                  position: 'absolute',
                  top: 8,
                  right: 8,
                  color: '#757575',
                  '&:hover': {
                    color: '#ff3f6c',
                    bgcolor: 'rgba(255, 63, 108, 0.08)'
                  }
                }}
              >
                <Close fontSize="small" />
              </IconButton>
              <Grid container spacing={2} alignItems="center">
                <GridItem xs={12} sm={3}>
                  <Box
                    component="img"
                    src={item.imageUrl}
                    alt={item.name}
                    sx={{
                      width: '100%',
                      maxHeight: 150,
                      objectFit: 'contain'
                    }}
                  />
                </GridItem>
                <GridItem xs={12} sm={3}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                    {item.name}
                  </Typography>
                  {item.size && (
                    <Typography variant="body2" color="text.secondary">
                      Size: {item.size}
                    </Typography>
                  )}
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="body2">
                      ₹{item.price}
                    </Typography>
                    {item.mrpPrice && item.mrpPrice > item.price && (
                      <Typography variant="body2" color="text.secondary" sx={{ textDecoration: 'line-through' }}>
                        ₹{item.mrpPrice}
                      </Typography>
                    )}
                  </Box>
                  {item.mrpPrice && item.mrpPrice > item.price && (
                    <Typography variant="body2" color="success.main" sx={{ fontWeight: 500 }}>
                      {Math.round(((item.mrpPrice - item.price) / item.mrpPrice) * 100)}% off
                    </Typography>
                  )}
                </GridItem>
                <GridItem xs={8} sm={4}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <IconButton
                      size="small"
                      onClick={() => handleUpdateQuantity(item.productId, item.quantity - 1, item.variantId)}
                    >
                      <Remove />
                    </IconButton>
                    <Typography sx={{ mx: 2 }}>{item.quantity}</Typography>
                    <IconButton
                      size="small"
                      onClick={() => handleUpdateQuantity(item.productId, item.quantity + 1, item.variantId)}
                    >
                      <Add />
                    </IconButton>
                  </Box>
                </GridItem>
                <GridItem xs={4} sm={2} sx={{ textAlign: 'right' }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                    ₹{((item.price || 0) * item.quantity).toFixed(0)}
                  </Typography>
                </GridItem>
              </Grid>
            </Paper>
          ))}

          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
            <Button
              variant="outlined"
              component={Link}
              to="/products"
            >
              Continue Shopping
            </Button>
            <Button
              variant="outlined"
              color="error"
              onClick={handleClearCart}
            >
              Clear Cart
            </Button>
          </Box>
        </GridItem>
        <GridItem xs={12} md={4}>
          {/* First Paper - Offers Section */}
          <Paper
            elevation={0}
            sx={{
              p: 0,
              mb: 2,
              border: '1px solid #e0e0e0',
              borderRadius: 2,
              position: 'relative',
              overflow: 'hidden',
              transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
              }
            }}
          >
            {/* Header */}
            <Box sx={{
              bgcolor: 'rgba(76, 175, 80, 0.08)',
              p: 1.5,
              borderBottom: '1px solid rgba(76, 175, 80, 0.2)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}>
              <Typography
                variant="body1"
                sx={{
                  color: '#000000',
                  fontWeight: 600,
                  fontSize: '15px'
                }}
              >
                AVAILABLE OFFERS
              </Typography>

              <Chip
                label={`${availableOffers} ${availableOffers === 1 ? 'Offer' : 'Offers'}`}
                size="small"
                sx={{
                  bgcolor: 'white',
                  color: '#4caf50',
                  fontWeight: 500,
                  fontSize: '12px',
                  height: '24px',
                  border: '1px solid #4caf50'
                }}
              />
            </Box>

            {availableOffers > 0 ? (
              <Box
                onClick={() => setDrawerOpen(true)}
                sx={{
                  cursor: 'pointer',
                  '&:hover': { bgcolor: 'rgba(76, 175, 80, 0.04)' },
                  position: 'relative',
                  zIndex: 1,
                  p: 2
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                  <Box sx={{
                    width: 40,
                    height: 40,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    bgcolor: 'rgba(76, 175, 80, 0.08)',
                    borderRadius: '50%',
                    border: '1px dashed #4caf50'
                  }}>
                    <LocalOffer sx={{ fontSize: 20, color: '#4caf50' }} />
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <Typography
                      variant="body1"
                      sx={{
                        fontWeight: 600,
                        fontSize: '16px',
                        color: '#282c3f'
                      }}
                    >
                      {maxSavings > 0
                        ? `Save up to ₹${maxSavings.toFixed(0)} on this order`
                        : `${availableOffers} ${availableOffers === 1 ? 'coupon' : 'coupons'} available for you`
                      }
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        color: '#282c3f',
                        fontSize: '13px',
                        mt: 0.5
                      }}
                    >
                      Tap to view and apply available coupons
                    </Typography>
                  </Box>
                  <Button
                    variant="text"
                    size="small"
                    onClick={() => setDrawerOpen(true)}
                    sx={{
                      color: '#4caf50',
                      fontWeight: 600,
                      textTransform: 'none',
                      fontSize: '14px',
                      textDecoration: 'underline',
                      padding: '4px 8px',
                      borderRadius: '4px',
                      '&:hover': {
                        bgcolor: 'rgba(76, 175, 80, 0.08)',
                        textDecoration: 'underline'
                      },
                      display: 'flex',
                      alignItems: 'center',
                      gap: 0.5
                    }}

                  >
                    View Offers
                  </Button>
                </Box>
              </Box>
            ) : (
              <Box sx={{ textAlign: 'center', py: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  No offers available for your cart
                </Typography>
              </Box>
            )}
          </Paper>

          {/* Second Paper - Price Details */}
          <Paper
            elevation={0}
            sx={{
              border: '1px solid #e0e0e0',
              borderRadius: 2,
              transition: 'all 0.2s ease',
              '&:hover': { boxShadow: '0 2px 8px rgba(0,0,0,0.08)' }
            }}
          >
            <Box sx={{ p: 2, borderBottom: '1px solid #e0e0e0' }}>
              <Typography
                variant="h6"
                sx={{
                  fontSize: '16px',
                  fontWeight: 600,
                  color: '#282c3f'
                }}
              >
                PRICE DETAILS
              </Typography>
            </Box>

            <Box sx={{ p: 2 }}>
              {/* Applied Coupon Section */}
              {couponCode && (
                <Box
                  sx={{
                    mb: 2,
                    p: 1.5,
                    bgcolor: '#fff1f4',
                    borderRadius: 1,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    border: '1px dashed #ff3f6c'
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <ConfirmationNumber sx={{ color: '#ff3f6c', fontSize: 20 }} />
                    <Box>
                      <Typography variant="body2" sx={{ color: '#282c3f', fontWeight: 600 }}>
                        {couponCode}
                      </Typography>
                      <Typography variant="caption" sx={{ color: '#03a685', display: 'block' }}>
                        You saved ₹{discountAmount.toFixed(0)} with this coupon
                      </Typography>
                    </Box>
                  </Box>
                  <Button
                    variant="outlined"
                    size="small"
                    color="error"
                    startIcon={<Close fontSize="small" />}
                    onClick={() => {
                      // @ts-ignore
                      dispatch(applyCoupon({ code: '', discountAmount: 0 }));
                    }}
                    sx={{
                      borderColor: '#ff3f6c',
                      color: '#ff3f6c',
                      '&:hover': { borderColor: '#ff3f6c', backgroundColor: '#fff1f4' }
                    }}
                  >
                    Remove
                  </Button>
                </Box>
              )}

              {/* Price Breakdown */}
              <Box sx={{ '& > div': { mb: 2 } }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography sx={{ color: '#282c3f', fontSize: '14px' }}>
                    Total MRP ({totalItems} items)
                  </Typography>
                  <Typography sx={{ color: '#282c3f', fontSize: '14px' }}>
                    ₹{(totalAmount || 0).toFixed(0)}
                  </Typography>
                </Box>

                {discountAmount > 0 && (
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Box>
                      <Typography sx={{ color: '#282c3f', fontSize: '14px' }}>
                        Discount on MRP
                      </Typography>
                      {couponCode && (
                        <Typography sx={{ color: '#ff3f6c', fontSize: '12px', fontWeight: 500 }}>
                          Coupon: {couponCode}
                        </Typography>
                      )}
                    </Box>
                    <Typography sx={{ color: '#03a685', fontSize: '14px' }}>
                      -₹{(discountAmount || 0).toFixed(0)}
                    </Typography>
                  </Box>
                )}

                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography sx={{ color: '#282c3f', fontSize: '14px' }}>
                    Delivery Charges
                  </Typography>
                  <Typography sx={{ color: '#03a685', fontSize: '14px' }}>
                    FREE
                  </Typography>
                </Box>

                {/* Savings on MRP */}
                {totalItems > 0 && items.some(item => item.mrpPrice && item.mrpPrice > item.price) && (
                  <Box sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    bgcolor: '#f5f5f5',
                    p: 1.5,
                    borderRadius: 1,
                    mt: 1
                  }}>
                    <Typography sx={{ color: '#03a685', fontSize: '14px', fontWeight: 500 }}>
                      Saving on MRP
                    </Typography>
                    <Typography sx={{ color: '#03a685', fontSize: '14px', fontWeight: 500 }}>
                      ₹{items.reduce((total, item) => {
                        const saving = item.mrpPrice && item.mrpPrice > item.price ?
                          (item.mrpPrice - item.price) * item.quantity : 0;
                        return total + saving;
                      }, 0).toFixed(0)}
                    </Typography>
                  </Box>
                )}
              </Box>

              <Divider sx={{ my: 2 }} />

              {/* Total Amount */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
                <Typography sx={{
                  color: '#282c3f',
                  fontSize: '16px',
                  fontWeight: 600
                }}>
                  Total Amount
                </Typography>
                <Typography sx={{
                  color: '#282c3f',
                  fontSize: '16px',
                  fontWeight: 600
                }}>
                  ₹{((totalAmount || 0) - (discountAmount || 0)).toFixed(0)}
                </Typography>
              </Box>

              <Button
                variant="contained"
                fullWidth
                size="large"
                onClick={handleCheckout}
                sx={{
                  py: 1.5,
                  backgroundColor: '#ff3f6c',
                  '&:hover': { backgroundColor: '#ff527b' },
                  textTransform: 'uppercase',
                  fontWeight: 600,
                  fontSize: '14px',
                  borderRadius: '4px',
                  boxShadow: '0 2px 4px rgba(255, 63, 108, 0.2)'
                }}
              >
                Proceed to Checkout
              </Button>

              {!isAuthenticated && (
                <Alert
                  severity="info"
                  sx={{
                    mt: 2,
                    '& .MuiAlert-message': {
                      fontSize: '12px'
                    }
                  }}
                >
                  You can checkout as a guest or <Link to="/login" style={{ color: '#ff3f6c', fontWeight: 500 }}>login</Link> to use your saved information.
                </Alert>
              )}
            </Box>
          </Paper>
        </GridItem>
      </Grid>

      <CouponDrawer
        open={drawerOpen}
        onClose={() => setDrawerOpen(false)}
        cartTotal={totalAmount}
        totalItems={totalItems}
      />
    </Container>
    </CustomerLayout>
  );
};

export default Cart;
