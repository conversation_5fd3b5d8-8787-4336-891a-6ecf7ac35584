import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import {
  Typography,
  Container,
  Grid,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Button,
  Box,
  IconButton,
  Divider,
  Paper,
  Breadcrumbs,
  Snackbar
} from '@mui/material';
import { Delete, ShoppingCart } from '@mui/icons-material';
import { RootState } from '../store';
import { removeFromWishlist } from '../store/slices/wishlistSlice';
import { addToCart, CartItem } from '../store/slices/cartSlice';
import QuickAddDrawer from '../components/common/QuickAddDrawer';

const Wishlist: React.FC = () => {
  const dispatch = useDispatch();
  const { items } = useSelector((state: RootState) => state.wishlist);
  const [selectedProduct, setSelectedProduct] = useState<any>(null);
  const [quickAddDrawerOpen, setQuickAddDrawerOpen] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [isMobile, setIsMobile] = useState(window.innerWidth < 600);

  // Add event listener for window resize
  React.useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 600);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleRemoveFromWishlist = (productId: number) => {
    // @ts-ignore
    dispatch(removeFromWishlist(productId));
  };

  const handleQuickAdd = (item: any) => {
    // Fetch product details if needed
    // For now, we'll create a product object from the wishlist item
    const product = {
      id: item.productId,
      name: item.name,
      price: item.price,
      mrp: item.mrpPrice || item.mrp || (item.price * 1.2),
      imageUrl: item.imageUrl,
      stockQuantity: 10, // Default value since wishlist doesn't store this
      // If we have variant information, we would include it here
      variants: item.variants || []
    };

    setSelectedProduct(product);
    setQuickAddDrawerOpen(true);
  };

  const handleDrawerClose = () => {
    setQuickAddDrawerOpen(false);
  };

  const handleAddSuccess = (message: string) => {
    setSnackbarMessage(message);
    setSnackbarOpen(true);
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
        <Link to="/" style={{ textDecoration: 'none', color: 'inherit' }}>
          Home
        </Link>
        <Typography color="text.primary">My Wishlist</Typography>
      </Breadcrumbs>

      <Typography variant="h4" gutterBottom>
        My Wishlist
      </Typography>

      {items.length === 0 ? (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h6" gutterBottom>
            Your wishlist is empty
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            Add items to your wishlist to keep track of products you're interested in.
          </Typography>
          <Button
            variant="contained"
            component={Link}
            to="/products"
            sx={{ mt: 2 }}
          >
            Browse Products
          </Button>
        </Paper>
      ) : (
        <Grid container spacing={3}>
          {items.map((item) => (
            <Grid item key={item.productId} xs={12} sm={6} md={4} lg={3}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <Box sx={{ position: 'relative' }}>
                  <CardMedia
                    component="img"
                    height="200"
                    image={item.imageUrl || 'https://source.unsplash.com/random?product'}
                    alt={item.name}
                    sx={{ cursor: 'pointer' }}
                    onClick={() => window.location.href = `/product/${item.productId}`}
                  />
                  <IconButton
                    sx={{
                      position: 'absolute',
                      top: 8,
                      right: 8,
                      backgroundColor: 'rgba(255, 255, 255, 0.8)',
                      '&:hover': { backgroundColor: 'rgba(255, 255, 255, 0.9)' }
                    }}
                    onClick={() => handleRemoveFromWishlist(item.productId)}
                  >
                    <Delete />
                  </IconButton>
                </Box>
                <CardContent sx={{ flexGrow: 1 }}>
                  <Typography gutterBottom variant="h6" component="h2" noWrap>
                    {item.name}
                  </Typography>
                  <Typography variant="h6" color="primary">
                    ${item.price.toFixed(2)}
                  </Typography>
                </CardContent>
                <Divider />
                <CardActions>
                  <Button
                    fullWidth
                    variant="contained"
                    startIcon={<ShoppingCart />}
                    onClick={() => handleQuickAdd(item)}
                    sx={{
                      borderRadius: 2,
                      backgroundColor: '#ff3f6c',
                      '&:hover': { backgroundColor: '#ff527b' }
                    }}
                  >
                    Add to Cart
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Quick Add Drawer */}
      <QuickAddDrawer
        open={quickAddDrawerOpen}
        onClose={handleDrawerClose}
        product={selectedProduct}
        onSuccess={handleAddSuccess}
        isMobile={isMobile}
      />

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={() => setSnackbarOpen(false)}
        message={snackbarMessage}
      />
    </Container>
  );
};

export default Wishlist;
