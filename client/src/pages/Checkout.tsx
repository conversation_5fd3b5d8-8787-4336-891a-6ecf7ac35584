import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Typography,
  Container,
  Box,
  Paper,
  Grid as Mui<PERSON>rid,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Step,
  StepLabel,
  TextField,
  Divider,
  CircularProgress,
  Alert,
  Checkbox,
  FormControlLabel
} from '@mui/material';
import CustomerLayout from '../components/layout/CustomerLayout';
import '../styles/customerLayout.css';
import { createOrder } from '../store/slices/orderSlice';
import { clearCart } from '../store/slices/cartSlice';
import { RootState } from '../store';
import PaymentMethod from '../components/checkout/PaymentMethod';
import OtpVerification from '../components/checkout/OtpVerification';

const steps = ['Shipping & Payment', 'Confirmation'];

// Create a wrapper component for Grid with proper types
const Grid = (props: any) => <MuiGrid {...props} />;
const GridItem = (props: any) => <MuiGrid item {...props} />;

const Checkout: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { items, totalItems, totalAmount, discountAmount, couponCode } = useSelector((state: RootState) => state.cart);
  const { user, isAuthenticated } = useSelector((state: RootState) => state.auth);
  const { loading = false, error = null, success = false, order = null } = useSelector((state: RootState) => state.orders || {});

  // State for direct checkout item
  const [directCheckoutItem, setDirectCheckoutItem] = useState<any>(null);

  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState({
    email: user?.email || '',
    firstName: user?.firstName || '',
    lastName: user?.lastName || '',
    phone: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: '',
    billingAddress: '',
    billingCity: '',
    billingState: '',
    billingZipCode: '',
    billingCountry: ''
  });
  const [paymentMethod, setPaymentMethod] = useState('COD');
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [isVerified, setIsVerified] = useState(isAuthenticated);
  const [sameBillingAddress, setSameBillingAddress] = useState(true);

  // Check for direct checkout item in session storage
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const isDirect = searchParams.get('direct') === 'true';

    if (isDirect) {
      try {
        const storedItem = sessionStorage.getItem('directCheckoutItem');
        if (storedItem) {
          const parsedItem = JSON.parse(storedItem);
          setDirectCheckoutItem(parsedItem);
        }
      } catch (error) {
        console.error('Error parsing direct checkout item:', error);
      }
    }
  }, [location.search]);

  useEffect(() => {
    // Redirect to cart if cart is empty and no direct checkout item
    if (items.length === 0 && !directCheckoutItem && activeStep === 0) {
      navigate('/cart');
    }

    // Move to confirmation step if order is successful
    if (success && order && activeStep === 1) {
      setActiveStep(2);
      // Clear cart and direct checkout item after successful order
      // @ts-ignore
      dispatch(clearCart());
      sessionStorage.removeItem('directCheckoutItem');
    }
  }, [items.length, navigate, success, order, activeStep, dispatch, directCheckoutItem]);

  const validateForm = () => {
    const errors: Record<string, string> = {};

    // Only validate email and phone if not authenticated
    if (!isAuthenticated) {
      if (!formData.email) errors.email = 'Email is required';
      else if (!/\S+@\S+\.\S+/.test(formData.email)) errors.email = 'Email is invalid';
      if (!formData.phone) errors.phone = 'Phone number is required';
    }

    // Always validate shipping address
    if (!formData.firstName) errors.firstName = 'First name is required';
    if (!formData.lastName) errors.lastName = 'Last name is required';
    if (!formData.address) errors.address = 'Address is required';
    if (!formData.city) errors.city = 'City is required';
    if (!formData.state) errors.state = 'State is required';
    if (!formData.zipCode) errors.zipCode = 'ZIP code is required';
    if (!formData.country) errors.country = 'Country is required';

    // Validate billing address if different from shipping
    if (!sameBillingAddress) {
      if (!formData.billingAddress) errors.billingAddress = 'Billing address is required';
      if (!formData.billingCity) errors.billingCity = 'Billing city is required';
      if (!formData.billingState) errors.billingState = 'Billing state is required';
      if (!formData.billingZipCode) errors.billingZipCode = 'Billing ZIP code is required';
      if (!formData.billingCountry) errors.billingCountry = 'Billing country is required';
    }

    // Validate payment method
    if (!paymentMethod) errors.paymentMethod = 'Payment method is required';

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleNext = () => {
    if (activeStep === 0) {
      if (validateForm()) {
        setActiveStep(1);
      }
    }
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked, type } = e.target;

    if (type === 'checkbox' && name === 'sameBillingAddress') {
      setSameBillingAddress(checked);

      // If checked, copy shipping address to billing address
      if (checked) {
        setFormData({
          ...formData,
          billingAddress: formData.address,
          billingCity: formData.city,
          billingState: formData.state,
          billingZipCode: formData.zipCode,
          billingCountry: formData.country
        });
      }
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }

    // Clear error when user types
    if (formErrors[name]) {
      setFormErrors({
        ...formErrors,
        [name]: ''
      });
    }
  };

  const handleVerificationSuccess = () => {
    setIsVerified(true);
    // Update form data with user information if available
    if (user) {
      setFormData({
        ...formData,
        email: user.email || formData.email,
        firstName: user.firstName || formData.firstName,
        lastName: user.lastName || formData.lastName
      });
    }
  };

  const handlePlaceOrder = () => {
    // Determine billing address based on checkbox
    const billingAddress = sameBillingAddress
      ? formData.address
      : formData.billingAddress;
    const billingCity = sameBillingAddress
      ? formData.city
      : formData.billingCity;
    const billingState = sameBillingAddress
      ? formData.state
      : formData.billingState;
    const billingZipCode = sameBillingAddress
      ? formData.zipCode
      : formData.billingZipCode;
    const billingCountry = sameBillingAddress
      ? formData.country
      : formData.billingCountry;

    // Determine order items based on whether we have a direct checkout item or cart items
    let orderItems = [];

    if (directCheckoutItem) {
      // If we have a direct checkout item, use it
      orderItems = [{
        productId: directCheckoutItem.productId,
        quantity: directCheckoutItem.quantity,
        SKU: directCheckoutItem.sku || '',
        Size: directCheckoutItem.size || '',
        price: directCheckoutItem.price || 0,
        variantId: directCheckoutItem.variantId || 0
      }];
    } else {
      // Otherwise use cart items
      orderItems = items.map(item => ({
        productId: item.productId,
        quantity: item.quantity,
        SKU: item.sku || '',
        Size: item.size || '',
        price: item.price || 0,
        variantId: item.variantId || 0
      }));
    }

    const orderData = {
      userId: isAuthenticated ? user?.userId : undefined,
      isGuestOrder: !isAuthenticated,
      customerEmail: formData.email,
      customerPhone: formData.phone,
      shippingAddress: formData.address,
      shippingCity: formData.city,
      shippingState: formData.state,
      shippingZipCode: formData.zipCode,
      shippingCountry: formData.country,
      billingAddress: billingAddress,
      billingCity: billingCity,
      billingState: billingState,
      billingZipCode: billingZipCode,
      billingCountry: billingCountry,
      paymentMethod: paymentMethod,
      orderItems: orderItems,
      isDirectCheckout: !!directCheckoutItem
    };

    // @ts-ignore
    dispatch(createOrder(orderData));
  };

  const renderShippingForm = () => (
    <Box>
      <Paper sx={{ p: 3, mb: 3 }}>
        {!isVerified && (
          <Box mb={3}>
            <Typography variant="h6" gutterBottom>
              Verify Your Email
            </Typography>
            <Divider sx={{ mb: 2 }} />
            <OtpVerification onVerificationSuccess={handleVerificationSuccess} />
          </Box>
        )}

        <Typography variant="h6" gutterBottom>
          Shipping Information
        </Typography>
        <Divider sx={{ mb: 2 }} />

        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField
              required
              fullWidth
              label="First Name"
              name="firstName"
              value={formData.firstName}
              onChange={handleInputChange}
              error={!!formErrors.firstName}
              helperText={formErrors.firstName}
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              required
              fullWidth
              label="Last Name"
              name="lastName"
              value={formData.lastName}
              onChange={handleInputChange}
              error={!!formErrors.lastName}
              helperText={formErrors.lastName}
              size="small"
            />
          </Grid>
          <Grid item xs={12}>
            <Box sx={{ position: 'relative' }}>
              <TextField
                required
                fullWidth
                label="Email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                error={!!formErrors.email}
                helperText={formErrors.email}
                disabled={isAuthenticated}
                size="small"
              />
              {isAuthenticated && (
                <Box
                  sx={{
                    position: 'absolute',
                    right: 12,
                    top: 10,
                    color: 'success.main',
                    fontSize: '0.875rem',
                    display: 'flex',
                    alignItems: 'center',
                    backgroundColor: 'white',
                    padding: '0 4px'
                  }}
                >
                  Verified ✓
                </Box>
              )}
            </Box>
          </Grid>
          <Grid item xs={12}>
            <TextField
              required
              fullWidth
              label="Phone Number"
              name="phone"
              value={formData.phone}
              onChange={handleInputChange}
              error={!!formErrors.phone}
              helperText={formErrors.phone}
              size="small"
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              required
              fullWidth
              label="Address"
              name="address"
              value={formData.address}
              onChange={handleInputChange}
              error={!!formErrors.address}
              helperText={formErrors.address}
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              required
              fullWidth
              label="City"
              name="city"
              value={formData.city}
              onChange={handleInputChange}
              error={!!formErrors.city}
              helperText={formErrors.city}
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              required
              fullWidth
              label="State/Province"
              name="state"
              value={formData.state}
              onChange={handleInputChange}
              error={!!formErrors.state}
              helperText={formErrors.state}
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              required
              fullWidth
              label="ZIP / Postal Code"
              name="zipCode"
              value={formData.zipCode}
              onChange={handleInputChange}
              error={!!formErrors.zipCode}
              helperText={formErrors.zipCode}
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              required
              fullWidth
              label="Country"
              name="country"
              value={formData.country}
              onChange={handleInputChange}
              error={!!formErrors.country}
              helperText={formErrors.country}
              size="small"
            />
          </Grid>
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={sameBillingAddress}
                  onChange={handleInputChange}
                  name="sameBillingAddress"
                  color="primary"
                />
              }
              label="Billing address same as shipping address"
            />
          </Grid>
        </Grid>

        {!sameBillingAddress && (
          <Box mt={3}>
            <Typography variant="h6" gutterBottom>
              Billing Information
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  required
                  fullWidth
                  label="Billing Address"
                  name="billingAddress"
                  value={formData.billingAddress}
                  onChange={handleInputChange}
                  error={!!formErrors.billingAddress}
                  helperText={formErrors.billingAddress}
                  size="small"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  required
                  fullWidth
                  label="Billing City"
                  name="billingCity"
                  value={formData.billingCity}
                  onChange={handleInputChange}
                  error={!!formErrors.billingCity}
                  helperText={formErrors.billingCity}
                  size="small"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  required
                  fullWidth
                  label="Billing State/Province"
                  name="billingState"
                  value={formData.billingState}
                  onChange={handleInputChange}
                  error={!!formErrors.billingState}
                  helperText={formErrors.billingState}
                  size="small"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  required
                  fullWidth
                  label="Billing ZIP / Postal Code"
                  name="billingZipCode"
                  value={formData.billingZipCode}
                  onChange={handleInputChange}
                  error={!!formErrors.billingZipCode}
                  helperText={formErrors.billingZipCode}
                  size="small"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  required
                  fullWidth
                  label="Billing Country"
                  name="billingCountry"
                  value={formData.billingCountry}
                  onChange={handleInputChange}
                  error={!!formErrors.billingCountry}
                  helperText={formErrors.billingCountry}
                  size="small"
                />
              </Grid>
            </Grid>
          </Box>
        )}
      </Paper>
    </Box>
  );

  const renderOrderReview = () => (
    <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 3 }}>
      {/* Left Side - Addresses */}
      <Box sx={{ flex: '1 1 auto', width: { xs: '100%', md: '60%' } }}>
        <Paper sx={{ p: 2, mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Typography variant="h6" sx={{ fontSize: '1rem' }}>
              Shipping Address
            </Typography>
            {!sameBillingAddress && (
              <Typography variant="body2" sx={{ fontSize: '0.875rem', color: 'text.secondary' }}>
                Different billing address
              </Typography>
            )}
          </Box>
          <Divider sx={{ mb: 1 }} />

          <Box sx={{ mb: 2 }}>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              <Box sx={{ flex: '1 1 50%', minWidth: '200px' }}>
                <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                  {formData.firstName} {formData.lastName}
                </Typography>
                <Typography variant="body2">
                  {formData.address}
                </Typography>
                <Typography variant="body2">
                  {formData.city}, {formData.state} {formData.zipCode}
                </Typography>
                <Typography variant="body2">
                  {formData.country}
                </Typography>
              </Box>
              <Box sx={{ flex: '1 1 50%', minWidth: '200px' }}>
                <Typography variant="body2">
                  <strong>Email:</strong> {formData.email}
                </Typography>
                <Typography variant="body2">
                  <strong>Phone:</strong> {formData.phone}
                </Typography>
              </Box>
            </Box>
          </Box>

          {!sameBillingAddress && (
            <Box>
              <Typography variant="h6" sx={{ fontSize: '1rem', mt: 2 }}>
                Billing Address
              </Typography>
              <Divider sx={{ mb: 1 }} />

              <Box sx={{ mb: 1 }}>
                <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                  {formData.firstName} {formData.lastName}
                </Typography>
                <Typography variant="body2">
                  {formData.billingAddress}, {formData.billingCity}
                </Typography>
                <Typography variant="body2">
                  {formData.billingState}, {formData.billingZipCode}, {formData.billingCountry}
                </Typography>
              </Box>
            </Box>
          )}
        </Paper>
      </Box>

      {/* Right Side - Order Summary and Payment */}
      <Box sx={{ flex: '0 0 auto', width: { xs: '100%', md: '35%' } }}>
        <Paper sx={{ p: 2, mb: 3 }}>
          <Typography variant="h6" sx={{ fontSize: '1rem' }}>
            Order Summary
          </Typography>
          <Divider sx={{ mb: 1 }} />

          {/* Order Items - Compact View */}
          <Box sx={{ maxHeight: '100px', overflowY: 'auto', mb: 1 }}>
            {directCheckoutItem ? (
              <Box key={`${directCheckoutItem.productId}-${directCheckoutItem.variantId || 'default'}`} sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box sx={{ width: 24, height: 24, mr: 1 }}>
                    <img
                      src={directCheckoutItem.imageUrl}
                      alt={directCheckoutItem.name}
                      style={{ width: '100%', height: '100%', objectFit: 'cover', borderRadius: 2 }}
                    />
                  </Box>
                  <Typography variant="caption" noWrap sx={{ maxWidth: '150px' }}>
                    {directCheckoutItem.name} {directCheckoutItem.size && `(${directCheckoutItem.size})`} x {directCheckoutItem.quantity}
                  </Typography>
                </Box>
                <Typography variant="caption">
                  ₹{((directCheckoutItem.price || 0) * directCheckoutItem.quantity).toFixed(2)}
                </Typography>
              </Box>
            ) : (
              items.map((item) => (
                <Box key={`${item.productId}-${item.variantId || 'default'}`} sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Box sx={{ width: 24, height: 24, mr: 1 }}>
                      <img
                        src={item.imageUrl}
                        alt={item.name}
                        style={{ width: '100%', height: '100%', objectFit: 'cover', borderRadius: 2 }}
                      />
                    </Box>
                    <Typography variant="caption" noWrap sx={{ maxWidth: '150px' }}>
                      {item.name} {item.size && `(${item.size})`} x {item.quantity}
                    </Typography>
                  </Box>
                  <Typography variant="caption">
                    ₹{((item.price || 0) * item.quantity).toFixed(2)}
                  </Typography>
                </Box>
              ))
            )}
          </Box>

          <Divider sx={{ mb: 1 }} />

          {/* Price Summary - Compact */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
            <Typography variant="body2">
              Subtotal ({directCheckoutItem ? directCheckoutItem.quantity : totalItems} {directCheckoutItem ? 'item' : 'items'})
            </Typography>
            <Typography variant="body2">
              ₹{(directCheckoutItem ? (directCheckoutItem.price * directCheckoutItem.quantity) : totalAmount || 0).toFixed(2)}
            </Typography>
          </Box>

          {!directCheckoutItem && discountAmount > 0 && (
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
              <Typography variant="body2" color="success.main">
                Discount {couponCode && `(${couponCode})`}
              </Typography>
              <Typography variant="body2" color="success.main">
                -₹{discountAmount.toFixed(2)}
              </Typography>
            </Box>
          )}

          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
            <Typography variant="body2">
              Shipping
            </Typography>
            <Typography variant="body2">
              Free
            </Typography>
          </Box>

          <Divider sx={{ my: 1 }} />

          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="subtitle1">
              Total
            </Typography>
            <Typography variant="subtitle1">
              ₹{(directCheckoutItem ? (directCheckoutItem.price * directCheckoutItem.quantity) : (totalAmount - discountAmount) || 0).toFixed(2)}
            </Typography>
          </Box>
        </Paper>

        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" sx={{ fontSize: '1rem' }}>
            Payment Method
          </Typography>
          <Divider sx={{ mb: 1 }} />

          <PaymentMethod
            paymentMethod={paymentMethod}
            setPaymentMethod={setPaymentMethod}
            errors={formErrors}
          />

          <Button
            variant="contained"
            color="primary"
            fullWidth
            onClick={handlePlaceOrder}
            disabled={loading}
            sx={{ mt: 2 }}
            size="large"
          >
            {loading ? <CircularProgress size={24} /> : 'Place Order'}
          </Button>
        </Paper>
      </Box>
    </Box>
  );

  const renderOrderConfirmation = () => (
    <Box sx={{ textAlign: 'center' }}>
      <Typography variant="h5" gutterBottom>
        Thank you for your order!
      </Typography>
      <Typography variant="subtitle1">
        Your order number is #{order?.orderNumber}. We have emailed your order confirmation to {formData.email}.
      </Typography>
      <Button
        variant="contained"
        color="primary"
        onClick={() => navigate('/')}
        sx={{ mt: 3 }}
      >
        Continue Shopping
      </Button>
    </Box>
  );

  return (
    <CustomerLayout>
      <Container className="content-container section-spacing">
        <Typography variant="h4" gutterBottom component="h1">
          Checkout
        </Typography>

      <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
        {steps.map((label) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
          </Step>
        ))}
      </Stepper>

      {activeStep === 0 && (
        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 4 }}>
          <Box sx={{ flex: '1 1 auto', width: { xs: '100%', md: '60%' } }}>
            <Paper sx={{ p: 3 }}>
              {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {error}
                </Alert>
              )}
              {renderShippingForm()}
            </Paper>
          </Box>

          <Box sx={{ flex: '0 0 auto', width: { xs: '100%', md: '35%' } }}>
            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Order Summary
              </Typography>

              {/* Order Items */}
              <Box sx={{ maxHeight: '300px', overflowY: 'auto', mb: 2 }}>
                {directCheckoutItem ? (
                  <Box key={`${directCheckoutItem.productId}-${directCheckoutItem.variantId || 'default'}`} sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Box sx={{ width: 40, height: 40, mr: 1 }}>
                        <img
                          src={directCheckoutItem.imageUrl}
                          alt={directCheckoutItem.name}
                          style={{ width: '100%', height: '100%', objectFit: 'cover', borderRadius: 4 }}
                        />
                      </Box>
                      <Box>
                        <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                          {directCheckoutItem.name} {directCheckoutItem.size && `(${directCheckoutItem.size})`} x {directCheckoutItem.quantity}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          ₹{(directCheckoutItem.price || 0).toFixed(2)} each
                        </Typography>
                      </Box>
                    </Box>
                    <Typography variant="body2">
                      ₹{((directCheckoutItem.price || 0) * directCheckoutItem.quantity).toFixed(2)}
                    </Typography>
                  </Box>
                ) : (
                  items.map((item) => (
                    <Box key={`${item.productId}-${item.variantId || 'default'}`} sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box sx={{ width: 40, height: 40, mr: 1 }}>
                          <img
                            src={item.imageUrl}
                            alt={item.name}
                            style={{ width: '100%', height: '100%', objectFit: 'cover', borderRadius: 4 }}
                          />
                        </Box>
                        <Box>
                          <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                            {item.name} {item.size && `(${item.size})`} x {item.quantity}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            ₹{(item.price || 0).toFixed(2)} each
                          </Typography>
                        </Box>
                      </Box>
                      <Typography variant="body2">
                        ₹{((item.price || 0) * item.quantity).toFixed(2)}
                      </Typography>
                    </Box>
                  ))
                )}
              </Box>

              <Divider sx={{ mb: 2 }} />

              {/* Price Summary */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body1">
                  Subtotal ({directCheckoutItem ? directCheckoutItem.quantity : totalItems} {directCheckoutItem ? 'item' : 'items'})
                </Typography>
                <Typography variant="body1">
                  ₹{(directCheckoutItem ? (directCheckoutItem.price * directCheckoutItem.quantity) : totalAmount || 0).toFixed(2)}
                </Typography>
              </Box>

              {!directCheckoutItem && discountAmount > 0 && (
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body1" color="success.main">
                    Discount {couponCode && `(${couponCode})`}
                  </Typography>
                  <Typography variant="body1" color="success.main">
                    -₹{discountAmount.toFixed(2)}
                  </Typography>
                </Box>
              )}

              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body1">
                  Shipping
                </Typography>
                <Typography variant="body1">
                  Free
                </Typography>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                <Typography variant="h6">
                  Total
                </Typography>
                <Typography variant="h6">
                  ₹{(directCheckoutItem ? (directCheckoutItem.price * directCheckoutItem.quantity) : (totalAmount - discountAmount) || 0).toFixed(2)}
                </Typography>
              </Box>

              <Button
                variant="contained"
                color="primary"
                fullWidth
                onClick={handleNext}
                disabled={loading || !isVerified}
                sx={{ mt: 2 }}
              >
                {loading ? <CircularProgress size={24} /> : 'Proceed to Payment'}
              </Button>
            </Paper>
          </Box>
        </Box>
      )}

      {activeStep === 1 && (
        <Box>
          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}
          <Box sx={{ mb: 2 }}>
            <Button
              onClick={handleBack}
              variant="outlined"
              size="small"
              startIcon={<span>←</span>}
              sx={{ mb: 1 }}
            >
              Back to Shipping
            </Button>
          </Box>
          {renderOrderReview()}
        </Box>
      )}

      {activeStep === 2 && (
        <Paper sx={{ p: 3 }}>
          {renderOrderConfirmation()}
        </Paper>
      )}
    </Container>
    </CustomerLayout>
  );
};

export default Checkout;
