import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { usePara<PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import {
  Typography,
  Grid as Mui<PERSON>rid,
  Button,
  Container,
  Box,
  CircularProgress,
  Paper,
  Breadcrumbs,
  Divider,
  Chip,
  TextField,
  Alert,
  IconButton,
  Rating,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Stack
} from '@mui/material';
import { getValidImageUrl, createImageErrorHandler } from '../utils/imageUtils';

// Create a wrapper component for Grid with proper types
const Grid = (props: any) => <MuiGrid {...props} />;
const GridItem = (props: any) => <MuiGrid item {...props} />;
import CustomerLayout from '../components/layout/CustomerLayout';
import '../styles/customerLayout.css';
import {
  Favorite,
  FavoriteBorder,
  ShoppingCart,
  LocalShipping,
  AssignmentReturn,
  Security,
  ArrowForward
} from '@mui/icons-material';
import { fetchProductById } from '../store/slices/productSlice';
import { addToCart, CartItem } from '../store/slices/cartSlice';
import { addToWishlist, removeFromWishlist, WishlistItem } from '../store/slices/wishlistSlice';
import { RootState } from '../store';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`product-tabpanel-${index}`}
      aria-labelledby={`product-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const ProductDetail: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { product, loading, error } = useSelector((state: RootState) => state.products);
  const { items: wishlistItems } = useSelector((state: RootState) => state.wishlist);

  const [quantity, setQuantity] = useState(1);
  const [addedToCart, setAddedToCart] = useState(false);
  const [selectedImage, setSelectedImage] = useState(0);
  const [tabValue, setTabValue] = useState(0);
  const [selectedVariant, setSelectedVariant] = useState<number | null>(null);
  const [currentPrice, setCurrentPrice] = useState<number>(0);
  const [currentMRP, setCurrentMRP] = useState<number>(0);

  useEffect(() => {
    if (id) {
      // @ts-ignore
      dispatch(fetchProductById(parseInt(id)));
    }
  }, [dispatch, id]);

  // Set default variant when product loads
  useEffect(() => {
    if (product && product.variants && product.variants.length > 0) {
      // Find the first in-stock variant
      const inStockVariant = product.variants.find(v => v.stockQuantity > 0 && v.isActive);
      const variantToUse = inStockVariant || product.variants[0];

      if (variantToUse) {
        setSelectedVariant(variantToUse.id);
        setCurrentPrice(variantToUse.price || 0);
        setCurrentMRP(variantToUse.mrp || variantToUse.price * 1.2 || 0);
      }
    }
  }, [product]);

  const handleQuantityChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(event.target.value);
    if (value > 0) {
      setQuantity(value);
    }
  };

  const [variantError, setVariantError] = useState<string>('');

  const createCartItem = () => {
    if (!product || !selectedVariant) {
      setVariantError('Please select a size');
      return null;
    }

    setVariantError('');
    const selectedVariantObj = product.variants?.find(v => v.id === selectedVariant);
    if (!selectedVariantObj) {
      setVariantError('Selected variant not available');
      return null;
    }

    return {
      productId: product.id,
      name: product.name,
      price: currentPrice,
      quantity: quantity,
      imageUrl: product.imageUrl || (product.images && product.images.length > 0 ? product.images[0].imageUrl : ''),
      categoryId: product.categoryId || 0,
      categoryName: product.categoryName || '',
      collectionIds: product.collectionIds || [],
      collectionNames: product.collectionNames || [],
      mrpPrice: currentMRP,
      variantId: selectedVariant,
      size: selectedVariantObj.size || ''
    };
  };

  const handleAddToCart = () => {
    const cartItem = createCartItem();
    if (cartItem) {
      // @ts-ignore
      dispatch(addToCart(cartItem));
      setAddedToCart(true);
      setTimeout(() => setAddedToCart(false), 3000);
    }
  };

  const handleBuyNow = () => {
    const cartItem = createCartItem();
    if (cartItem) {
      // Store the item in session storage for direct checkout
      sessionStorage.setItem('directCheckoutItem', JSON.stringify(cartItem));
      navigate('/checkout?direct=true');
    }
  };

  const handleToggleWishlist = () => {
    if (!product) return;

    const isInWishlist = isProductInWishlist(product.id);

    if (isInWishlist) {
      // @ts-ignore
      dispatch(removeFromWishlist(product.id));
    } else {
      const wishlistItem: WishlistItem = {
        productId: product.id,
        name: product.name || '',
        price: currentPrice,
        imageUrl: product.imageUrl || (product.images && product.images.length > 0 ? product.images[0].imageUrl : '') || ''
      };
      // @ts-ignore
      dispatch(addToWishlist(wishlistItem));
    }
  };

  const isProductInWishlist = (productId: number) => {
    return wishlistItems.some(item => item.productId === productId);
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleImageClick = (index: number) => {
    setSelectedImage(index);
  };

  const handleVariantSelect = (variantId: number) => {
    setSelectedVariant(variantId);

    // Update price based on selected variant
    if (product && product.variants) {
      const selectedVariantObj = product.variants.find(v => v.id === variantId);
      if (selectedVariantObj) {
        setCurrentPrice(selectedVariantObj.price || 0);
        setCurrentMRP(selectedVariantObj.mrp || selectedVariantObj.price * 1.2 || 0);
      }
    }
  };

  if (loading) {
    return (
      <CustomerLayout>
        <Container className="content-container section-spacing">
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        </Container>
      </CustomerLayout>
    );
  }

  if (error || !product) {
    return (
      <CustomerLayout>
        <Container className="content-container section-spacing">
          <Alert severity="error">
            {error || 'Product not found'}
          </Alert>
        </Container>
      </CustomerLayout>
    );
  }

  // Calculate discount percentage
  const discountPercentage = (currentMRP > 0 && currentPrice > 0) ?
    Math.round(((currentMRP - currentPrice) / currentMRP) * 100) : 0;
  const isWishlisted = product ? isProductInWishlist(product.id) : false;

  return (
    <CustomerLayout>
      <Container className="content-container section-spacing">
      <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
        <Link to="/" style={{ textDecoration: 'none', color: 'inherit' }}>
          Home
        </Link>
        <Link to="/products" style={{ textDecoration: 'none', color: 'inherit' }}>
          Products
        </Link>
        <Link to={`/category/${product.categoryId}`} style={{ textDecoration: 'none', color: 'inherit' }}>
          {product.categoryName}
        </Link>
        <Typography color="text.primary">{product.name}</Typography>
      </Breadcrumbs>

      {addedToCart && (
        <Alert severity="success" sx={{ mb: 2 }}>
          Product added to cart successfully!
        </Alert>
      )}

      <Paper sx={{ p: { xs: 2, md: 4 } }}>
        <Grid container spacing={4} sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, flexWrap: { xs: 'wrap', md: 'nowrap' } }}>
          {/* Product Images Section */}
          <GridItem xs={12} md={6} sx={{ flex: { xs: '0 0 100%', md: '0 0 50%' }, maxWidth: { xs: '100%', md: '50%' }, overflow: 'hidden' }}>
            <Box sx={{ position: 'relative' }}>
              <img
                src={product.images && product.images.length > 0
                  ? getValidImageUrl(product.images[selectedImage].imageUrl || product.images[selectedImage].url)
                  : getValidImageUrl(product.mainImage || product.imageUrl)}
                alt={product.name}
                style={{ width: '100%', height: '100%', objectFit: 'contain' }}
                onError={createImageErrorHandler()}
              />

              {discountPercentage > 0 && (
                <Chip
                  label={`${discountPercentage}% OFF`}
                  color="error"
                  size="medium"
                  sx={{
                    position: 'absolute',
                    top: 16,
                    left: 16,
                    fontWeight: 'bold',
                    fontSize: '1rem',
                    py: 1
                  }}
                />
              )}
            </Box>

            {/* Thumbnail Images */}
            {product.images && product.images.length > 1 && (
              <Box sx={{ display: 'flex', mt: 2, overflowX: 'auto', pb: 1 }}>
                {product.images.map((image, index) => (
                  <Box
                    key={index}
                    onClick={() => handleImageClick(index)}
                    sx={{
                      width: 80,
                      height: 80,
                      mr: 1,
                      border: index === selectedImage ? '2px solid #ff3f6c' : '1px solid #ddd',
                      cursor: 'pointer',
                      opacity: index === selectedImage ? 1 : 0.7,
                      transition: 'all 0.2s',
                      '&:hover': { opacity: 1 }
                    }}
                  >
                    <img
                      src={getValidImageUrl(image.imageUrl || image.url)}
                      alt={`${product.name} - view ${index + 1}`}
                      style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                      onError={createImageErrorHandler()}
                    />
                  </Box>
                ))}
              </Box>
            )}
          </GridItem>

          {/* Product Details Section */}
          <GridItem xs={12} md={6} sx={{ flex: { xs: '0 0 100%', md: '0 0 50%' }, maxWidth: { xs: '100%', md: '50%' }, overflow: 'hidden' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', width: '100%', flexWrap: 'wrap' }}>
              <Box sx={{ flex: '1 1 auto', pr: 2, overflow: 'hidden', maxWidth: 'calc(100% - 50px)', wordBreak: 'break-word' }}>
                <Typography
                  variant="h4"
                  gutterBottom
                  component="h1"
                  sx={{
                    wordWrap: 'break-word',
                    overflowWrap: 'break-word',
                    hyphens: 'auto',
                    lineHeight: 1.2,
                    fontSize: { xs: '1.25rem', sm: '1.5rem', md: '1.75rem' },
                    maxWidth: '100%',
                    display: 'block',
                    whiteSpace: 'normal',
                    textOverflow: 'ellipsis'
                  }}
                >
                  {product.name}
                </Typography>
                <Typography variant="subtitle1" color="text.secondary" gutterBottom>
                  {product.categoryName}
                </Typography>
              </Box>

              <IconButton
                onClick={handleToggleWishlist}
                sx={{
                  color: isWishlisted ? 'error.main' : 'action.active',
                  border: '1px solid #ddd',
                  borderRadius: '50%',
                  p: 1,
                  flex: '0 0 auto',
                  ml: 1
                }}
              >
                {isWishlisted ? <Favorite /> : <FavoriteBorder />}
              </IconButton>
            </Box>

            <Divider sx={{ my: 2 }} />

            {/* Price Section */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Typography variant="h4" component="span" sx={{ fontWeight: 'bold', mr: 2 }}>
                ₹{currentPrice.toFixed(2)}
              </Typography>
              <Typography variant="h6" component="span" sx={{ textDecoration: 'line-through', color: 'text.secondary', mr: 2 }}>
                ₹{currentMRP.toFixed(2)}
              </Typography>
              {discountPercentage > 0 && (
                <Chip
                  label={`${discountPercentage}% OFF`}
                  color="error"
                  size="small"
                  sx={{ fontWeight: 'bold' }}
                />
              )}
            </Box>

            {/* Variant Sizes */}
            {product.variants && product.variants.length > 0 && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Size: <span style={{ color: variantError ? 'red' : 'inherit' }}>{variantError ? `(${variantError})` : ''}</span>
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {product.variants.map((variant) => (
                    <Chip
                      key={variant.id}
                      label={variant.size || ''}
                      onClick={() => handleVariantSelect(variant.id)}
                      color={selectedVariant === variant.id ? 'primary' : 'default'}
                      variant={selectedVariant === variant.id ? 'filled' : 'outlined'}
                      disabled={variant.stockQuantity <= 0 || !variant.isActive}
                      sx={{
                        borderRadius: '4px',
                        fontWeight: selectedVariant === variant.id ? 'bold' : 'normal',
                        cursor: 'pointer',
                        border: variantError && !selectedVariant ? '1px solid red' : undefined
                      }}
                    />
                  ))}
                </Box>
              </Box>
            )}

            {/* Tags/Categories */}
            <Box sx={{ mb: 3 }}>
              <Chip
                label={product.categoryName}
                component={Link}
                to={`/category/${product.categoryId}`}
                clickable
                color="primary"
                variant="outlined"
                sx={{ mr: 1, mb: 1 }}
              />
              {product.collectionNames.map((name, index) => (
                <Chip
                  key={index}
                  label={name}
                  component={Link}
                  to={`/collection/${product.collectionIds[index]}`}
                  clickable
                  sx={{ mr: 1, mb: 1 }}
                />
              ))}
            </Box>

            {/* Availability */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle1" gutterBottom>
                Availability: {product.stockQuantity > 0 ? (
                  <span style={{ color: 'green', fontWeight: 'bold' }}>In Stock</span>
                ) : (
                  <span style={{ color: 'red', fontWeight: 'bold' }}>Out of Stock</span>
                )}
              </Typography>
            </Box>

            {/* Quantity and Add to Cart */}
            {product.stockQuantity > 0 && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" gutterBottom>Quantity:</Typography>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <TextField
                    type="number"
                    InputProps={{
                      inputProps: { min: 1, max: product.stockQuantity },
                      sx: { borderRadius: 2 }
                    }}
                    value={quantity}
                    onChange={handleQuantityChange}
                    sx={{ width: '100px', mr: 2 }}
                    size="small"
                  />
                </Box>
              </Box>
            )}

            {/* Action Buttons */}
            <Stack direction="row" spacing={2} sx={{ mb: 3 }}>
              <Button
                variant="contained"
                color="primary"
                size="large"
                fullWidth
                onClick={handleAddToCart}
                disabled={product.stockQuantity === 0}
                startIcon={<ShoppingCart />}
                sx={{
                  py: 1.5,
                  borderRadius: 2,
                  backgroundColor: '#ff3f6c',
                  '&:hover': { backgroundColor: '#ff527b' }
                }}
              >
                ADD TO CART
              </Button>

              <Button
                variant="contained"
                size="large"
                fullWidth
                onClick={handleBuyNow}
                disabled={product.stockQuantity === 0}
                endIcon={<ArrowForward />}
                sx={{
                  py: 1.5,
                  borderRadius: 2,
                  backgroundColor: '#fb641b',
                  '&:hover': { backgroundColor: '#fa7e3c' }
                }}
              >
                BUY NOW
              </Button>
            </Stack>

            {/* Delivery & Returns */}
            <Paper variant="outlined" sx={{ p: 2, mb: 3, borderRadius: 2 }}>
              <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold' }}>
                Delivery Options
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <LocalShipping sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2">Free delivery on orders over $50</Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <AssignmentReturn sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2">30-day return policy</Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Security sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2">Secure payment</Typography>
              </Box>
            </Paper>
          </GridItem>
        </Grid>

        {/* Product Details Tabs */}
        <Box sx={{ width: '100%', mt: 4 }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs value={tabValue} onChange={handleTabChange} aria-label="product details tabs">
              <Tab label="Description" id="product-tab-0" aria-controls="product-tabpanel-0" />
              <Tab label="Specifications" id="product-tab-1" aria-controls="product-tabpanel-1" />
              <Tab label="Custom Attributes" id="product-tab-2" aria-controls="product-tabpanel-2" />
            </Tabs>
          </Box>

          <TabPanel value={tabValue} index={0}>
            <Typography variant="body1" paragraph>
              {product.description}
            </Typography>
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            <TableContainer>
              <Table>
                <TableBody>
                  {product.variants && product.variants.length > 0 && selectedVariant && (
                    <TableRow>
                      <TableCell component="th" scope="row" sx={{ width: '30%', fontWeight: 'bold' }}>SKU</TableCell>
                      <TableCell>
                        {product.variants.find(v => v.id === selectedVariant)?.sku || 'N/A'}
                      </TableCell>
                    </TableRow>
                  )}
                  <TableRow>
                    <TableCell component="th" scope="row" sx={{ fontWeight: 'bold' }}>Style Code</TableCell>
                    <TableCell>{product.styleCode || 'N/A'}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell component="th" scope="row" sx={{ fontWeight: 'bold' }}>Color</TableCell>
                    <TableCell>{product.color || 'N/A'}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell component="th" scope="row" sx={{ fontWeight: 'bold' }}>Product Type</TableCell>
                    <TableCell>{product.productTypeName || 'N/A'}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell component="th" scope="row" sx={{ fontWeight: 'bold' }}>Category</TableCell>
                    <TableCell>{product.categoryName}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell component="th" scope="row" sx={{ fontWeight: 'bold' }}>Collections</TableCell>
                    <TableCell>{product.collectionNames.join(', ') || 'N/A'}</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </TableContainer>
          </TabPanel>

          <TabPanel value={tabValue} index={2}>
            {product.customAttributes ? (
              <TableContainer>
                <Table>
                  <TableBody>
                    {(() => {
                      try {
                        // Check if customAttributes is already an object or a JSON string
                        const attributes = typeof product.customAttributes === 'string'
                          ? JSON.parse(product.customAttributes)
                          : product.customAttributes;

                        if (typeof attributes !== 'object' || attributes === null) {
                          return (
                            <TableRow>
                              <TableCell colSpan={2}>
                                <Typography>Invalid custom attributes format</Typography>
                              </TableCell>
                            </TableRow>
                          );
                        }

                        return Object.entries(attributes).map(([key, value]) => (
                          <TableRow key={key}>
                            <TableCell component="th" scope="row" sx={{ width: '30%', fontWeight: 'bold' }}>
                              {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                            </TableCell>
                            <TableCell>{value as string}</TableCell>
                          </TableRow>
                        ));
                      } catch (error) {
                        console.error('Error parsing custom attributes:', error);
                        return (
                          <TableRow>
                            <TableCell colSpan={2}>
                              <Typography>Error parsing custom attributes</Typography>
                            </TableCell>
                          </TableRow>
                        );
                      }
                    })()}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Typography>No custom attributes available</Typography>
            )}
          </TabPanel>
        </Box>
      </Paper>
    </Container>
    </CustomerLayout>
  );
};

export default ProductDetail;
