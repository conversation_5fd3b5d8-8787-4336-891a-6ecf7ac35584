import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import {
  Typography,
  Container,
  Box,
  Paper,
  TextField,
  Button,
  Grid,
  Divider,
  Alert,
  CircularProgress,
  Checkbox,
  FormControlLabel
} from '@mui/material';
import { login, clearError } from '../store/slices/authSlice';
import { RootState } from '../store';

const Login: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated, user, loading, error } = useSelector((state: RootState) => state.auth);

  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [rememberMe, setRememberMe] = useState(false);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // Get redirect path from location state or default to home
  const from = location.state?.from?.pathname || '/';

  useEffect(() => {
    // Redirect if already authenticated
    if (isAuthenticated) {
      // If user is admin and trying to access customer login, redirect to admin dashboard
      if (user?.roles.includes('Admin') && location.pathname === '/login') {
        navigate('/admin', { replace: true });
      } else {
        navigate(from, { replace: true });
      }
    }

    // Clear any previous errors
    // @ts-ignore
    dispatch(clearError());
  }, [isAuthenticated, user, navigate, dispatch, from, location]);

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.email) errors.email = 'Email is required';
    else if (!/\S+@\S+\.\S+/.test(formData.email)) errors.email = 'Email is invalid';

    if (!formData.password) errors.password = 'Password is required';

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });

    // Clear error when user types
    if (formErrors[name]) {
      setFormErrors({
        ...formErrors,
        [name]: ''
      });
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      // @ts-ignore
      dispatch(login(formData));
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 8 }}>
      <Grid container spacing={4}>
        <Grid item xs={12} md={6}>
          <Box
            sx={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              pr: { md: 8 }
            }}
          >
            <Typography
              variant="h3"
              component="h1"
              sx={{
                fontWeight: 'bold',
                mb: 2,
                color: '#ff3f6c'
              }}
            >
              Welcome Back
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ mb: 4 }}>
              Login to access your account, track orders, and enjoy a personalized shopping experience.
            </Typography>
            <Box
              component="img"
              src="https://images.unsplash.com/photo-*************-668a904cb5dd?q=80&w=1000"
              alt="Ethnic Wear"
              sx={{
                display: { xs: 'none', md: 'block' },
                maxWidth: '100%',
                borderRadius: 2,
                boxShadow: '0 10px 30px rgba(0,0,0,0.1)'
              }}
            />
          </Box>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper
            sx={{
              p: 4,
              borderRadius: 2,
              boxShadow: '0 4px 20px rgba(0,0,0,0.1)'
            }}
          >
            <Box sx={{ textAlign: 'center', mb: 4 }}>
              <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold' }}>
                Customer Login
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Login to your customer account
              </Typography>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            <form onSubmit={handleSubmit}>
              <TextField
                fullWidth
                label="Email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                error={!!formErrors.email}
                helperText={formErrors.email}
                margin="normal"
                required
                sx={{ mb: 2 }}
              />

              <TextField
                fullWidth
                label="Password"
                name="password"
                type="password"
                value={formData.password}
                onChange={handleInputChange}
                error={!!formErrors.password}
                helperText={formErrors.password}
                margin="normal"
                required
                sx={{ mb: 1 }}
              />

              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={rememberMe}
                      onChange={(e) => setRememberMe(e.target.checked)}
                      sx={{
                        color: '#ff3f6c',
                        '&.Mui-checked': {
                          color: '#ff3f6c',
                        },
                      }}
                    />
                  }
                  label="Remember me"
                />
                <Link to="/forgot-password" style={{ color: '#ff3f6c', textDecoration: 'none' }}>
                  Forgot Password?
                </Link>
              </Box>

              <Button
                type="submit"
                fullWidth
                variant="contained"
                size="large"
                disabled={loading}
                sx={{
                  py: 1.5,
                  backgroundColor: '#ff3f6c',
                  '&:hover': { backgroundColor: '#ff527b' },
                  mb: 2
                }}
              >
                {loading ? <CircularProgress size={24} color="inherit" /> : 'Login'}
              </Button>

              <Box sx={{ textAlign: 'center', mt: 2 }}>
                <Typography variant="body1">
                  Don't have an account?{' '}
                  <Link to="/register" style={{ color: '#ff3f6c', textDecoration: 'none', fontWeight: 'bold' }}>
                    Register
                  </Link>
                </Typography>
              </Box>

              <Box sx={{ textAlign: 'center', mt: 2 }}>
                <Typography variant="body1">
                  Login with OTP instead?{' '}
                  <Link to="/otp-login" style={{ color: '#ff3f6c', textDecoration: 'none', fontWeight: 'bold' }}>
                    OTP Login
                  </Link>
                </Typography>
              </Box>

              <Box sx={{ textAlign: 'center', mt: 3 }}>
                <Typography variant="body2" color="text.secondary">
                  Are you an admin?{' '}
                  <Link to="/admin/login" style={{ color: '#ff3f6c', textDecoration: 'none' }}>
                    Admin Login
                  </Link>
                </Typography>
              </Box>
            </form>

            <Divider sx={{ my: 3 }} />

            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Demo Customer Account:
              </Typography>
              <Typography variant="body2" color="text.secondary">
                <EMAIL> / Customer123!
              </Typography>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default Login;
