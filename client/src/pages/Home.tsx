import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Container, Box, Typography, Button } from '@mui/material';
import { fetchProducts } from '../store/slices/productSlice';
import { fetchCategories } from '../store/slices/categorySlice';
import { fetchCollections } from '../store/slices/collectionSlice';
import { fetchWebsiteConfiguration, fetchWebsiteBanners } from '../store/slices/websiteConfigSlice';
import { RootState } from '../store';
import CustomerLayout from '../components/layout/CustomerLayout';
import '../styles/customerLayout.css';

// Home page components
import Banner from '../components/home/<USER>';
import CategorySection from '../components/home/<USER>';
import ProductCarousel from '../components/home/<USER>';
import CollectionSection from '../components/home/<USER>';

const Home: React.FC = () => {
  const dispatch = useDispatch();
  // Only use what we need from each selector
  const { categories, loading: categoriesLoading } = useSelector((state: RootState) => state.categories);
  const { collections, loading: collectionsLoading } = useSelector((state: RootState) => state.collections);
  const { configuration, banners } = useSelector((state: RootState) => state.websiteConfig);
  const { items: wishlistItems } = useSelector((state: RootState) => state.wishlist);

  // Fetch new arrivals and best selling products
  const [newArrivals, setNewArrivals] = React.useState([]);
  const [bestSelling, setBestSelling] = React.useState([]);
  const [newArrivalsLoading, setNewArrivalsLoading] = React.useState(false);
  const [bestSellingLoading, setBestSellingLoading] = React.useState(false);

  useEffect(() => {
    // @ts-ignore
    dispatch(fetchProducts({ isFeatured: true, pageSize: 8 }));
    // @ts-ignore
    dispatch(fetchCategories());
    // @ts-ignore
    dispatch(fetchCollections());
    // @ts-ignore
    dispatch(fetchWebsiteConfiguration());
    // @ts-ignore
    dispatch(fetchWebsiteBanners(true));

    // Fetch new arrivals
    setNewArrivalsLoading(true);
    fetch('http://localhost:5295/api/Product/NewArrivals?maxItems=10')
      .then(response => response.json())
      .then(data => {
        setNewArrivals(data);
        setNewArrivalsLoading(false);
      })
      .catch(error => {
        console.error('Error fetching new arrivals:', error);
        setNewArrivalsLoading(false);
      });

    // Fetch best selling products
    setBestSellingLoading(true);
    fetch('http://localhost:5295/api/Product/BestSelling?maxItems=10')
      .then(response => response.json())
      .then(data => {
        setBestSelling(data);
        setBestSellingLoading(false);
      })
      .catch(error => {
        console.error('Error fetching best selling products:', error);
        setBestSellingLoading(false);
      });
  }, [dispatch]);

  // Create default configuration for initial render
  const defaultConfig = {
    showBannerSection: true,
    showCategorySection: true,
    showNewArrivalsSection: true,
    showCollectionSection: true,
    showBestSellingSection: true,
    categorySectionTitle: 'Shop By Category',
    newArrivalsSectionTitle: 'New Arrivals',
    collectionSectionTitle: 'Shop By Collection',
    bestSellingSectionTitle: 'Best Selling'
  };

  // Use configuration from Redux or default if not available yet
  const config = configuration || defaultConfig;

  return (
    <CustomerLayout>
        {/* Banner Section - Full Width */}
        {config.showBannerSection && (
          <Box className="banner-container no-side-padding">
            <Banner banners={banners || []} />
          </Box>
        )}

      {/* Main Content - With Margins */}
      <Container className="content-container">
          {/* Shop By Category Section */}
          {config.showCategorySection && (
            <Box className="category-section">
              <CategorySection
                title={config.categorySectionTitle}
                categories={categories || []}
                loading={categoriesLoading}
              />
            </Box>
          )}

          {/* New Arrivals Section */}
          {config.showNewArrivalsSection && (
            <Box className="product-carousel-section">
              <ProductCarousel
                title={config.newArrivalsSectionTitle}
                products={newArrivals}
                loading={newArrivalsLoading}
                viewAllLink="/products?sortBy=date&sortOrder=desc"
                wishlistItems={wishlistItems || []}
              />
            </Box>
          )}

          {/* Shop By Collection Section */}
          {config.showCollectionSection && (
            <Box className="collection-section">
              <CollectionSection
                title={config.collectionSectionTitle}
                collections={collections || []}
                loading={collectionsLoading}
              />
            </Box>
          )}

          {/* Best Selling Section */}
          {config.showBestSellingSection && (
            <Box className="product-carousel-section">
              <ProductCarousel
                title={config.bestSellingSectionTitle}
                products={bestSelling}
                loading={bestSellingLoading}
                viewAllLink="/products?sortBy=popularity&sortOrder=desc"
                wishlistItems={wishlistItems || []}
              />
            </Box>
          )}

          {/* Newsletter Subscription */}
          <Box sx={{ py: { xs: 6, md: 8 }, mb: { xs: 2, md: 4 } }}>
            <Box
              sx={{
                textAlign: 'center',
                p: { xs: 3, md: 4 },
                borderRadius: 2,
                background: 'linear-gradient(145deg, #ffffff 0%, #f5f5f5 100%)',
                boxShadow: '0 10px 30px rgba(0,0,0,0.05)',
                border: '1px solid rgba(0,0,0,0.05)',
                position: 'relative',
                overflow: 'hidden',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '6px',
                  background: 'linear-gradient(90deg, #ff3f6c, #ff7eb3)',
                }
              }}
            >
              <Typography
                variant="h4"
                component="h2"
                sx={{
                  fontWeight: 'bold',
                  mb: 2,
                  fontSize: { xs: '1.5rem', sm: '2rem', md: '2.25rem' }
                }}
              >
                Join Our Newsletter
              </Typography>
              <Typography
                variant="body1"
                color="text.secondary"
                sx={{
                  mb: 4,
                  maxWidth: '600px',
                  mx: 'auto',
                  fontSize: { xs: '0.875rem', sm: '1rem' }
                }}
              >
                Subscribe to our newsletter and get 10% off your first purchase
              </Typography>
              <Box
                component="form"
                sx={{
                  display: 'flex',
                  flexDirection: { xs: 'column', sm: 'row' },
                  gap: 2,
                  maxWidth: 500,
                  mx: 'auto',
                  position: 'relative',
                  zIndex: 1
                }}
              >
                <input
                  type="email"
                  placeholder="Your email address"
                  style={{
                    flex: 1,
                    padding: '14px 16px',
                    borderRadius: '4px',
                    border: '1px solid #ddd',
                    fontSize: '16px',
                    boxShadow: '0 2px 10px rgba(0,0,0,0.05)',
                  }}
                />
                <Button
                  variant="contained"
                  color="primary"
                  sx={{
                    px: { xs: 3, sm: 4 },
                    py: { xs: 1.5, sm: 'auto' },
                    fontWeight: 'bold',
                    fontSize: { xs: '0.875rem', sm: '1rem' },
                    boxShadow: '0 4px 10px rgba(0,0,0,0.1)',
                    '&:hover': {
                      boxShadow: '0 6px 15px rgba(0,0,0,0.15)',
                      transform: 'translateY(-2px)'
                    },
                    transition: 'all 0.3s ease'
                  }}
                >
                  Subscribe
                </Button>
              </Box>
            </Box>
          </Box>
      </Container>
    </CustomerLayout>
  );
};

export default Home;
