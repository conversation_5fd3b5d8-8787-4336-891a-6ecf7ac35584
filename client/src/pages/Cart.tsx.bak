import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import {
  Typography,
  Container,
  Box,
  Paper,
  Grid,
  Button,
  IconButton,
  Divider,
  TextField,
  Card,
  CardMedia,
  Alert
} from '@mui/material';
import { Add, Remove, Delete } from '@mui/icons-material';
import { removeFromCart, updateQuantity, clearCart, applyCoupon } from '../store/slices/cartSlice';
import { RootState } from '../store';
import CouponInput from '../components/cart/CouponInput';

const Cart: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { items, totalItems, totalAmount, discountAmount, couponCode } = useSelector((state: RootState) => state.cart);
  const { isAuthenticated } = useSelector((state: RootState) => state.auth);

  const handleRemoveItem = (productId: number, variantId?: number) => {
    // @ts-ignore
    dispatch(removeFromCart({ productId, variantId }));
  };

  const handleUpdateQuantity = (productId: number, quantity: number, variantId?: number) => {
    if (quantity > 0) {
      // @ts-ignore
      dispatch(updateQuantity({ productId, quantity, variantId }));
    }
  };

  const handleClearCart = () => {
    if (window.confirm('Are you sure you want to clear your cart?')) {
      // @ts-ignore
      dispatch(clearCart());
    }
  };

  const handleCheckout = () => {
    navigate('/checkout');
  };

  if (items.length === 0) {
    return (
      <Container sx={{ py: 4 }} maxWidth="lg">
        <Typography variant="h4" gutterBottom component="h1">
          Shopping Cart
        </Typography>
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="h6" gutterBottom>
            Your cart is empty
          </Typography>
          <Button
            variant="contained"
            color="primary"
            component={Link}
            to="/products"
            sx={{ mt: 2 }}
          >
            Continue Shopping
          </Button>
        </Paper>
      </Container>
    );
  }

  return (
    <Container sx={{ py: 4 }} maxWidth="lg">
      <Typography variant="h4" gutterBottom component="h1">
        Shopping Cart
      </Typography>

      <Grid container spacing={4}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            {items.map((item) => (
              <Box key={`${item.productId}-${item.variantId || 'default'}`} sx={{ mb: 3 }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={3} sm={2}>
                    <Card>
                      <CardMedia
                        component="img"
                        height="100"
                        image={item.imageUrl || 'https://source.unsplash.com/random?product'}
                        alt={item.name}
                      />
                    </Card>
                  </Grid>
                  <Grid item xs={9} sm={4}>
                    <Typography variant="subtitle1" component={Link} to={`/product/${item.productId}`} sx={{ textDecoration: 'none', color: 'inherit' }}>
                      {item.name}
                    </Typography>
                    {item.size && (
                      <Typography variant="body2" color="text.secondary">
                        Size: {item.size}
                      </Typography>
                    )}
                    {item.sku && (
                      <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                        SKU: {item.sku}
                      </Typography>
                    )}
                    <Typography variant="body2" color="text.secondary">
                  <Grid item xs={6} sm={3}>
                    <Typography variant="body2" color="text.secondary">
                      ₹{(item.price || 0).toFixed(2)} each
                    </Typography>
                    {item.discountAmount > 0 && (
                      <Typography variant="body2" color="error">
                        Discount: -₹{item.discountAmount.toFixed(2)}
                      </Typography>
                    )}
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <IconButton
                        size="small"
                        onClick={() => handleUpdateQuantity(item.productId, item.quantity - 1, item.variantId)}
                        disabled={item.quantity <= 1}
                      >
                        <Remove fontSize="small" />
                      </IconButton>
                      <TextField
                        size="small"
                        value={item.quantity}
                        onChange={(e) => {
                          const value = parseInt(e.target.value);
                          if (!isNaN(value)) {
                            handleUpdateQuantity(item.productId, value, item.variantId);
                          }
                        }}
                        inputProps={{ min: 1, style: { textAlign: 'center' } }}
                        sx={{ width: '60px', mx: 1 }}
                      />
                      <IconButton
                        size="small"
                        onClick={() => handleUpdateQuantity(item.productId, item.quantity + 1, item.variantId)}
                      >
                        <Add fontSize="small" />
                      </IconButton>
                    </Box>
                  </Grid>
                  <Grid item xs={4} sm={2} sx={{ textAlign: 'right' }}>
                  </Grid>
                  <Grid item xs={2} sm={1} sx={{ textAlign: 'right' }}>
                    <IconButton
                      color="error"
                      onClick={() => handleRemoveItem(item.productId, item.variantId)}
                    >
                      <Delete />
                    </IconButton>
                  </Grid>
                </Grid>
                <Divider sx={{ mt: 2 }} />
              </Box>
            ))}

            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
              <Button
                variant="outlined"
                component={Link}
                to="/products"
              >
                Continue Shopping
              </Button>
              <Button
                variant="outlined"
                color="error"
                onClick={handleClearCart}
              >
                Clear Cart
              </Button>
            </Box>
          </Paper>
        </Grid>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Order Summary
            </Typography>
            <CouponInput
              onApplyCoupon={(code, discount) => {
                // @ts-ignore
                dispatch(applyCoupon({ code, discountAmount: discount }));
              }}
              cartTotal={totalAmount}
              totalItems={totalItems}
            />

            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body1">
                Subtotal ({totalItems} items)
              </Typography>
              <Typography variant="body1">
                ₹{(totalAmount || 0).toFixed(2)}
              </Typography>
            </Box>

            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body1">
                Shipping
              </Typography>
              <Typography variant="body1">
                Free
              </Typography>
            </Box>

            {discountAmount > 0 && (
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body1" color="error">
                  Discount {couponCode && `(${couponCode})`}
                </Typography>
                <Typography variant="body1" color="error">
                  -₹{discountAmount.toFixed(2)}
                </Typography>
              </Box>
            )}

            <Divider sx={{ my: 2 }} />

            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
              <Typography variant="h6">
                Total
              </Typography>
              <Typography variant="h6">
                ₹{((totalAmount || 0) - (discountAmount || 0)).toFixed(2)}
              </Typography>
            </Box>

            {!isAuthenticated && (
              <Alert severity="info" sx={{ mb: 2 }}>
                You can checkout as a guest or <Link to="/login">login</Link> to use your saved information.
              </Alert>
            )}

            <Button
              variant="contained"
              color="primary"
              fullWidth
              size="large"
              onClick={handleCheckout}
            >
              Proceed to Checkout
            </Button>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default Cart;
