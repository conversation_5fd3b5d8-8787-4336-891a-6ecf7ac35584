import api from './api';

export interface ProductFilterValues {
  productTypes: { id: number; name: string }[];
  sizes: string[];
  colors: string[];
}

export const getProductFilters = async (
  categoryId?: number,
  collectionId?: number
): Promise<ProductFilterValues> => {
  try {
    let url = '/Product/filters';
    const params = new URLSearchParams();
    
    if (categoryId) {
      params.append('categoryId', categoryId.toString());
    }
    
    if (collectionId) {
      params.append('collectionId', collectionId.toString());
    }
    
    const queryString = params.toString();
    if (queryString) {
      url += `?${queryString}`;
    }
    
    const response = await api.get(url);
    return response.data;
  } catch (error) {
    console.error('Error fetching product filters:', error);
    return {
      productTypes: [],
      sizes: [],
      colors: []
    };
  }
};
