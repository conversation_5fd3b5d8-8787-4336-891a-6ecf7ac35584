import api from './api';

export interface ProductType {
  id: number;
  name: string;
  description: string;
}

export const getProductTypes = async (): Promise<ProductType[]> => {
  try {
    const response = await api.get('/Product/types');
    return response.data;
  } catch (error) {
    console.error('Error fetching product types:', error);
    return []; // Return empty array instead of throwing error
  }
};

export const getProductType = async (id: number): Promise<ProductType> => {
  const response = await api.get(`/ProductType/${id}`);
  return response.data;
};

export const createProductType = async (productType: Omit<ProductType, 'id'>): Promise<ProductType> => {
  try {
    const response = await api.post('/ProductType', productType);
    return response.data;
  } catch (error) {
    console.error('Error creating product type:', error);
    throw error;
  }
};

export const updateProductType = async (productType: ProductType): Promise<void> => {
  await api.put(`/ProductType/${productType.id}`, productType);
};

export const deleteProductType = async (id: number): Promise<void> => {
  await api.delete(`/ProductType/${id}`);
};
