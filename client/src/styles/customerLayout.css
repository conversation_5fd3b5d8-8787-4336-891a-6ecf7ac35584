/* Customer Portal Layout Styles */

/* Global container styles for consistent layout */
.full-width-container {
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  overflow: hidden;
}

/* Banner container - always full width (edge to edge) */
.banner-container {
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  position: relative;
}

/* Banner divider - adds a subtle separation between banner and content */
.banner-container::after {
  content: '';
  display: block;
  width: 100%;
  height: 1px;
  background: linear-gradient(to right, rgba(0,0,0,0.02), rgba(0,0,0,0.1), rgba(0,0,0,0.02));
  margin-top: 0;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.banner-container .MuiContainer-root {
  max-width: 100% !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  width: 100% !important;
}

/* Content container with margins - for ALL content except banners */
.content-container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  box-sizing: border-box;
}

/* Section spacing for consistent vertical rhythm */
.section-spacing {
  margin-top: 3rem;
  margin-bottom: 3rem;
}

/* Category section specific spacing */
.category-section {
  margin-top: 3rem;
  margin-bottom: 3rem;
}

/* Product carousel section spacing */
.product-carousel-section {
  margin-top: 3rem;
  margin-bottom: 3rem;
}

/* Collection section spacing */
.collection-section {
  margin-top: 3rem;
  margin-bottom: 3rem;
}

/* Grid layout for product listings */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  width: 100%;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .content-container {
    padding: 0 1.5rem;
  }

  .section-spacing,
  .category-section,
  .product-carousel-section,
  .collection-section {
    margin-top: 2.5rem;
    margin-bottom: 2.5rem;
  }
}

@media (max-width: 768px) {
  .content-container {
    padding: 0 1rem;
  }

  .section-spacing,
  .category-section,
  .product-carousel-section,
  .collection-section {
    margin-top: 2rem;
    margin-bottom: 2rem;
  }
}

@media (max-width: 600px) {
  .content-container {
    padding: 0 0.75rem;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 0.5rem;
  }

  .section-spacing,
  .category-section,
  .product-carousel-section,
  .collection-section {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
  }
}

/* Section padding for vertical spacing */
.section-padding {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

/* Inner content with optional side padding */
.inner-content {
  padding-left: 1rem;
  padding-right: 1rem;
}

/* No side padding option */
.no-side-padding {
  padding-left: 0;
  padding-right: 0;
}

/* Paper content padding */
.content-paper {
  padding: 1rem;
  margin-bottom: 1rem;
}

/* Override for all pages in customer portal */
.customer-portal-page {
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding: 0;
}
