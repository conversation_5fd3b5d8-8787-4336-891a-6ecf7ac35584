{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.1", "@mui/material": "^7.0.1", "@mui/x-date-pickers": "^7.28.3", "@reduxjs/toolkit": "^2.6.1", "@types/react-color": "^3.0.13", "@types/react-helmet": "^6.1.11", "axios": "^1.8.4", "date-fns": "^2.30.0", "formik": "^2.4.6", "react": "^19.0.0", "react-color": "^2.19.3", "react-dom": "^19.0.0", "react-helmet": "^6.1.0", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.4.1", "react-toastify": "^11.0.5", "swiper": "^11.2.6", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}