{"folders": [{"path": "."}], "settings": {"omnisharp.enableRoslynAnalyzers": true, "omnisharp.enableEditorConfigSupport": true, "dotnet.defaultSolution": "MyShop.sln", "omnisharp.useModernNet": true, "omnisharp.path": "latest", "csharp.format.enable": true, "csharp.semanticHighlighting.enabled": true, "editor.formatOnSave": true, "editor.formatOnType": true, "dotnet.server.useOmnisharp": true, "dotnet.server.path": "latest"}, "launch": {"version": "0.2.0", "configurations": [{"name": "F5 Debug", "type": "coreclr", "request": "launch", "preLaunchTask": "build-f5", "program": "${workspaceFolder}/API/bin/Debug/net8.0/MyShop.API.dll", "args": [], "cwd": "${workspaceFolder}/API", "console": "internalConsole", "stopAtEntry": false, "internalConsoleOptions": "openOnSessionStart", "env": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "http://localhost:5295"}}]}}